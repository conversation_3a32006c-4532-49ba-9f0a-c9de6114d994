{"extends": ["eslint:recommended", "next", "next/core-web-vitals"], "globals": {"React": "readonly"}, "root": true, "parser": "@typescript-eslint/parser", "env": {"browser": true, "node": true, "es6": true}, "rules": {"no-unused-vars": [0, {"args": "after-used", "argsIgnorePattern": "^_"}], "quotes": ["error", "single"], "no-duplicate-imports": "error", "arrow-parens": ["error", "always"], "camelcase": "error", "comma-dangle": ["error", "always-multiline"], "indent": ["error", 2, {"SwitchCase": 1}], "no-console": 0, "no-var": "error", "one-var": ["error", {"const": "never", "let": "consecutive"}], "quote-props": ["error", "consistent-as-needed"], "no-extra-semi": "warn", "semi": ["error", "never"], "space-before-function-paren": ["error", "always"], "react-hooks/exhaustive-deps": 0}}