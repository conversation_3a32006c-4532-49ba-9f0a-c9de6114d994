# NFT LAND Client

## Versions

| Software | Versions |
| --- |----------|
| node | v20.9.0  |
| yarn | v1.22.22 |

* When we install `JS SDK` for connecting `Nft Land Contracts`, it might be necessary to change the versions

## First Read
- [Architecture (Repository Pattern)](/documents/architecture.md)
* [Specificaitons of v1.1](/documents/specifications-v1-1.md)

## Commands

| Command | Description |
| --- | --- |
| `yarn dev` | Starting web server for development purpose |
| `yarn build` | Building typescript codes |
| `yarn build-static` |  |
| `yarn start` | Starting webserver for production with built code |
| `yarn stg` | Starting webserver for staging with built code |
| `yarn lint` | Check lint |
| `yarn lint:fix` | Fix lint automatically |

| `yarn test` | Running tests |

## Getting Started

First, install package and dependencies:

```bash
yarn install
```

Second, run the development server:

```bash
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.
