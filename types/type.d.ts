import Chain from 'domain/value_objects/chain'
import Marketplace from 'domain/value_objects/marketplace'

export type Category = {
  id: string,
  name: string,
  slug: string,
  thumbnail: string,
}

export type ProductType = {
  id?: string,
  name: string,
  title?: string,
  avatarIcon: string,
  heart?: number,
  symbol: string,
  price: number,
  floorPrice?: number,
  volume?: number,
  offerPrice?: number,
  offerSymbol?: string,
  remainDay?: number,
  imageSrc: string,
  category?: string[],
  watermarkIcon?: string,
  link?: string,
  videoSrc?: string,
  rank?: string | number,
  className?: string,
}

export type Asset = {}

export type Options = {
  name?: string,
  value?: string | number,
  icon?: string,
}

export type FilterParams = {
  categoryIds?: Array<string>,
  collectionIds?: string[],
  filterStatuses?: Array<string>,
  filterEventTypes?: Array<string>,
  marketplaces?: Marketplace[],
  listed?: boolean,
  rarityScore?: string,
  paymentTokens?: Array<string>,
  priceTokenUnit?: string | number,
  minPrice?: number | '',
  maxPrice?: number | '',
  chains?: Chain[],
  periodRange?: string,
  userId?: string,
  page?: number,
  limit?: number,
  sortBy?: string,
  ownerId?: string,
  chainId?: number | '',
  traits?: any[],
  // for activity page
  unitPriceFrom?: number | '',
  unitPriceTo?: number | '',
  currencyId?: string,
}
 & TableFilterParams

export type TableFilterParams = {
  categoryId?: string,
  chain?: string,
  lastDayNumber?: string,
}

export type FormData = {
  logoImage?: string,
}

export type CountDown = {
  day: number,
  hrs: number,
  min: number,
  sec: number,
}

export type NftDropProps = {
  name?: string,
  category?: string,
  characterImage: string,
  price?: number,
  volume?: number,
  priceSymbol?: string,
  description?: string,
  date?: string,
  time?: string,
  countDown: CountDown,
}

export type NftTableItemType = {
  imagesrc: string,
  name: string,
}

export type CollectionTableItemProps = {
  name?: string,
  imagesrc: string,
  nickname?: string,
  className?: string,
}

export type PriceBarTableItemProps = {
  price?: string,
  percentage?: string,
}

export type NullableBoolean = boolean | null