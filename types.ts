let TYPES = {

  // local datasource
  StorageDatasource: Symbol('StorageDatasource'),
  TokenManager: Symbol('TokenManager'),

  // remote service
  ApiClient: Symbol('ApiClient'),

  // remote datasource
  ActivityDatasource: Symbol('ActivityDatasource'),
  AnnouncementDatasource: Symbol('AnnouncementDatasource'),
  AssetDailyStatDatasource: Symbol('AssetDailyStatDatasource'),
  AssetDatasource: Symbol('AssetDatasource'),
  AssetImpressionDatasource: Symbol('AssetImpressionDatasource'),
  AssetLikeDatasource: Symbol('AssetLikeDatasource'),
  BoostedCollectionDatasource: Symbol('BoostedCollectionDatasource'),
  CategoryDatasource: Symbol('CategoryDatasource'),
  CollectionDailyStatDatasource: Symbol('CollectionDailyStatDatasource'),
  CollectionDatasource: Symbol('CollectionDatasource'),
  CollectionImpressionDatasource: Symbol('CollectionImpressionDatasource'),
  CollectionLikeDatasource: Symbol('CollectionLikeDatasource'),
  CollectionWatchDatasource: Symbol('CollectionWatchDatasource'),
  ContractDatasource: Symbol('ContractDatasource'),
  EmailVerificationDatasource: Symbol('EmailVerificationDatasource'),
  ImpressionDatasource: Symbol('ImpressionDatasource'),
  LikeDatasource: Symbol('LikeDatasource'),
  ListingDatasource: Symbol('ListingDatasource'),
  MainBannerDatasource: Symbol('MainBannerDatasource'),
  MultimediaDatasource: Symbol('MultimediaDatasource'),
  OfferDatasource: Symbol('OfferDatasource'),
  ReportDatasource: Symbol('ReportDatasource'),
  UserDatasource: Symbol('UserDatasource'),
  UserNotificationSettingDatasource: Symbol('UserNotificationSettingDatasource'),
  UserOfferSettingDatasource: Symbol('UserOfferSettingDatasource'),
  UserPublicAddressDatasource: Symbol('UserPublicAddressDatasource'),
  VendingMachineRemoteDatasource: Symbol('VendingMachineRemoteDatasource'),
  WatchDatasource: Symbol('WatchDatasource'),

  // repositories
  ActivityRepository: Symbol('ActivityRepository'),
  AnnouncementRepository: Symbol('AnnouncementRepository'),
  AssetDailyStatRepository: Symbol('AssetDailyStatRepository'),
  AssetImpressionRepository: Symbol('AssetImpressionRepository'),
  AssetLikeRepository: Symbol('AssetLikeRepository'),
  AssetRepository: Symbol('AssetRepository'),
  BoostedCollectionRepository: Symbol('BoostedCollectionRepository'),
  CategoryRepository: Symbol('CategoryRepository'),
  CollectionDailyStatRepository: Symbol('CollectionDailyStatRepository'),
  CollectionImpressionRepository: Symbol('CollectionImpressionRepository'),
  CollectionLikeRepository: Symbol('CollectionLikeRepository'),
  CollectionRepository: Symbol('CollectionRepository'),
  CollectionWatchRepository: Symbol('CollectionWatchRepository'),
  ContractRepository: Symbol('ContractRepository'),
  EmailVerificationRepository: Symbol('EmailVerificationRepository'),
  ImpressionRepository: Symbol('ImpressionRepository'),
  LikeRepository: Symbol('LikeRepository'),
  ListingRepository: Symbol('ListingRepository'),
  MainBannerRepository: Symbol('MainBannerRepository'),
  MultimediaRepository: Symbol('MultimediaRepository'),
  OfferRepository: Symbol('OfferRepository'),
  ReportRepository: Symbol('ReportRepository'),
  TokenRepository: Symbol('TokenRepository'),
  UserNotificationSettingRepository: Symbol('UserNotificationSettingRepository'),
  UserOfferSettingRepository: Symbol('UserOfferSettingRepository'),
  UserPublicAddressRepository: Symbol('UserPublicAddressRepository'),
  UserRepository: Symbol('UserRepository'),
  VendingMachineRepository: Symbol('VendingMachineRepository'),
  WatchRepository: Symbol('WatchRepository'),

  // use cases
  AuthenticationUseCase: Symbol('AuthenticationUseCase'),
}

export default TYPES