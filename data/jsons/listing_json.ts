import { User<PERSON><PERSON> } from './user_json'
import { ListingAssetJson } from './listing_asset_json'
import { AssetJson } from './asset_json'

export type ListingJson = {
    id: string;
    userId: string;
    assets: AssetJson[];
    quantities: number[];
    listingTypeId: number;
    auctionMethodId: number|null;
    paymentTokenId: number;
    price: number|null;
    startingPrice: number|null;
    endingPrice: number|null;
    startAt: string|null;
    endAt: string|null;
    isBundle: boolean;
    bundleName: string|null;
    bundleDescription: string|null;
    isReservedForBuyer: boolean;
    reservedBuyerAddress: string|null;
    hasReservePrice: boolean;
    reservePrice: number|null;
    statusId: number;
    createdAt: string;
    updatedAt: string;
    signature: string;
    orderParams: string;
    orderHash: string;

    user: UserJson|null;
    listingAssets: ListingAssetJson[];
    listingType: any;
    auctionMethod: any;
    paymentToken: any|null;
    status: any;
}
