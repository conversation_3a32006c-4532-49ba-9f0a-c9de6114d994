import { AssetAttributeLevelJson } from 'data/jsons/asset_attribute_level_json'
import { AssetAttributePropertyJson } from 'data/jsons/asset_attribute_property_json'
import { AssetAttributeStatJson } from 'data/jsons/asset_attribute_stat_json'

export type AssetsCreateMetadata = {
    name: string,
    imageId: string | null,
    collectionId: string | null,
    description: string | null;
    properties: AssetAttributePropertyJson[] | [],
    levels: AssetAttributeLevelJson[] | [],
    stats: AssetAttributeStatJson[] | [],
}
