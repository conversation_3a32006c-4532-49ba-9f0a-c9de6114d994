import { AssetAttributeLevelJson } from 'data/jsons/asset_attribute_level_json'
import { AssetAttributePropertyJson } from 'data/jsons/asset_attribute_property_json'
import { AssetAttributeStatJson } from 'data/jsons/asset_attribute_stat_json'


export type AssetsCreate = {
    name: string,
    externalLink: string | null,
    description: string | null;
    properties: AssetAttributePropertyJson[] | [],
    levels: AssetAttributeLevelJson[] | [],
    stats: AssetAttributeStatJson[] | [],
    collectionId: string | null,
    unlockableContent: string|null,
    hasUnlockableContent: Boolean|Number,
    isExplicit: Boolean,
    supply: Number,
    tokenId: Number,
    tokenUri: string | null,
    metadataId: string | null,
    imageId: string | null,
    txHash: string | null
}
