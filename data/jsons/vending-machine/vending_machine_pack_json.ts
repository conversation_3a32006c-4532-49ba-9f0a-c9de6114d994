export interface OddsJson {
  id?: number;
  fromPrice: number;
  toPrice: number;
  rate: number;
}

export type VendingMachinePackJson = {
  id: string;
  name: string;
  description: string;
  price: number;
  categoryId: string;
  imageUrl: string;
  setting: Record<string, any>;
  isEnabled: boolean;
  createdAt: string;
  updatedAt: string;
}

export type VendingMachinePackListJson = {
  limit: number;
  page: number;
  total: number;
  items: VendingMachinePackJson[];
}
