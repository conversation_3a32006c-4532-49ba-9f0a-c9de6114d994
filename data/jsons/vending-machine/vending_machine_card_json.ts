import { VendingMachineCategoryJson } from './vending_machine_category_json'

export type VendingMachineImportCardJson = {
  id: string
  cardId: string
  status: string
  message: string | null
  scrapedAt: string | null
  createdAt: string
  updatedAt: string
}

export type VendingMachineImportCardListJson = {
  limit: number
  page: number
  total: number
  items: VendingMachineImportCardJson[]
}

export type VendingMachineCardCsvJson = {
  count: number
  items: VendingMachineImportCardJson[]
}

export type VendingMachineCardJson = {
  id: string
  categoryId: string | null
  category: VendingMachineCategoryJson | undefined
  cardId: string
  currency: string
  scrapedAt: string
  source: string
  status: string
  price: string
  shop: string
  stock: number
  mintedQty: number
  buyBackQty: number
  convertedQty: number
  isEnabled: boolean
  metadata: any
  createdAt: string
  updatedAt: string
}

export type VendingMachineCardListJson = {
  limit: number
  page: number
  total: number
  items: VendingMachineCardJson[]
}

export type PayloadUpdateVendingMachineCardJson = {
  isEnabled?: boolean
  stock?: number
  price?: string
  status?: string
}

export type CheckoutJson = {
  id: string
  date: string
  category: string
  price: string
  cardId: string
  quantity: number
  mintTxHash: string
  toAddress: string
  status: string
  checkoutTxHash: string
  checkoutBlockchainTimestamp: string
  mintBlockchainTimestamp: string
  chainId: number
  lastError: string
}

export type ListCheckoutJson = {
  checkouts: CheckoutJson[]
  pagination: {
    currentPage: number
    perPage: number
    total: number
    lastPage: number
  }
}
