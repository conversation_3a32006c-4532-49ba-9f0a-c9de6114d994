import { AssetAttributeLevelJson } from './asset_attribute_level_json'
import { AssetAttributePropertyJson } from './asset_attribute_property_json'
import { AssetAttributeStatJson } from './asset_attribute_stat_json'
import { CollectionJson } from './collection_json'
import { ContractJson } from './contract_json'
import { MultimediaJson } from './multimedia_json'
import { UserJson } from './user_json'

export type AssetJson = {
    id: string;
    collectionId: string | null;
    contractId: string;
    chainId: number;
    address: string;
    tokenId: string;
    tokenUri: string|null;
    metadataId: string|null;
    imageId: string|null;
    txHash: string|null;
    name: string|null;
    externalUrl: string|null;
    description: string|null;
    thumbnailId: string|null;
    multimediaId: string|null;
    hasUnlockableContent: boolean;
    unlockableContent: string|null;
    isExplicit: boolean;
    metadataStatusId: number;
    metadataJson: string|null;
    numOwners: number|null;
    totalSupply: number|null;
    numTotalImpressions: number|null;
    numTotalLikes: number|null;
    currentPaymentTokenId: number | null;
    currentPrice: number|null;
    minListingId: number|null;
    minListingTypeId: number|null;
    minListingPaymentTokenId: number|null;
    minListingPrice: number|null;
    minListingExpiresAt: string|null;
    minListingCreatedAt: string|null;
    lastSoldListingId: number|null;
    lastSoldPaymentTokenId: number|null;
    lastSoldPrice: number|null;
    lastSoldAt: string|null;
    hasActiveOffers: boolean;
    createdAt: string;
    updatedAt: string;

    collection: CollectionJson|null;
    contract: ContractJson|null;
    chain: any|null;
    thumbnail: MultimediaJson|null;
    multimedia: MultimediaJson|null;
    metadataStatus: any;
    currentPaymentToken: any|null;
    minListingType: any|null;
    minListingPaymentToken: any;
    lastSoldPaymentToken: any|null;
    attributeLevels: AssetAttributeLevelJson[];
    attributeProperties: AssetAttributePropertyJson[];
    attributeStats: AssetAttributeStatJson[];
    marketplaces: any[];
    creators: UserJson[];
    owners: UserJson[];
}
