import { AssetJson } from './asset_json'
import { UserJson } from './user_json'

export type ActivityJson = {
    id: string;
    assetId: string;
    activityEventId: number;
    collectionId: string | null;
    chainId: number;
    paymentTokenId: number | null;
    price: number|null;
    quantity: number|null;
    fromUserId: string | null;
    toUserId: string | null;
    createdAt: string;
    updatedAt: string;

    asset: AssetJson;
    activityEvent: any;
    chain: any;
    paymentToken: any|null;
    fromUser: UserJson|null;
    toUser: UserJson | null;
    minedAt: string;
    recipient: string;
    offerer: string;
    
}
