export type UserNotificationSettingJson = {
    id: string;
    userId: string;
    onItemSold: boolean;
    onBidActivity: boolean;
    onPriceChange: boolean;
    onAuctionExpiration: boolean;
    onOutbid: boolean;
    onReferralSuccess: boolean;
    onOwnedAssetUpdates: boolean;
    onSuccessfullyPurchase: boolean;
    onNewsLetter: boolean;
    minimumBidThresholdUsd: number;
    minimumBidThresholdJpy: number;
    createdAt: string;
    updatedAt: string;
}
