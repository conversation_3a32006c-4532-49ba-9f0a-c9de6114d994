import { MultimediaJson } from './multimedia_json'

export type UserJson = {
    id: string;
    username: string;
    email: string|null;
    thumbnailId: string | null;
    backgroundId: string | null;
    description: string|null;
    twitterUsername: string|null;
    instagramUsername: string|null;
    webUrl: string|null;
    languageId: number;
    lastAccessedAt: string|null;
    createdAt: string;
    updatedAt: string;

    thumbnail: MultimediaJson|null;
    background: MultimediaJson|null;
    language: any;
    publicAddresses: string[];

    receivePercent?: number;
    roleId?: number;
    internalWallet?: string;
    nonce?: string|number;
}
