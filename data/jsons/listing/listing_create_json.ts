export type ListingCreateJson = {
    assetIds: string[] | null;
    quantities: number[];
    listingType: string;
    auctionMethod: string|null;
    paymentTokenId: string|number|null;
    price: number;
    endingPrice: number|null;
    startAt: string;
    endAt: string;
    isBundle: boolean;
    bundleName: string|null;
    bundleDescription: string|null;
    isReservedForBuyer: boolean;
    reservedBuyerAddress: string|null;
    hasReservePrice: boolean;
    reservePrice: number|null;
    signature: string;
    orderParams: string;
    orderHash: string|null;
}
