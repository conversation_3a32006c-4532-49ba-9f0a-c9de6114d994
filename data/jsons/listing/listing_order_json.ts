import { User<PERSON><PERSON> } from '../user_json'
import { ListingAssetJson } from '../listing_asset_json'
import { AssetJson } from '../asset_json'
import { ListingJson } from '../listing_json'

export type ListingOrderJson = {
    id: string;
    createdAt: string;
    updatedAt: string;
    quantity: number | 0,
    orderCode: string | null,
    orderAmount: number | 0,
    orderAmountType: string | null,
    orderChainId: string | number | null,
    orderPaymentToken: string | null,
    orderPaymentUrl: string | null,
    orderTxHash: string | null,
    txHash: string | null,
    
    userId: string;
    userAddress: string;
    user: UserJson|null;

    listingId: string;
    listing: ListingJson|null;

    statusId: number;
    status: any;
    orderStatusId: number;
    orderStatus: any;
}
