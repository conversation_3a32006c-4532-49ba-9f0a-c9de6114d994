import { Asset<PERSON><PERSON> } from './asset_json'
import { UserJson } from './user_json'

export type OfferJson = {
    id: string;
    listingId: string | null;
    assetId: string | null;
    quantity: number;
    userId: string;
    paymentTokenId: number;
    price: number;
    statusId: number;
    expiresAt: string|null;
    createdAt: string;
    updatedAt: string;
    orderHash: string;
    orderParams: string;
    signature: string|null;
    asset: AssetJson|null;
    user: UserJson|null;
    paymentToken: any;
    status: any;
}
