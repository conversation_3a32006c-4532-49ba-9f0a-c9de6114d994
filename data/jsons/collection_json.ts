import { CategoryJson } from './category_json'
import { MultimediaJson } from './multimedia_json'
import { UserJson } from './user_json'

export type CollectionJson = {
  id: string;
  slug: string;
  name: string | null;
  symbol: string | null;
  description: string | null;
  logoImageId: string | null;
  featuredImageId: string | null;
  bannerImageId: string | null;
  categoryId: string | null;
  webUrl: string | null;
  discordUrl: string | null;
  telegramUrl: string | null;
  twitterUrl: string | null;
  mediumUsername: string | null;
  twitterUsername: string | null;
  instagramUsername: string | null;
  isExplicit: boolean;
  numAssets: number;
  numOwners: number;
  totalVolumeUsd: number | null;
  totalVolumeJpy: number | null;
  floorPriceUsd: number | null;
  floorPriceJpy: number | null;
  createdAt: string;
  updatedAt: string;

  logoImage: MultimediaJson | null;
  featuredImage: MultimediaJson | null;
  bannerImage: MultimediaJson | null;
  category: CategoryJson | null;
  owner: UserJson;
  paymentTokens: any[];
  payoutAddresses: string[];
  collaborators: UserJson[];
  // collectionAddress: string | null;
  contractAddress: string | null;
  payouts: any[];
  chainId: number;
  schemaId: number;
  contract: any;
  rankingVolume: string | number,
  recommendationImage: MultimediaJson | null,
  traitTypes: any[]
};
