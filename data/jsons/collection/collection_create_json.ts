export type CollectionCreateJson = {
    slug: string;
    name: string;
    symbol: string | null;
    chainId: number | null;
    description: string|null;
    logoImageId: string|null;
    featuredImageId: string|null;
    bannerImageId: string|null;
    categoryId: string|null;
    webUrl: string|null;
    discordUrl: string|null;
    telegramUrl: string | null;
    twitterUrl: string|null;
    mediumUsername: string|null;
    twitterUsername: string|null;
    instagramUsername: string|null;
    isExplicit: boolean;
    collaboratorIds: string[];
    paymentTokenIds: any[];
    payouts: any[];
    contractAddress: string | null;
    schemaId: number | null;
}
