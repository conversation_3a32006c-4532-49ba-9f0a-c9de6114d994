import { injectable } from 'inversify'
import 'reflect-metadata'

import StorageDatasource from '../interfaces/storage_datasource'

@injectable()
export default class LocalStorageDatasource implements StorageDatasource {

  read (key: string): string|null {
    if (typeof window === 'undefined') {
      return null // Server-side: return null for localStorage
    }
    return window.localStorage.getItem(key)
  }

  write (key: string, value: string): void {
    if (typeof window === 'undefined') {
      return // Server-side: do nothing
    }
    window.localStorage.setItem(key, value)
  }

  delete (key: string): void {
    if (typeof window === 'undefined') {
      return // Server-side: do nothing
    }
    window.localStorage.removeItem(key)
  }

  deleteAll (): void {
    if (typeof window === 'undefined') {
      return // Server-side: do nothing
    }
    window.localStorage.clear()
  }

}