import 'reflect-metadata'

import { Query } from '../interfaces/main_banner_datasource/query'
import { MainBannerJson } from 'data/jsons/main_banner_json'
import MainBannerDatasource from '../interfaces/main_banner_datasource'

export const mainBannerMockData = [
  {
    title: 'Banner 1',
    bannerId: 'xxxxxxxxxxxxxxx-1',
    url: 'google.com',
    priority: 1,
    statusId: 9,
    expiresAt: null,
    createdAt: '2024-02-18 13:12:00',
    updatedAt: '2024-02-18 13:12:00',
    banner: {
      url: '/asset/images/Hero_Banner1.png',
    },
    status: {
      id: 9,
      name: 'opened',
      label: 'opened',
    },
  },
  {
    title: 'Banner 2',
    bannerId: 'xxxxxxxxxxxxxxx-2',
    url: 'google.com',
    priority: 2,
    statusId: 9,
    expiresAt: null,
    createdAt: '2024-02-18 13:12:00',
    updatedAt: '2024-02-18 13:12:00',
    banner: {
      url: '/asset/images/Hero_Banner2.png',
    },
    status: {
      id: 9,
      name: 'opened',
      label: 'opened',
    },
  },
  {
    title: 'Banner 3',
    bannerId: 'xxxxxxxxxxxxxxx-3',
    url: 'google.com',
    priority: 3,
    statusId: 9,
    expiresAt: null,
    createdAt: '2024-02-18 13:12:00',
    updatedAt: '2024-02-18 13:12:00',
    banner: {
      url: '/asset/images/Hero_Banner3.png',
    },
    status: {
      id: 9,
      name: 'opened',
      label: 'opened',
    },
  },
]

export default class MainBannerMockDatasource implements MainBannerDatasource {

  constructor (){}

  findById (id: string): Promise<MainBannerJson> {
    return new Promise((resolve, reject) => {
      const idInt = Number.parseInt(id)
      if (idInt < 0 || idInt > 2) {
        reject(new Error(`id is invalid, id:${id}`))
      } else {
        resolve(mainBannerMockData[idInt] as MainBannerJson)
      }
    })
  }

  search (_: Query): Promise<MainBannerJson[]> {
    return new Promise((resolve, reject) => {
      resolve(mainBannerMockData as MainBannerJson[])
    })
  }

  totalCount (_: Query): Promise<number> {
    return Promise.resolve(mainBannerMockData.length)
  }

}
