import { VendingMachineCategory<PERSON>son, VendingMachineCategoryListJson } from 'data/jsons/vending-machine/vending_machine_category_json'
import { Query } from 'domain/repositories/vending_machine_category_repository/query'

export default interface VendingMachineCategoryDatasource {
    list(query: Query): Promise<VendingMachineCategoryListJson>
    getById(id: string): Promise<VendingMachineCategoryJson>
    create(params: CreateCategoryParams): Promise<VendingMachineCategoryJson>
    update(id: number, params: UpdateCategoryParams): Promise<VendingMachineCategoryJson>
    delete(id: number): Promise<void>
}

export interface CreateCategoryParams {
    name: string;
    enable?: boolean;
}

export interface UpdateCategoryParams {
    name?: string;
    enable?: boolean;
}
