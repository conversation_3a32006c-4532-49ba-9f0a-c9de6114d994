export class Query {

  public collectionId: string|null = null
  public userId: string|null = null
  public minCreatedAt: string|null = null
  public maxCreatedAt: string|null = null

  public limit:number = 60
  public page:number = 1

  public static fromJson (json?:{[key:string]:any}) {
    return new this(json)
  }

  constructor (options?:{[key:string]:any}) {
    if (options === undefined) {
      return
    }
    if (options['collectionId'] !== undefined) {
      this.collectionId = options['collectionId']
    }
    if (options['userId'] !== undefined) {
      this.userId = options['userId']
    }
    if (options['minCreatedAt'] !== undefined) {
      this.minCreatedAt = options['minCreatedAt']
    }
    if (options['maxCreatedAt'] !== undefined) {
      this.maxCreatedAt = options['maxCreatedAt']
    }

    if (options['limit'] != undefined) {
      this.limit = options['limit']
    }
    if (options['page'] != undefined) {
      this.page = options['page']
    }
  }

  public toJson (): {[key:string]:any} {
    return {
      collectionId: this.collectionId,
      userId: this.userId,
      minCreatedAt: this.minCreatedAt,
      maxCreatedAt: this.maxCreatedAt,
      limit: this.limit,
      page: this.page,
    }
  }
}
