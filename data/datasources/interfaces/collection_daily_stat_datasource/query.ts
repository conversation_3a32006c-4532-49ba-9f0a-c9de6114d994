export class Query {

  public periodRange: string|null = null
  public collectionId: string|null = null
  public limit:number = 60
  public page: number = 1
  public assetIds: string[] | null = null
  public collectionIds: string[] | null = null

  public static fromJson (json?:{[key:string]:any}) {
    return new this(json)
  }

  constructor (options?:{[key:string]:any}) {
    if (options === undefined) {
      return
    }
    if (options['periodRange'] !== undefined) {
      this.periodRange = options['periodRange']
    }
    if (options['collectionId'] !== undefined) {
      this.collectionId = options['collectionId']
    }

    if (options['limit'] != undefined) {
      this.limit = options['limit']
    }
    if (options['page'] != undefined) {
      this.page = options['page']
    }
    if (options['assetIds'] != undefined) {
      this.assetIds = options['assetIds']
    }
    if (options['collectionIds'] != undefined) {
      this.collectionIds = options['collectionIds']
    }
  }

  public toJson (): {[key:string]:any} {
    return {
      periodRange: this.periodRange,
      collectionId:this.collectionId,
      limit: this.limit,
      page: this.page,
      assetIds: this.assetIds,
      collectionIds: this.collectionIds
    }
  }
}
