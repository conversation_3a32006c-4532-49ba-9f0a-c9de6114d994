import { CollectionCreateJson } from 'data/jsons/collection/collection_create_json'
import { CollectionUpdateJson } from 'data/jsons/collection/collection_update_json'
import { CollectionJson } from 'data/jsons/collection_json'
import { Query } from './collection_datasource/query'

export default interface CollectionDatasource {
    findById(id: string): Promise<CollectionJson>
    findBySlug(slug: string, isExact: boolean, chainId?: number): Promise<any>
    search(query: Query): Promise<CollectionJson[]>
    totalCount(query: Query): Promise<number>
    create(params:CollectionCreateJson): Promise<CollectionJson>
    update(params:CollectionUpdateJson): Promise<CollectionJson>
    import(params: CollectionJson & { txHash: string } & { contractAddress: string }): Promise<CollectionJson>
    importFromContractAddress(params: CollectionJson & { contractAddress: string }): Promise<CollectionJson>
}
