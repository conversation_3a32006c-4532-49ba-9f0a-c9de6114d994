
export class Query {
  public categoryIds: string[] | null = null
  public ownerId: string | null = null

  public sortBy: string | null = null
  public chains: string[] | null = null
  public limit: number = 60
  public page: number = 1

  public filterStatuses: string[] | null = null
  public priceTokenUnit: string | null = null // USD or ETH
  public minPrice: number | null = null
  public maxPrice: number | null = null
  public collectionIds: string[] | null = null
  public typeId: number | null = null
  public periodRange : string | null = null
  public chainId: number | null = null

  public static fromJson (json?: { [key: string]: any }) {
    return new this(json)
  }

  constructor (options?: { [key: string]: any }) {
    if (options === undefined) {
      return
    }
    if (options['categoryIds'] !== undefined) {
      this.categoryIds = options['categoryIds']
    }
    if (options['ownerId'] !== undefined) {
      this.ownerId = options['ownerId']
    }
    if (options['sortBy'] !== undefined) {
      this.sortBy = options['sortBy']
    }
    if (options['limit'] != undefined) {
      this.limit = options['limit']
    }
    if (options['page'] != undefined) {
      this.page = options['page']
    }
    if (options['chains'] != undefined) {
      this.chains = options['chains']
    }

    if (options['filterStatuses'] != undefined) {
      this.filterStatuses = options['filterStatuses']
    }
    if (options['priceTokenUnit'] != undefined) {
      this.priceTokenUnit = options['priceTokenUnit']
    }
    if (options['minPrice'] != undefined) {
      this.minPrice = options['minPrice']
    }
    if (options['maxPrice'] != undefined) {
      this.maxPrice = options['maxPrice']
    }
    if (options['collectionIds'] != undefined) {
      this.collectionIds = options['collectionIds']
    }
    if (options['typeId'] != undefined) {
      this.typeId = options['typeId']
    }
    if (options['periodRange']!= undefined) {
      this.periodRange = options['periodRange']
    }
    if (options['chainId']!= undefined) {
      this.chainId = options['chainId']
    }

  }

  public toJson (): { [key: string]: any } {
    return {
      categoryIds: this.categoryIds,
      chains: this.chains,
      sortBy: this.sortBy,
      limit: this.limit,
      page: this.page,
      ownerId: this.ownerId,

      filterStatuses: this.filterStatuses,
      priceTokenUnit: this.priceTokenUnit,
      minPrice: this.minPrice,
      maxPrice: this.maxPrice,
      collectionIds: this.collectionIds,
      typeId: this.typeId,
      periodRange: this.periodRange,
      chainId: this.chainId,

    }
  }
}
