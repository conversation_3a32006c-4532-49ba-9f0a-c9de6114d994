import { UserNotificationSettingUpdateJson } from 'data/jsons/user_notification_setting/user_notification_setting_update_json'
import { UserNotificationSettingJson } from 'data/jsons/user_notification_setting_json'

export default interface UserNotificationSettingDatasource {
    mine(): Promise<UserNotificationSettingJson>
    update(params:UserNotificationSettingUpdateJson): Promise<UserNotificationSettingJson>
}