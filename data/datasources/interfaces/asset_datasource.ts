import { <PERSON><PERSON><PERSON><PERSON> } from 'data/jsons/asset_json'
import { Query } from './asset_datasource/query'
import { AssetsCreateMetadata } from 'data/jsons/assets/assets_create_metadata_json'
import { AssetsCreate } from 'data/jsons/assets/assets_create_json'
import { AssetsRefreshMetadata } from 'data/jsons/assets/assets_refresh_metadata_json'

export default interface AssetDatasource {
    findById(id:string): Promise<AssetJson>
    findByChainAndAddressAndTokenId(chain:string, address:string, tokenId:string): Promise<AssetJson>
    search(query: Query): Promise<AssetJson[]>
    totalCount(query: Query): Promise<number>
    createMetadata(asset: AssetsCreateMetadata): Promise<any>
    create(asset: AssetsCreate): Promise<AssetJson>
    checkOwnership(id: string, userAddress: string): Promise<Boolean>
    refreshMetadata(params: AssetsRefreshMetadata): Promise<any>
}