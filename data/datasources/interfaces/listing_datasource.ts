import { Listing<PERSON><PERSON>J<PERSON> } from 'data/jsons/listing/listing_create_json'
import { ListingUpdateJson } from 'data/jsons/listing/listing_update_json'
import { ListingCreateOrderSlashJson } from 'data/jsons/listing/listing_create_order_slash_json'
import { ListingOrderJson } from 'data/jsons/listing/listing_order_json'
import { ListingJson } from 'data/jsons/listing_json'
import { ListingOrderUpdateJson } from 'data/jsons/listing/listing_update_order_json'
import { Query } from './listing_datasource/query'
import { QueryOrder } from './listing_datasource/query_order'

export default interface ListingDatasource {
    findById(id: string): Promise<ListingJson>
    search(query: Query): Promise<ListingJson[]>
    totalCount(query: Query): Promise<number>
    create(params:ListingCreateJson): Promise<ListingJson>
    update(params:ListingUpdateJson): Promise<ListingJson>
    createOrderSlash(params:ListingCreateOrderSlashJson): Promise<string>
    findAssetIdByPaymentToken(id: string): Promise<ListingOrderJson>
    searchOrder(query: QueryOrder): Promise<ListingOrderJson[]>
    totalCountOrder(query: QueryOrder): Promise<number>
    getSignatureClaimNft(id: string): Promise<any>
    getSignatureRefundToken(id: string): Promise<any>
    updateOrder(params: ListingOrderUpdateJson): Promise<ListingOrderJson>
}
