import { LoginStatusJson } from 'data/jsons/login_status_json'
import { LoginSuccessJson } from 'data/jsons/login_success_json'
import { UserUpdateJson } from 'data/jsons/user/user_update_json'
import { UserJson } from 'data/jsons/user_json'

export default interface UserDatasource {
    me(): Promise<UserJson>
    findByPublicAddress(publicAddress: string): Promise<UserJson>
    findById(id: string): Promise<UserJson>
    findByUsername(username: string, isExact : boolean): Promise<any>
    loginStatus(): Promise<LoginStatusJson>
    nonce(publicAddress: string): Promise<number>
    create(publicAddress: string): Promise<UserJson>
    login(publicAddress: string, signature:string): Promise<LoginSuccessJson>
    logout(): Promise<boolean>
    isUsernameAvailable(username: string): Promise<boolean>
    isEmailAvailable(email: string): Promise<boolean>
    update(params:UserUpdateJ<PERSON>): Promise<UserJson>
    updateEmail(email: string): Promise<UserJson>
    updateLanguage(language: string): Promise<UserJson>
    loginWithGoogle(): Promise<string>
    setTokenUser(token: string): void
    linkWallet(publicAddress: string, signature:string): Promise<any>
}