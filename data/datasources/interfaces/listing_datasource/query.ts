export class Query {

  public assetId: string|null = null
  public userId: string|null = null
  public paymentToken: string|null = null
  public minPrice: number|null = null
  public maxPrice: number|null = null

  public sortBy: string|null = null
  public limit:number = 60
  public page:number = 1
  public statusIds:number[]|null = null

  public static fromJson (json?:{[key:string]:any}) {
    return new this(json)
  }

  constructor (options?:{[key:string]:any}) {
    if (options === undefined) {
      return
    }
    if (options['assetId'] !== undefined) {
      this.assetId = options['assetId']
    }
    if (options['userId'] !== undefined) {
      this.userId = options['userId']
    }
    if (options['paymentToken'] !== undefined) {
      this.paymentToken = options['paymentToken']
    }
    if (options['minPrice'] !== undefined) {
      this.minPrice = options['minPrice']
    }
    if (options['maxPrice'] !== undefined) {
      this.maxPrice = options['maxPrice']
    }

    if (options['sortBy'] != undefined) {
      this.sortBy = options['sortBy']
    }
    if (options['limit'] != undefined) {
      this.limit = options['limit']
    }
    if (options['page'] != undefined) {
      this.page = options['page']
    }
    if (options['statusIds'] != undefined) {
      this.statusIds = options['statusIds']
    }
  }

  public toJson (): {[key:string]:any} {
    return {
      assetId: this.assetId,
      statusIds: this.statusIds,
      userId: this.userId,
      paymentToken: this.paymentToken,
      minPrice: this.minPrice,
      maxPrice: this.maxPrice,
      sortBy: this.sortBy,
      limit: this.limit,
      page: this.page,
    }
  }
}
