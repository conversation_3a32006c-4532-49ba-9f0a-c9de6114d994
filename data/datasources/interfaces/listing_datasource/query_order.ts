export class QueryOrder {
  public sortBy: string|null = null
  public limit:number = 20
  public page:number = 1


  public static from<PERSON><PERSON> (json?:{[key:string]:any}) {
    return new this(json)
  }

  constructor (options?:{[key:string]:any}) {
    if (options === undefined) {
      return
    }

    if (options['sortBy'] != undefined) {
      this.sortBy = options['sortBy']
    }
    if (options['limit'] != undefined) {
      this.limit = options['limit']
    }
    if (options['page'] != undefined) {
      this.page = options['page']
    }
    
  }

  public toJson (): {[key:string]:any} {
    return {
      sortBy: this.sortBy,
      limit: this.limit,
      page: this.page,
    }
  }
}
