import { DateTime } from 'luxon'

export class Query {

  public assetId: string|null = null
  public userId: string|null = null
  public minCreatedAt: DateTime|null = null
  public maxCreatedAt: DateTime|null = null

  public limit:number = 60
  public page:number = 1
  public sortBy: string | null = null
  public chainId: number|null = null
  public static fromJson (json?:{[key:string]:any}) {
    return new this(json)
  }

  constructor (options?:{[key:string]:any}) {
    if (options === undefined) {
      return
    }
    if (options['assetId'] !== undefined) {
      this.assetId = options['assetId']
    }
    if (options['userId'] !== undefined) {
      this.userId = options['userId']
    }
    if (options['minCreatedAt'] !== undefined) {
      this.minCreatedAt = options['minCreatedAt']
    }
    if (options['maxCreatedAt'] !== undefined) {
      this.maxCreatedAt = options['maxCreatedAt']
    }

    if (options['limit'] != undefined) {
      this.limit = options['limit']
    }
    if (options['page'] != undefined) {
      this.page = options['page']
    }
    if (options['sortBy'] != undefined) {
      this.sortBy = options['sortBy']
    }
    if (options['chainId']!= undefined) {
      this.chainId = options['chainId']
    }
  }

  public toJson (): {[key:string]:any} {
    return {
      assetId: this.assetId,
      userId: this.userId,
      minCreatedAt: this.minCreatedAt,
      maxCreatedAt: this.maxCreatedAt,
      limit: this.limit,
      page: this.page,
      sortBy: this.sortBy,
      chainId: this.chainId
    }
  }
}
