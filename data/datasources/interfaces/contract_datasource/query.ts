export class Query {

  public chain: string|null = null
  public schema: string|null = null
  public address: string|null = null
  public collectionId: string|null = null

  public limit:number = 60
  public page:number = 1

  public static fromJson (json?:{[key:string]:any}) {
    return new this(json)
  }

  constructor (options?:{[key:string]:any}) {
    if (options === undefined) {
      return
    }
    if (options['chain'] !== undefined) {
      this.chain = options['chain']
    }
    if (options['schema'] !== undefined) {
      this.schema = options['schema']
    }
    if (options['address'] !== undefined) {
      this.address = options['address']
    }
    if (options['collectionId'] !== undefined) {
      this.collectionId = options['collectionId']
    }

    if (options['limit'] != undefined) {
      this.limit = options['limit']
    }
    if (options['page'] != undefined) {
      this.page = options['page']
    }
  }

  public toJson (): {[key:string]:any} {
    return {
      chain: this.chain,
      schema: this.schema,
      address: this.address,
      collectionId: this.collectionId,
      limit: this.limit,
      page: this.page,
    }
  }
}
