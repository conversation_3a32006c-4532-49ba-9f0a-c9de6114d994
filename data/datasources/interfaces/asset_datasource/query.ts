export class Query {
  public keywords: string[]|null = null
  public filterStatuses: string[]|null = null
  public paymentToken: string|null = null
  public minPrice: number|null = null
  public maxPrice: number|null = null
  public collectionIds: string[] |null = null
  public chainIds: number[]|null = null
  public categoryIds: string[]|null = null
  public paymentTokens: string[]|null = null
  public marketplaces: string[]|null = null
  public filterListedAt: string|null = null
  public ownerId: string|null = null
  public creatorId: string|null = null

  public sortBy: string|null = null
  public limit:number = 60
  public page: number = 1
  public priceTokenUnit: string|null = null
  public type: string | null = null
  public periodRange: string | null = null
  public chainId: number | null = null
  public traits: Array<{name: string, values: string[]}> | null = null
  public static fromJson (json?:{[key:string]:any}) {
    return new this(json)
  }

  constructor (options?:{[key:string]:any}) {
    if (options === undefined) {
      return
    }
    if (options['keywords'] !== undefined) {
      this.keywords = options['keywords']
    }
    if (options['filterStatuses'] !== undefined) {
      this.filterStatuses = options['filterStatuses']
    }
    if (options['paymentToken'] !== undefined) {
      this.paymentToken = options['paymentToken']
    }
    if (options['minPrice'] !== undefined) {
      this.minPrice = options['minPrice']
    }
    if (options['maxPrice'] !== undefined) {
      this.maxPrice = options['maxPrice']
    }
    if (options['collectionIds'] !== undefined) {
      this.collectionIds = options['collectionIds']
    }
    // if (options['chains'] !== undefined) {
    //   this.chains = options['chains']
    // }
    if (options['chainIds'] !== undefined) {
      this.chainIds = options['chainIds']
    }
    if (options['categoryIds'] !== undefined) {
      this.categoryIds = options['categoryIds']
    }
    if (options['paymentTokens'] !== undefined) {
      this.paymentTokens = options['paymentTokens']
    }
    if (options['marketplaces'] !== undefined) {
      this.marketplaces = options['marketplaces']
    }
    if (options['filterListedAt'] !== undefined) {
      this.filterListedAt = options['filterListedAt']
    }
    if (options['ownerId'] !== undefined) {
      this.ownerId = options['ownerId']
    }
    if (options['creatorId'] !== undefined) {
      this.creatorId = options['creatorId']
    }

    if (options['sortBy'] !== undefined) {
      this.sortBy = options['sortBy']
    }
    if (options['limit'] != undefined) {
      this.limit = options['limit']
    }
    if (options['page'] != undefined) {
      this.page = options['page']
    }

    if (options['priceTokenUnit'] != undefined) {
      this.priceTokenUnit = options['priceTokenUnit']
    }
    if(options['type'] != undefined){
      this.type = options['type']
    }
    if(options['periodRange']!= undefined){
      this.periodRange = options['periodRange']
    }
    if(options['chainId']!= undefined){
      this.chainId = options['chainId']
    }
    if(options['traits'] != undefined){
      this.traits = options['traits']
    }
  }

  public toJson (): {[key:string]:any} {
    return {
      keywords: this.keywords,
      filterStatuses: this.filterStatuses,
      paymentToken: this.paymentToken,
      minPrice: this.minPrice,
      maxPrice: this.maxPrice,
      collectionIds: this.collectionIds,
      chainIds: this.chainIds,
      categoryIds: this.categoryIds,
      paymentTokens: this.paymentTokens,
      marketplaces: this.marketplaces,
      filterListedAt: this.filterListedAt,
      ownerId: this.ownerId,
      creatorId: this.creatorId,
      sortBy: this.sortBy,
      limit: this.limit,
      page: this.page,
      priceTokenUnit: this.priceTokenUnit,
      type: this.type,
      periodRange: this.periodRange,
      chainId: this.chainId,
      traits: this.traits,
    }
  }
}
