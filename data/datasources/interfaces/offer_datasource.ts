import { Offer<PERSON><PERSON><PERSON><PERSON> } from 'data/jsons/offer/offer_create_json'
import { OfferUpdateJson } from 'data/jsons/offer/offer_update_json'
import { OfferCancelJson } from 'data/jsons/offer/offer_cancel_json'
import { OfferAcceptJson } from 'data/jsons/offer/offer_accept_json'
import { OfferJson } from 'data/jsons/offer_json'
import { Query } from './offer_datasource/query'

export default interface OfferDatasource {
    findById(id: string): Promise<OfferJson>
    search(query: Query): Promise<OfferJson[]>
    totalCount(query: Query): Promise<number>
    create(params:OfferCreateJson): Promise<OfferJson>
    cancel(params:OfferCancelJson): Promise<OfferJson>
    accept(params:OfferAcceptJson): Promise<OfferJson>
    update(params:OfferUpdateJson): Promise<OfferJson>
    
}
