import { VendingMachine<PERSON><PERSON><PERSON><PERSON>, VendingMachine<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'data/jsons/vending-machine/vending_machine_pack_json'
import { Query } from 'domain/repositories/vending_machine_pack_repository/query'

export default interface VendingMachinePackDatasource {
    list(query: Query): Promise<VendingMachinePackListJson>
    getById(id: string): Promise<VendingMachinePackJson>
    create(params: CreatePackParams): Promise<VendingMachinePackJson>
    update(id: string, params: UpdatePackParams): Promise<VendingMachinePackJson>
    delete(id: string): Promise<void>
}

export interface CreatePackParams {
    name: string;
    description: string;
    price: number | string;
    categoryId: string;
    imageUrl: string;
    setting?: any;
    isEnabled?: boolean;
}

export interface UpdatePackParams {
    name?: string;
    description?: string;
    price?: number | string;
    categoryId?: string;
    imageUrl?: string;
    setting?: any;
    isEnabled?: boolean;
}
