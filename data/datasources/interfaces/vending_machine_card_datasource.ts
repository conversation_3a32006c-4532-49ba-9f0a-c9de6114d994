import {
  VendingMachineImportCard<PERSON>son,
  VendingMachineCardJson,
  PayloadUpdateVendingMachineCardJson,
  VendingMachineImportCardList<PERSON>son,
  VendingMachineCardList<PERSON>son,
  ListCheckoutJson,
} from 'data/jsons/vending-machine/vending_machine_card_json'
import { QueryCheckout } from 'domain/repositories/vending_machine_card_repository/query_checkout'
import { Query } from 'domain/repositories/vending_machine_card_repository/query'

export default interface VendingMachineCardDatasource {
  listImportCard(query: Query): Promise<VendingMachineImportCardListJson>
  listCard(query: Query): Promise<VendingMachineCardListJson>
  listCardIds(query: Query): Promise<VendingMachineCardListJson>
  importCard(params: any): Promise<VendingMachineImportCardJson>
  batchImportCard(file: File): Promise<any>
  importCardTemplate(): Promise<any>
  updateCard(
    id: string,
    payload: PayloadUpdateVendingMachineCardJson
  ): Promise<VendingMachineCardJson>
  mapCategory(cardId: string, categoryId: string): Promise<any>
  currencyRate(baseCurrency: string, currency: string): Promise<number>
  listCheckout(query: QueryCheckout): Promise<ListCheckoutJson>
  exportCheckout(query: QueryCheckout): Promise<any>
}
