export class Query {

  public assetIds: string[]|null = null
  public fromUserId: string|null = null
  public toUserId: string|null = null
  //public collectionId: string|null = null
  public chains: string[]|null = null
  public filterEventTypes: string[]|null = null

  public sortBy: string|null = null
  public limit:number = 60
  public page: number = 1
  
  public collectionIds: string | null = null

  public userId : string | null = null
  public chainId: number | null = null

  public unitPriceFrom: number | '' = ''
  public unitPriceTo: number | '' = ''
  public currencyId: string | '' = ''

  public traits: Array<{name: string, values: string[]}> | null = null
  public static fromJson (json?:{[key:string]:any}) {
    return new this(json)
  }

  constructor (options?:{[key:string]:any}) {
    if (options === undefined) {
      return
    }
    if (options['assetIds'] !== undefined) {
      this.assetIds = options['assetIds']
    }
    if (options['fromUserId'] !== undefined) {
      this.fromUserId = options['fromUserId']
    }
    if (options['toUserId'] !== undefined) {
      this.toUserId = options['toUserId']
    }
    // if (options['collectionId'] !== undefined) {
    //   this.collectionId = options['collectionId']
    // }
    if (options['chains'] !== undefined) {
      this.chains = options['chains']
    }
    if (options['filterEventTypes'] !== undefined) {
      this.filterEventTypes = options['filterEventTypes']
    }

    if (options['sortBy'] !== undefined) {
      this.sortBy = options['sortBy']
    }
    if (options['limit'] != undefined) {
      this.limit = options['limit']
    }
    if (options['page'] != undefined) {
      this.page = options['page']
    }
    if (options['collectionIds'] != undefined) {
      this.collectionIds = options['collectionIds']
    }
    if (options['userId'] != undefined) {
      this.userId = options['userId']
    }
    if (options['chainId']!= undefined) {
      this.chainId = options['chainId']
    }
    if (options['unitPriceFrom'] != undefined) {
      this.unitPriceFrom = options['unitPriceFrom']
    }
    if (options['unitPriceTo'] != undefined) {
      this.unitPriceTo = options['unitPriceTo']
    }
    if (options['currencyId'] != undefined) {
      this.currencyId = options['currencyId']
    }
    if (options['traits'] != undefined) {
      this.traits = options['traits']
    }
  }

  public toJson (): {[key:string]:any} {
    return {
      assetIds: this.assetIds,
      fromUserId: this.fromUserId,
      toUserId: this.toUserId,
      //collectionId: this.collectionId,
      chains: this.chains,
      filterEventTypes: this.filterEventTypes,
      sortBy: this.sortBy,
      limit: this.limit,
      page: this.page,
      collectionIds: this.collectionIds,
      userId: this.userId,
      chainId: this.chainId,
      unitPriceFrom: this.unitPriceFrom,
      unitPriceTo: this.unitPriceTo,
      currencyId: this.currencyId,
      traits: this.traits,
    }
  }
}
