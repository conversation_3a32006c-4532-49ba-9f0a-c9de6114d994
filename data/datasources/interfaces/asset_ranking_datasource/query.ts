import PeriodRange from 'domain/repositories/asset_daily_stat_repository/period_range'

export class Query {
  public periodRange: PeriodRange|null = null
  public categoryId: string | null = null
  public chain: string | null = null
  public limit:number = 60
  public page:number = 1
  public chainId: number|null = null

  public static fromJson (json?:{[key:string]:any}) {
    return new this(json)
  }

  constructor (options?:{[key:string]:any}) {
    if (options === undefined) {
      return
    }
    if (options['periodRange'] !== undefined) {
      this.periodRange = options['periodRange'] as PeriodRange
    }
    if (options['categoryId'] !== undefined) {
      this.categoryId = options['categoryId'] as string
    }
    if (options['chain'] !== undefined) {
      this.chain = options['chain'] as string
    }

    if (options['limit'] != undefined) {
      this.limit = options['limit']
    }
    if (options['page'] != undefined) {
      this.page = options['page']
    }
    if (options['chainId']!= undefined) {
      this.chainId = options['chainId']
    }
  }

  public toJson ():{[key:string]:any} {
    return {
      periodRange: this.periodRange || '',
      categoryId: this.categoryId,
      chain: this.chain,
      limit: this.limit,
      page: this.page,
      chainId: this.chainId,
    }
  }
} 