import container from 'inversify.config'
import TYPES from 'types'
import 'reflect-metadata'

import { Query } from '../interfaces/collection_like_datasource/query'
import type ApiClient from './api_client'
import CollectionWatchDatasource from '../interfaces/collection_watch_datasource'
import { CollectionWatchJson } from 'data/jsons/collection_watch_json'

export default class CollectionWatchRemoteDatasource implements CollectionWatchDatasource {

  private apiClient:ApiClient

  constructor (){
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  search (query: Query): Promise<CollectionWatchJson[]> {
    return new Promise((resolve, reject) => {
      this.apiClient.get('/collectionWatches', query.toJson())
        .then((data) => {
          resolve(data.collectionWatches as CollectionWatchJson[])
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    const params = query.toJson()
    delete params.limit
    delete params.page

    return new Promise((resolve, reject) => {
      this.apiClient.get('/collectionWatches/totalCount', params)
        .then((data) => {
          resolve(data.totalCount as number)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  collectionIds (query: Query): Promise<string[]> {
    return new Promise((resolve, reject) => {
      this.apiClient.get('/collectionWatches/collectionIds', query.toJson())
        .then((data) => {
          resolve(data.collectionIds as string[])
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
