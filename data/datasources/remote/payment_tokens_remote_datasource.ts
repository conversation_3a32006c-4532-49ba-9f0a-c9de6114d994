import container from 'inversify.config'
import 'reflect-metadata'
import TYPES from 'types'

import { PaymentTokensJson } from 'data/jsons/payment_tokens_json'
import PaymentTokensDatasource from '../interfaces/payment_tokens_datasource'

import type ApiClient from './api_client'

export default class PaymentTokensRemoteDatasource implements PaymentTokensDatasource {

  private apiClient: ApiClient

  public constructor (){
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  getPaymentTokens (chainId?: number): Promise<PaymentTokensJson[]> {
    return new Promise((resolve, reject) => {
      console.log('getPaymentTokens', chainId)
      const path = chainId ? `/currencies/findByChainId/${chainId}` : '/currencies'
      this.apiClient.get(path)
        .then((data) => {
          resolve(data?.currencies as PaymentTokensJson[])
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
}
