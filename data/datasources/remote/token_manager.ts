import { injectable } from 'inversify'
import 'reflect-metadata'

import type StorageDatasource from '../interfaces/storage_datasource'
import LocalStorageDatasource from '../local/local_storage_datasource'

@injectable()
export default class TokenManager {
  private storageDatasource: StorageDatasource = new LocalStorageDatasource()

  constructor (private keyPrefix: string = 'nft_') {}

  setStorageDatasource (storageDatasource: StorageDatasource) {
    this.storageDatasource = storageDatasource
  }

  setKeyPrefix (keyPrefix: string) {
    this.keyPrefix = keyPrefix
  }

  getToken (): string | null {
    return this.storageDatasource.read(`${this.keyPrefix}token`)
  }

  setToken (token: string): void {
    this.storageDatasource.write(`${this.keyPrefix}token`, token)
  }

  clearToken (): void {
    this.storageDatasource.delete(`${this.keyPrefix}token`)
  }
}
