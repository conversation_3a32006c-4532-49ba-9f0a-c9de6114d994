import container from 'inversify.config'
import TYPES from 'types'
import 'reflect-metadata'

import { Query } from '../interfaces/collection_impression_datasource/query'
import type ApiClient from './api_client'
import CollectionImpressionDatasource from '../interfaces/collection_impression_datasource'
import { CollectionImpressionJson } from 'data/jsons/collection_impression_json'

export default class CollectionImpressionRemoteDatasource implements CollectionImpressionDatasource {

  private apiClient:ApiClient

  constructor (){
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  search (query: Query): Promise<CollectionImpressionJson[]> {
    return new Promise((resolve, reject) => {
      this.apiClient.get('/collectionImpressions', query.toJson())
        .then((data) => {
          resolve(data.collectionImpressions as CollectionImpressionJson[])
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    const params = query.toJson()
    delete params.limit
    delete params.page

    return new Promise((resolve, reject) => {
      this.apiClient.get('/collectionImpressions/totalCount', params)
        .then((data) => {
          resolve(data.totalCount as number)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  collectionIds (query: Query): Promise<string[]> {
    return new Promise((resolve, reject) => {
      this.apiClient.get('/collectionImpressions/collectionIds', query.toJson())
        .then((data) => {
          resolve(data.collectionIds as string[])
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
