import { injectable } from 'inversify'
import container from 'inversify.config'
import TYPES from 'types'

import type ApiClient from './api_client'

export interface CheckoutCreateParams {
  vmGachaBoxId: string
  paymentToken: string
  authorizedPayer: string
  paymentValue: number|string

}

export interface CheckoutCreateResponse {
  checkoutId: string
  requestData: any
  signature: string
}

export interface MintedCard {
  id: string
  image: string
  name: string
  tokenId: string
  attributes: {
    cardId: string
    cardNumber: string
    series: string
    shop: string
    status: string
    scrapedAt: string
  }
  mintTxHash: string
  mintBlockchainTimestamp: string
  buyBackPrice: number
  buybackExpirationTimestamp: string
  packPrice: number
  chainId: number
}

export interface MintedCardsResponse {
  success: boolean
  data: {
    mintedCards: MintedCard[]
    pagination: {
      page: number
      limit: number
      total: number
      hasMore: boolean
    }
  }
}

@injectable()
export default class CheckoutRemoteDatasource {
  private apiClient: ApiClient

  constructor () {
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  create (params: CheckoutCreateParams): Promise<CheckoutCreateResponse> {
    return new Promise((resolve, reject) => {
      this.apiClient.post('/checkouts/create', params)
        .then((rs) => {
          const data = rs.data as CheckoutCreateResponse
          resolve(data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }
  checkoutTxStatus (checkoutId: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(`/checkouts/${checkoutId}`)
        .then((rs) => {
          const data = rs as any
          resolve(data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  getMintedCards (userId: string, page: number = 1, limit: number = 20): Promise<MintedCardsResponse> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(`/checkouts/minted-cards?userId=${userId}&page=${page}&limit=${limit}`)
        .then((rs) => {
          const data = rs.data as MintedCardsResponse
          resolve(data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }
}