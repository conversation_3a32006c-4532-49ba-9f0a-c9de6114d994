import container from 'inversify.config'
import TYPES from 'types'
import 'reflect-metadata'

import type ApiClient from './api_client'
import UserPublicAddressDatasource from '../interfaces/user_public_address_datasource'

export default class UserPublicAddressRemoteDatasource implements UserPublicAddressDatasource {

  private apiClient:ApiClient

  constructor (){
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  search (): Promise<string[]> {
    return new Promise((resolve, reject) => {
      this.apiClient.get('/userPublicAddresses')
        .then((data) => {
          resolve(data.publicAddresses as string[])
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  create (publicAddress: string, signature: string): Promise<string> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/userPublicAddresses/create',
        {
          publicAddress: publicAddress,
          signature: signature,
        }
      )
        .then((_) => {
          resolve(publicAddress)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  delete (publicAddress: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/userPublicAddresses/delete',
        { publicAddress: publicAddress }
      )
        .then((data) => {
          resolve(data.result as boolean)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
