import container from 'inversify.config'
import TYPES from 'types'
import 'reflect-metadata'

import { Query } from '../interfaces/asset_impression_datasource/query'
import type ApiClient from './api_client'
import AssetImpressionDatasource from '../interfaces/asset_impression_datasource'
import { AssetImpressionJson } from 'data/jsons/asset_impression_json'

export default class AssetImpressionRemoteDatasource implements AssetImpressionDatasource {

  private apiClient:ApiClient

  constructor (){
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  search (query: Query): Promise<AssetImpressionJson[]> {
    return new Promise((resolve, reject) => {
      this.apiClient.get('/assetImpressions', query.toJson())
        .then((data) => {
          resolve(data.assetImpressions as AssetImpressionJson[])
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    const params = query.toJson()
    delete params.limit
    delete params.page

    return new Promise((resolve, reject) => {
      this.apiClient.get('/assetImpressions/totalCount', params)
        .then((data) => {
          resolve(data.totalCount as number)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  assetIds (query: Query): Promise<string[]> {
    return new Promise((resolve, reject) => {
      this.apiClient.get('/assetImpressions/assetIds', query.toJson())
        .then((data) => {
          resolve(data.assetIds as string[])
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
