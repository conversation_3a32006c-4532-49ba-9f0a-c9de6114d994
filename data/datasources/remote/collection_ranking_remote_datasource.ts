import container from 'inversify.config'
import 'reflect-metadata'
import TYPES from 'types'

import { CollectionJson } from 'data/jsons/collection_json'
import CollectionRankingDatasource from '../interfaces/collection_ranking_datasource'
import { Query } from '../interfaces/collection_ranking_datasource/query'
import type ApiClient from './api_client'


export default class CollectionRankingRemoteDatasource implements CollectionRankingDatasource {

  private apiClient:ApiClient

  constructor (){
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  findById (id: string): Promise<CollectionJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(`/collections/${id}`)
        .then((data) => {
          resolve(data.collection as CollectionJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  findBySlug (slug: string): Promise<CollectionJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(
        `/collections/findBySlug/${slug}`
      )
        .then((data) => {
          resolve(data.collection as CollectionJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  search (query: Query): Promise<CollectionJson[]> {
    return new Promise((resolve, reject) => {
      this.apiClient.get('/collections/ranking', query.toJson())
        .then((data) => {
          if (data) {
            const collections = data.rankings?.map((ranking: any) => { return { ...ranking.collection, rankingVolume: ranking.total } })
            resolve(collections as CollectionJson[])
          }
          else { 
            resolve([])
          }
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    const params = query.toJson()
    delete params.sortBy
    delete params.limit
    delete params.page

    return new Promise((resolve, reject) => {
      this.apiClient.get('/collections/ranking/totalCount', params)
        .then((data) => {
          resolve(data.totalCount as number)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
