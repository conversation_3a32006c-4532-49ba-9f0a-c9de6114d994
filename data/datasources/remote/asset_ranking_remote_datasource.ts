import container from 'inversify.config'
import 'reflect-metadata'
import TYPES from 'types'

import { AssetJson } from 'data/jsons/asset_json'
import AssetRankingDatasource from '../interfaces/asset_ranking_datasource'
import { Query } from '../interfaces/asset_ranking_datasource/query'
import type ApiClient from './api_client'

export default class AssetRankingRemoteDatasource implements AssetRankingDatasource {
  private apiClient:ApiClient

  constructor (){
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  search (query: Query): Promise<AssetJson[]> {
    return new Promise((resolve, reject) => {
      this.apiClient.get('/assets/ranking', query.toJson())
        .then((data) => {
          if (data) {
            const assets = data.rankings?.map((ranking: any) => { 
              return { ...ranking.asset, rankingVolume: ranking.total } 
            })
            resolve(assets as AssetJson[])
          }
          else { 
            resolve([])
          }
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    const params = query.toJson()
    delete params.sortBy
    delete params.limit
    delete params.page

    return new Promise((resolve, reject) => {
      this.apiClient.get('/assets/ranking/totalCount', params)
        .then((data) => {
          resolve(data.totalCount as number)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
} 