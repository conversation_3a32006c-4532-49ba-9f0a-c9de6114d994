import container from 'inversify.config'
import TYPES from 'types'
import 'reflect-metadata'

import { Query } from '../interfaces/category_datasource/query'
import type ApiClient from './api_client'
import CategoryDatasource from '../interfaces/category_datasource'
import { CategoryJson } from 'data/jsons/category_json'

export default class CategoryRemoteDatasource implements CategoryDatasource {

  private apiClient:ApiClient

  constructor (){
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  findById (id: string): Promise<CategoryJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(`/categories/${id}`)
        .then((data) => {
          resolve(data.category as CategoryJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  findBySlug (slug: string): Promise<CategoryJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(
        `/categories/findBySlug/${slug}`
      )
        .then((data) => {
          resolve(data.category as Category<PERSON><PERSON>)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  search (query: Query): Promise<CategoryJson[]> {
    return new Promise((resolve, reject) => {
      this.apiClient.get('/categories', query.toJson())
        .then((data) => {
          resolve(data.categories as CategoryJson[])
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    const params = query.toJson()
    delete params.limit
    delete params.page

    return new Promise((resolve, reject) => {
      this.apiClient.get('/categories/totalCount', params)
        .then((data) => {
          resolve(data.totalCount as number)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
