import ApiClient from 'data/datasources/remote/api_client'

export interface DashboardMetrics {
  salesVolume: {
    value: number
    currency: string
    previousValue: number
    changePercent: number
    trend: 'up' | 'down' | 'stable'
  }
  averagePurchases: {
    value: number
    unit: string
    previousValue: number
    changePercent: number
    trend: 'up' | 'down' | 'stable'
  }
  numberOfBuybacks: {
    value: number
    unit: string
    previousValue: number
    changePercent: number
    trend: 'up' | 'down' | 'stable'
  }
  buybackRate: {
    value: number
    unit: string
    previousValue: number
    changePercent: number
    trend: 'up' | 'down' | 'stable'
  }
  offerAcceptanceAmount: {
    value: number
    currency: string
    previousValue: number
    changePercent: number
    trend: 'up' | 'down' | 'stable'
  }
  buybackCirculationRate: {
    value: number
    unit: string
    previousValue: number
    changePercent: number
    trend: 'up' | 'down' | 'stable'
  }
  chartData: {
    salesVolumeChart: Array<{
      date: string
      value: number
    }>
    averagePurchasesChart: Array<{
      date: string
      value: number
    }>
    buybackRateChart: Array<{
      date: string
      value: number
    }>
    offerAcceptanceChart: Array<{
      date: string
      value: number
    }>
    circulationRateChart: Array<{
      date: string
      value: number
    }>
  }
}

export interface DashboardResponse {
  success: boolean
  data: DashboardMetrics
  message?: string
}

export default class VendingMachineDashboardRemoteDatasource {
  private apiClient: ApiClient

  constructor () {
    this.apiClient = new ApiClient()
  }

  getDashboardMetrics (period: string = '7d'): Promise<DashboardResponse> {
    return new Promise((resolve, reject) => {
      // Mock data - replace with real API call later
      setTimeout(() => {
        const mockData: DashboardResponse = {
          success: true,
          data: {
            salesVolume: {
              value: 125430.50,
              currency: 'USDC',
              previousValue: 98230.20,
              changePercent: 27.7,
              trend: 'up',
            },
            averagePurchases: {
              value: 3.2,
              unit: 'packs/wallet',
              previousValue: 2.8,
              changePercent: 14.3,
              trend: 'up',
            },
            numberOfBuybacks: {
              value: 1247,
              unit: 'buybacks',
              previousValue: 1098,
              changePercent: 13.6,
              trend: 'up',
            },
            buybackRate: {
              value: 23.5,
              unit: '%',
              previousValue: 19.8,
              changePercent: 18.7,
              trend: 'up',
            },
            offerAcceptanceAmount: {
              value: 89430.75,
              currency: 'USDC',
              previousValue: 72180.60,
              changePercent: 23.9,
              trend: 'up',
            },
            buybackCirculationRate: {
              value: 71.3,
              unit: '%',
              previousValue: 73.5,
              changePercent: -3.0,
              trend: 'down',
            },
            chartData: {
              salesVolumeChart: [
                { date: '2024-01-01', value: 15230 },
                { date: '2024-01-02', value: 18450 },
                { date: '2024-01-03', value: 16890 },
                { date: '2024-01-04', value: 22340 },
                { date: '2024-01-05', value: 19560 },
                { date: '2024-01-06', value: 24780 },
                { date: '2024-01-07', value: 28180 },
              ],
              averagePurchasesChart: [
                { date: '2024-01-01', value: 2.8 },
                { date: '2024-01-02', value: 2.9 },
                { date: '2024-01-03', value: 3.0 },
                { date: '2024-01-04', value: 3.1 },
                { date: '2024-01-05', value: 3.1 },
                { date: '2024-01-06', value: 3.2 },
                { date: '2024-01-07', value: 3.2 },
              ],
              buybackRateChart: [
                { date: '2024-01-01', value: 18.2 },
                { date: '2024-01-02', value: 19.5 },
                { date: '2024-01-03', value: 20.8 },
                { date: '2024-01-04', value: 21.2 },
                { date: '2024-01-05', value: 22.9 },
                { date: '2024-01-06', value: 23.1 },
                { date: '2024-01-07', value: 23.5 },
              ],
              offerAcceptanceChart: [
                { date: '2024-01-01', value: 10230 },
                { date: '2024-01-02', value: 12450 },
                { date: '2024-01-03', value: 11890 },
                { date: '2024-01-04', value: 15340 },
                { date: '2024-01-05', value: 13560 },
                { date: '2024-01-06', value: 16780 },
                { date: '2024-01-07', value: 19180 },
              ],
              circulationRateChart: [
                { date: '2024-01-01', value: 75.8 },
                { date: '2024-01-02', value: 74.5 },
                { date: '2024-01-03', value: 73.2 },
                { date: '2024-01-04', value: 72.9 },
                { date: '2024-01-05', value: 72.6 },
                { date: '2024-01-06', value: 71.8 },
                { date: '2024-01-07', value: 71.3 },
              ],
            },
          },
        }
        resolve(mockData)
      }, 800) // Simulate API delay
    })
  }

  // Real API call method (commented out for now)
  /*
  getDashboardMetrics(period: string = '7d'): Promise<DashboardResponse> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(`/admin/vending-machine/dashboard?period=${period}`)
        .then((rs) => {
          const data = rs.data as DashboardResponse
          resolve(data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }
  */
}
