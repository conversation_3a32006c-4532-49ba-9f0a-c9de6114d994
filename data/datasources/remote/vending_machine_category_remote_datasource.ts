import container from 'inversify.config'
import TYPES from 'types'
import 'reflect-metadata'

import { Query } from 'domain/repositories/vending_machine_category_repository/query'
import type ApiClient from './api_client'
import VendingMachineCategoryDatasource, { CreateCategoryParams, UpdateCategoryParams } from '../interfaces/vending_machine_category_datasource'
import { VendingMachineCategoryJson, VendingMachineCategoryListJson } from 'data/jsons/vending-machine/vending_machine_category_json'

export default class VendingMachineCategoryRemoteDatasource implements VendingMachineCategoryDatasource {

  private apiClient: ApiClient

  constructor () {
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  list (query: Query): Promise<VendingMachineCategoryListJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.get('/vm/categories', query.toJson())
        .then((data) => {
          resolve(data as VendingMachineCategoryListJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  getById (id: string): Promise<VendingMachineCategoryJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(`/vm/categories/${id}`)
        .then((data) => {
          resolve(data as VendingMachineCategoryJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  create (params: CreateCategoryParams): Promise<VendingMachineCategoryJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.post('/vm/categories', params)
        .then((data) => {
          resolve(data as VendingMachineCategoryJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  update (id: number, params: UpdateCategoryParams): Promise<VendingMachineCategoryJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.put(`/vm/categories/${id}`, params)
        .then((data) => {
          resolve(data as VendingMachineCategoryJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  delete (id: number): Promise<void> {
    return new Promise((resolve, reject) => {
      this.apiClient.delete(`/vm/categories/${id}`)
        .then(() => {
          resolve()
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
}
