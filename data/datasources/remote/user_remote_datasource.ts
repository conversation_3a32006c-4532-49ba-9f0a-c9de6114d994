import container from 'inversify.config'
import TYPES from 'types'
import 'reflect-metadata'

import type ApiClient from './api_client'
import UserDatasource from '../interfaces/user_datasource'
import { LoginSuccessJson } from 'data/jsons/login_success_json'
import { UserUpdateJson } from 'data/jsons/user/user_update_json'
import { UserJson } from 'data/jsons/user_json'
import { LoginStatusJson } from 'data/jsons/login_status_json'
import TokenManager from './token_manager'

export default class UserRemoteDatasource implements UserDatasource {

  private apiClient:ApiClient
  private tokenManager: TokenManager = new TokenManager()

  constructor (){
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  me (): Promise<UserJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.get('/users/me')
        .then((data) => {
          resolve(data.user as UserJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  findByPublicAddress (publicAddress: string): Promise<UserJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(
        `/users/findByPublicAddress/${publicAddress}`
      )
        .then((data) => {
          resolve(data.user as UserJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  findByUsername (username: string, isExact : boolean): Promise<any> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(
        `/users/findByUsername/${username}?isExact=${isExact}`
      )
        .then((data) => {
          if (isExact) {
            resolve(data.user as UserJson)
          } else {
            console.log(data, ' resolve(User.fromJson(json))')
            resolve(data.users)
          }
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  findById (id: string): Promise<UserJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(`/users/${id}`)
        .then((data) => {
          resolve(data.user as UserJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  loginStatus (): Promise<LoginStatusJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.get('/users/loginStatus')
        .then((data) => {
          resolve(data as LoginStatusJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  nonce (publicAddress: string): Promise<number> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(
        '/users/nonce',
        { publicAddress: publicAddress }
      )
        .then((data) => {
          resolve(data.nonce as number)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  create (publicAddress: string): Promise<UserJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/users/create',
        { publicAddress: publicAddress }
      )
        .then((data) => {
          resolve(data.user as UserJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  login (publicAddress: string, signature: string): Promise<LoginSuccessJson> {
    return new Promise((resolve, reject) => {
      this.tokenManager.clearToken()
      this.apiClient.post(
        '/users/login',
        {
          publicAddress: publicAddress,
          signature: signature,
        }
      )
        .then((data) => {
          this.tokenManager.setToken(data.token)
          resolve(data as LoginSuccessJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  logout (): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/users/logout'
      )
        .then((data) => {
          this.tokenManager.clearToken()
          resolve(data.result as boolean)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  isUsernameAvailable (username: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(
        `/users/isUsernameAvailable/${username}`
      )
        .then((data) => {
          resolve(data.result as boolean)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  isEmailAvailable (email: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(
        `/users/isEmailAvailable/${email}`
      )
        .then((data) => {
          resolve(data.result as boolean)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  update (params: UserUpdateJson): Promise<UserJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/users/update',
        params
      )
        .then((data) => {
          resolve(data.user as UserJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  updateEmail (email: string): Promise<UserJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/users/updateEmail',
        { email: email }
      )
        .then((data) => {
          resolve(data.user as UserJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  updateLanguage (language: string): Promise<UserJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/users/updateLanguage',
        { language: language }
      )
        .then((data) => {
          resolve(data.user as UserJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
  
  loginWithGoogle (): Promise<string> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(
        '/auth/google/connect',
      )
        .then((data) => {
          resolve(data?.url || '')
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  setTokenUser (token: string): void {
    this.tokenManager.clearToken()
    this.tokenManager.setToken(token)
  }

  linkWallet (publicAddress: string, signature: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/users/linkWallet',
        {
          publicAddress: publicAddress,
          signature: signature,
        }
      )
        .then((data) => {
          console.log('linkWallet-------data', data)
          resolve(data)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
