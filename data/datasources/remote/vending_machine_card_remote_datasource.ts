import container from 'inversify.config'
import TYPES from 'types'
import 'reflect-metadata'

import { Query } from 'domain/repositories/vending_machine_card_repository/query'
import { QueryCheckout } from 'domain/repositories/vending_machine_card_repository/query_checkout'
import type ApiClient from './api_client'
import VendingMachineCardDatasource from '../interfaces/vending_machine_card_datasource'
import {
  VendingMachineImportCardJson,
  VendingMachineCardJson,
  PayloadUpdateVendingMachineCardJson,
  VendingMachineImportCardListJson,
  VendingMachineCardListJson,
  VendingMachineCardCsvJson,
  ListCheckoutJson,
} from 'data/jsons/vending-machine/vending_machine_card_json'

/* eslint-disable space-before-function-paren  */
/* eslint-disable indent  */
export default class VendingMachineCardRemoteDatasource
  implements VendingMachineCardDatasource
{
  private apiClient: ApiClient

  constructor() {
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  listImportCard(query: Query): Promise<VendingMachineImportCardListJson> {
    return new Promise((resolve, reject) => {
      this.apiClient
        .get('/vm/import-requests', query.toJson())
        .then((data) => {
          resolve(data as VendingMachineImportCardListJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  listCardIds(query: Query): Promise<VendingMachineCardListJson> {
    return new Promise((resolve, reject) => {
      this.apiClient
        .get('/vm/cards/cardIds', query.toJson())
        .then((data) => {
          resolve(data as VendingMachineCardListJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  listCard(query: Query): Promise<VendingMachineCardListJson> {
    return new Promise((resolve, reject) => {
      this.apiClient
        .get('/vm/cards', query.toJson())
        .then((data) => {
          resolve(data as VendingMachineCardListJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  importCard(params: any): Promise<VendingMachineImportCardJson> {
    return new Promise((resolve, reject) => {
      this.apiClient
        .post('/vm/import-requests', params)
        .then((data) => {
          resolve(data as VendingMachineImportCardJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  importCardTemplate(): Promise<VendingMachineCardCsvJson> {
    return new Promise((resolve, reject) => {
      this.apiClient
        .get('vm/import-requests/csv/templates')
        .then((data) => {
          resolve(data as VendingMachineCardCsvJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  batchImportCard(file: File): Promise<any> {
    return new Promise((resolve, reject) => {
      this.apiClient
        .upload('/vm/import-requests/csv', 'file', file)
        .then((data) => {
          resolve(data)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  updateCard(
    id: string,
    payload: PayloadUpdateVendingMachineCardJson
  ): Promise<VendingMachineCardJson> {
    return new Promise((resolve, reject) => {
      this.apiClient
        .put(`/vm/cards/${id}`, payload)
        .then((data) => {
          resolve(data as VendingMachineCardJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  mapCategory(cardId: string, categoryId: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.apiClient
        .put(`vm/cards/${cardId}/mapCategory`, { categoryId })
        .then((data) => {
          resolve(data)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  currencyRate(baseCurrency: string, currency: string): Promise<number> {
    return new Promise((resolve, reject) => {
      this.apiClient
        .get(
          `vm/currency-rate/rate?baseCurrency=${baseCurrency}&currency=${currency}`
        )
        .then((data) => {
          resolve(data.rate)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  listCheckout(query: QueryCheckout): Promise<ListCheckoutJson> {
    return new Promise((resolve, reject) => {
      this.apiClient
        .get('/checkouts', query.toJson())
        .then((data) => {
          resolve(data?.data as ListCheckoutJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  exportCheckout(query: QueryCheckout): Promise<any> {
    const params = query.toJson()
    delete params.limit
    delete params.page
    return new Promise((resolve, reject) => {
      this.apiClient
        .get('/checkouts/export-csv', params)
        .then((data) => {
          resolve(data)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
}
