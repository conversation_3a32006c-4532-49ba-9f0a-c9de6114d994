import container from 'inversify.config'
import TYPES from 'types'
import 'reflect-metadata'

import type ApiClient from './api_client'
import ImpressionDatasource from '../interfaces/impression_datasource'
import { ImpressionJson } from 'data/jsons/impression_json'

export default class ImpressionRemoteDatasource implements ImpressionDatasource {

  private apiClient:ApiClient

  constructor (){
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  create (targetEntityType: string, targetEntityId: string): Promise<ImpressionJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/impressions/create',
        {
          targetEntityType: targetEntityType,
          targetEntityId: targetEntityId,
        }
      )
        .then((data) => {
          resolve(data.impression as ImpressionJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
