import container from 'inversify.config'
import TYPES from 'types'
import 'reflect-metadata'

import { Query } from '../interfaces/collection_like_datasource/query'
import type ApiClient from './api_client'
import CollectionLikeDatasource from '../interfaces/collection_like_datasource'
import { CollectionLikeJson } from 'data/jsons/collection_like_json'

export default class CollectionLikeRemoteDatasource implements CollectionLikeDatasource {

  private apiClient:ApiClient

  constructor (){
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  search (query: Query): Promise<CollectionLikeJson[]> {
    return new Promise((resolve, reject) => {
      this.apiClient.get('/collectionLikes', query.toJson())
        .then((data) => {
          resolve(data.collectionLikes as CollectionLikeJson[])
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    const params = query.toJson()
    delete params.limit
    delete params.page

    return new Promise((resolve, reject) => {
      this.apiClient.get('/collectionLikes/totalCount', params)
        .then((data) => {
          resolve(data.totalCount as number)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  collectionIds (query: Query): Promise<string[]> {
    return new Promise((resolve, reject) => {
      this.apiClient.get('/collectionLikes/collectionIds', query.toJson())
        .then((data) => {
          resolve(data.collectionIds as string[])
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
