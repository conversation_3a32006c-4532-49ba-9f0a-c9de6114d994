import container from 'inversify.config'
import TYPES from 'types'
import 'reflect-metadata'

import type ApiClient from './api_client'
import SettingDatasource from '../interfaces/setting_datasource'
import { SettingJson } from 'data/jsons/setting_json'

/* eslint-disable space-before-function-paren */
export default class SettingRemoteDatasource implements SettingDatasource {
  private apiClient: ApiClient

  constructor() {
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  getSetting(key: string): Promise<SettingJson> {
    return new Promise((resolve, reject) => {
      this.apiClient
        .get(`/configurations/findByKey?key=${key}`)
        .then((data) => {
          resolve(data as Setting<PERSON><PERSON>)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  updateSetting(params: SettingJson): Promise<SettingJson> {
    return new Promise((resolve, reject) => {
      this.apiClient
        .post('/configurations/update', params)
        .then((data) => {
          resolve(data as Setting<PERSON><PERSON>)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
}
