import container from 'inversify.config'
import TYPES from 'types'
import 'reflect-metadata'

import { Query } from '../interfaces/listing_datasource/query'
import { QueryOrder } from '../interfaces/listing_datasource/query_order'
import type ApiClient from './api_client'
import ListingDatasource from '../interfaces/listing_datasource'
import { ListingJson } from 'data/jsons/listing_json'
import { ListingCreateJson } from 'data/jsons/listing/listing_create_json'
import { ListingUpdateJson } from 'data/jsons/listing/listing_update_json'
import { ListingCreateOrderSlashJson } from 'data/jsons/listing/listing_create_order_slash_json'
import { ListingOrderJson } from 'data/jsons/listing/listing_order_json'
import { ListingOrderUpdateJson } from 'data/jsons/listing/listing_update_order_json'

export default class ListingRemoteDatasource implements ListingDatasource {

  private apiClient:ApiClient

  constructor (){
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  findById (id: string): Promise<ListingJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(`/listings/${id}`)
        .then((data) => {
          resolve(data.listing as ListingJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  search (query: Query): Promise<ListingJson[]> {
    return new Promise((resolve, reject) => {
      this.apiClient.get('/listings', query.toJson())
        .then((data) => {
          resolve(data.listings as ListingJson[])
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    const params = query.toJson()
    delete params.sortBy
    delete params.limit
    delete params.page

    return new Promise((resolve, reject) => {
      this.apiClient.get('/listings/totalCount', params)
        .then((data) => {
          resolve(data.totalCount as number)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  create (params: ListingCreateJson): Promise<ListingJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/listings/create',
        params
      )
        .then((data) => {
          resolve(data.listing as ListingJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  update (params: ListingUpdateJson): Promise<ListingJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/listings/update',
        params
      )
        .then((data) => {
          resolve(data.listing as ListingJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  createOrderSlash (params: ListingCreateOrderSlashJson): Promise<string> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/listings/payment/createOrder',
        params
      )
        .then((data) => {
          resolve(data?.order?.orderPaymentUrl || '')
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
  
  findAssetIdByPaymentToken (id: string): Promise<ListingOrderJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(
        `/listings/payment/findByPaymentToken/${id}`,
      )
        .then((data) => {
          resolve(data?.order as ListingOrderJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  searchOrder (query: QueryOrder): Promise<ListingOrderJson[]> {
    return new Promise((resolve, reject) => {
      this.apiClient.get('/listings/payments', query.toJson())
        .then((data) => {
          resolve(data.order as ListingOrderJson[])
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCountOrder (query: QueryOrder): Promise<number> {
    const params = query.toJson()
    delete params.sortBy
    delete params.limit
    delete params.page

    return new Promise((resolve, reject) => {
      this.apiClient.get('/listings/payments/totalCount', params)
        .then((data) => {
          resolve(data.totalCount as number)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  getSignatureClaimNft (id: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(`/listings/payment/signature/${id}`)
        .then((data) => {
          resolve(data?.result)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  getSignatureRefundToken (id: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(`/listings/payment/refundSignature/${id}`)
        .then((data) => {
          resolve(data?.result)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
  
  updateOrder (params: ListingOrderUpdateJson): Promise<ListingOrderJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.post('listings/payment/updateOrder', params)
        .then((data) => {
          resolve(data.order as ListingOrderJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
}
