import container from 'inversify.config'
import TYPES from 'types'
import 'reflect-metadata'

import { Query } from '../interfaces/offer_datasource/query'
import type ApiClient from './api_client'
import OfferDatasource from '../interfaces/offer_datasource'
import { OfferJson } from 'data/jsons/offer_json'
import { OfferCreateJson } from 'data/jsons/offer/offer_create_json'
import { OfferUpdateJson } from 'data/jsons/offer/offer_update_json'
import { OfferCancelJson } from 'data/jsons/offer/offer_cancel_json'
import { OfferAcceptJson } from 'data/jsons/offer/offer_accept_json'

export default class OfferRemoteDatasource implements OfferDatasource {

  private apiClient:ApiClient

  constructor (){
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  findById (id: string): Promise<OfferJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(`/offers/${id}`)
        .then((data) => {
          resolve(data.offer as OfferJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  search (query: Query): Promise<OfferJson[]> {
    return new Promise((resolve, reject) => {
      this.apiClient.get('/offers', query.toJson())
        .then((data) => {
          resolve(data.offers as OfferJson[])
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    const params = query.toJson()
    delete params.sortBy
    delete params.limit
    delete params.page

    return new Promise((resolve, reject) => {
      this.apiClient.get('/offers/totalCount', params)
        .then((data) => {
          resolve(data.totalCount as number)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  create (params: OfferCreateJson): Promise<OfferJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/offers/create',
        params
      )
        .then((data) => {
          resolve(data.offer as OfferJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  cancel (params: OfferCancelJson): Promise<OfferJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/offers/cancel',
        params
      )
        .then((data) => {
          resolve(data.offer as OfferJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  accept (params: OfferAcceptJson): Promise<OfferJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/offers/accept',
        params
      )
        .then((data) => {
          resolve(data.offer as OfferJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  update (params: OfferUpdateJson): Promise<OfferJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/offers/update',
        params
      )
        .then((data) => {
          resolve(data.offer as OfferJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
