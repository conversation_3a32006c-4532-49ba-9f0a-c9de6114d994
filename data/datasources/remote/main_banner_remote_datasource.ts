import container from 'inversify.config'
import 'reflect-metadata'
import TYPES from 'types'

import { MainBannerJson } from 'data/jsons/main_banner_json'
import MainBannerDatasource from '../interfaces/main_banner_datasource'
import { Query } from '../interfaces/main_banner_datasource/query'
import type ApiClient from './api_client'

export default class MainBannerRemoteDatasource implements MainBannerDatasource {

  private apiClient:ApiClient

  constructor (){
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
     this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  findById (id: string): Promise<MainBannerJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(`/mainBanners/${id}`)
        .then((data) => {
          resolve(data.mainBanner as MainBannerJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  search (query: Query): Promise<MainBannerJson[]> {
    return new Promise((resolve, reject) => {
      this.apiClient.get('/mainBanners', query.toJson())
        .then((data) => {
          console.log("🚀 ~ MainBannerRemoteDatasource ~ .then ~ data:", data)
          resolve(data.mainBanners as MainBannerJson[])
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    const params = query.toJson()
    delete params.limit
    delete params.page

    return new Promise((resolve, reject) => {
      this.apiClient.get('/mainBanners/totalCount', params)
        .then((data) => {
          resolve(data.totalCount as number)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
