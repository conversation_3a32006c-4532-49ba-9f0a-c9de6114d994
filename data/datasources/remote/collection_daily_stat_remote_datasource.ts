import container from 'inversify.config'
import TYPES from 'types'
import 'reflect-metadata'

import { Query } from '../interfaces/collection_daily_stat_datasource/query'
import type ApiClient from './api_client'
import CollectionDailyStatDatasource from '../interfaces/collection_daily_stat_datasource'
import { CollectionDailyStatJson } from 'data/jsons/collection_daily_stat_json'

export default class CollectionDailyStatRemoteDatasource implements CollectionDailyStatDatasource {

  private apiClient:ApiClient

  constructor (){
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  findById (id: string): Promise<CollectionDailyStatJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(`/collectionDailyStats/${id}`)
        .then((data) => {
          resolve(data.collectionDailyStat as CollectionDailyStatJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  search (query: Query): Promise<any> {
    return new Promise((resolve, reject) => {
      this.apiClient.get('/collectionDailyStats', query.toJson())
        .then((data : any) => {
          resolve(data)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    const params = query.toJson()
    delete params.limit
    delete params.page

    return new Promise((resolve, reject) => {
      this.apiClient.get('/collectionDailyStats/totalCount', params)
        .then((data) => {
          resolve(data.totalCount as number)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
