import container from 'inversify.config'
import TYPES from 'types'
import 'reflect-metadata'

import type ApiClient from './api_client'
import EmailVerificationDatasource from '../interfaces/email_verification_datasource'
import { EmailVerificationJson } from 'data/jsons/email_verification_json'

export default class EmailVerificationRemoteDatasource implements EmailVerificationDatasource {

  private apiClient:ApiClient

  constructor (){
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  issue (email: string): Promise<EmailVerificationJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/emailVerifications/issue',
        { email: email }
      )
        .then((data) => {
          resolve(data.emailVerification as EmailVerificationJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  verify (token: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/emailVerifications/verify',
        { token: token }
      )
        .then((data) => {
          resolve(data.result as boolean)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  hasActiveRequest (): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(
        '/emailVerifications/hasActiveRequest'
      )
        .then((data) => {
          resolve(data.result as boolean)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  resendEmail (): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/emailVerifications/resendEmail'
      )
        .then((data) => {
          resolve(data.result as boolean)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
