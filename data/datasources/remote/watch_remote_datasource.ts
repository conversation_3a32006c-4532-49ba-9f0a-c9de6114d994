import container from 'inversify.config'
import TYPES from 'types'
import 'reflect-metadata'

import type ApiClient from './api_client'
import WatchDatasource from '../interfaces/watch_datasource'
import { WatchJson } from 'data/jsons/watch_json'

export default class WatchRemoteDatasource implements WatchDatasource {

  private apiClient:ApiClient

  constructor (){
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  create (targetEntityType: string, targetEntityId: string): Promise<WatchJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/watches/create',
        {
          targetEntityType: targetEntityType,
          targetEntityId: targetEntityId,
        }
      )
        .then((data) => {
          resolve(data.watch as WatchJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  delete (targetEntityType: string, targetEntityId: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/watches/delete',
        {
          targetEntityType: targetEntityType,
          targetEntityId: targetEntityId,
        }
      )
        .then((data) => {
          resolve(data.result as boolean)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
