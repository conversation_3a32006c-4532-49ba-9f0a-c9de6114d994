import container from 'inversify.config'
import TYPES from 'types'
import 'reflect-metadata'

import type ApiClient from './api_client'
import LikeDatasource from '../interfaces/like_datasource'
import { LikeJson } from 'data/jsons/like_json'

export default class LikeRemoteDatasource implements LikeDatasource {

  private apiClient:ApiClient

  constructor (){
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  create (targetEntityType: string, targetEntityId: string): Promise<LikeJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/likes/create',
        {
          targetEntityType: targetEntityType,
          targetEntityId: targetEntityId,
        }
      )
        .then((data) => {
          resolve(data.like as LikeJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  delete (targetEntityType: string, targetEntityId: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/likes/delete',
        {
          targetEntityType,
          targetEntityId,
        }
      )
        .then((data) => {
          resolve(data.result as boolean)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
}
