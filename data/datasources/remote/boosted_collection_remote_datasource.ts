import container from 'inversify.config'
import TYPES from 'types'
import 'reflect-metadata'

import { Query } from '../interfaces/boosted_collection_datasource/query'
import type ApiClient from './api_client'
import BoostedCollectionDatasource from '../interfaces/boosted_collection_datasource'
import { BoostedCollectionJson } from 'data/jsons/boosted_collection_json'

export default class BoostedCollectionRemoteDatasource implements BoostedCollectionDatasource {

  private apiClient:ApiClient

  constructor (){
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  findById (id: string): Promise<BoostedCollectionJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(`/boostedCollections/${id}`)
        .then((data) => {
          resolve(data.boostedCollection as BoostedCollectionJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  search (query: Query): Promise<BoostedCollectionJson[]> {
    return new Promise((resolve, reject) => {
      this.apiClient.get('/boostedCollections', query.toJson())
        .then((data) => {
          resolve(data.boostedCollections as BoostedCollectionJson[])
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    const params = query.toJson()
    delete params.limit
    delete params.page

    return new Promise((resolve, reject) => {
      this.apiClient.get('/boostedCollections/totalCount', params)
        .then((data) => {
          resolve(data.totalCount as number)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
