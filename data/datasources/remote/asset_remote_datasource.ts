import container from 'inversify.config'
import 'reflect-metadata'
import TYPES from 'types'

import { AssetJson } from 'data/jsons/asset_json'
import AssetDatasource from '../interfaces/asset_datasource'
import { Query } from '../interfaces/asset_datasource/query'
import { AssetsCreateMetadata } from 'data/jsons/assets/assets_create_metadata_json'
import { AssetsCreate } from 'data/jsons/assets/assets_create_json'
import { AssetsRefreshMetadata } from 'data/jsons/assets/assets_refresh_metadata_json'
import type ApiClient from './api_client'

export default class AssetRemoteDatasource implements AssetDatasource {

  private apiClient:ApiClient

  constructor (){
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  findById (id: string): Promise<AssetJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(`/assets/${id}`)
        .then((data) => {
          resolve(data.asset as Asset<PERSON><PERSON>)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  findByChainAndAddressAndTokenId (chain: string, address: string, tokenId: string): Promise<AssetJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(
        `/assets/findByChainAndAddressAndTokenId/${chain}/${address}/${tokenId}`
      )
        .then((data) => {
          resolve(data.asset as AssetJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  search (query: Query): Promise<AssetJson[]> {
    return new Promise((resolve, reject) => {
      this.apiClient.get('/assets', query.toJson())
        .then((data) => {
          resolve(data.assets as AssetJson[])
        })
        .catch((e) => {
          console.log('🚀 ~ AssetRemoteDatasource ~ returnnewPromise ~ e:', e)
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    const params = query.toJson()
    delete params.sortBy
    delete params.limit
    delete params.page

    return new Promise((resolve, reject) => {
      this.apiClient.get('/assets/totalCount', params)
        .then((data) => {
          resolve(data.totalCount as number)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  createMetadata (params: AssetsCreateMetadata): Promise<any> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/assets/createMetadata',
        params
      )
        .then((data) => {
          resolve(data)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  create (params: AssetsCreate): Promise<any> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/assets/create',
        params
      )
        .then((data) => {
          resolve(data.asset as AssetJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  checkOwnership (id: string, userAddress: string): Promise<Boolean> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(`/assets/${id}/check-ownership?address=${userAddress}`)
        .then((data) => {
          resolve(data?.isOwner || false)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  refreshMetadata (params: AssetsRefreshMetadata): Promise<any> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/assets/refresh',
        params
      )
        .then((data) => {
          resolve(data)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
