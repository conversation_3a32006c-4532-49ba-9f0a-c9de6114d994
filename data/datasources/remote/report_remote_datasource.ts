import container from 'inversify.config'
import TYPES from 'types'
import 'reflect-metadata'

import type ApiClient from './api_client'
import ReportDatasource from '../interfaces/report_datasource'
import { ReportJson } from 'data/jsons/report_json'

export default class ReportRemoteDatasource implements ReportDatasource {

  private apiClient:ApiClient

  constructor (){
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  create (targetEntityType: string, targetEntityId: string, reasonId: number): Promise<ReportJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/reports/create',
        {
          targetEntityType: targetEntityType,
          targetEntityId: targetEntityId,
          reasonId: reasonId,
        }
      )
        .then((data) => {
          resolve(data.report as ReportJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
