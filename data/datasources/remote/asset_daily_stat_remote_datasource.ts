import container from 'inversify.config'
import TYPES from 'types'
import 'reflect-metadata'

import { Query } from '../interfaces/asset_daily_stat_datasource/query'
import type ApiClient from './api_client'
import AssetDailyStatDatasource from '../interfaces/asset_daily_stat_datasource'
import { AssetDailyStatJson } from 'data/jsons/asset_daily_stat_json'

export default class AssetDailyStatRemoteDatasource implements AssetDailyStatDatasource {

  private apiClient:ApiClient

  constructor () {
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  findById (id: string): Promise<AssetDailyStatJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(`/assetDailyStats/${id}`)
        .then((data) => {
          resolve(data.assetDailyStat as AssetDailyStatJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  search (query: Query): Promise<AssetDailyStatJson[]> {
    return new Promise((resolve, reject) => {
      this.apiClient.get('/assetDailyStats', query.toJson())
        .then((data : any) => {
          console.log('🚀 ~ AssetDailyStatRemoteDatasource ~ .then ~ data:', data)
          resolve(data)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    const params = query.toJson()
    delete params.limit
    delete params.page

    return new Promise((resolve, reject) => {
      this.apiClient.get('/assetDailyStats/totalCount', params)
        .then((data) => {
          resolve(data.totalCount as number)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
