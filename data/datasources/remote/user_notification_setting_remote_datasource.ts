import container from 'inversify.config'
import TYPES from 'types'
import 'reflect-metadata'

import type ApiClient from './api_client'
import UserNotificationSettingDatasource from '../interfaces/user_notification_setting_datasource'
import { UserNotificationSettingUpdateJson } from 'data/jsons/user_notification_setting/user_notification_setting_update_json'
import { UserNotificationSettingJson } from 'data/jsons/user_notification_setting_json'

export default class UserNotificationSettingRemoteDatasource implements UserNotificationSettingDatasource {

  private apiClient:ApiClient

  constructor (){
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  mine (): Promise<UserNotificationSettingJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(
        '/userNotificationSettings/mine'
      )
        .then((data) => {
          resolve(data.userNotificationSetting as UserNotificationSettingJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  update (params: UserNotificationSettingUpdateJson): Promise<UserNotificationSettingJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/userNotificationSettings/update',
        params
      )
        .then((data) => {
          resolve(data.userNotificationSetting as UserNotificationSettingJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
