import container from 'inversify.config'
import TYPES from 'types'
import 'reflect-metadata'

import { Query } from 'domain/repositories/vending_machine_pack_repository/query'
import type ApiClient from './api_client'
import VendingMachinePackDatasource, { CreatePackParams, UpdatePackParams } from '../interfaces/vending_machine_pack_datasource'
import { VendingMachinePackJson, VendingMachinePackListJson } from 'data/jsons/vending-machine/vending_machine_pack_json'

export default class VendingMachinePackRemoteDatasource implements VendingMachinePackDatasource {

  private apiClient: ApiClient

  constructor () {
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  list (query: Query): Promise<VendingMachinePackListJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.get('/vm/gacha-boxes', query.toJson())
        .then((data) => {
          resolve(data as VendingMachinePackListJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  getById (id: string): Promise<VendingMachinePackJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(`/vm/gacha-boxes/${id}`)
        .then((data) => {
          resolve(data as VendingMachinePackJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  create (params: CreatePackParams): Promise<VendingMachinePackJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.post('/vm/gacha-boxes', params)
        .then((data) => {
          resolve(data as VendingMachinePackJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  update (id: string, params: UpdatePackParams): Promise<VendingMachinePackJson> {
    console.log('🚀 ~ VendingMachinePackRemoteDatasource ~ update ~ params:', params)
    return new Promise((resolve, reject) => {
      this.apiClient.put(`/vm/gacha-boxes/${id}`, params)
        .then((data) => {
          resolve(data as VendingMachinePackJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  delete (id: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.apiClient.delete(`/vm/gacha-boxes/${id}`)
        .then(() => {
          resolve()
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
}
