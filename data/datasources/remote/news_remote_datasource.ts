
import type NewsDatasource from 'data/datasources/interfaces/news_datasource'
import container from 'inversify.config'

import type ApiClient from './api_client'
import TYPES from 'types'
export default class NewsRemoteDatasource implements NewsDatasource {
  private apiClient: ApiClient

  constructor (){
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  getNews (): Promise<any> {
    return new Promise((resolve, reject) => {
      this.apiClient.get('/news')
        .then((data) => {
          resolve(data)
        })
        .catch((e) => {
          console.log('🚀 ~ NewsRemoteDatasource ~ getNews ~ e:', e)
          reject(e)
        })
    })
  }
  editNews (news: any[]): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this.apiClient.post('/news', news)
        .then((data: any ) => {
          resolve(data)
        })
        .catch((e) => {
          console.log('🚀 ~ NewsRemoteDatasource ~ editNews ~ e:', e)
          reject(e)
        })
    })
  }

}