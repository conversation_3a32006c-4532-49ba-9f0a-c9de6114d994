import ApiClient from 'data/datasources/remote/api_client'
import { MintedCard } from './checkout_remote_datasource'

export interface BuybackCard extends MintedCard {
  buybackOfferId?: string
  buybackOfferPrice?: number
  buybackExpiryDate?: string
  isEligibleForBuyback?: boolean
}

export interface BuybackCardsResponse {
  buybackCards: BuybackCard[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export interface AcceptOfferParams {
  cardId: string
  buybackOfferId: string
}

export interface RejectOfferParams {
  cardId: string
  buybackOfferId: string
}

export default class BuybackRemoteDatasource {
  private apiClient: ApiClient

  constructor () {
    this.apiClient = new ApiClient()
  }

  // Get buyback eligible cards
  getBuybackCards (userId: string, page: number = 1, limit: number = 20): Promise<BuybackCardsResponse> {
    return new Promise((resolve, reject) => {
      // For now, using mock data - replace with real API call later
      setTimeout(() => {
        // Mock buyback cards based on minted cards structure
        const mockBuybackCards: any[] = [
          {
            id: 'buyback-card-1',
            tokenId: '1234567890',
            name: 'Polygon Amoy Card #001',
            image: '/asset/images/vending-machine/card.jpg',
            attributes: {
              series: 'Polygon Amoy',
              cardNumber: '001',
              shop: 'digital',
              cardId: 'card-001',
              status: 'active',
              scrapedAt: new Date().toISOString(),
            },
          
            packPrice: 20,
            buyBackPrice: '2000',
            buybackExpirationTimestamp: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days from now
            checkoutId: 'checkout-1',
            tokenIndex: 0,
            mintTxHash: '0x1234567890abcdef',
            mintBlockchainTimestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            chainId: 80002,
            buybackOfferId: 'offer-1',
            buybackOfferPrice: 18,
            isEligibleForBuyback: true,
          },
          {
            id: 'buyback-card-2',
            tokenId: '1234567890',
            name: 'Polygon Amoy Card #002',
            image: '/asset/images/vending-machine/card.jpg',
            attributes: {
              series: 'Polygon Amoy',
              cardNumber: '001',
              shop: 'digital',
              cardId: 'card-001',
              status: 'active',
              scrapedAt: new Date().toISOString(),
            },
          
            packPrice: 20,
            buyBackPrice: '2000',
            buybackExpirationTimestamp: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days from now
            checkoutId: 'checkout-1',
            tokenIndex: 0,
            mintTxHash: '0x1234567890abcdef',
            mintBlockchainTimestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            chainId: 80002,
            buybackOfferId: 'offer-1',
            buybackOfferPrice: 18,
            isEligibleForBuyback: true,
          },
          {
            id: 'buyback-card-3',
            tokenId: '1234567890',
            name: 'Polygon Amoy Card #003',
            image: '/asset/images/vending-machine/card.jpg',
            attributes: {
              series: 'Polygon Amoy',
              cardNumber: '001',
              shop: 'digital',
              cardId: 'card-001',
              status: 'active',
              scrapedAt: new Date().toISOString(),
            },
          
            packPrice: 20,
            buyBackPrice: '2000',
            buybackExpirationTimestamp: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days from now
            checkoutId: 'checkout-1',
            tokenIndex: 0,
            mintTxHash: '0x1234567890abcdef',
            mintBlockchainTimestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            chainId: 80002,
            buybackOfferId: 'offer-1',
            buybackOfferPrice: 18,
            isEligibleForBuyback: true,
          },
          {
            id: 'buyback-card-4',
            tokenId: '1234567890',
            name: 'Polygon Amoy Card #004',
            image: '/asset/images/vending-machine/card.jpg',
            attributes: {
              series: 'Polygon Amoy',
              cardNumber: '001',
              shop: 'digital',
              cardId: 'card-001',
              status: 'active',
              scrapedAt: new Date().toISOString(),
            },
          
            packPrice: 20,
            buyBackPrice: '2000',
            buybackExpirationTimestamp: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days from now
            checkoutId: 'checkout-1',
            tokenIndex: 0,
            mintTxHash: '0x1234567890abcdef',
            mintBlockchainTimestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            chainId: 80002,
            buybackOfferId: 'offer-1',
            buybackOfferPrice: 18,
            isEligibleForBuyback: true,
          },
        ]

        const response: BuybackCardsResponse = {
          buybackCards: mockBuybackCards,
          pagination: {
            page,
            limit,
            total: mockBuybackCards.length,
            totalPages: Math.ceil(mockBuybackCards.length / limit),
          },
        }

        resolve(response)
      }, 800) // Simulate API delay
    })
  }

  // Accept buyback offer
  acceptOffer (params: AcceptOfferParams): Promise<{ success: boolean; message: string }> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        console.log('Accepting buyback offer:', params)
        resolve({
          success: true,
          message: 'Buyback offer accepted successfully',
        })
      }, 500)
    })
  }

  // Reject buyback offer
  rejectOffer (params: RejectOfferParams): Promise<{ success: boolean; message: string }> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        console.log('Rejecting buyback offer:', params)
        resolve({
          success: true,
          message: 'Buyback offer rejected successfully',
        })
      }, 500)
    })
  }

  // Cancel buyback request (admin)
  cancelBuybackRequest (requestId: string): Promise<{ success: boolean; message: string }> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        console.log('Cancelling buyback request:', requestId)
        resolve({
          success: true,
          message: 'Buyback request cancelled successfully',
        })
      }, 500)
    })
  }

  // Real API call methods (commented out for now)
  /*
  getBuybackCards(userId: string, page: number = 1, limit: number = 20): Promise<BuybackCardsResponse> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(`/buyback/cards?userId=${userId}&page=${page}&limit=${limit}`)
        .then((rs) => {
          const data = rs.data as BuybackCardsResponse
          resolve(data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  acceptOffer(params: AcceptOfferParams): Promise<{ success: boolean; message: string }> {
    return new Promise((resolve, reject) => {
      this.apiClient.post('/buyback/accept-offer', params)
        .then((rs) => {
          const data = rs.data as { success: boolean; message: string }
          resolve(data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  rejectOffer(params: RejectOfferParams): Promise<{ success: boolean; message: string }> {
    return new Promise((resolve, reject) => {
      this.apiClient.post('/buyback/reject-offer', params)
        .then((rs) => {
          const data = rs.data as { success: boolean; message: string }
          resolve(data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }
  */
}
