import container from 'inversify.config'
import TYPES from 'types'
import 'reflect-metadata'

import type ApiClient from './api_client'
import UserOfferSettingDatasource from '../interfaces/user_offer_setting_datasource'
import { UserOfferSettingJson } from 'data/jsons/user_offer_setting_json'

export default class UserOfferSettingRemoteDatasource implements UserOfferSettingDatasource {

  private apiClient:ApiClient

  constructor (){
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  search (): Promise<UserOfferSettingJson[]> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(
        '/userOfferSettings'
      )
        .then((data) => {
          resolve(data.userOfferSettings as UserOfferSettingJson[])
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  update (collectionId: string, minimumPrice: number): Promise<UserOfferSettingJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/userOfferSettings/update',
        {
          collectionId: collectionId,
          minimumPrice: minimumPrice,
        }
      )
        .then((data) => {
          resolve(data.userOfferSetting as UserOfferSettingJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
