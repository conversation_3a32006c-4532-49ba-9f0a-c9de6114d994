import { injectable } from 'inversify'
import 'reflect-metadata'

import axios, { Axios, AxiosError, AxiosResponse } from 'axios'
import { ErrorsJson } from 'data/jsons/errors_json'
import BaseError from 'errors/base_error'
import { BadGatewayError, BadRequestError, ForbiddenError, GatewayTimeoutError, GeneralError, InternalServerError, MethodNotAllowedError, NotFoundError, NotImplementedError, RequestTimeoutError, ServiceUnavailableError, TooManyRequestsError, UnauthorizedError } from 'errors/custom_datasource_errors'
import TokenManager from './token_manager'

@injectable()
export default class ApiClient {

  static defaultBaseUrl = process.env.NEXT_PUBLIC_API_URL ?? 'https://internal-api.nft-land.me/v1'
  // static defaultTimeout = 3500
  static defaultTimeout = 7200000 // 2 hours
  static defaultResponseType = 'json'
  static defaultResponseEncoding = 'utf8'
  static defaultHeaders = {
    'Content-Type': 'application/json;charset=utf-8',
    'Access-Control-Allow-Origin': '*',
  }

  //static defaultHeaders = { 'Content-type': 'application/json' }
  static formUrlEncodedContentType = { 'Content-type': 'application/x-www-form-urlencoded' }

  static authorizationBearer (token: string): string {
    return `Bearer ${token}`
  }

  private tokenManager: TokenManager = new TokenManager()
  private axios: Axios

  constructor (
    baseURL: string = ApiClient.defaultBaseUrl,
    timeout: number = ApiClient.defaultTimeout,
    headers?: { [key: string]: string }
  ) {
    let config: { [key: string]: any } = {
      baseURL: baseURL,
      timeout: timeout,
    }
    config.headers = (headers !== undefined)
      ? { ...ApiClient.defaultHeaders, ...headers }
      : ApiClient.defaultHeaders

    this.axios = axios.create(config)
    this._registerRequestInterceptor()
    this._registerResponseInterceptor()
  }

  public setDefaultBaseUrl (baseUrl: string) {
    this.axios.defaults.baseURL = baseUrl
  }

  public setDefaultTimeout (timeout: number) {
    this.axios.defaults.timeout = timeout
  }

  public setDefaultHeaders (headers: { [key: string]: string }) {
    this.axios.defaults.headers.common = headers
  }

  public setDefaultHeader (key: string, value: string) {
    this.axios.defaults.headers.common[key] = value
  }

  public get (url: string, params: { [key: string]: any } | null = null, _?: { [key: string]: any }): Promise<{ [key: string]: any }> {
    let config: { [key: string]: any } = {}
    if (params !== null) {
      config.params = params
    }

    return new Promise((resolve, reject) => {
      this.axios.get(url, config)
        .then((response) => {
          resolve(response.data)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  public post (url: string, params: { [key: string]: any } | null = null, _?: { [key: string]: any }): Promise<{ [key: string]: any }> {
    let data: { [key: string]: any } = {}
    if (params !== null) {
      data = params
    }
    return new Promise((resolve, reject) => {
      this.axios.post(url, data)
        .then((response) => {
          resolve(response.data)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  public put (url: string, params: { [key: string]: any } | null = null, _?: { [key: string]: any }): Promise<{ [key: string]: any }> {
    let data: { [key: string]: any } = {}
    if (params !== null) {
      data = params
    }
    return new Promise((resolve, reject) => {
      this.axios.put(url, data)
        .then((response) => {
          resolve(response.data)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  public delete (url: string, params: { [key: string]: any } | null = null, _?: { [key: string]: any }): Promise<{ [key: string]: any }> {
    let config: { [key: string]: any } = {}
    if (params !== null) {
      config.params = params
    }
    return new Promise((resolve, reject) => {
      this.axios.delete(url, config)
        .then((response) => {
          resolve(response.data)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  public upload (url: string, key: string, file: File, _?: { [key: string]: any }): Promise<{ [key: string]: any }> {
    return new Promise((resolve, reject) => {
      const form = new FormData()
      form.append(key, file)
      const headers = { 'content-type': 'multipart/form-data' }
      this.axios.post(url, form, { headers })
        .then((response) => {
          resolve(response.data)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  private _registerRequestInterceptor () {
    this.axios.interceptors.request.use((config) => {
      const token = this.tokenManager.getToken()
      if (token !== null) {
        config!.headers!.Authorization = ApiClient.authorizationBearer(token)
      }
      return config
    })
  }

  private _registerResponseInterceptor () {
    this.axios.interceptors.response.use(
      (response: AxiosResponse) => {
        return response
      },
      (error: any) => {

        if (error.response === undefined || error.response!.data === undefined) {
          const generalError: BaseError = new GeneralError(error.message, [error.message])
          return Promise.reject(generalError)
        }

        let errorMessages: string[] = []

        const data: any = error.response!.data!
        if (data.message !== undefined) {
          errorMessages.push(data.message)
        } else if (data.errors !== undefined) {
          const errorResponse: ErrorsJson = data as ErrorsJson
          errorMessages = errorResponse.errors.map((error) => { return error.message })
        }

        let customError: BaseError | null = null
        switch (error.response.status) {
          case 400:
            if(error.response.data?.errors) {
              customError = new BadRequestError(error.message, errorMessages, error.response?.data?.errors)
            } else {
              customError = new BadRequestError(error.message, errorMessages)
            }
            break
          case 401:
            customError = new UnauthorizedError(error.message, errorMessages)
            break
          case 403:
            customError = new ForbiddenError(error.message, errorMessages)
            break
          case 404:
            customError = new NotFoundError(error.message, errorMessages)
            break
          case 405:
            customError = new MethodNotAllowedError(error.message, errorMessages)
            break
          case 408:
            customError = new RequestTimeoutError(error.message, errorMessages)
            break
          case 429:
            customError = new TooManyRequestsError(error.message, errorMessages)
            break
          case 500:
            customError = new InternalServerError(error.message, errorMessages)
            break
          case 501:
            customError = new NotImplementedError(error.message, errorMessages)
            break
          case 502:
            customError = new BadGatewayError(error.message, errorMessages)
            break
          case 503:
            customError = new ServiceUnavailableError(error.message, errorMessages)
            break
          case 504:
            customError = new GatewayTimeoutError(error.message, errorMessages)
            break
          default:
            customError = new GeneralError(error.message, errorMessages)
        }
        return Promise.reject(customError)
      }
    )
  }
 
}
