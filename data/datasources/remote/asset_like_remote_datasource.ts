import container from 'inversify.config'
import TYPES from 'types'
import 'reflect-metadata'

import { Query } from '../interfaces/asset_like_datasource/query'
import type ApiClient from './api_client'
import AssetLikeDatasource from '../interfaces/asset_like_datasource'
import { AssetLikeJson } from 'data/jsons/asset_like_json'

export default class AssetLikeRemoteDatasource implements AssetLikeDatasource {

  private apiClient:ApiClient

  constructor (){
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  search (query: Query): Promise<AssetLikeJson[]> {
    return new Promise((resolve, reject) => {
      this.apiClient.get('/assetLikes', query.toJson())
        .then((data) => {
          resolve(data.assetLikes as AssetLikeJson[])
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    const params = query.toJson()
    delete params.limit
    delete params.page

    return new Promise((resolve, reject) => {
      this.apiClient.get('/assetLikes/totalCount', params)
        .then((data) => {
          resolve(data.totalCount as number)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  assetIds (query: Query): Promise<string[]> {
    return new Promise((resolve, reject) => {
      this.apiClient.get('/assetLikes/assetIds', query.toJson())
        .then((data) => {
          resolve(data.assetIds as string[])
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
