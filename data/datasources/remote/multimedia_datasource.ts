import container from 'inversify.config'
import TYPES from 'types'
import 'reflect-metadata'

import { MultimediaJson } from 'data/jsons/multimedia_json'
import MultimediaDatasource from '../interfaces/multimedia_datasource'
import type ApiClient from './api_client'

export default class MultimediaRemoteDatasource implements MultimediaDatasource {

  private apiClient:ApiClient

  constructor (){
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  upload (file: File): Promise<MultimediaJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.upload(
        '/multimedias/upload',
        'multimedia',
        file
      )
        .then((data) => {
          resolve(data.multimedia as MultimediaJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
