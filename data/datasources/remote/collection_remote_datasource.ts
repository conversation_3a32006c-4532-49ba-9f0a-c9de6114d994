import container from 'inversify.config'
import TYPES from 'types'
import 'reflect-metadata'

import { Query } from '../interfaces/collection_datasource/query'
import type ApiClient from './api_client'
import CollectionDatasource from '../interfaces/collection_datasource'
import { CollectionJson } from 'data/jsons/collection_json'
import { CollectionCreateJson } from 'data/jsons/collection/collection_create_json'
import { CollectionUpdateJson } from 'data/jsons/collection/collection_update_json'

export default class CollectionRemoteDatasource implements CollectionDatasource {

  private apiClient:ApiClient

  constructor (){
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  findById (id: string): Promise<CollectionJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(`/collections/${id}`)
        .then((data) => {
          resolve(data.collection as CollectionJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  findBySlug (slug: string, isExact: boolean, chainId?: number): Promise<any> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(
        `/collections/findBySlug/${slug}?isExact=${isExact}&chainId=${chainId}`
      )
        .then((data) => {
          if (isExact) {
            resolve(data.collection as CollectionJson)
          } else {
            resolve(data.collections as CollectionJson[])
          }
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  search (query: Query): Promise<CollectionJson[]> {
    console.log('🚀 ~ CollectionRemoteDatasource ~ search ~ query:', query)
    return new Promise((resolve, reject) => {
      this.apiClient.get('/collections', query.toJson())
        .then((data) => {
          resolve(data.collections as CollectionJson[])
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    const params = query.toJson()
    delete params.sortBy
    delete params.limit
    delete params.page

    return new Promise((resolve, reject) => {
      this.apiClient.get('/collections/totalCount', params)
        .then((data) => {
          resolve(data.totalCount as number)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  create (params: CollectionCreateJson): Promise<CollectionJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/collections/create',
        params
      )
        .then((data) => {
          resolve(data.collection as CollectionJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  import (params: CollectionJson & { txHash: string } & { contractAddress: string }): Promise<CollectionJson> {
    console.log('🚀 ~ CollectionRemoteDatasource ~ import ~ params:', params)
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/collections/import',
        params
      )
        .then((data) => {
          resolve(data.collection as CollectionJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  importFromContractAddress (params: CollectionJson & { contractAddress: string }): Promise<CollectionJson> {
    console.log('🚀 ~ CollectionRemoteDatasource ~ importFromContractAddress ~ params:', params)
    return new Promise((resolve, reject) => {
      this.apiClient.post('/collections/import', params)
        .then((data) => {
          resolve(data.collection as CollectionJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  update (params: CollectionUpdateJson): Promise<CollectionJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.post(
        '/collections/update',
        params
      )
        .then((data) => {
          resolve(data.collection as CollectionJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
