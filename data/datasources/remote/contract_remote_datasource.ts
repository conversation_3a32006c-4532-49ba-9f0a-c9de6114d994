import container from 'inversify.config'
import TYPES from 'types'
import 'reflect-metadata'

import { Query } from '../interfaces/contract_datasource/query'
import type ApiClient from './api_client'
import ContractDatasource from '../interfaces/contract_datasource'
import { ContractJson } from 'data/jsons/contract_json'

export default class ContractRemoteDatasource implements ContractDatasource {

  private apiClient:ApiClient

  constructor (){
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  findById (id: string): Promise<ContractJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(`/contracts/${id}`)
        .then((data) => {
          resolve(data.contract as ContractJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  findByChainAndAddress (chain: string, address: string): Promise<ContractJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(
        `/contracts/findByChainAndAddress/${chain}/${address}`
      )
        .then((data) => {
          resolve(data.contract as ContractJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  search (query: Query): Promise<ContractJson[]> {
    return new Promise((resolve, reject) => {
      this.apiClient.get('/contracts', query.toJson())
        .then((data) => {
          resolve(data.contracts as ContractJson[])
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    const params = query.toJson()
    delete params.limit
    delete params.page

    return new Promise((resolve, reject) => {
      this.apiClient.get('/contracts/totalCount', params)
        .then((data) => {
          resolve(data.totalCount as number)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
