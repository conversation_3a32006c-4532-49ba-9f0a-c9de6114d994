import container from 'inversify.config'
import 'reflect-metadata'
import TYPES from 'types'

import { ActivityJson } from 'data/jsons/activity_json'
import ActivityDatasource from '../interfaces/activity_datasource'
import { Query } from '../interfaces/activity_datasource/query'
import type ApiClient from './api_client'

export default class ActivityRemoteDatasource implements ActivityDatasource {

  private apiClient: ApiClient

  public constructor (){
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  findById (id: string): Promise<ActivityJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(`/activities/${id}`)
        .then((data) => {
          resolve(data.activity as ActivityJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  search (query: Query): Promise<ActivityJson[]> {
    return new Promise((resolve, reject) => {
      this.apiClient.get('/activities', query.toJson())
        .then((data) => {
          resolve(data.activities as ActivityJson[])
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    const params = query.toJson()
    delete params.sortBy
    delete params.limit
    delete params.page

    return new Promise((resolve, reject) => {
      this.apiClient.get('/activities/totalCount', params)
        .then((data) => {
          resolve(data.totalCount as number)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
