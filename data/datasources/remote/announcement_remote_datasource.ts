import container from 'inversify.config'
import TYPES from 'types'
import 'reflect-metadata'

import { Query } from '../interfaces/announcement_datasource/query'
import type ApiClient from './api_client'
import AnnouncementDatasource from '../interfaces/announcement_datasource'
import { AnnouncementJson } from 'data/jsons/announcement_json'

export default class AnnouncementRemoteDatasource implements AnnouncementDatasource {

  private apiClient:ApiClient

  constructor (){
    this.apiClient = container.get<ApiClient>(TYPES.ApiClient)
  }

  findById (id: string): Promise<AnnouncementJson> {
    return new Promise((resolve, reject) => {
      this.apiClient.get(`/announcements/${id}`)
        .then((data) => {
          resolve(data.announcement as AnnouncementJson)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  search (query: Query): Promise<AnnouncementJson[]> {
    const params = query.toJson()
    return new Promise((resolve, reject) => {
      this.apiClient.get('/announcements', params)
        .then((data) => {
          resolve(data.announcements as AnnouncementJson[])
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    const params = query.toJson()
    delete params.limit
    delete params.page

    return new Promise((resolve, reject) => {
      this.apiClient.get('/announcements/totalCount', params)
        .then((data) => {
          resolve(data.totalCount as number)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
