import { generateData } from './utils'

export const menu = [
  {
    title: 'Home',
    link: '/',
  },
  {
    title: 'Categories',
    link: '/categories/all',
    children: [
      {
        title: 'All',
        link: '/categories/all',
      },
      {
        title: 'Digital Art',
        link: '/categories/digitalArt',
      },
    ],
  },
  {
    title: 'Assets',
    link: '/assets',
  },
  {
    title: 'Collections',
    link: '/collections',
    children: [
      {
        title: 'Ranking',
        link: '/collections/ranking',
      },
    ],
  },
  {
    title: 'Activities',
    link: '/activities',
  },
  {
    title: 'Create asset',
    link: '/assets/create',
  },
  // Vending Machine menu item - will be conditionally displayed based on network
  {
    title: 'Vending Machine',
    link: '/vending-machine',
  },
  // {
  //   title: 'Analytics',
  //   link: '/analytics',
  // },
  // {
  //   title: 'Create',
  //   link: '/assets/create',
  //   children: [
  //     {
  //       title: 'Generator',
  //       link: '/generator',
  //     },
  //   ],
  // },
]

export const userNav = [
  {
    icon: 'user',
    title: 'Profile',
    link: '/account',
    iconSize: 'w-[22px] h-[24px]',
    viewBox: '0 0 22 22',
  },
  {
    icon: 'eye',
    title: 'Watchlist',
    link: '/account/watchingCollections',
    iconSize: 'w-[26px] h-[20px]',
  },
  {
    icon: 'grid',
    title: 'Collections',
    link: '/account/collections',
    iconSize: 'w-[20px] h-[20px]',
  },
  {
    icon: 'tag',
    title: 'Vending Machine',
    link: '/account/vending-machine',
    iconSize: 'w-[20px] h-[20px]',
    children: [
      {
        title: 'Minted History',
        link: '/account/vending-machine/minted-history',
      },
      {
        title: 'Buy Back',
        link: '/account/vending-machine/buy-back',
      },
    ],
  },
  {
    icon: 'payment',
    title: 'Payments',
    link: '/listings/payment/list',
    iconSize: 'w-[20px] h-[20px] payment',
    viewBox: '0 0 18 18',
  },
  {
    icon: 'setting',
    title: 'Settings',
    link: '/account/profileSettings',
    iconSize: 'w-[20px] h-[20px]',
  },
]

export const cartItems = generateData(3, {
  imageSrc: '/asset/images/<EMAIL>',
  title: '1,141LAND AND MORE',
  name: 'Alexa Smith',
  price: 1234,
  priceSymbol: 'ETH',
})

export const darkerBgPages = ['/assets/[id]', '/assets/[...same]', '/gacha']

export const noneBgImgPages = [
  '/admin',
  '/admin/accounts',
  '/admin/account-detail',
  '/admin/category',
]