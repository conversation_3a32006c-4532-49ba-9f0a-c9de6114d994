import { removeArrEl } from 'lib/utils'
import { useState } from 'react'
import { FilterParams } from 'types/type'

export function useChangeFilterParams (initParams: FilterParams) {
  const [filterParams, setFilterParams] = useState(initParams)

  function changeFilterParam (param: keyof FilterParams, value: any, activeState?: boolean) {
    if (typeof filterParams[param] !== 'object') {
      setFilterParams((prevState: any) => ({
        ...prevState,
        [param]: value,
      }))
    } else {
      setFilterParams((prevState: any) => ({
        ...prevState,
        [param]: activeState
          ? 
          // For payment tokens, compare by id
          param === 'paymentTokens' 
            ? prevState[param].some((token: any) => token.id === value.id)
              ? prevState[param]
              : [...prevState[param], value]
            // For other arrays, use includes
            : prevState[param].includes(value)
              ? prevState[param]
              : [...prevState[param], value]
          : 
          param === 'paymentTokens'
            ? prevState[param].filter((token: any) => token.id !== value.id)
            : removeArrEl(value, prevState[param]),
      }))
    }
  }

  return {
    filterParams,
    setFilterParams,
    changeFilterParam,
  }
}
