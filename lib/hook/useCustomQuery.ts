import {
  useQuery,
  type UseQueryOptions,
  type UseQueryResult,
  type QueryKey,
} from '@tanstack/react-query'

export function useCustomQuery<
  TQueryFnData,
  TError = unknown,
  TData = TQueryFnData,
> (
  queryKey: QueryKey,
  queryFn: () => Promise<TQueryFnData>,
  options?: Omit<UseQueryOptions<TQueryFnData, TError, TData, QueryKey>, 'queryKey' | 'queryFn'>
): UseQueryResult<TData, TError> {
  return useQuery<TQueryFnData, TError, TData, QueryKey>({
    queryKey,
    queryFn,
    ...(options ?? {}),
  })
}
