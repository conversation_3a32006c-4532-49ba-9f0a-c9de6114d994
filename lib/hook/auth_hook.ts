import MetaMaskOnboarding from '@metamask/onboarding'
import { MessageToSignService } from 'domain/services/utils/message_to_sign_service'
import Web3Service from 'domain/services/web3_service'
import { ethers } from 'ethers'
import { getRpcUrl } from 'lib/utils'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'
import AuthUseCase from 'presentation/use_cases/auth_use_case'
import UserUseCase from 'presentation/use_cases/user_use_case'
import { useCallback, useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import { useAccount, useConfig, useConnect, useDisconnect, useSignMessage, useSwitchChain } from 'wagmi'
import { metaMask, walletConnect } from 'wagmi/connectors'
import * as chains from '@wagmi/core/chains'
import eventBus from 'lib/eventBus'
const metamaskConnector = metaMask()
const walletConnectConnector = walletConnect({
  projectId: process.env.NEXT_PUBLIC_WALLET_CONNECT_PROJECT_ID || '',
})
const authUseCase = new AuthUseCase()
const userUseCase = new UserUseCase()
let web3Service: Web3Service

// declare global {
//   interface Window {
//     ethereum: any;
//   }
// }

export function useAuthentication () {
  const [isMounted, setIsMounted] = useState<boolean>(false)
  const [isMetamaskInstalled, setIsMetamaskInstalled] =
    useState<boolean>(false)

  const authStateProvider = AuthStateProvider((state: any) => ({
    init: state.init,
    me: state.me,
    reload: state.reload,
    logout: state.logout,
  }))

  let ethereum: any = null,
    provider: any = null

  if (isMounted && isMetamaskInstalled) {
    ethereum = window.ethereum
    provider = new ethers.providers.Web3Provider(ethereum)
  }
  const { address: addressConnect, connector, chainId: currentChainId, isConnected } = useAccount()
  const { connectAsync } = useConnect()
  const { disconnect } = useDisconnect()
  const { signMessageAsync } = useSignMessage()
  const { switchChain, switchChainAsync } = useSwitchChain()
  const config = useConfig()

  const isAddessUser = (address: string) => {
    if(address && authStateProvider?.me?.publicAddresses?.length) {
      const addressFind = authStateProvider?.me?.publicAddresses.find((i: any) => i.toLocaleLowerCase() == address.toLocaleLowerCase())
      return addressFind? true : false
    }
    return null
  }
  
  const loginBE = async (address : string) => {
    // link wallet - start
    if(authStateProvider.me && authStateProvider.me.isLinkWallet == false) {
      try {
        const nonce =  authStateProvider.me?.nonce || 0
        const signature =  await sign(nonce, address)
        if(signature) {
          await authUseCase.linkWallet(address, signature as string)
          await authStateProvider.reload()
          // eventBus.emit('ConnectRequire-ReloadPage')
          window.location.reload()
        }
        return
      } catch (error: any) {
        disconnect()
        if(error.getErrors) {
          const errors = error.getErrors()
          if(errors?.length) {
            toast.error(errors[0])
            return 
          }
        }
        toast.error(error?.message || error || 'Failed to connect')
        return 
      }
    }
    // link wallet - end
    await userUseCase
      .nonce(address)
      .then(async (nonce: number) => {
        await sign(nonce, address).then(async (signature) => {
          if (signature) {
            await authUseCase
              .create(address, signature)
              .then(() => {
                authStateProvider.init()
                if(isAddessUser(address) === false) {
                  toast.warning('Wallet mismatch: Connected to a different account')
                  setTimeout(() => {
                    window.location.reload()
                  }, 3000)
                  return
                } else {
                  window.location.reload()
                }
                // check if is Please connect to a wallet component is reload page
                // eventBus.emit('ConnectRequire-ReloadPage')
              })
              .catch(() => {
                disconnect()
                toast.error('Failed to login')
              })
          }
        })
      })
      .catch(async () => {
        await userUseCase
          .create(address)
          .then(async () => {
            await userUseCase.nonce(address).then(async (nonce: number) => {
              await sign(nonce, address).then(async (signature) => {
                if (signature) {
                  await authUseCase
                    .create(address, signature)
                    .then(async () => {
                      authStateProvider.init()
                      if(isAddessUser(address) === false) {
                        toast.warning('Wallet mismatch: Connected to a different account')
                        setTimeout(() => {
                          window.location.reload()
                        }, 3000)
                        return
                      } else {
                        window.location.reload()
                      }
                    })
                    .catch(() => {
                      disconnect()
                      toast.error('Failed to login')
                    })
                }
              })
            })
          })
          .catch(() => {
            toast.error('Failed to create user')
          })
      })
  }
  const login = async (walletType : string = 'metamask') => {
    try {
      disconnect()
    } catch (error) {
      // no-op
    }
    try {
      let address = ''
      if (walletType === 'metamask') {
        address = await connectMetamask()
      } else { 
        address = await connectWalletConnect()
      }
      loginBE(address)
    } catch (error) {
      console.log('🚀 ~ login ~ error:', error)
      toast.error('Failed to connect')
    }
  }

  const connectMetamask = async () => {
    let rs = await connectAsync({ connector: metamaskConnector })
    return rs.accounts[0]
  }
  const connectWalletConnect = async () => {
    let rs = await connectAsync({
      connector:walletConnectConnector,
    })
    return Promise.resolve(rs.accounts[0])
  }

  const sign = async (nonce: number, publicAddress: string) => {
    let signature: string
    try {
      //signature = await web3Service.sign(nonce, publicAddress, '')
      const messageToSign = MessageToSignService.agreePrivacyPolicyAndTermsOfService(`${nonce}`)
      const signature = await signMessageAsync({ message: messageToSign })
      return signature
    } catch (error) {
      console.log('🚀 ~ sign ~ error:', error)
      disconnect()
      toast.error('Failed to sign')
    }
  }
  const logoutBE = async () => {
    try {
      await authUseCase.logout()
      authStateProvider.logout()
    } catch (error) {
      console.log('🚀 ~ logoutBE ~ error:', error)
      
    }
  }
  const logout = async () => {
    try {
      await logoutBE()
      await disconnect() // disconnect wagmi
      window.location.reload()
    } catch (error) {
      window.location.reload()
    }
  }


  const getProvider =  async () => {
    if (connector) {
      const _provider = await connector?.getProvider()
      return _provider
    }
    if (isMetamaskInstalled && isMounted) {
      return provider
    }
  }
  const getProviderEthers = useCallback(async () => {
    try {
      if(connector){
        const _provider = await connector?.getProvider() || window.ethereum
        const ethersProvider = new ethers.providers.Web3Provider(_provider)
        // const provider = getEthersProvider(config)
        return ethersProvider
      }
      if(isMetamaskInstalled && isMounted) {
        return provider
      }
    }catch(error) {
      console.log('🚀 ~ getProviderEthers ~ error:', error)
      return null
    }
  }, [connector, isMetamaskInstalled, isMounted])
  const accountChanged = async (accounts: string[]) => {
    // await logout()
    // if (accounts.length > 0) {
    //   await login()
    // }
  }

  // const checkChainId =  () => {
  //   if (chainId) {
  //     return chainId
  //   }
  //   return null
  //   // if (isMetamaskInstalled) {
  //   //   const chainId = await window.ethereum.request({
  //   //     method: 'eth_chainId',
  //   //   })
  //   //   return parseInt(chainId, 16)
  //   // }
  // }

  const getChainInfo = (chainId: Number) => {
    const arrChain = Object.values(chains)
    return arrChain?.find((i) => i.id == Number(chainId))
  }

  const switchNetwork = async (chain: any) => {
    try {
      const chainConfig = config.chains?.find((i:any) => i.id == chain.chainId)
      if(!chainConfig) {
        return toast.error('Chain not supported')
      }
      // connected wagmi
      if (connector) { 
        const rs = await switchChainAsync({ chainId: chain.chainId })
        console.log('🚀 ~ switchChainAsync ~ rs:', rs)
        return rs
      } else { 
        // not connected
        logout()
        return toast.error('Connector missing')
        // if (window.ethereum) {
        //   let rs  = await window.ethereum.request({
        //     method: 'wallet_switchEthereumChain',
        //     params: [{ chainId: ethers.utils.hexValue(chain.chainId) }],
        //   })
        //   return rs
        // }
      
      }
    } catch (error: any) {
      console.log('🚀 ~ switchNetwork ~ error:', error)
      let codes = [4902, -32603]
      if (
        codes.includes(error?.code) ||
        codes.some((code) => error?.message?.includes(code))
      ) {
        try {
          const provider = await getProviderEthers()
          if(!provider) throw error
          const chainInfo = getChainInfo(chain.chainId)
          if(!chainInfo) throw error
          await provider.request({
            method: 'wallet_addEthereumChain',
            params: [
              {
                chainId: ethers.utils.hexValue(chain.chainId),
                chainName: chainInfo?.name,
                rpcUrls: chainInfo?.rpcUrls?.default?.http,
                blockExplorerUrls: [chainInfo?.blockExplorers?.default?.url],
                nativeCurrency: chainInfo?.nativeCurrency,
              },
            ],
          })
        } catch (err: any) {
          console.log('🚀 ~ switchNetwork ~ err:', err)
          // throw err
          return toast.error(err?.message || err)
        }
      }

      // throw error
      return toast.error(error?.message || error)
    }
  }

  const checkMetamaskAvailability = async () => {
    let ethereum = window.ethereum
    const metamaskStatus = MetaMaskOnboarding.isMetaMaskInstalled()
    if (!ethereum || !metamaskStatus) {
      setIsMetamaskInstalled(false)
    } else {
      web3Service = new Web3Service(ethereum)
      setIsMetamaskInstalled(true)
      ethereum.removeListener('accountsChanged', accountChanged) // Ensure previous listeners are removed
      ethereum.on('accountsChanged', accountChanged)
      ethereum.removeListener('chainChanged', accountChanged)
      ethereum.on('chainChanged', async () => {
        window.location.reload()
      })
      //checkChainId()
    }
  }

  useEffect(() => {
    setIsMounted(true)
    //checkMetamaskAvailability()
  }, [])


  return {
    addressConnect,
    currentChainId,
    login,
    loginBE,
    logoutBE,
    sign,
    logout,
    getProvider,
    getProviderEthers,
    switchNetwork,
    disconnect,
  }
}
