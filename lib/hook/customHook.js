import { useState, useEffect, useMemo } from 'react'

export const screen = {
  xs: 376,
  sm: 640,
  md: 768,
  md2: 910,
  lg: 1024,
  xl: 1366,
  xxl: 1920,
}

export const pageSizes = {
  pageSize10: 10,
  pageSize20: 20,
  pageSize25: 25,
  pageSize30: 30,
}

export function useScreenWidth () {
  const [windowWidth, setWindowWidth] = useState(null)
  const isWindow = typeof window !== 'undefined'

  const getWidth = () => isWindow ? window.innerWidth : windowWidth
  const resize = () => setWindowWidth(getWidth())

  useEffect(() => {
    if (isWindow) {
      setWindowWidth(getWidth())
      window.addEventListener('resize', resize)
      return () => window.removeEventListener('resize', resize)
    }
    //eslint-disable-next-line
    }, [isWindow]);

  return windowWidth
}

export function useClickOutside (ref) {
  const [clickOutside, setClickOutside] = useState(false)

  useEffect(() => {
    function handleClickOutside (e) {
      if (ref.current && !ref.current.contains(e.target)) {
        setClickOutside(true)
      } else {
        setClickOutside(false)
      }
    }

    document.addEventListener('click', handleClickOutside)
    return () => {
      document.removeEventListener('click', handleClickOutside)
    }
  }, [ref])
  return clickOutside
}

export function usePagination ({
  totalCount,
  pageSize,
  siblingCount = 1,
  currentPage,
  isShortened = false,
}) {
  const paginationRange = useMemo(() => {
    const totalPageCount = Math.ceil(totalCount / pageSize)
    const totalPageNumbers = siblingCount + (isShortened ? 3 : 5)

    if (totalPageNumbers >= totalPageCount) {
      return _range(1, totalPageCount)
    }

    const leftSiblingIndex = Math.max(currentPage - siblingCount, 1)
    const rightSiblingIndex = Math.min(
      currentPage + siblingCount,
      totalPageCount
    )

    const shouldShowLeftDots = leftSiblingIndex > 2
    const shouldShowRightDots = rightSiblingIndex < totalPageCount - 2

    const firstPageIndex = 1
    const lastPageIndex = totalPageCount

    if (!shouldShowLeftDots && shouldShowRightDots) {
      let leftItemCount = 3 + (isShortened ? 1 : 2) * siblingCount,
        leftRange = _range(1, leftItemCount)

      return [...leftRange, DOTS, totalPageCount]
    }

    if (shouldShowLeftDots && !shouldShowRightDots) {

      let rightItemCount = 3 + (isShortened ? 1 : 2) * siblingCount,
        rightRange = _range(
          totalPageCount - rightItemCount + 1,
          totalPageCount
        )
      return [firstPageIndex, DOTS, ...rightRange]
    }

    if (shouldShowLeftDots && shouldShowRightDots) {
      let middleRange = _range(leftSiblingIndex, rightSiblingIndex)
      return [firstPageIndex, DOTS, ...middleRange, DOTS, lastPageIndex]
    }

  }, [totalCount, pageSize, siblingCount, isShortened, currentPage])

  return paginationRange
}

function _range (start, end) {
  let length = end - start + 1
  return Array.from({ length }, (_, idx) => idx + start)
}

export const DOTS = '...'

export function useDebounce (value, delay) {
  const [debouncedValue, setDebouncedValue] = useState(value)

  useEffect(() => {
    // Update debounced value after delay
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  },[value, delay])

  return debouncedValue
}

export function useDelayUnmount (isMounted, delayTime) {
  const [shouldRender, setShouldRender] = useState(false)

  useEffect(() => {
    let timeoutId
    if (isMounted && !shouldRender) {
      setShouldRender(true)
    } else if (!isMounted && shouldRender) {
      timeoutId = setTimeout(() => setShouldRender(false), delayTime)
    }
    return () => clearTimeout(timeoutId)
  }, [isMounted, delayTime, shouldRender])
  return shouldRender
}
