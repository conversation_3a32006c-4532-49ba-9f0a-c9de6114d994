import { useCallback } from 'react'
import { useRouter } from 'next/router'
import { useAuthentication } from './auth_hook'
import eventBus from 'lib/eventBus'
const SINGULARITY_KEY = process.env.NEXT_PUBLIC_SINGULARITY_KEY
declare global {
interface Window {
    SingularityEvent: any
    Singularity: any
    emitter?: any
}
}

export default function useSingularity () {
  /**
   * HOOKS
   */
  const { getProvider } = useAuthentication()
  const router = useRouter()

  /**
   * FUNCTIONS
   */
  const connectWalletConnectProvider = useCallback(async () => {
    const provider = await getProvider()
    if (provider) {
      window.SingularityEvent?.loginWithProvider(provider)
    }
  }, [getProvider])

  const singularityLogout = useCallback(async () => {
    await window.SingularityEvent?.logout()
  }, [])

  const initSingularity = useCallback(() => {
    const handleSingularityMount = () => {
      let key: any
      if (router.query?.key) {
        key = router.query.key as string
      } else if (localStorage.getItem('singularity-key')) {
        key = localStorage.getItem('singularity-key')
      } else {
        key = SINGULARITY_KEY // default key
      }
      localStorage.setItem('singularity-key', key)

      window.Singularity?.init(SINGULARITY_KEY, async () => {
  

        console.log('🚀 Singularity init complete, emitting SingularityLoaded')
        eventBus?.emit('SingularityLoaded')
        console.log('AAAAAA')
        await connectWalletConnectProvider() // TODO Optional
        window.SingularityEvent?.subscribe('SingularityEvent-logout', () => {
          console.log('logout event received')
          window.SingularityEvent?.close()
        })

        window.SingularityEvent?.subscribe('SingularityEvent-open', () => {
          console.log('SingularityEvent-open')
        })

        window.SingularityEvent?.subscribe('SingularityEvent-close', () => {
          console.log('subscribe close drawer ')
        })

        window.SingularityEvent?.subscribe(
          'SingularityEvent-onTransactionApproval',
          (data: string) => {
            console.log('Txn approved', JSON.parse(data))
          }
        )

        window.SingularityEvent?.subscribe(
          'SingularityEvent-onTokenExpired',
          (data: string) => {
            console.log('Token expired', JSON.parse(data))
          }
        )

        window.SingularityEvent?.subscribe(
          'SingularityEvent-onTransactionSuccess',
          (data: string) => {
            console.log('Txn Successful', JSON.parse(data))
          }
        )

        window.SingularityEvent?.subscribe(
          'SingularityEvent-onTransactionFailure',
          (data: string) => {
            console.log('Txn failed', JSON.parse(data))
          }
        )

        window.SingularityEvent?.subscribe(
          'SingularityEvent-login',
          (data: any) => {
            console.log('login data --->', data)
          }
        )
      })
    }

    window.document.body.addEventListener(
      'Singularity-mounted',
      handleSingularityMount
    )

    // Cleanup function
    return () => {
      window.document.body.removeEventListener(
        'Singularity-mounted',
        handleSingularityMount
      )
    }
  }, [router.query, connectWalletConnectProvider])

  return {
    connectWalletConnectProvider,
    initSingularity,
    singularityLogout,
  }
}
