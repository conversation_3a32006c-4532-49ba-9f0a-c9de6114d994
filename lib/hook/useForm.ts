import { useState } from 'react'

export function useForm (initData: any) {
  const [formData, setFormData] = useState(initData)
  const setFormValue = (field: any, value: any, setAllValue?: boolean) => {
    if (value === undefined || value == null) return
    
    if (typeof formData[field] !== 'object') {
      setFormData((prevState: any) => ({
        ...prevState,
        [field]: value,
      }))
    } else {
      setFormData((prevState: any) => {
        let options
        if(setAllValue) {
          options = value
        }
        else if (value.length) {
          options = value.map((option: any) => option?.value || option)
        } else {
          options = value
        }
        return {
          ...prevState,
          [field]: options,
        }
      })
    }
  }

  return {
    formData,
    setFormValue,
    setFormData,
  }
}
