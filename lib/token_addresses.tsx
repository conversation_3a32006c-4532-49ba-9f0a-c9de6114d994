export const nativeTokenAddresses = '******************************************'

export const tokenAddresses: any = {
  ethereum: {
    ETH: nativeTokenAddresses,
    WETH: '******************************************',
    DAI: '******************************************',
    USDC: '******************************************',
    APE: '******************************************',
    ASH: '',
    BAT: '******************************************',
    CUBE: '******************************************',
    GALA: '******************************************',
    LINK: '******************************************',
    MANA: '******************************************',
    NCT: '******************************************',
    REVV: '******************************************',
    SAND: '******************************************',
    UNI: '******************************************',
    VOLT: '',
  },
  polygon: {
    MATIC: nativeTokenAddresses,
    WETH: '******************************************',
    DAI: '******************************************',
    USDC: '******************************************',
    REVV: '******************************************',
    SAND: '******************************************',
  },
  arbitrum: {
    WETH: '******************************************',
  },
  avalanche: {
    AVAX: nativeTokenAddresses,
  },
  binanceSmartChain: {
    BNB: nativeTokenAddresses,
  },
  klaytn: {
    KLAY: nativeTokenAddresses,
  },
  optimisn: {
    OP: nativeTokenAddresses,
    WETH: '******************************************',
  },
  solana: {
    SOL: nativeTokenAddresses,
  },
}
