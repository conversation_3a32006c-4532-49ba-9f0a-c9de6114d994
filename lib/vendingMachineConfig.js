import instantMintCheckoutAbi from './vendingMachine/InstantMintCheckout.json'

export const PACKAGES_HOME_LIMIT = 5
export const PACKAGES_BY_CATEGORY_LIMIT = 25

export const VENDING_MACHINE_CONFIG = {
  80002: {
    chainId: 80002,
    chainName: 'polygonAmoy',
    instantMintCheckoutAddress: '******************************************',
    instantMintCheckoutAbi: instantMintCheckoutAbi,
    paymentTokenAddress: '******************************************',
    paymentTokenDecimals: 18,
    rpc: 'https://api.zan.top/polygon-amoy',
  },
  11155111: {
    chainId: 11155111,
    chainName: 'SepoliaEthereum',
    instantMintCheckoutAddress: '******************************************',
    instantMintCheckoutAbi: instantMintCheckoutAbi,
    paymentTokenAddress: '******************************************',
    paymentTokenDecimals: 6,
    rpc: 'https://ethereum-sepolia-rpc.publicnode.com',
  },
  137: {
    chainId: 137,
    chainName: 'polygon',
    instantMintCheckoutAddress: '******************************************',
    instantMintCheckoutAbi: instantMintCheckoutAbi,
    paymentTokenAddress: '******************************************',
    paymentTokenDecimals: 6,
    rpc: 'https://polygon-rpc.com',
  },
}
export const VENDING_MACHINE_CONFIG_DEFAULT = VENDING_MACHINE_CONFIG[80002]

export const CHECKOUT_STATUS ={
  PENDING : 'PENDING',
  MINTING : 'MINTING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
}