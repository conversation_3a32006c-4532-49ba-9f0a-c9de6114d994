const { ethers } = require('ethers')
const axios = require('axios') 
import { getRpcUrl } from './utils'
import { Contract, providers } from 'ethers'

const COMMON_ABI = [
  'function name() view returns (string)',
  'function symbol() view returns (string)',
  'function owner() view returns (address)',
  'function contractURI() view returns (string)',
]

const ERC721_ABI = [
  ...COMMON_ABI,
  'function tokenURI(uint256 tokenId) view returns (string)',
  'function ownerOf(uint256 tokenId) view returns (address)',
]

const ERC1155_ABI = [
  ...COMMON_ABI,
  'function uri(uint256 tokenId) view returns (string)',
  'function balanceOf(address account, uint256 tokenId) view returns (uint256)',
]

const IERC165_ABI = [
  'function supportsInterface(bytes4 interfaceId) view returns (bool)',
]

const ERC721_INTERFACE_ID = '0x80ac58cd'
const ERC1155_INTERFACE_ID = '0xd9b67a26'


const getCommonInfo = async (contract, type) => {
  try {
    // First verify the contract exists
    const code = await contract.provider.getCode(contract.address)
    if (code === '0x') {
      throw new Error('Contract does not exist at this address')
    }

    let name, symbol, owner = ''
    try {
      name = await contract.name()
    } catch (error) {
      console.warn('Contract does not implement name() function')
    }

    try {
      symbol = await contract.symbol()
    } catch (error) {
      console.warn('Contract does not implement symbol() function')
    }

    try {
      owner = await contract.owner()
    } catch (error) {
      console.warn('Contract does not implement owner() function')
    }

    // if (!name && !symbol) {
    //   throw new Error('Contract does not implement required ERC721/ERC1155 interface')
    // }

    return {
      type,
      name: name || '',
      symbol: symbol || '',
      owner: owner || null,
    }
  } catch (error) {
    console.error('Error fetching common info:', error.message)
    throw error
  }
}


const getERC721Info = async (provider, contractAddress, tokenId = null) => {
  console.log('🚀 ~ getERC721Info ~ provider:', provider)
  try {
    const contract = new ethers.Contract(contractAddress, ERC721_ABI, provider)

    const commonInfo = await getCommonInfo(contract, 'ERC721')

    if (tokenId) {
      const tokenURI = await contract.tokenURI(tokenId)
      const owner = await contract.ownerOf(tokenId)

      let metadata = null
      if (tokenURI.startsWith('http')) {
        const response = await axios.get(tokenURI)
        metadata = response.data
      }

      return {
        ...commonInfo,
        tokenId,
        tokenURI,
        owner,
        metadata,
      }
    }

    return commonInfo
  } catch (error) {
    console.error('Error fetching ERC-721 info:', error.message)
    throw error
  }
}


const getERC1155Info = async (provider, contractAddress, tokenId = null, userAddress = null) => {
  try {
    const contract = new ethers.Contract(contractAddress, ERC1155_ABI, provider)

    const commonInfo = await getCommonInfo(contract, 'ERC1155')

    if (tokenId) {
      const uri = await contract.uri(tokenId)

      let metadata = null
      if (uri.startsWith('http')) {
        const tokenIdHex = tokenId.toString(16).padStart(64, '0') // Convert tokenId to hex
        const metadataURL = uri.replace('{id}', tokenIdHex)
        const response = await axios.get(metadataURL)
        metadata = response.data
      }

      const balance = userAddress
        ? await contract.balanceOf(userAddress, tokenId)
        : null

      return {
        ...commonInfo,
        tokenId,
        uri,
        metadata,
        balance,
      }
    }

    return commonInfo
  } catch (error) {
    console.error('Error fetching ERC-1155 info:', error.message)
    throw error
  }
}


const getTokenInfo = async ({
  provider,
  contractAddress,
  type,
  tokenId = null,
  userAddress = null,
}) => {
  console.log('🚀 ~ type:', type)
  console.log('🚀 ~ provider:', provider)
  try {
    // Check interface support first
    const erc165Contract = new ethers.Contract(contractAddress, IERC165_ABI, provider)
    
    try {
      if (type === 'ERC721') {
        const isERC721 = await erc165Contract.supportsInterface(ERC721_INTERFACE_ID)
        if (!isERC721) {
          throw new Error('Contract does not implement ERC721 interface')
        }
      } else if (type === 'ERC1155') {
        const isERC1155 = await erc165Contract.supportsInterface(ERC1155_INTERFACE_ID)
        if (!isERC1155) {
          throw new Error('Contract does not implement ERC1155 interface')
        }
      }
    } catch (error) {
      console.error('Error checking interface:', error.message)
      // Continue anyway as some contracts might not implement ERC165 correctly
      throw error
    }
  
    if (type === 'ERC721') {
      return await getERC721Info(provider, contractAddress, tokenId)
    } else if (type === 'ERC1155') {
      return await getERC1155Info(provider, contractAddress, tokenId, userAddress)
    } else {
      throw new Error('Invalid contract type. Use "ERC721" or "ERC1155".')
    }
  } catch (error) {
    console.error('Error in getTokenInfo:', error.message)
    throw error
  }
}

const getOwnerFromTxCreator = async (txHash, provider) => {
  try {
    if (!txHash || !provider) {
      throw new Error('Transaction hash and provider are required')
    }

    // Get transaction details
    const tx = await provider.getTransaction(txHash)
    if (!tx) {
      return null
    }

    // Get transaction receipt to ensure it was successful
    const receipt = await provider.getTransactionReceipt(txHash)
    if (!receipt) {
      return null
    }

    if (receipt.status === 0) {
      return null
    }

    // The from address is the creator
    const creator = tx.from
    console.log('🚀 ~ getOwnerFromTxCreator ~ creator:', creator)
    return creator
  } catch (error) {
    console.error('Error in getOwnerFromTxCreator:', error.message)
    throw error
  }
}

async function getERC721ContractInfo (contract) {
  console.log('Getting ERC721 contract info...')
  try {
    const [tokenURI = '', name = '', symbol = '', owner = '', contractURI = ''] = await Promise.all([
      contract.tokenURI(1).catch((e) => {
        console.error('Error getting tokenURI:', e.message)
        return ''
      }),
      contract.name().catch((e) => {
        console.error('Error getting name:', e.message)
        return 'Unknown Collection'
      }),
      contract.symbol().catch((e) => {
        console.error('Error getting symbol:', e.message)
        return ''
      }),
      contract.owner().catch((e) => {
        console.error('Error getting owner:', e.message)
        return ''
      }),
      contract.contractURI().catch((e) => {
        console.error('Error getting contractURI:', e.message)
        return ''
      }),
    ])

    console.log('ERC721 contract basic info:', { name, symbol, owner })
    return processContractInfo(tokenURI, name, symbol, owner, false, contractURI)
  } catch (error) {
    console.error('Error in getERC721ContractInfo:', error)
    throw error
  }
}

async function getERC1155ContractInfo (contract) {
  console.log('Getting ERC1155 contract info...')
  try {
    const [tokenURI = '', name = '', symbol = '', owner = '', contractURI = ''] = await Promise.all([
      contract.uri(1).catch((e) => {
        console.error('Error getting tokenURI:', e.message)
        return ''
      }),
      contract.name().catch((e) => {
        console.error('Error getting name:', e.message)
        return 'Unknown Collection'
      }),
      contract.symbol().catch((e) => {
        console.error('Error getting symbol:', e.message)
        return ''
      }),
      contract.owner().catch((e) => {
        console.error('Error getting owner:', e.message)
        return ''
      }),
      contract.contractURI().catch((e) => {
        console.error('Error getting contractURI:', e.message)
        return ''
      }),
    ])

    console.log('ERC1155 contract basic info:', { name, symbol, owner })
    return processContractInfo(tokenURI, name, symbol, owner, true, contractURI)
  } catch (error) {
    console.error('Error in getERC1155ContractInfo:', error)
    throw error
  }
}
async function checkImage (url) {
  try {
    const response = await fetch(url, { method: 'HEAD' })
    if (!response.ok) {
      console.warn(`⚠️ Image error (${response.status}):`, url)
      return false
    }
    return true
  } catch (error) {
    console.error('⚠️ Image error:', error.message)
    return false
  }
}

async function processContractInfo (tokenURI, name, symbol, owner, isERC1155 = false, contractURI = '') {
  let metadata = null
  
  // First try to get metadata from contractURI
  if (contractURI) {
    try {
      let uri = contractURI
      if (uri.startsWith('ipfs://')) {
        uri = `https://ipfs.io/ipfs/${uri.slice(7)}`
      }
      
      console.log('Fetching metadata from contractURI:', uri)
      const response = await axios.get(uri, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        timeout: 10000,
      })
      metadata = response.data
      console.log('Contract metadata received:', metadata)
      // check metadata.image can fetch
      let imageUrl = metadata?.image || metadata?.image_url
      if(imageUrl.startsWith('ipfs://')) {
        imageUrl = `https://ipfs.io/ipfs/${imageUrl.slice(7)}`
      }
      if (imageUrl) {
        const isValid = await checkImage(imageUrl)
        console.log('Image contractURI is valid:', isValid)
        if (!isValid) {
          console.warn('🚨 Image contractURI error 403/404')
          imageUrl = null
        }
      }
      return {
        name: metadata.name || name,
        symbol: metadata.symbol || symbol,
        owner,
        banner: metadata.banner_image_url || metadata.banner || metadata.background_image || imageUrl, // add logic from client
        icon: imageUrl,
        description: metadata.description || null,
        externalLink: metadata.external_link || null,
        sellerFeeBasisPoints: metadata.seller_fee_basis_points || null,
        feeRecipient: metadata.fee_recipient || null,
      }
    } catch (error) {
      console.error('Error fetching contractURI metadata:', error.message)
      // Continue to try tokenURI if contractURI fails
    }
  }

  // Fallback to tokenURI if contractURI failed or doesn't exist
  if (!metadata && tokenURI) {
    try {
      let uri = tokenURI
      if (uri.startsWith('ipfs://')) {
        uri = `https://ipfs.io/ipfs/${uri.slice(7)}`
      }
      if (isERC1155) {
        uri = uri.replace('{id}', '1')
      }

      console.log('Fetching metadata from tokenURI:', uri)
      const response = await axios.get(uri, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        timeout: 10000,
      })
      metadata = response.data
      console.log('Token metadata received:', metadata)
      // check metadata.image can fetch
      let imageUrl = metadata?.image || metadata?.image_url
      if(imageUrl.startsWith('ipfs://')) {
        imageUrl = `https://ipfs.io/ipfs/${imageUrl.slice(7)}`
      }
      if (imageUrl) {
        const isValid = await checkImage(imageUrl)
        console.log('Image tokenURI is valid:', isValid)
        if (!isValid) {
          console.warn('🚨 Image tokenURI  error 403/404')
          metadata.image = null
          // eslint-disable-next-line camelcase
          metadata.image_url = null
        }
      }
    } catch (error) {
      console.error('Error fetching tokenURI metadata:', error.message)
    }
  }

  // Helper function to convert IPFS URLs
  const convertIpfsUrl = (url) => {
    if (!url) return null
    if (url.startsWith('ipfs://')) {
      return `https://w3s.link/ipfs/${url.slice(7)}`
    }
    return url
  }
  let banner = metadata?.banner_image_url || metadata?.banner || metadata?.background_image
  if (banner?.startsWith('ipfs://')) {
    banner = `https://w3s.link/ipfs/${banner.slice(7)}`
  }
  let icon = metadata?.image || metadata?.image_url
  if (icon?.startsWith('ipfs://')) {
    icon = `https://w3s.link/ipfs/${icon.slice(7)}`
  }
  return {
    name,
    symbol,
    owner,
    banner: banner || icon,// add logic from client
    icon: icon,
    contractType: isERC1155 ? 'ERC1155' : 'ERC721',
    description: metadata?.description || null,
    externalLink: metadata?.external_link || null,
  }
}

export const getContractInfo = async (chainId, contractAddress) => {
  const defaultResponse = {
    name: '',
    symbol: '',
    owner: '',
    banner: null,
    icon: null,
    contractType: null,
    success: false,
  }

  try {
    console.log('Getting contract info for:', { chainId, contractAddress })
    const rpcUrl = getRpcUrl(chainId)
    if (!rpcUrl) {
      console.error('No RPC URL found for chainId:', chainId)
      return defaultResponse
    }
    console.log('Using RPC URL:', rpcUrl)

    const provider = new providers.JsonRpcProvider(rpcUrl)
    
    // First verify the contract exists
    const code = await provider.getCode(contractAddress)
    if (code === '0x') {
      console.error('No contract code found at address:', contractAddress)
      return defaultResponse
    }
    console.log('Contract code exists at address')

    let contract = new Contract(contractAddress, [...IERC165_ABI, ...COMMON_ABI, ...ERC721_ABI], provider),

      isERC721 = false,
      isERC1155 = false

    try {
      console.log('Checking ERC721 interface...')
      isERC721 = await contract.supportsInterface(ERC721_INTERFACE_ID)
      console.log('ERC721 interface check result:', isERC721)
    } catch (error) {
      console.error('Error checking ERC721 interface:', error.message)
      // Try direct method calls instead
      try {
        await contract.name()
        await contract.symbol()
        isERC721 = true
        console.log('Contract supports name() and symbol(), assuming ERC721')
      } catch (e) {
        console.error('Error calling name/symbol directly:', e.message)
      }
    }
    
    if (!isERC721) {
      console.log('Checking ERC1155 interface...')
      contract = new Contract(contractAddress, [...IERC165_ABI, ...COMMON_ABI, ...ERC1155_ABI], provider)
      try {
        isERC1155 = await contract.supportsInterface(ERC1155_INTERFACE_ID)
        console.log('ERC1155 interface check result:', isERC1155)
      } catch (error) {
        console.error('Error checking ERC1155 interface:', error.message)
        // Try direct method call
        try {
          await contract.uri(1)
          isERC1155 = true
          console.log('Contract supports uri(), assuming ERC1155')
        } catch (e) {
          console.error('Error calling uri directly:', e.message)
        }
      }
    }

    if (!isERC721 && !isERC1155) {
      console.log('Contract does not implement either ERC721 or ERC1155 interface')
      return defaultResponse
    }

    // Get contract info based on type
    console.log('Getting contract info for type:', isERC721 ? 'ERC721' : 'ERC1155')
    const contractInfo = await (isERC721 ? getERC721ContractInfo(contract) : getERC1155ContractInfo(contract))
    
    const result = {
      ...contractInfo,
      contractType: isERC721 ? 'ERC721' : 'ERC1155',
      success: true,
    }
    console.log('Contract info result:', result)
    return result

  } catch (error) {
    console.error('Error in getContractInfo:', error)
    return defaultResponse
  }
  
}

const tokenService = {
  getTokenInfo,
  getOwnerFromTxCreator,
  getContractInfo,
}
export default tokenService
