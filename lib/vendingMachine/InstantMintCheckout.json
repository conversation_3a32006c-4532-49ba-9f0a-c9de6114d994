[{"inputs": [{"internalType": "address", "name": "paymentReceiver_", "type": "address"}, {"internalType": "address", "name": "checkoutOracle_", "type": "address"}, {"internalType": "uint256", "name": "_subscriptionId", "type": "uint256"}, {"internalType": "address", "name": "_vrfCoordinator", "type": "address"}, {"internalType": "bytes32", "name": "_keyHash", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "have", "type": "address"}, {"internalType": "address", "name": "want", "type": "address"}], "name": "OnlyCoordinatorCanFulfill", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": true, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "CheckoutOracleUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "caller", "type": "address"}, {"indexed": false, "internalType": "string", "name": "checkoutId", "type": "string"}], "name": "CheckoutSuccess", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "PaymentProcessed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": true, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "PaymentReceiverUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "requestId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "rng", "type": "uint256"}], "name": "RandomNumberReceived", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "requestId", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "checkoutId", "type": "string"}], "name": "RandomNumberRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "registry", "type": "address"}, {"indexed": true, "internalType": "address", "name": "receiver", "type": "address"}, {"indexed": false, "internalType": "bytes32", "name": "proofOfIntegrity", "type": "bytes32"}, {"indexed": false, "internalType": "string", "name": "checkoutId", "type": "string"}], "name": "TokenMinted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "COORDINATOR", "outputs": [{"internalType": "contract IVRFCoordinatorV2Plus", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "checkoutOracle", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "checkoutId", "type": "string"}, {"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "address", "name": "paymentToken", "type": "address"}, {"internalType": "uint256", "name": "paymentValue", "type": "uint256"}, {"internalType": "uint256", "name": "expirationTimestamp", "type": "uint256"}, {"internalType": "bytes", "name": "postCheckoutCallData", "type": "bytes"}], "name": "encodeErc20CheckoutRequest", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "checkoutId", "type": "string"}, {"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint256", "name": "paymentValue", "type": "uint256"}, {"internalType": "uint256", "name": "expirationTimestamp", "type": "uint256"}, {"internalType": "bytes", "name": "postCheckoutCallData", "type": "bytes"}], "name": "encodeNativeCheckoutRequest", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "tokenProofOfIntegrity", "type": "bytes32"}, {"internalType": "address", "name": "tokenRegistry<PERSON>ddress", "type": "address"}, {"internalType": "address", "name": "tokenReceiver", "type": "address"}], "name": "encodePostCheckoutCallData", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "requestData", "type": "bytes"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "name": "erc20Checkout", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "checkoutId", "type": "string"}], "name": "exists", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "checkoutId", "type": "string"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "mintToken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "requestData", "type": "bytes"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "name": "nativeCheckout", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "paymentReceiver", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}, {"internalType": "uint256[]", "name": "randomWords", "type": "uint256[]"}], "name": "rawFulfillRandomWords", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "s_requestIds", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newOracle", "type": "address"}], "name": "updateCheckoutOracle", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newReceiver", "type": "address"}], "name": "updatePaymentReceiver", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]