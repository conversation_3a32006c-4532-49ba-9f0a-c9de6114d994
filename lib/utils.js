import Chain from 'domain/value_objects/chain'
export const isEmpty = (input) => {
  if (typeof input === 'undefined' || input === null) {
    return false
  } else if (typeof input === 'string' || input instanceof String) {
    return !input.trim()
  } else if (typeof input === 'number') {
    return false
  } else if (Array.isArray(input) || input instanceof Array) {
    return input.length === 0
  } else if (
    typeof input === 'object' ||
    Object.keys(input).length >= 0 ||
    input instanceof Object
  ) {
    return !Object.keys(input).length
  }
  throw 'Unexpected type of input'
}

// Capitalize first string or first string of every word
export const capitalize = (str, firstChar = true) => {
  let string = str.trim()
  if (firstChar) return string[0].toUpperCase() + string.substring(1)

  let arr = string.split(' ').map((word) => {
    return word[0].toUpperCase() + word.substring(1)
  })
  return arr.join(' ')
}

export const generateData = (total, object, options) => {
  const result = []
  for (let i = 0; i < total; i++) {
    result.push({
      id: i,
      ...object,
      ...options,
    })
  }
  return result
}

export const removeArrEl = (element, arr) => {
  return arr?.filter((item) => {
    return item !== element
  })
}

export const padTo2Digits = (num) => {
  return num.toString().padStart(2, '0')
}

export const formatDate = (date, isReverse = false) => {
  if (date) {
    return [
      isReverse ? date.getFullYear() : padTo2Digits(date.getDate()),
      padTo2Digits(date.getMonth() + 1),
      isReverse ? padTo2Digits(date.getDate()) : date.getFullYear(),
    ].join('-')
  } else {
    return ''
  }
}

const monthNames = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
]
export const getFullMonth = (month) => {
  return monthNames[month]
}

const monthShortenedNames = [
  'Jan',
  'Feb',
  'Mar',
  'Apr',
  'May',
  'Jun',
  'Jul',
  'Aug',
  'Sep',
  'Oct',
  'Nov',
  'Dec',
]
export const getShortenedMonth = (month) => {
  return monthShortenedNames[month]
}

export const shortenAddress = (address, prefixLetters = 6) => {
  if (address) {
    const length = address.length
    return (
      address.substring(0, prefixLetters) +
      '...' +
      address.substring(length - 4)
    )
  } else {
    return ''
  }
}

export const validateDate = (date) => {
  // TODO check FEB
  const regex = /^([0-2][0-9]|(3)[0-1])(\/)(((0)[0-9])|((1)[0-2]))(\/)\d{4}$/i
  return regex.test(date)
}

export const getContractAddress = (chainId) => {
  switch (chainId) {
    case 1:
      return {
        domainRegistory: '',
        marketPlaceAddress: '',
      }
    //sepolia
    case 11155111:
      return {
        domainRegistory: process.env.ETHEREUM_SEPOLIA_DOMAIN_REGISTRY_ADDRESS,
        marketPlaceAddress: process.env.ETHEREUM_SEPOLIA_MARKETPLACE_ADDRESS,
      }
    //polygon amoy
    case 80002:
      return {
        domainRegistory: process.env.POLYGON_AMOY_DOMAIN_REGISTRY_ADDRESS,
        marketPlaceAddress: process.env.POLYGON_AMOY_MARKETPLACE_ADDRESS,
      }
  }
}

export const getRpcUrl = (chainId) => {
  const rpcUrls = {
    //mainnet chains
    1: 'https://1rpc.io/eth',
    59144: 'https://polygon-pokt.nodies.app',
    137: 'https://polygon-rpc.com',
    8453: 'https://base-rpc.publicnode.com',
    11297108109: 'https://palm-mainnet.public.blastapi.io',
    42220: 'https://rpc.ankr.com/celo',
    29548: 'https://rpc.oasys.mycryptoheroes.net',
    812397: 'https://rpc.sgverse.net',
    19011: 'https://rpc.mainnet.oasys.homeverse.games',
    248: 'https://rpc.mainnet.oasys.games',
    56: 'https://binance.llamarpc.com',
    42161: 'https://arb1.arbitrum.io/rpc',
    43114: 'https://api.avax.network/ext/bc/C/rpc',
    10: 'https://mainnet.optimism.io',
    1088: 'https://chainlist.org/chain/1088',
    1101: 'https://zkevm-rpc.com',
    2400: 'https://rpc.tcgverse.xyz',
    999: 'https://rpc-proxy.hyper7rush.online/main/evm/999',
    //testnet chains
    11155111: 'https://ethereum-sepolia-rpc.publicnode.com',
    59141: 'https://rpc.sepolia.linea.build',
    80002: 'https://api.zan.top/polygon-amoy',
    84532: 'https://sepolia.base.org',
    11297108099: 'https://palm-testnet.public.blastapi.io',
    44787: 'https://alfajores-forno.celo-testnet.org',
    40875: 'https://rpc.testnet.oasys.homeverse.games',
    17000: 'https://ethereum-holesky-rpc.publicnode.com',
    420: 'https://rpc.oasys.sand.mchdfgh.xyz', 
    247101234: 'https://sgv-testnet.alt.technology',
    12401: 'https://rpc.testnet.tcgverse.xyz',
    10143: 'https://testnet-rpc.monad.xyz',
    97: 'https://bsc-testnet-rpc.publicnode.com',
    9372: 'https://rpc.testnet.oasys.games',
    43113: 'https://avalanche-fuji-c-chain-rpc.publicnode.com',
    421614: 'https://sepolia-rollup.arbitrum.io/rpc',
    11155420: 'https://sepolia.optimism.io',
    2442: 'https://rpc.cardona.zkevm-rpc.com',
    59902: 'https://sepolia.metisdevops.link',
    998: 'https://rpc.hyperliquid-testnet.xyz/evm',
  }
  return rpcUrls[chainId]
}
export const getChainName = (chainId) => {
  if(chainId === 11155111) {
    return 'ethereum-sepolia' // just for link to old collection not support any more
  }
  const chain = Chain.getResourceArray()
  const findChain = chain.find((i) => i.chainId === chainId)
  return findChain?.name || 'unknown'
}
export const getChainId = (chainName) =>{
  if(chainName === 'ethereum-sepolia') return 11155111  // just for link to old collection not support any more
  const chain = Chain.getResourceArray()
  const findChain = chain.find((i) => i.name === chainName)
  return findChain?.chainId || 'unknown'
}

export const uniqByKeepLast = (a, _key) => {
  const key = (it) => it[_key]
  return [
    ...new Map(
      a.map((x) => [key(x), x])
    ).values(),
  ]
}

export const uniqByKeepFirst = (a, _key) => {
  let seen = new Set()
  const key = (it) => it[_key]
  return a.filter((item) => {
    let k = key(item)
    return seen.has(k) ? false : seen.add(k)
  })
}

export const truncateString = (str, maxLength) => {
  return str.length > maxLength
    ? str.slice(0, maxLength) + '...'
    : str
}