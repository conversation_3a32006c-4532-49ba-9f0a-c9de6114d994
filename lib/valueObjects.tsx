export const paymentTokenImages:any = {
  eth: '/asset/icons/ETH.svg',
  weth: '/asset/icons/weth.png',
  dai: '/asset/icons/dai.png',
  usdc: '/asset/icons/usdc.png',
  usdt: '/asset/icons/usdt.png',
  oas: '/asset/icons/oas.png',
  woas: '/asset/icons/oas.png',
  mch: '/asset/icons/mch.png',
  pol: '/asset/icons/pol.png',
  mchc: '/asset/icons/mchc.png',
  nlt: '/asset/icons/nlt.png',
  mon: '/asset/icons/mon.png',
  bnb: '/asset/icons/bnb.png',
  avax: '/asset/icons/avax.png',
  smetis: '/asset/icons/smetis.png',
  metis: '/asset/icons/metis.png',
  hype: '/asset/icons/hype.png',
}

export const auctionMethodIcons:any = {
  sellToHighestBidder: 'arrowUpRight',
  sellWithDecliningPrice: 'arrowDownLeft',
}

export const rowOptions = [
  {
    name: '20 rows',
    value: '20',
  },
  {
    name: '30 rows',
    value: '30',
  },
  {
    name: '40 rows',
    value: '40',
  },
  {
    name: '50 rows',
    value: '50',
  },
  {
    name: '60 rows',
    value: '60',
  },
]

export const categoriesIcons:any = {
  all: '/asset/icons/app.png',
  limit: '/asset/icons/handLimit.png',
  digitalArt: '/asset/icons/digitalArt.png',
  music: '/asset/icons/music.png',
  gaming: '/asset/icons/gaming.png',
  virtualWorlds: '/asset/icons/virtualWorld.png',
  tradingCards: '/asset/icons/tradingCard.png',
  photo: '/asset/icons/photo.png',
  utility: '/asset/icons/utility.png',
  sports: '/asset/icons/sport.png',
  domainNames: '/asset/icons/domainNames.png',
  collect: '/asset/icons/collect.png',
  ai: '/asset/icons/ai.png',
  defi: '/asset/icons/defi.png',
}
