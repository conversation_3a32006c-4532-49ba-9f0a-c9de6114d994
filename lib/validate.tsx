import Web3 from 'web3'

export const isURL = (value: string): boolean => {
  try {
    if (!value) {
      return false
    }
    const res = value.match(
      /(http(s)?:\/\/)?(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%._+~#?&//=]*)/g,
    )
    return res !== null
  } catch (error) {
    return false
  }
}

export const isUrlXUser = (value: string): boolean => {
  try {
    if (!value) {
      return false
    }
    const res = value.match(/((https?):\/\/)?(www\.)?x\.com\/(?![a-zA-Z0-9_]+\/)([a-zA-Z0-9_]+)/g)
    return res !== null
  } catch (error) {
    return false
  }
}

export const isUrlDiscord = (value: string): boolean => {
  try {
    if (!value) {
      return false
    }
    const res = value.match(
      /(https:\/\/)?(www\.)?(((discord(app)?)?\.com\/invite)|((discord(app)?)?\.gg))\/(?<invite>.+)/gm,
    )
    return res !== null
  } catch (error) {
    return false
  }
}
export const isUrlInstagram = (value: string): boolean => {
  try {
    if (!value) {
      return false
    }
    const res = value.match(
      /(?<=instagram.com\/)[A-Za-z0-9_.]+/g,
    )
    return res !== null
  } catch (error) {
    return false
  }
}

export const isUrlMedium = (value: string): boolean => {
  try {
    if (!value) {
      return false
    }
    const res = value.match(
      /(?<=medium.com\/@)[A-Za-z0-9_.]+/g,
    )
    return res !== null
  } catch (error) {
    return false
  }
}
export const isUrlTelegram = (value: string): boolean => {
  try {
    if (!value) {
      return false
    }
    const res = value.match(
      /^(https:\/\/t\.me\/|tg:\/\/resolve\?domain=)([a-zA-Z0-9_]{5,32})$/,
    )
    return res !== null
  } catch (error) {
    return false
  }
}

export const isUsername = (value: string): boolean => {
  try {
    if (!value) {
      return false
    }
    const res = value.match(/^[a-zA-Z0-9_-]*$/)
    return res !== null
  } catch (error) {
    return false
  }
}

export const isAddress = (value: string): boolean => {
  try {
    if (!value) {
      return false
    }
    return Web3.utils.isAddress(value)
  } catch (error) {
    return false
  }
}

export const isNumber = (value: string): boolean => {
  try {
    if (!value) {
      return false
    }
    const res = value.match(/^-?\d+(\.\d+)?$/)
    return res !== null
  } catch (error) {
    return false
  }
}

type PropsPrice = {
  value: string,
  fieldName?: string,
  min: number|null,
  maxDecimals: number,
  max?:number|null
  greaterNumber?:number|null
};
export const validatePrice = ({
  value = '', 
  fieldName = 'amount', 
  min = null, 
  max = null, 
  maxDecimals = 4,
  greaterNumber = null,
}: PropsPrice): any => {
  
  if(!isNumber(value)) {
    return {
      code: 'INVALID_AMOUNT',
      message: 'Please enter a valid amount',
    }
  }
  
  if(greaterNumber !== null && Number(value) <= greaterNumber) {
    return {
      code: 'INVALID_MIN_AMOUNT',
      message: `The ${fieldName} must be greater than ${greaterNumber}`,
    }
  }
  
  if(min !== null && Number(value) < min) {
    return {
      code: 'INVALID_MIN_AMOUNT',
      message: `The ${fieldName} must be ${min} or more`,
    }
  } 

  if(max !== null && Number(value) > Number(max)) {
    return {
      code: 'INVALID_MAX_AMOUNT',
      message: `The ${fieldName} field must be ${max} or less`,
    }
  }

  const arrPrice = `${Number(value)}`.split('.')
  if(arrPrice?.length > 1 && arrPrice[1].length > maxDecimals) {
    return {
      code: 'INVALID_MAX_DECIMALS',
      message: maxDecimals == 0 ? `The ${fieldName} field must be an integer` :  `The ${fieldName} cannot have precision greater than ${maxDecimals} decimal places`,
    }
  }
  return null
}