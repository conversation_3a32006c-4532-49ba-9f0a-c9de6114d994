table {
  thead {
    &:not(.sticky-header) {
      tr th {
        @apply pt-3.5 py-3 px-[11px];
        &.sortable-col {
          button svg {
            @apply transition;
          }
          &.active {
            &:not(.show-full-icons) {
              @apply border-b-[3px] border-primary-2 w-fit;
              button svg {
                @apply fill-primary-2;
              }
              button.arrow-up svg {
                @apply rotate-180;
              }
              button.arrow-down svg {
                @apply rotate-0;
              }
            }
          }
        }
      }
    }
    &.sticky-header {
      > tr {
        > th {
          padding: 0;
          &.sortable-col {
            button svg {
              @apply transition;
            }
            &.active {
              &:not(.show-full-icons) {
                @apply w-fit;
                button svg {
                  @apply fill-primary-2;
                }
                button.arrow-up svg {
                  @apply rotate-180;
                }
                button.arrow-down svg {
                  @apply rotate-0;
                }
                > div {
                  @apply border-b-[3px] border-primary-2;
                }
              }
            }
          }
        }
      }
    }
  }
  tbody td {
    .text-red {
      @apply text-red-1;
    }
    .text-green {
      @apply text-green-1;
    }
  }
}

.ranking-table {
  @apply min-w-[800px];
  thead {
    > tr {
      > th {
        @apply dark:text-white;
        &:first-child {
          padding-left: 1.7rem;
        }
      }
    }
  }
  tbody {
    tr {
      td {
        @apply py-3 dark:text-white;
        &:first-child {
          padding-left: 1.7rem;
        }
      }
      &:first-child {
        border-top: 1px solid #d0d3e5;
        td {
          @apply pt-4 pb-3;
        }
      }
      border: none;
    }
  }
}

.watchlist-table {
  @apply min-w-[800px];
  thead {
    tr {
      th {
        @apply text-dark-primary dark:text-white;
        &:first-child {
          padding-left: 1.7rem;
        }
      }
    }
  }
  tbody {
    tr {
      td {
        @apply py-3 text-dark-primary dark:text-white;
        &:first-child,
        &:nth-child(2) {
          padding-left: 1.7rem;
        }
        &:last-child {
          padding-right: 1rem;
        }
      }
      &:first-child {
        border-top: 1px solid #d0d3e5;
        td {
          @apply pt-4 pb-3;
        }
      }
      border: none;
    }
  }
}

.setting-offer-table {
  @apply text-dark-primary dark:text-white xl:min-w-[765px] min-w-[700px];
  tbody tr {
    td {
      @apply py-4;
      .input-price {
        @apply w-28 pr-4 text-dark-blue;
      }
      .usd-price {
        @apply ml-2 font-medium text-dark-blue;
      }
    }
    td:last-child {
      @apply text-right;
    }
  }
}

.common-table {
  thead {
    tr {
      th {
        &:first-child {
          padding-left: 1rem;
          padding-right: 1rem;
        }
      }
    }
  }
  tbody {
    tr {
      border: none;
      &:first-child {
        border-top: 1px solid #d0d3e5;
        td {
          padding-top: 15px;
        }
      }
      &:last-child {
        td {
          padding-bottom: 15px;
        }
      }
      td {
        padding: 10px 11px;
      }
    }
  }
  &.collection {
    .table-header-group {
      .table-row {
        th:nth-child(3),
        th:nth-child(4),
        th:nth-child(5),
        th:nth-child(6),
        th:nth-child(7),
        th:nth-child(8),
        th:nth-child(9),
        th:nth-child(10),
        th:nth-child(11),
        th:nth-child(12),
        th:nth-child(13) {
          > div {
            @apply flex justify-end;
          }
        }
        th:nth-child(14) {
          > div {
            @apply flex justify-end pr-3;
          }
        }
      }
    }
    .percentage-bar {
      @apply mx-0;
    }
  }
  &.nft {
    .table-header-group {
      .table-row {
        th:nth-child(5) {
          > div {
            @apply flex justify-end pr-4;
          }
        }
        th:nth-child(6),
        th:nth-child(7),
        th:nth-child(8) {
          > div {
            @apply flex justify-end pr-4;
          }
        }
      }
    }
  }
  &.mint {
    .table-header-group {
      .table-row {
        th:nth-child(3),
        th:nth-child(4),
        th:nth-child(5),
        th:nth-child(6),
        th:nth-child(7),
        th:nth-child(8),
        th:nth-child(9) {
          > div {
            @apply flex justify-end;
          }
        }
        th:nth-child(9) {
          @apply pr-12;
        }
      }
    }
    .percentage-bar {
      @apply mx-0;
    }
    tbody {
      tr {
        td:nth-child(1) {
          @apply pl-4;
        }
        td:nth-child(2),
        td:nth-child(3),
        td:nth-child(4),
        td:nth-child(5),
        td:nth-child(6),
        td:nth-child(7),
        td:nth-child(8) {
          > div {
            padding-right: 17px !important;
          }
        }
        td:nth-child(9) {
          @apply pr-12;
          > div {
            padding-right: 19px !important;
          }
        }
      }
    }
  }
  &.leaderboard {
    .table-header-group {
      .table-row {
        th:nth-child(3),
        th:nth-child(4),
        th:nth-child(5),
        th:nth-child(6),
        th:nth-child(7),
        th:nth-child(8) {
          > div {
            @apply flex justify-end;
          }
        }
      }
    }
    .percentage-bar {
      @apply mx-0;
    }
  }
  &.top-buyer {
    @apply text-dark-primary dark:text-white;
    .table-header-group {
      .table-row {
        th:nth-child(3),
        th:nth-child(4) {
          > div {
            @apply flex justify-end;
          }
        }
        th:nth-child(5) {
          > div {
            @apply flex justify-end pr-3;
          }
        }
      }
    }
    .percentage-bar {
      @apply mx-0;
    }
    tbody {
      tr {
        &:first-child {
          td {
            @apply pt-4;
          }
        }
        &:last-child {
          td {
            @apply pb-4;
          }
        }
        td {
          &:first-child {
            @apply pl-4;
          }
          &:nth-child(2) {
            @apply pl-2;
          }
          &:nth-child(3) {
            @apply pr-3.5;
          }
          &:nth-child(4) {
            @apply pr-3;
          }
        }
      }
    }
  }
  &.top-seller {
    @apply text-dark-primary dark:text-white;
    .table-header-group {
      .table-row {
        th:nth-child(3),
        th:nth-child(4) {
          > div {
            @apply flex justify-end;
          }
        }
        th:nth-child(5) {
          > div {
            @apply flex justify-end pr-3;
          }
        }
      }
    }
    .percentage-bar {
      @apply mx-0;
    }
    tbody {
      tr {
        &:first-child {
          td {
            @apply pt-4;
          }
        }
        &:last-child {
          td {
            @apply pb-4;
          }
        }
        td {
          &:first-child {
            @apply pl-4;
          }
          &:nth-child(2) {
            @apply pl-2;
          }
          &:nth-child(3) {
            @apply pr-3.5;
          }
          &:nth-child(4) {
            @apply pr-6;
          }
        }
      }
    }
  }
  &.new {
    .table-header-group {
      .table-row {
        th {
          &:nth-child(2) {
            @apply pl-2.5;
          }
          &:nth-child(3) {
            > div {
              @apply flex justify-end;
            }
          }
          &:nth-child(4),
          &:nth-child(5),
          &:nth-child(6),
          &:nth-child(7) {
            > div {
              @apply flex justify-end pr-1;
            }
          }
          &:nth-child(8) {
            @apply lg:pr-52;
            > div {
              @apply flex justify-end pr-4;
            }
          }
        }
      }
    }
    .percentage-bar {
      @apply mx-0;
    }
    tbody {
      tr {
        td {
          &:nth-child(3) {
            @apply lg:pr-3.5;
          }
          &:nth-child(8) {
            @apply lg:pr-52;
          }
        }
      }
    }
  }
  &.top-sale {
    .table-header-group {
      .table-row {
        th:nth-child(4),
        th:nth-child(5),
        th:nth-child(6) {
          > div {
            @apply flex justify-end;
          }
        }
        th:nth-child(7) {
          > div {
            @apply flex justify-end pr-4;
          }
        }
      }
    }
  }
  &.holder {
    .table-header-group {
      .table-row {
        th:nth-child(3),
        th:nth-child(4),
        th:nth-child(5),
        th:nth-child(6),
        th:nth-child(7),
        th:nth-child(8) {
          > div {
            @apply flex justify-end;
          }
        }
        th:nth-child(9) {
          > div {
            @apply flex justify-end pr-3;
          }
        }
      }
    }
    .percentage-bar {
      @apply mx-0;
    }
  }
  &.activity {
    .table-header-group {
      .table-row {
        th {
          &:first-child {
            @apply pl-10;
          }
          &:nth-child(4),
          &:nth-child(5),
          &:nth-child(6),
          &:nth-child(7) {
            > div {
              @apply flex justify-end;
            }
          }
          &:last-child {
            @apply pr-10;
            > div {
              @apply justify-end;
            }
          }
        }
      }
    }
    .percentage-bar {
      @apply mx-0;
    }
    tbody {
      tr {
        td {
          &:first-child {
            @apply pl-10;
          }
          &:last-child {
            @apply pr-10;
          }
        }
      }
    }
  }
  &.whale-activity-analytics {
    @apply text-dark-primary dark:text-white;
    .table-header-group {
      .table-row {
        th {
          &:first-child {
            @apply pl-10;
          }
          &:nth-child(4),
          &:nth-child(5),
          &:nth-child(6),
          &:nth-child(7) {
            > div {
              @apply flex justify-end;
            }
          }
          &:last-child {
            @apply pr-10;
            > div {
              @apply justify-end;
            }
          }
        }
      }
    }
    .percentage-bar {
      @apply mx-0;
    }
    tbody {
      tr {
        td {
          &:first-child {
            @apply pl-10;
          }
          &:last-child {
            @apply pr-10;
          }
        }
      }
    }
  }
  &.account-pnl {
    @apply text-dark-primary dark:text-white;
    .table-header-group {
      .table-row {
        th:nth-child(4),
        th:nth-child(5),
        th:nth-child(6),
        th:nth-child(7) {
          > div {
            @apply flex justify-end;
          }
        }
        th:nth-child(8) {
          > div {
            @apply flex justify-end pr-3;
          }
        }
      }
    }
  }
  &.admin-accounts {
    .table-header-group {
      .table-row {
        th {
          &:nth-child(2),
          &:nth-child(3),
          &:nth-child(4),
          &:nth-child(5),
          &:nth-child(6) {
            > div {
              @apply flex justify-end;
            }
          }
          &:nth-child(7) {
            > div {
              @apply flex justify-end pr-10;
            }
          }
        }
        
      }
    }
    .percentage-bar {
      @apply mx-0;
    }
    tbody {
      tr {
        td {
          &:nth-child(7) {
            @apply pr-12;
          }
        }
      }
    }
  }
}

.table-no-border {
  thead > tr > th:first-child {
    padding-left: 1.7rem;
  }
  tbody {
    tr {
      &:first-child {
        border-top: 1px solid #d0d3e5;
      }
      border: none;

      td:first-child {
        padding-left: 1.7rem;
      }
    }
  }
  &.whale-involved-board {
    @apply text-dark-primary dark:text-white;
    .table-header-group {
      .table-row {
        th:nth-child(3),
        th:nth-child(4),
        th:nth-child(5),
        th:nth-child(6),
        th:nth-child(7) {
          > div {
            @apply flex justify-end;
          }
        }
        th:nth-child(8) {
          > div {
            @apply flex justify-end pr-3;
          }
        }
      }
    }
    .percentage-bar {
      @apply mx-0;
    }
    tbody {
      tr {
        &:first-child {
          td {
            @apply pt-4;
          }
        }
        &:not(:first-child) {
          td {
            &:first-child {
              @apply pt-0;
            }
          }
        }
        &:last-child {
          td {
            @apply pb-4;
          }
        }
        td {
          @apply pb-5;
        }
      }
    }
  }
  &.whale-bought {
    @apply text-dark-primary dark:text-white;
    .table-header-group {
      .table-row {
        th:nth-child(4),
        th:nth-child(5),
        th:nth-child(6),
        th:nth-child(7) {
          > div {
            @apply flex justify-end;
          }
        }
        th:nth-child(8) {
          > div {
            @apply flex justify-end pr-3;
          }
        }
      }
    }
    tbody {
      tr {
        &:first-child {
          td {
            @apply pt-4;
          }
        }
        &:not(:first-child) {
          td {
            &:first-child {
              @apply pt-0;
            }
          }
        }
        &:last-child {
          td {
            @apply pb-4;
          }
        }
        td {
          @apply pb-5;
        }
      }
    }
  }
  &.whale-minted {
    @apply text-dark-primary dark:text-white;
    .table-header-group {
      .table-row {
        th:nth-child(3),
        th:nth-child(4),
        th:nth-child(5),
        th:nth-child(6),
        th:nth-child(7),
        th:nth-child(8) {
          > div {
            @apply flex justify-end;
          }
        }
        th:nth-child(9) {
          > div {
            @apply flex justify-end pr-3;
          }
        }
      }
    }
    .percentage-bar {
      @apply mx-0;
    }
    tbody {
      tr {
        &:first-child {
          td {
            @apply pt-4;
          }
        }
        &:not(:first-child) {
          td {
            &:first-child {
              @apply pt-0;
            }
          }
        }
        &:last-child {
          td {
            @apply pb-4;
          }
        }
        td {
          @apply pb-5;
        }
      }
    }
  }
  &.whale-minters {
    @apply text-dark-primary dark:text-white;
    .table-header-group {
      .table-row {
        th:nth-child(3),
        th:nth-child(4),
        th:nth-child(5),
        th:nth-child(6) {
          > div {
            @apply flex justify-end;
          }
        }
        th:nth-child(7) {
          > div {
            @apply flex justify-end pr-3;
          }
        }
      }
    }
    .percentage-bar {
      @apply mx-0;
    }
    tbody {
      tr {
        &:first-child {
          td {
            @apply pt-4;
          }
        }
        &:not(:first-child) {
          td {
            &:first-child {
              @apply pt-4;
            }
          }
        }
        &:last-child {
          td {
            @apply pb-4;
          }
        }
        td {
          @apply pb-5;
        }
      }
    }
  }
  &.all-whales-board {
    @apply text-dark-primary dark:text-white;
    .table-header-group {
      .table-row {
        th:nth-child(4),
        th:nth-child(5),
        th:nth-child(6),
        th:nth-child(7),
        th:nth-child(8),
        th:nth-child(9),
        th:nth-child(10),
        th:nth-child(11) {
          > div {
            @apply flex justify-end;
          }
        }
        th:nth-child(12) {
          > div {
            @apply flex justify-end pr-3;
          }
        }
      }
    }
    .percentage-bar {
      @apply mx-0;
    }
  }
  &.related-address {
    @apply text-dark-primary dark:text-white;
    .table-header-group {
      .table-row {
        th:nth-child(2),
        th:nth-child(3) {
          > div {
            @apply flex justify-end;
          }
        }
        th:nth-child(4) {
          > div {
            @apply flex justify-end pr-3;
          }
        }
      }
    }
  }
  &.account-collections {
    @apply text-dark-primary dark:text-white;
    .table-header-group {
      .table-row {
        th:nth-child(2),
        th:nth-child(3),
        th:nth-child(4),
        th:nth-child(5),
        th:nth-child(6),
        th:nth-child(7) {
          > div {
            @apply flex justify-end;
          }
        }
        th:nth-child(8) {
          > div {
            @apply flex justify-end pr-3;
          }
        }
      }
    }
    .percentage-bar {
      @apply mx-0;
    }
    tbody {
      tr {
        &:first-child {
          td {
            @apply pt-4;
          }
        }
        &:not(:first-child) {
          td {
            &:first-child {
              @apply pt-4;
            }
          }
        }
        &:last-child {
          td {
            @apply pb-4;
          }
        }
        td {
          @apply pb-5;
        }
      }
    }
  }
  
}

.admin-dashboard-table {
  @apply text-dark-primary dark:text-white;
  thead > tr > th {
    @apply px-4 pt-3 pb-2 text-right;
    > div {
      @apply justify-end;
    }
    &:last-child {
      @apply pl-4 pr-10;
    }
  }
  tbody > tr > td {
    @apply px-4 py-5 text-right;
    > div {
      @apply justify-end;
    }
    &:last-child {
      @apply pl-4 pr-10;
    }
  }
}

table {
  .wallet-address {
    max-width: 128px;
    @apply px-2 font-medium truncate;
  }

  &.admin-account-detail {
    thead > tr > th:first-child,
    tr td:first-child {
      padding-left: 1.7rem;
    }
    tr td {
      width: 0;
    }

    tbody tr:last-child {
      @apply border-b;
    }
  }
}
.filter-table {
  display: flex;
  flex-flow: wrap;
  gap: 30px 65px;
  .filter-left {
    width: 100%;
    flex-shrink: 0;
  }
  .table-container {
    width: 100%;
    flex: 1;
    &.account-pnl {
      @include md {
        max-width: calc(100% - 400px);
      }
    }
  }
  @include md() {
    .filter-left {
      width: 335px;
      flex-shrink: 0;
    }
  }
}

.activities-board-table {
  @apply flex-grow overflow-y-auto;
  max-height: calc(100% - 45px);
}

.ask-table {
  thead > tr > th {
    @apply px-4 pt-3 pb-2;
    &:first-child {
      > div {
        @apply pl-4;
      }
    }
    &:nth-last-child(2) {
      @apply w-24;
    }
    &:last-child {
      @apply w-9;
    }
  }
  tbody > tr > td {
    @apply px-4;
    padding-top: 19px;
    padding-bottom: 17px;
    &:first-child {
      color: #1c2340;
    }
    &:nth-child(2) {
      @apply pl-2 md:pr-16 pr-4;
    }
    &:nth-child(3) {
      @apply pl-2 text-dark-primary;
    }
    &:nth-child(4) {
      @apply pl-2 w-24 text-dark-2;
    }
    &:last-child {
      @apply pr-0 w-9;
    }
  }
}

.for-sale-table {
  thead > tr > th {
    @apply px-4 pt-3 pb-2;
  }
  tbody > tr > td {
    @apply px-4 py-2;
    &:first-child {
      color: #1c2340;
    }
  }
}
