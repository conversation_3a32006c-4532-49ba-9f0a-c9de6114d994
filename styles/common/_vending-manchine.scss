.package-type-tab {
  box-shadow: 0px 3px 6px #00000029;
  border-radius: 30px;
  color: #222222;
  font-weight: 500;
  font-size: 16px;
}
.package-section{
  .package-section-title{
    font-size: 35px;
    line-height: 46px;
    color: #222222;
    font-weight: 500;
    font-style: italic;
    text-align: center;
    margin-bottom: 40px;
  }
  .package-item{
    border: 1px solid #AAAAAA;
    border-radius: 20px;
    padding: 16px;
    .package-item-image{
      border-radius: 10px;
    }
    .package-item-title{
      font-size: 1rem;
      line-height: 1.5rem;
      color: #1A1A1A;
      font-weight: 600;
      text-align: left;
    }
    .package-item-price{
      font-size: 1rem;
      line-height: 1.5rem;
      color: #9F9F9F;
      font-weight: 600;
      text-align: left;
    }
  }
}

.package-detail-container{
  .package-detail-right{
    .package-detail-right-title{
      font-size: 2rem;
      line-height: 2.5rem;
      color: #222222;
      font-weight: 400;
      font-style: italic;
    }
    .package-detail-right-description{
      font-size: 1rem;
      line-height: 1.25rem;
      color: #222222;
      font-weight: normal;
    }
    .package-detail-right-odds-title{
      font-size: 1.25rem;
      line-height: 1.5rem;
      color: #222222;
      font-weight: 600;
      margin-bottom: 4px;
    }
    .package-detail-right-odds-expected-value{
      font-size: 1rem;
      line-height: 1.5rem;
      color: #222222;
      font-weight: 500;
      margin-bottom: 8px;
    }
    .package-detail-right-odds-description{
      font-size: 1rem;
      line-height: 1.25rem;
      color: #222222;
      font-weight: 300;
    }
  }
}
.odds-card{
  background: #FFFFFF 0% 0% no-repeat padding-box;
  border: 1px solid #D0D3E5;
  border-radius: 10px;
  padding: 12px 16px;
  .odds-card-range{
    font-size: 0.75rem;
    line-height: 1rem;
    color: #222222;
    font-weight: 500;
  }
  .odds-card-percentage{
    font-size: 1rem;
    line-height: 1.5rem;
    color: #222222;
    font-weight: 500;
  }
}
.more-packs{
  .more-packs-title{
    font-size: 1.25rem;
    line-height: 1.5rem;
    color: #222222;
    font-weight: 600;
  }
  .more-packs-tabs{
    .more-packs-tab{
      font-size: 0.875rem;
      line-height: 1.5rem;
      box-shadow: 0px 3px 6px #00000029;
      border-radius: 30px;
      padding: 8px 16px;
      color: #222222;
      font-weight: 600;
      min-width: 80px;
      text-align: center;
      &.active{
        background: #00CBE5 0% 0% no-repeat padding-box;
        color: #FFFFFF;
      }

    }
  }
  .more-packs-card{
    border: 1px solid #D0D3E5;
    border-radius: 10px;
    padding: 12px;
    .more-packs-card-image{
      border-radius: 10px;
    }
    .more-packs-card-title{
      font-size: 0.875rem;
      line-height: 1.5rem;
      color: #222222;
      font-weight: 600;
    }
    .more-packs-card-price{
      font-size: 1.25rem;
      line-height: 1.75rem;
      color: #222222;
      font-weight: 500;
    }
  }

}
.package-price{
  font-size: 2rem;
  line-height: 2.5rem;
  color: #02AACF;
  font-weight: 500;
}
.package-buy-now-button{
  background: transparent linear-gradient(180deg, #00BCE599 0%, #02AACF 100%) 0% 0% no-repeat padding-box;
  border-radius: 25px;
  padding: 13px 30px;
  color: #FFFFFF;
  font-weight: 500;
  font-size: 1rem;
  line-height: 1.5rem;
  text-align: center;
  &:hover{
    opacity: 0.8;
  }
}
