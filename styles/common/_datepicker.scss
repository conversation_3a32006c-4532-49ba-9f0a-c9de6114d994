.popup-mode {
  padding: 0px !important;
  margin: 0px !important;
  > .mt-5{
    margin: 0px;
    .react-datepicker-wrapper {
      .react-datepicker__input-container{ 
        input{
        border-radius: 8px;
        background: transparent;
        }
      }
    }
  }
}
.react-datepicker-popper{
  background-color: #fff;
  border: 1px solid rgb(208 211 229);
  border-radius: 0.625rem;
  padding-top:8px;
  .react-datepicker__header {}
  .react-datepicker{
    .react-datepicker__triangle{
      &::before,&::after{
        display: none;
      }
    }
  }
}