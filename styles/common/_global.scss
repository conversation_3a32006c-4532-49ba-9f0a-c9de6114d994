html,
body {
  padding: 0;
  margin: 0;
  font-family: Roboto, -apple-system, BlinkMacSystemFont, Segoe UI, Oxygen,
    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
  scroll-behavior: smooth;
  // @apply overflow-x-hidden;
}

#__next {
  @apply flex flex-col justify-between min-h-screen;
}

a {
  color: inherit;
  text-decoration: none;
}

* {
  box-sizing: border-box;
}

/* Chrome, Safari, Edge, Opera */
input.no-arrow::-webkit-outer-spin-button,
input.no-arrow::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input.no-arrow {
  -moz-appearance: textfield;
}

.dark {
  .dark-fill-white path,
  .dark-fill-white rect {
    fill: white;
  }
}

.fill-white path,
.fill-white rect {
  fill: white;
}

.light-fill-black path,
.light-fill-black rect {
  fill: black;
}

.dark .swiper .swiper-pagination .swiper-pagination-bullet {
  background: gains<PERSON>;
}

.dark .swiper-slide {
  background-color: transparent;
}

.icon-wrapper {
  span {
    vertical-align: middle;
  }
}

.svg-wapper {
  svg.payment {
    fill: none !important;
    @apply stroke-dark-primary dark:stroke-white;
  }
  &:hover {
    svg {
      @apply fill-primary-1;
      &.payment {
        @apply stroke-primary-1;
      }
    }
  }
  > span {
    @apply text-dark-primary dark:text-white hover:text-primary-1;
  }
  &:hover {
    > span {
      @apply text-primary-1;
    }
  }
}

.btn-base {
  padding: 13px 50px;
  // transition: all;
  // transition-duration: 350ms;
}
.btn-sm {
  padding: 13px 40px;
}
.btn-xs {
  padding: 13px 25px;
}
.btn-base.px-md {
  padding: 13px 35px;
}
.btn-base.py-md {
  padding: 8px 35px;
}
.btn-icon {
  &.active {
    svg {
      fill: #02aacf;
    }
  }
  &:hover {
    svg {
      fill: #02aacf;
    }
  }
}
// .btn-base:hover {
//   // box-shadow: 0px 3px 20px 2px rgb(2 125 253 / 70%);
//   text-shadow: 1px 1px 3px #00000044;
// }

.icon-18 {
  width: 18px;
  height: 18px;
}
.icon-22 {
  width: 22px;
  height: 22px;
}
.icon-21 {
  width: 21px;
  height: 21px;
}
.icon-25 {
  width: 25px;
  height: 25px;
}

.shadow-normal {
  box-shadow: 0 3px 6px rgba(119, 119, 119, 0.3);
}
// Toggle filter
.fist-mt-none {
  li:first-child {
    margin-top: 0;
  }
}

.toggle-content {
  &:not(.no-padding-top) {
    padding: 1.375rem 30px 1.375rem 43px;
    @media (max-width: 1024px) and (min-width: 768px) {
      padding: 1.375rem 20px 1.375rem 23px;
    }
    @media (max-width: 480px) {
      padding: 1.375rem 20px 1.375rem 23px;
    }
  }
}

.toggle-content.p-4 {
  padding: 1rem;
}

.toggle-content.no-padding {
  padding: 0;
}

// Percenage bar
.percentage-bar {
  @apply rounded-20 w-9/12 mx-auto mt-10p bg-purple-1/[.3];
  height: 3px;
  .percentage-track {
    @apply rounded-20 bg-purple-1;
    height: 100%;
  }
}

.border-red {
  @apply border-red-1;
}
.text-red {
  @apply text-red-1;
}

.heading-brandon {
  font-family: 'Brandon';
  @apply text-4xl font-medium dark:text-dark-primary;
}

body {
  @apply relative overflow-x-hidden;
  &::before {
    content: "";
    position: absolute;
    top: 100px;
    left: 0;
    background-image: none;
    background-repeat: no-repeat;
    width: 100%;
    height: calc(100% - 100px);
    z-index: -1;
    background-position-y: -215px;
    background-position-x: -100px;
    filter: blur(50px);
  }
  // &.darker-bg {
  //   &::before {
  //     background-image: url("./../../public/asset/images/dots_bg.svg");
  //   }
  // }
  // &.light-bg {
  //   &::before {
  //     background-image: url("./../../public/asset/images/dots_bg_2.svg");
  //   }
  // }
}

@media (max-width: 767px) {
  body {
    &::before {
      top: 0;
      height: 100%;
      background-position-y: 0;
      background-position-x: 50%;
      background-size: 150%;
    }
  }
}

@include md() {
  .px-second {
    padding-left: calc(100vw * (100) / $xl);
    padding-right: calc(100vw * (100) / $xl);
  }
  .pr-second {
    padding-right: calc(100vw * (100) / $xl);
  }
  .pl-second {
    padding-right: calc(100vw * (100) / $xl);
  }
  .pr-third {
    padding-right: calc(100vw * (60) / $xl);
  }
}

@include xl() {
  .heading-text {
    font-size: calc(100vw * (50) / $xl);
  }
}

@media (max-width: 575px) {
  .text-heading {
    line-height: calc(36 / 30);
  }
}

.text-stroke-thin {
  -webkit-text-stroke-width: thin;
}
.text-stroke-bold {
  -webkit-text-stroke-width: bold;
}
.line-clamp-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}
.remove-after::after {
  content: none !important;
}
.remove-before::before {
  content: none !important;
}
#SingularityEvent__wrapper__iframe{
  z-index: 1000 !important;
}