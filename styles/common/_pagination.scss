.pagination-wrap .pagination {
  .pagination-item {
    @apply flex items-center justify-center xs:px-2.5 px-1.5 pb-1 pt-1.5 md:min-w-[44px];
    @apply font-medium cursor-pointer text-sm xs:text-base;
    @apply text-dark-primary dark:text-white;
    &.shortened {
      @apply px-1.5 md:min-w-[44px];
    }
    &.selected {
      @apply rounded-30 dark:bg-blue-8;
      box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
    }
    &.dots {
      cursor: default;
      &.shortened {
        @apply px-0.5 md:min-w-0;
      }
    }
  }
  .pagination-arrow {
    &.disabled {
      @apply fill-gray-3;
      pointer-events: none;
    }
  }
}

.pagination-dot {
  @apply items-center;

  .pagination-item {
    @apply px-2 text-3xl cursor-pointer transition hover:text-primary-2;

    &.selected {
      @apply text-primary-2;
    }
  }
}
