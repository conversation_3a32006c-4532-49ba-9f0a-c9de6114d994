@mixin xs() {
  @media (min-width: #{$xs + px}) {
    @content;
  }
}

@mixin sm() {
  @media (min-width: #{$sm + px}) {
    @content;
  }
}

@mixin md() {
  @media (min-width: #{$md + px}) {
    @content;
  }
}

@mixin md2() {
  @media (min-width: #{$md2 + px}) {
    @content;
  }
}

@mixin lg() {
  @media (min-width: #{$lg + px}) {
    @content;
  }
}

@mixin xl() {
  @media (min-width: #{$xl + px}) {
    @content;
  }
}

@mixin xml() {
  @media (min-width: #{$xml + px}) {
    @content;
  }
}

@mixin xxl() {
  @media (min-width: #{$xxl + px}) {
    @content;
  }
}

@function toR($size) {
  @return ($size / 16) * 1rem;
}

// @mixin vwCalc($size, $breakpoint) {

// }
