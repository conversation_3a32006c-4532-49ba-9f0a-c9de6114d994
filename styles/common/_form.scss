@include xl() {
  .collection-form-cont {
    padding-left: calc(100vw * (225) / $xl);
    padding-right: calc(100vw * (225) / $xl);
    .collection-form {
      max-width: 855px;
    }
  }
}

.upload-image-circle {
  width: 88px;
  height: 88px;
  border-radius: 100%;
  position: relative;
  overflow: hidden;
}

.upload-image-square {
  width: 220px;
  height: 137px;
  border-radius: 10px;
}

.upload-image-supersquare {
  width: 150px;
  height: 150px;
  border-radius: 10px;
}

.upload-image-reactangle {
  width: 100%;
  height: 137px;
  border-radius: 10px;
}

input[type="radio"]:before,
input[type="radio"]:after {
  box-sizing: border-box;
}

.radio-tag-wrapper {
  input[type="radio"] {
    display: none;
  }

  input[type="radio"]+label:before {
    content: "";
    display: inline-block;
    width: 16px;
    height: 16px;
    padding: 6px;
    margin-right: 5px;
    background-clip: content-box;
    border: 1px solid #707070;
    background-color: transparent;
    border-radius: 50%;
  }

  input[type="radio"]:checked + label:before {
    padding: 2px;
    background-color: #02AACF;
    border: 1px solid #02AACF;
  }

  input[type="radio"] + label {
    @apply flex items-center;
  }
}

.issue-list {
  width: 100% !important;
  @include md2() {
    @apply w-auto;
  }
}

.input-with-suffix-icon {
  @apply relative;
  input {
    padding-right: 50px !important;
  }
  &.sm {
    input { padding-right: 60px !important; }
  }
}

.input-with-prefix-icon {
  @apply relative;
  input {
    padding-left: 75px !important;
  }
  &.sm {
    input { padding-left: 60px !important; }
  }
}
input:disabled {
  opacity: 0.5;
}