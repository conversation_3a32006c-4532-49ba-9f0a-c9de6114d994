// mx-auto pt-4 px-2 lg:px-0 md:py-10
.container-df {
  padding-left: calc(100vw * (20) / $xs);
  padding-right: calc(100vw * (20) / $xs);
}
.-mr-df {
  margin-right: calc(100vw * (20) / $xs * -1);
}

@include md() {
  .container-df {
    padding-left: calc(100vw * (20) / $md);
    padding-right: calc(100vw * (20) / $md);
  }
  .-mr-df {
    margin-right: calc(100vw * (20) / $md * -1);
  }
}

@include lg() {
  .container-lg {
    padding-left: calc(100vw * (60) / $lg);
    padding-right: calc(100vw * (60) / $lg);
  }
}

@include xl() {
  .container-df {
    padding-left: calc(100vw * (43) / $xl);
    padding-right: calc(100vw * (43) / $xl);
  }
  .container-lg {
    padding-left: calc(100vw * (100) / $xl);
    padding-right: calc(100vw * (100) / $xl);
  }
}

