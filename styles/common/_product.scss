.collection-main {
  margin-left: -10px;
  margin-right: -10px;
  .collection {
    margin-right: 8px;
    margin-left: 8px;
    margin-bottom: 20px;
    width: 160px;
    .collection-bg {
      height: 100px;
    }
    .collection-info {
      .name {
        @apply text-sm;
        margin-top: 2px;
      }
      .nickname,
      .price-symbol {
        @apply text-xs;
        margin-top: 2px;
      }
    }
  }
  .collection-character {
    width: 64px;
    height: 64px;
    margin-top: -32px;
  }

  @include sm() {
    .collection {
      margin-right: 10px;
      margin-left: 10px;
    }
  }

}

.collection-cont{
  @apply grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 xl:grid-cols-5 gap-5;

  .collection {
    @apply w-full;
    .collection-bg {
      height: 100px;
    }
    .collection-character {
      width: calc(100vw * (64) / $xs);
      height: calc(100vw * (64) / $xs);
      margin-top: calc((100vw * (64) / $xs) / 2 * -1);
    }
    .collection-info {
      padding: 0 calc(100vw * (15) / $xl) 1rem;
      .name {
        @apply text-sm truncate;
        margin-top: 2px;
      }
      .nickname,
      .price-symbol {
        @apply text-xs;
        margin-top: 2px;
      }
      .price-symbol {
        margin-top: 8px;
      }
    }

    @include md() {
      .collection-character {
        width: calc(100vw * (100) / $xl);
        height: calc(100vw * (100) / $xl);
        margin-top: calc((100vw * (100) / $xl) / 2 * -1);
      }
    }

    @include xl() {
      .collection-character {
        width: calc(100vw * (64) / $xl);
        height: calc(100vw * (64) / $xl);
        margin-top: calc((100vw * (64) / $xl) / 2 * -1);
      }
    }
  }
}

.product-layout {
  @apply flex;
  &:not(.asset-detail) {
    @apply flex-col md:flex-row gap-6 md:gap-12;
  }
  &.asset-detail {
    @apply xl:gap-13 gap-6 flex-col xl:flex-row;
    .product-layout-filter {
      @apply min-w-full xl:min-w-[370px] xl:w-4/12 w-full;
    }
  }
  .product-layout-filter {
    @apply min-w-full md:min-w-[275px] md:w-[275px] lg:min-w-[337px] lg:w-[337px];
  }

  &.owner {
    @apply xl:gap-[31px];

    .owner-board-wrap {
      @include md() {
        width: calc(100% - 335px);
      }

      @include lg() {
        width: calc(100% - 335px - 65px);
      }

      @include xl() {
        // width: 75%;
      }
    }
  }
  &.activity {
    @apply md:gap-[31px];

    .activity-board-wrapper {
      @apply flex flex-wrap w-full;
      @include md() {
        width: calc(100% - 335px);
      }
      @include lg() {
        width: calc(100% - 335px - 31px);
      }
      tbody {
        tr {
          @apply border-gray-3;
          td {
            &:first-child,
            &:last-child {
              // color: #1C2340;
            }
            &:nth-child(3) {
              // @apply text-dark-primary;
            }
          }
        }
      }
    }
  }
}

.assets-page .product-layout {
  .product-wrap {
    padding-right: 0;
    justify-content: center;
  }
  @include md() {
    .product-wrap {
      justify-content: flex-start;
    }
  }
  @include xl() {
    gap: 56px;
  }
}

.product-wrap.bundle {
  @apply gap-x-0;
  margin-left: -15px;
  margin-right: -15px;
  @include md2() {
    @apply gap-x-7;
  }
  .product-card {
    margin-left: 15px;
    margin-right: 15px;
    width: 95.6% !important;
    @include md2() {
      @apply w-full;
    }
  }
}

.product-single {
  max-width: 210px;
}

.product-cont {
  &.overview {
    @apply w-full grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-x-5;
    //grid-template-rows: 1fr 0 0;
    //overflow: hidden;
  }

  &.nft-rarity {
    @apply w-full grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5;
    &.no-data{
      @apply grid-cols-1
    }
  }

  .product-card .product-image {
    padding-top: 100%;
  }
  @include xl() {
    &.overview {
      padding: 0 37px;
    }
  }
}

.product-image.with-video {
  @apply transition duration-300;

  // &:hover {
    // .play-video {
    //   display: block;
    // }

    // &::after {
    //   content: '';
    //   width: 100%;
    //   height: 100%;
    //   position: absolute;
    //   top: 0;
    //   left: 0;
    //   background-color: rgba(0, 0, 0, 0.3);
    // }
  // }

  .play-video {
    // display: none;
    display: block;
    position: absolute;
    bottom: 0;
    right: 8px;
    z-index: 10;
    cursor: pointer;
  }

  .video-player {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.content-thumbnail-preview {
  height: min(100vw, 100vh);
  width: min(100vw, 100vh);
  background: transparent;
  border: 0px;
  position: initial;
  border-radius: initial;
}