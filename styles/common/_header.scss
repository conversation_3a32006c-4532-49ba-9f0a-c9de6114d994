.screen-main {
  height: calc(100vh - 86px);
  height: calc(100svh - 86px);
  margin-top: 86px;
}
@include md() { 
  .screen-main {
    height: calc(100vh - 98px);
    height: calc(100svh - 98px);
    margin-top: 98px;
  }
}
.header-wrapper {
  padding-top: 17px;
  padding-bottom: 17px;
  transition: all 0.3s ease;
  &.scrolled {
    @apply w-full fixed bg-white dark:bg-dark-primary;
  }
  .menu {
    .menu-ul {
      @apply min-w-[140px];
      > li {
        &:first-child {
          margin-top: 6px;
        }
      }
    }
  }
}
.logo {
  width:  120px;
  height: auto;
}
.news-tag {
  padding-left: 30px;
  padding-right: 30px;
  @apply translate-y-3/4 md:translate-y-1/2;
  min-height: 33px;
}

.hero-wrapper .news-tag {
  @apply translate-y-0 md:translate-y-1/2 pl-10 pr-10;
  @apply text-base;
  @media (max-width: 767px) {
    @apply flex items-center;
  }
}

@include md() {
  .header-wrapper {
    &.scrolled {
      padding-top: 24px;
      padding-bottom: 24px;
    }
    .search {
      margin-left: calc(100vw * (50) / $md);
      .search-wrap {
        width: calc(100vw * (200) / $md);
      }
    }
    // .nav-right {
    //   margin-right: calc(100vw * (40) / $md);
    // }
    .user-header {
      order: unset;
    }
  }
  // .logo {
  //   width: calc(100vw * (209) / $md);
  //   height: calc(100vw * (90) / $md);
  // }
  .news-tag {
    width: calc(100vw * (375) / $md);
    padding-left: 51px;
    padding-right: 64px;
    &.scrolled {
      @apply translate-y-0;
      bottom: 0;
    }
  }
}

@include lg() {
  .header-wrapper {
    .search {
      margin-left: calc(100vw * (50) / $lg);
      .search-wrap {
        width: calc(100vw * (300) / $lg);
      }
    }
    // .nav-right {
    //   margin-right: calc(100vw * (80) / $lg);
    // }
  }
  // .logo {
  //   width: calc(100vw * (209) / $lg);
  //   height: calc(100vw * (90) / $lg);
  // }
}

@include xl() {
  .header-wrapper {
    padding: 24px 0;
    .search {
      margin-left: calc(100vw * (71) / $xl);
      .search-wrap {
        width: calc(100vw * (378) / $xl);
      }
    }
    .nav-right {
      margin-right: calc(100vw * (100) / $xl);
    }
    .logo {
      margin-left: 18px;
    }
  }
  // .logo {
  //   width: calc(100vw * (209) / $xl);
  //   height: calc(100vw * (90) / $xl);
  // }
  .news-tag {
    width: calc(100vw * (675) / $xl);
  }
}
