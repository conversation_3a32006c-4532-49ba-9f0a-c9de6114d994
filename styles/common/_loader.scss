/* styles.css */
.loader {
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #fff; 
  border-radius: 50%;
  width: 24px; 
  height: 24px;
  animation: spin 1s linear infinite;
  margin: auto;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loader-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.border-gray .loader {
  border-top: 2px solid #374151; 
}