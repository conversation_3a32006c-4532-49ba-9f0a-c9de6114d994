.statistic-cont {
  @apply mt-6 pb-4 grid md:grid-cols-2 grid-cols-1 gap-5;

  .statistic-left {
    @apply h-fit flex sm:flex-nowrap flex-wrap sm:gap-0 gap-5;
  }

  .statistic-right {
    @apply grid xl:grid-cols-3 sm:grid-cols-2 grid-cols-1 gap-5;
  }

  @include xl() {

    padding-bottom: 87px;

    .statistic-left {
      padding-right: 69px;
    }

    .statistic-right {
      padding-right: 49px;
      padding-left: 20px;
    }
  }
}

.chart-space {
  @apply md:px-5 xl:px-20;
}
