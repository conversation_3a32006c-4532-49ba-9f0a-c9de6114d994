.topsale-section {
  .filter-tag {
    padding: calc(100vw * (15) / $xl) calc(100vw * (15) / $xl);
  }
  .tag-icon {
    width: 40px;
    height: 40px;
  }
  .tag-text {
    padding-left: 14px;
  }
  .product-side {
    width: 100%;
  }
  @include md() {
    .topsale-filter {
      margin-top: 108px;
    }
    .product-side {
      .sort-bar {
        //margin-right: calc(100vw * (27) / $md);
      }
      margin-right: calc(100vw * (30) / $md);
      margin-left: calc(100vw * (35) / $md);
      width: 100%;
    }
  }
  @include md2() {
    .topsale-filter {
      margin-top: 63px;
    }
  }
  @include lg() {
    .product-side {
      .sort-bar {
        //margin-right: calc(100vw * (36) / $lg);
      }
    }
  }
  @include xl() {
    .product-side {
      .sort-bar {
        //margin-right: calc(100vw * (50) / $xl);
      }
    }
  }
}
.product-wrap {
  //padding-right: calc(100vw * (50) / $xl);
  margin-top: 27px;
  &:not(.collection-main) {
    @apply grid grid-cols-2 md2:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-x-5;
  }
  &.collection-main {
    @apply grid grid-cols-1 xs:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-x-5 mx-0;
    &.full-page {
      @apply grid-cols-1 xs:grid-cols-2 sm:grid-cols-3 md2:grid-cols-4 xl:grid-cols-6 lg:px-4;
    }
    .collection {
      @apply flex-none w-full min-w-[160px] mx-0;
    }
  }
  &.no-gap {
    padding-right: 0;
  }
  &.no-data {
    @apply grid-cols-1;
  }
  .product-card {
    @apply mb-5 w-full;
    .product-image {
      padding-top: 100%;
    }
   /* @include sm() {
      width: 30%;
    }
    @include md() {
      width: 28%;
    }
    @include lg() {
      width: 21%;
    }
    @include xl() {
      width: 150px;
    }
    @include xml() {
      width: 18%;
    }*/
  }
}
.product-wrap.mt-0 {
  margin-top: 0;
}
