.nft-drop-wrap {
  @apply flex flex-col md:flex-row flex-wrap gap-4 lg:gap-8 xl:gap-16;


  .nft-right {
    .nft-submit-btn {
      @apply mb-4 md:mb-0;
    }
  }

  @include md() {
    .calendar-left {
      width: 40%;
    }

    .nft-right {
      flex: 1;
      position: relative;

      .nft-submit-btn {
        position: absolute;
        right: 0;
        top: -20px;
        transform: translateY(-100%);
      }
    }
  }

  @include lg() {
    .calendar-left {
      width: 30%;
    }
  }

  @include xl() {
    .calendar-left {
      width: 26.2%;
    }
  }
}


.nft-card-wrap {
  @apply px-10p pt-2 pb-3 w-full h-fit flex flex-col justify-center items-center rounded-10 border border-gray-3 relative;

  .category-badge {
    @apply text-xs font-medium text-yellow-1 rounded-25 px-2 py-[2px] bg-gradient-to-b from-yellow-2/10 to-yellow-3/10 absolute top-12 left-4;
  }
}

.duration-daterange {
  &.drop-date {
    @apply w-max px-[14px] pb-5 flex flex-col justify-center items-center;

    .react-datepicker__input-time-container {
      @apply mx-0 mb-0 mt-4 flex justify-center;
      background: rgba(0, 0, 0, 0);

      .react-datepicker-time__input {
        @apply m-0;
      }
    }
  }
}
