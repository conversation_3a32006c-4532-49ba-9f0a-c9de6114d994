// w = 1180
.user-setting {
  .user-setting-nav {
    padding-bottom: 17px;
    @apply bg-white dark:bg-dark-primary rounded-10 border border-gray-3 h-fit w-full;

    &.offers {
      @apply w-full;
    }

    .nav-item {
      @apply flex items-center justify-between font-medium cursor-pointer;
      padding: 7px 20px 7px 20px;
      margin-top: 10px;
      svg {
        @apply fill-dark-primary dark:fill-white;
      }
    }

    .nav-item.active {
      background-color: #208dfe0f;
      p {
        @apply text-primary-2;
      }
      svg {
        @apply fill-primary-2;
      }
    }
  }

  .user-setting-content {
    // @apply pr-second;
    width: 100%;
    .text-heading {
      text-align: left;
    }
  }

  .profile-setting {
    @apply flex flex-wrap xl:flex-nowrap w-full;
    > form {
      @apply lg:min-w-[630px] md:min-w-[580px];
    }
  }

  .acccount-support {
    @apply flex flex-col lg:max-w-[570px] w-full;
  }

  @include md2() {
    &:not(.offers) {
      gap: calc(100vw * (60) / $lg);
    }
    .user-setting-nav {
      &:not(.offers) {
        width: calc(100vw * (335) / $xl);
        flex: 0 0 calc(100vw * (335) / $xl);
      }

    }
    // .user-setting-content {
    //   width: auto;
    // }
  }

  @include xl() {
    gap: 80px;
    .user-setting-nav {
      width: calc(100vw * (335) / $xl);
      &.offers {
        width: calc(100vw * (335) / $xl);
        flex: 0 0 calc(100vw * (335) / $xl);
      }
      .nav-item {
        padding: 10px 30px 11px 43px;
      }
    }
  }
}
