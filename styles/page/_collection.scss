.duration-daterange {

  @apply mt-[6px] rounded-10 border border-gray-3 bg-white relative dark:bg-blue-3;
  &:not(.custom) {
    @apply min-w-fit;
  }
}

.duration-daterange,
.nft-drop-wrap {
  .react-datepicker {
    border: none;
    background: rgba(0, 0, 0, 0);

    .react-datepicker__month-container {
      @apply dark:bg-blue-3;
    }

    .react-datepicker__header {
      @apply bg-inherit border-none pt-0;

      .react-datepicker__current-month {
        @apply font-medium text-gray-1;
      }
    }

    .react-datepicker__day {
      @apply font-medium text-sm text-gray-1 bg-inherit;
      &.react-datepicker__day--selected {
        &.disabled {
          @apply text-gray-1 opacity-100;
        }
      }
      &.disabled {
        @apply text-gray-7 opacity-50;
      }
      &.react-datepicker__day--outside-month {
        @apply text-gray-7 opacity-50;
      }
    }
    .react-datepicker__day--selected, 
    .react-datepicker__day--in-range {
      @apply bg-gray-5 text-dark-primary font-medium text-sm;
    }

    .react-datepicker__day-names {
      margin-top: 15px;

      .react-datepicker__day-name {
        @apply text-sm text-gray-1 font-medium;
      }
    }

    .react-datepicker-time__input {
      input {
        @apply bg-gray-4 rounded-25 outline-none text-center text-base;
        padding: 13px 20px;
        width: 147px;
      }
    }

    .react-datepicker__input-time-container {
      @apply inline-flex justify-center;
      .react-datepicker-time__input-container {
        .react-datepicker-time__input {
          @apply ml-0;
        }
      }
    }
  }
}

.datetime-picker-duration-wrapper {
  .react-datepicker {
    .react-datepicker__input-time-container {
      @apply justify-start;
    }
  }
}

.react-datepicker-wrapper {
  .react-datepicker__input-container{
    input {
      @apply bg-gray-4 rounded-25 outline-none text-center;
      padding: 13px;
      width: 147px;
    }
  }
}
