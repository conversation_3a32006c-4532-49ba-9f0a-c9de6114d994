.generator-cont {
  @apply flex flex-wrap gap-5 md:gap-0;

  .filter-left {
    @apply w-full;

    .toggle-generator {
      @apply px-10p;
    }

    .toggle-collection {
      @apply px-5;
    }

  }
  .product-right {
    width: 100%;
    flex: 1;
  }

  @include md() {
    .filter-left {
      width: 335px;
      flex-shrink: 0;
    }
    .product-right {
      width: calc(100% - 300px);
      padding-left: 23px;
      padding-right: 0;
    }
  }
  @include lg() {
    // .filter-left {
    //   width: 335px;
    //   flex-shrink: 0;
    // }
    .product-right {
      width: calc(100% - 335px);
      padding-left: 53px;
      padding-right: 62px;
    }
  }
}

.generator-product {
  .closeable-product {
    @apply border border-transparent hover:border-primary-2;

    &:hover {
      .close-icon {
        @apply block;
      }
      box-shadow: none;
    }
  }
}

.react-flow {
  .react-flow__node-default {
    width: 130px;
    height: 98px;
    @apply rounded-10 border-gray-3 flex justify-center items-center text-lg font-medium;
  }

  .react-flow__handle {
    @apply bg-green-4 w-2 h-2;
  }

  .react-flow__panel.react-flow__attribution {
    display: none;
  }

  .react-flow__controls {
    @apply flex mt-1 ml-1 rounded-30 bg-gray-4 px-1;

    .react-flow__controls-button {
      @apply bg-transparent border-b-0 p-2;

      svg {
        @apply fill-primary-2;
      }
    }
  }
}

.input-range {
  -webkit-appearance: none;
  width: 140px;
  height: 8px;
  background: rgba(126, 135, 255, 0.2);
  border-radius: 5px;
  background-image: linear-gradient(#7E87FF, #7E87FF);
  //background-size: 70% 100%;
  background-repeat: no-repeat;

  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    height: 18px;
    width: 18px;
    border-radius: 50%;
    background: #7E87FF;
    cursor: ew-resize;
    box-shadow: 0 0 2px 0 #555;
    transition: background .3s ease-in-out;
  }

  &::-ms-thumb {
    -webkit-appearance: none;
    height: 18px;
    width: 18px;
    border-radius: 50%;
    background: #7E87FF;
    cursor: ew-resize;
    box-shadow: 0 0 2px 0 #555;
    transition: background .3s ease-in-out;
  }

  &::-webkit-slider-thumb:hover {
    background: #7E87FF;
  }

  &::-moz-range-thumb:hover {
    background: #7E87FF;
  }

  &::-ms-thumb:hover {
    background: #7E87FF;
  }

  /* Input Track */
  &::-webkit-slider-runnable-track  {
    -webkit-appearance: none;
    box-shadow: none;
    border: none;
    background: transparent;
  }

  &::-moz-range-track {
    -webkit-appearance: none;
    box-shadow: none;
    border: none;
    background: transparent;
  }

  &::-ms-track {
    -webkit-appearance: none;
    box-shadow: none;
    border: none;
    background: transparent;
  }
}
