.service-section {
  .service-file-types {
    width: calc(100vw * (1120) / $xl);
  }
  .service-image {
    // @apply w-full;
  }
  .service-content {
    @include md() {
      @apply flex items-start;
      padding-left: calc(100vw * (33) / $xl);
      .service-image {
        min-width: calc(100vw * (592) / $xl);
      }
      .service-text {
        width: calc(100vw * (485) / $xl);
        h2 {
          font-size: 23px;
        }
        p {
          font-size: 16px;
        }
      }
    }
  }
  .service-heading-1 {
    font-size: 35px;
  }
  .service-heading-2 {
    font-size: 45px;
  }
  .service-heading-2-description {
    width: calc(100vw * (786) / $xl);
    font-size: 20px;
  }
}
