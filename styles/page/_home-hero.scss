.hero-wrapper {
  .swiper-slide {
    width: 87%;
    height: calc(100vw * (228) / $xs);
    max-height: 250px;
  }
  .swiper-slide:nth-child(2n) {
    width: 100%;
  }
  .swiper-slide:nth-child(3n) {
    width: 100%;
  }
  .content {
    .text-heading {
      font-size: calc(100vw * (30) / $xs);
    }
  }
  .content-text {
    padding: 0 calc(100vw * (27) / $xs);
  }
  @include sm() {
    .swiper-slide {
      height: calc(100vw * (340) / $md);
      max-height: 250px;
      width: 60%;
    }
    .content {
      .text-heading {
        font-size: calc(100vw * (50) / $xl);
      }
    }
  }
  @include md() {
    .content-text {
      padding: 0;
    }
    .content {
      padding-left: calc(100vw * (120) / $xl);
    }
    .swiper-slide {
      width: 60%;
      height: calc(100vw * (340) / $xl);
      max-height: 250px;
    }
  }
  @include xl() {
    .swiper-slide:nth-child(2n) {
      width: 80%;
    }
    .swiper-slide:nth-child(3n) {
      width: 80%;
    }
  }
}

