.nowsale-section {
  .badge {
    padding: 1px 12px;
    top: 15px;
    left: 12px;
    span {
      @apply text-white;
      font-size: 12px;
      line-height: 20px;
      margin-left: 7px;
   }
  }
  @include xl() {
    padding-left: calc(100vw * (80) / $xl);
    padding-right: calc(100vw * (80) / $xl);
  }
  .collection-wrap {
    margin-right: calc(100vw * (21) / $xs * -1);
    .collection {
      min-width: calc(100vw * (160) / $xs);
      margin-right: calc(100vw * (20) / $xs);
      &:last-child {
        margin-right: 0;
      }
      .add-btn {
        position: absolute;
        bottom: -2.1rem;
        right: 14px;
        padding: 4px;
        background-color: #B1B5C3;
      }
    }
    .collection-bg {
      height: calc(100vw * (100) / $xs);
    }
    .collection-character {
      width: calc(100vw * (64) / $xs);
      height: calc(100vw * (64) / $xs);
      margin-top: calc((100vw * (64) / $xs) / 2 * -1);
    }

    @include md() {
      margin-right: 0;
      .collection {
        min-width: 24%;
        margin-right: 1%;
        &:last-child {
          margin-right: 0;
        }
      }
      .collection-bg {
        height: calc(100vw * (155) / $md);
      }
      .collection-character {
        width: calc(100vw * (100) / $xl);
        height: calc(100vw * (100) / $xl);
        margin-top: calc((100vw * (100) / $xl) / 2 * -1);
      }
    }

    @include xl() {
      .collection {
        min-width: calc(100vw * (247) / $xl);
        margin-right: calc(100vw * (30) / $xl);
        &:last-child {
          margin-right: 0;
        }
      }
      .collection-bg {
        height: calc(100vw * (155) / $xl);
      }
      .collection-character {
        width: calc(100vw * (100) / $xl);
        height: calc(100vw * (100) / $xl);
        margin-top: calc((100vw * (100) / $xl) / 2 * -1);
      }
    }
  }
}
