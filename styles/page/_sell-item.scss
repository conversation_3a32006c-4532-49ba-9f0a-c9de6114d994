.sell-item-container {
  margin-top: 42px;

  .sell-item-flex {
    @apply flex items-start flex-col-reverse md2:flex-row;
    gap: 30px;

    .sell-item-left {
      flex: 0 0 100%;
      max-width: 100%;
    }
    .sell-item-right {
      @apply w-full mt-4 md2:mt-0;
    }

  }

  @include md2() {
    .sell-item-flex {
      @apply flex-nowrap;
      gap: 0 30px;

      .sell-item-left {
        flex: 0 0 50%;
        max-width: 50%;
      }
      &.generator {
        .sell-item-right {
          @apply pl-7;
        }
      }
    }
  }

  @include xl() {
    padding-left: 77px;

    .sell-item-flex {
      gap: 0 77px;
      .sell-item-right {
        padding-right: 91px;
      }
    }
  }
}
