.recommend-section {
  .swiper .swiper-wrapper {
    padding-bottom: calc(100vw * (60) / $md);
  }
  .swiper-pagination {
    bottom: 0 !important;
    .swiper-pagination-bullet-active {
      @apply bg-green-1;
    }
    .swiper-pagination-bullet {
      margin: 0 7px;
    }
  }
  .recommend-card {
    .card-image {
      height: 14vw;
      @media (max-width: 767px) {
        height: calc(100vw * (392) / $xl);
      }
    }
    .card-content {
      max-height: calc(100vw * (202) / $sm);
    }
    .card-button {
      line-height: 20px;
      padding: calc(100vw * (15) / $xl) calc(100vw * (31) / $xl);
    }
  }

  .recommend-slide {
    @apply max-xs:h-[200px]
  }

  @include lg() {
    .swiper .swiper-wrapper {
      padding-bottom: calc(100vw * (50) / $lg);
    }
  }
  @include xl() {
    .swiper .swiper-wrapper {
      padding-bottom: calc(100vw * (50) / $xl);
    }
    .recommend-card {
      .card-content {
        max-height: calc(100vw * (202) / $xl);
      }
    }
  }
}
