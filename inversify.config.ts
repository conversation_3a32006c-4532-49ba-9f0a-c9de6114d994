import { Container } from 'inversify'
import TYPES from 'types'

import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
import ApiClient from 'data/datasources/remote/api_client'

import LocalStorageDatasource from 'data/datasources/local/local_storage_datasource'
import MainBannerDatasource from './data/datasources/interfaces/main_banner_datasource'
import MainBannerMockDatasource from './data/datasources/mock/main_banner_mock_datasource'

const container = new Container()

// StorageDatasource
container.bind<StorageDatasource>(TYPES.StorageDatasource).to(LocalStorageDatasource)

// ApiClient
const baseApiURL = process.env.NEXT_PUBLIC_API_URL ?? 'https://internal-api.nft-land.me'
container.bind<ApiClient>(TYPES.ApiClient).toConstantValue(
  new ApiClient(baseApiURL)
)

// Mock
container.bind<MainBannerDatasource>(TYPES.MainBannerDatasource).toConstantValue(
  new MainBannerMockDatasource()
)

export default container
