{"name": "@t2-web/nft_land_client", "version": "2.1.19", "private": true, "engines": {"node": "20.x"}, "scripts": {"dev": "next dev", "build": "next build", "build-static": "next build && next export", "start": "next start -p 8080", "stg": "next start -p 8081", "lint": "next lint", "lint:fix": "next lint --fix", "test": "jest --runInBand", "eslint:fix": "eslint --fix --ext .ts,.tsx .", "fixes": "yarn lint:fix && yarn eslint:fix"}, "dependencies": {"@ant-design/charts": "1.4.2", "@metamask/detect-provider": "^2.0.0", "@metamask/onboarding": "^1.0.1", "@react-three/drei": "^10.7.4", "@react-three/fiber": "^9.3.0", "@t2-web/nft_land_js": "^2.1.19", "@tanstack/react-query": "^5.56.2", "@truffle/hdwallet-provider": "^2.1.15", "@web3modal/wagmi": "^5.1.8", "array-move": "^4.0.0", "axios": "^1.6.7", "canvas-confetti": "^1.9.3", "clsx": "^1.2.1", "crypto-js": "^4.2.0", "ethers": "^5.7.2", "events": "^3.3.0", "file-saver": "^2.0.5", "framer-motion": "^12.23.12", "hardhat": "^2.9.1", "husky": "^8.0.3", "inversify": "^6.0.1", "luxon": "^3.4.4", "next": "14.2.0", "next-themes": "^0.2.1", "nprogress": "^0.2.0", "react": "^18.2.0", "react-date-range": "^1.4.0", "react-datepicker": "^4.8.0", "react-dom": "^18.2.0", "react-easy-sort": "^1.6.0", "react-toastify": "^9.1.1", "reactflow": "^11.4.0", "reflect-metadata": "^0.2.1", "sass": "^1.72.0", "singularity-init": "^1.0.3", "swiper": "^8.4.4", "three": "^0.179.1", "viem": "^2.21.7", "wagmi": "^2.12.11", "web3": "^4.6.0", "zustand": "^4.5.2"}, "devDependencies": {"@babel/core": "^7.24.0", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@types/canvas-confetti": "^1.9.0", "@types/crypto-js": "^4.2.2", "@types/file-saver": "^2.0.7", "@types/jest": "^29.5.12", "@types/luxon": "^3.4.2", "@types/ngprogress": "^1.0.32", "@types/node": "^20.11.27", "@types/nprogress": "^0.2.0", "@types/react": "^18.2.66", "@types/react-date-range": "^1.4.9", "@types/react-datepicker": "^6.2.0", "@types/react-dom": "^18.2.22", "autoprefixer": "^10.4.18", "axios-mock-adapter": "^1.22.0", "babel-loader": "^9.1.3", "eslint": "^8.25.0", "eslint-config-next": "12.3.1", "eth-testing": "^1.14.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "5.1.*"}, "resolutions": {"strip-ansi": "6.0.1", "ansi-regex": "5.0.1", "ansi-styles": "5.2.0", "chalk": "4.1.2", "supports-color": "8.1.1", "color-convert": "2.0.1", "color-name": "1.1.4", "wrap-ansi": "7.0.0", "is-arrayish": "0.3.2", "error-ex": "1.3.2", "color-string": "1.9.1", "has-ansi": "2.0.0", "debug": "4.3.4"}}