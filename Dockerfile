ARG NODE_VERSION=20
ARG ENV=prod

FROM node:${NODE_VERSION}-alpine AS base
RUN mkdir -p /home/<USER>/app \
    && chown node:node /home/<USER>/app
WORKDIR /home/<USER>/app

FROM base AS builder
RUN apk update && \
    apk upgrade && \
    apk add --no-cache git python3 py3-pip make g++
COPY package.json ./
COPY yarn.lock ./
RUN yarn install
COPY --chown=node:node . .
RUN yarn run build

FROM base AS production
COPY --from=builder --chown=node:node /home/<USER>/app/.next/standalone ./
COPY --from=builder --chown=node:node /home/<USER>/app/public ./public
COPY --from=builder --chown=node:node /home/<USER>/app/.next/static ./.next/static
EXPOSE 8080
CMD PORT='8080' HOSTNAME='0.0.0.0' node server.js
