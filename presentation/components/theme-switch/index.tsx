import { useState, useEffect } from 'react'
import { useTheme } from 'next-themes'
import Switch from 'presentation/components/switch'

const ThemeSwitch = () => {
  const [mounted, setMounted] = useState(false)
  const { theme, setTheme } = useTheme()

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return null
  }
  return (
    <Switch className="ml-2"
      defaultActive={theme === 'dark'}
      onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
    />
  )
}

export default ThemeSwitch
