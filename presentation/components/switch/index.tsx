import clsx from 'clsx'
import { useState, useEffect } from 'react'

type Props = {
  value?: boolean
  defaultActive?: boolean,
  disableColor?: string,
  enableColor?: string,
  disableColorCircle?: string,
  enableColorCircle?: string,
  size?: 'sm' | 'md' | 'lg',
  className?: string,
  onClick: (active: boolean) => void,
  loading?: boolean,
}

const Switch = ({ 
  value,
  defaultActive = false,
  disableColor = 'border-gray-3',
  enableColor = 'border-primary-2',
  disableColorCircle = 'bg-gray-3',
  enableColorCircle = 'bg-primary-2',
  size = 'sm',
  className,
  onClick,
  loading = false,
}: Props) => {

  const [toggleActive, setToggleActive] = useState(defaultActive)

  useEffect(() => {
    if (typeof value === 'boolean') {
      setToggleActive(value)
    }
  }, [value])

  const handleToggleActive = async () => {
    if (loading) return // Prevent clicks while loading

    const nextValue = !toggleActive

    // For uncontrolled mode only update local state
    if (typeof value !== 'boolean') {
      setToggleActive(nextValue)
    }

    await onClick(nextValue)
  }

  const bgClass = toggleActive ? enableColor : disableColor
  const circleClass = toggleActive ? enableColorCircle : disableColorCircle
  const contSizeClass = {
    sm: 'w-[31px] h-[18px]',
    md: 'w-[43px] h-[24px]',
    lg: 'w-[46px] h-[26px]',
  }
  const switchSizeClass = {
    sm: 'w-[14px] h-[14px]',
    md: 'w-[20px] h-[20px]',
    lg: 'w-[22px] h-[22px]',
  }

  return (
    <div className={
      clsx('relative rounded-full cursor-pointer transition border shrink-0', bgClass, contSizeClass[size as keyof Object], className)}
    onClick={handleToggleActive}
    >
      <span className={
        clsx('absolute top-1/2 -translate-y-1/2 rounded-full transition-transform',
          toggleActive ? 'translate-x-full' : 'translate-x-[1px]',
          circleClass,
          switchSizeClass[size as keyof Object]
        )}
      />
      {loading && (
        <div className={clsx('absolute left-[1px] right-[1px] inset-0 flex items-center ', 
          toggleActive ? 'justify-end' : 'justify-start',)}
        >
          <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin" />
        </div>
      )}
    </div>
  )
}

Switch.displayName = 'Switch'

export default Switch
