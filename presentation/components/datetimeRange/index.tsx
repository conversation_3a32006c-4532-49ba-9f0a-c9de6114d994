import { useRef, useState, useEffect } from 'react'
import DatePicker from 'react-datepicker'
import { durationDateOptions } from 'presentation/controllers/mockData/options'
import { formatDate, validateDate } from 'lib/utils'
import Select from 'presentation/components/select'
import Input from 'presentation/components/input'
import SvgIcon from 'presentation/components/svg-icon'
import 'react-datepicker/dist/react-datepicker.css'
import Timepicker from 'presentation/components/timepicker'
import clsx from 'clsx'
import { DateTime } from 'luxon'

const DatetimeRange = (props: any) => {
  const {
    onCloseDateRange,
    showTimeInput = true,
    showDuration = true,
    onSelect = () => {},
    onSelected = () => {},
    inline = false,
    showCloseButton = false,
    onChangeStartDatetime = () => {},
    onChangeEndDatetime = () => {},
    className = '',
    classNameTopInput = '',
    onlyOneDatepicker = false,
  } = props

  const dateRangeRef = useRef<HTMLInputElement>(null)
  const [isModalOpen, setIsModalOpen] = useState(true)
  const [startDate, setStartDate] = useState(new Date())
  const [endDate, setEndDate] = useState<any>(
    onlyOneDatepicker ? null : new Date()
  )
  const [startDateInput, setStartDateInput] = useState(formatDate(startDate))
  const [endDateInput, setEndDateInput] = useState<any>(
    endDate ? formatDate(endDate) : ''
  )
  const [startTime, setStartTime] = useState('00:00')
  const [endTime, setEndTime] = useState('00:00')
  const [duration, setDuration] = useState(0)

  const changeStartDate = (date: Date) => {
    setStartDate(date)
  }
  const changeEndDate = (date: Date) => {
    setEndDate(date)
  }

  const onChangeRange = (dates: [Date, Date]) => {
    const [start, end] = dates
    if (start) {
      setStartDate(start)
    }
    if (end) {
      setEndDate(end)
    }
  }

  const resetEndDate = (duration: any) => {
    const newEndDate = new Date()
    const durationInTime = (duration - 1) * 24 * 60 * 60 * 1000
    newEndDate.setTime(startDate.getTime() + durationInTime)
    setEndDate(newEndDate)
  }

  useEffect(() => {
    if (!onlyOneDatepicker) {
      if (!showDuration) {
        if (endDate.getTime() < startDate.getTime()) {
          setEndDate(startDate)
        }
      } else {
        if (duration > 0) {
          if (
            endDate.getTime() < startDate.getTime() ||
            endDate.getTime() === startDate.getTime()
          ) {
            resetEndDate(duration)
          } else {
            const diffTime = Math.abs(endDate.getTime() - startDate.getTime())
            const durationInTime = (duration - 1) * 24 * 60 * 60 * 1000
            if (diffTime !== durationInTime) {
              resetEndDate(duration)
            }
          }
        }
      }
    }
    if (startDate) {
      setStartDateInput(formatDate(startDate))
    }
    if (endDate) {
      setEndDateInput(formatDate(endDate))
    }
  }, [startDate, endDate])

  useEffect(() => {
    if (onlyOneDatepicker) {
      setEndDate(null)
      setEndDateInput('')
    }
  }, [startDate])

  useEffect(() => {
    if (startDate) {
      onChangeStartDatetime(
        DateTime.fromFormat(
          `${formatDate(startDate, true)} ${startTime}`,
          'yyyy-MM-dd HH:mm'
        )
      )
    }
    if (endDate) {
      onChangeEndDatetime(
        DateTime.fromFormat(
          `${formatDate(endDate, true)} ${endTime}`,
          'yyyy-MM-dd HH:mm'
        )
      )
    }
  }, [startDateInput, endDateInput, startTime, endTime])

  useEffect(() => {
    if (showDuration && duration > 0) {
      resetEndDate(duration)
    }
  }, [duration])

  useEffect(() => {
    if (showDuration) {
      setDuration(durationDateOptions[0].value)
      resetEndDate(durationDateOptions[0].value)
    }
  }, [])

  useEffect(() => {
    const checkIfClickedOutside = (e: any) => {
      if (
        isModalOpen &&
        dateRangeRef.current &&
        !dateRangeRef.current?.contains(e.target)
      ) {
        setIsModalOpen(false)
        if (!showDuration) {
          onCloseDateRange()
        }
      }
    }

    document.addEventListener('mousedown', checkIfClickedOutside)
    return () => {
      document.removeEventListener('mousedown', checkIfClickedOutside)
    }
  }, [isModalOpen])

  return (
    <div
      ref={dateRangeRef}
      className={clsx(
        'duration-daterange',
        inline && 'duration-inline',
        className
      )}
    >
      {(!inline || showCloseButton) && (
        <SvgIcon
          onClick={onCloseDateRange}
          name="plus"
          className={clsx(
            'w-6 h-6 stroke-none fill-gray-1 rotate-45',
            'absolute top-0 right-0 translate-y-1/2 -translate-x-1/2 cursor-pointer'
          )}
        />
      )}
      {showDuration && (
        <div className="px-5 pt-5">
          <p className="font-medium text-dark-primary dark:text-white mb-3">
            Date Range
          </p>

          <Select
            options={durationDateOptions}
            rounded
            arrowIconClass="stroke-none fill-primary-2"
            className="w-44"
            customWidth={true}
            onSelect={(option) => {
              setDuration(option.value)
              onSelect(option)
            }}
          />
        </div>
      )}
      <div
        className={clsx(
          'flex flex-col min-h-[300px] sm:w-auto w-full',
          !inline && 'mt-5',
        )}
      >
        <div
          className={clsx(
            !inline &&
              `sm:inline-flex sm:flex-nowrap space-x-3 ${
                !showDuration ? 'sm:justify-center' : 'pl-4'
              }`,
            inline && 'py-4 px-5',
            'flex flex-wrap items-end justify-start',
            classNameTopInput
          )}
        >
          <div
            className={`flex flex-col ${
              inline ? 'sm:w-4/5 w-full' : 'sm:pl-0 pl-3'
            }`}
          >
            <p className="font-medium text-dark-primary dark:text-white mb-3">
              Starting
            </p>
            <div
              className={clsx(
                'xs3:inline-flex xs3:flex-row flex flex-col',
                inline && 'sm:mb-4 xs3:space-x-4 xs3:space-y-0 space-y-4'
              )}
            >
              {!onlyOneDatepicker && (
                <Input
                  width="w-[147px]"
                  value={startDateInput}
                  onBlur={(e) => {
                    if (validateDate(e.target?.value)) {
                      const [day, month, year] = (
                        e.target?.value || '0/0/0'
                      ).split('/')
                      changeStartDate(new Date(year, parseInt(month) - 1, day))
                    } else {
                      setStartDateInput('')
                      setTimeout(() => {
                        setStartDateInput(formatDate(startDate))
                      }, 200)
                    }
                  }}
                />
              )}
              {inline && showTimeInput && (
                <Timepicker
                  onSelect={(value) =>
                    setStartTime(`${value.hour}:${value.minute}`)
                  }
                />
              )}
            </div>
          </div>
          {!onlyOneDatepicker && (
            <div
              className={`px-3 sm:py-3 pt-2 ${
                inline ? 'w-1/5 sm:mb-4' : 'sm:w-auto w-full sm:mb-8'
              }`}
            >
              {inline && <span className="sm:py-4"></span>}
              <span>━</span>
            </div>
          )}
          <div className={`flex flex-col ${inline ? 'sm:w-4/5 w-full' : ''}`}>
            <p className="font-medium text-dark-primary dark:text-white mb-3">
              Ending
            </p>
            <div
              className={clsx(
                'xs3:inline-flex xs3:flex-row flex flex-col',
                inline && 'mb-1.5 xs3:space-x-4 xs3:space-y-0 space-y-4'
              )}
            >
              {!onlyOneDatepicker && (
                <Input
                  width="w-[147px]"
                  value={endDateInput}
                  onBlur={(e) => {
                    if (validateDate(e.target?.value)) {
                      const [day, month, year] = (
                        e.target?.value || '0/0/0'
                      ).split('/')
                      const newEndDate = new Date(
                        year,
                        parseInt(month) - 1,
                        day
                      )
                      if (showDuration && duration > 0) {
                        const diffTime = Math.abs(
                          newEndDate.getTime() - startDate.getTime()
                        )
                        const durationInTime =
                          (duration - 1) * 24 * 60 * 60 * 1000
                        if (diffTime === durationInTime) {
                          changeEndDate(newEndDate)
                        } else {
                          setEndDateInput('')
                          setTimeout(() => {
                            setEndDateInput(formatDate(endDate))
                          }, 200)
                        }
                      } else {
                        changeEndDate(newEndDate)
                      }
                    } else {
                      setEndDateInput('')
                      setTimeout(() => {
                        setEndDateInput(formatDate(endDate))
                      }, 200)
                    }
                  }}
                />
              )}
              {inline && showTimeInput && (
                <Timepicker
                  onSelect={(value) =>
                    setEndTime(`${value.hour}:${value.minute}`)
                  }
                />
              )}
            </div>
          </div>
        </div>
        {onlyOneDatepicker ? (
          <div>
            <DatePicker
              selected={startDate}
              onChange={onChangeRange}
              startDate={startDate}
              endDate={endDate}
              selectsRange
              inline
            />
          </div>
        ) : (
          <div
            className={clsx(
              'sm:inline-flex sm:flex-nowrap flex flex-wrap',
              showDuration && 'datetime-picker-duration-wrapper',
              !inline && 'mt-5'
            )}
          >
            <div>
              <DatePicker
                dateFormat="DD/MM/YYYY"
                selected={startDate}
                onChange={changeStartDate}
                selectsStart
                startDate={startDate}
                endDate={endDate}
                inline
                dayClassName={(date) =>
                  date.getTime() > endDate.getTime() ? 'disabled' : ''
                }
              />
            </div>
            <div>
              <DatePicker
                dateFormat="DD/MM/YYYY"
                selected={endDate}
                onChange={changeEndDate}
                selectsEnd
                startDate={startDate}
                endDate={endDate}
                minDate={startDate}
                inline
                dayClassName={(date) => {
                  let d = new Date()
                  d.setDate(startDate.getDate() - 1)
                  return date.getTime() < d.getTime() ? 'disabled' : ''
                }}
              />
            </div>
          </div>
        )}

        {!inline && showTimeInput && (
          <div
            className={clsx(
              'xs3:inline-flex xs3:flex-nowrap xs3:space-x-3 flex flex-wrap items-end',
              !showDuration ? 'xs3:justify-center' : 'pl-5',
              'justify-start mb-6'
            )}
          >
            <Timepicker
              position="top"
              onSelect={(value) =>
                setStartTime(`${value.hour}:${value.minute}`)
              }
            />
            <span className="px-3 py-3 xs3:w-auto w-full">━</span>
            <Timepicker
              position="top"
              onSelect={(value) => setEndTime(`${value.hour}:${value.minute}`)}
            />
          </div>
        )}
      </div>
    </div>
  )
}

export default DatetimeRange
