import Container from 'presentation/components/container'
import eventBus from 'lib/eventBus'

import { useEffect } from 'react'
const RequireConnect = ({className, innerClassName}: {className?: string, innerClassName?: string}) => {

  const openWalletConnectModal = () => {
    eventBus.emit('openWalletConnectModal', true)
  }
  useEffect(() => {
    const reloadPage = (params: any) => {
    
      window.location.reload()
    }

    eventBus.on('ConnectRequire-ReloadPage', reloadPage)

    // Cleanup listener on component unmount
    return () => {
      eventBus.off('ConnectRequire-ReloadPage', reloadPage)
    }
  }, [])
  return (
    <Container className={`pt-9 md:pb-0 pb-9 ${className}`} >
      <div  className={`w-full flex justify-center items-center py-24 ${innerClassName || ''}`}>
        <span onClick={() => openWalletConnectModal()} className="bg-gradient-to-b from-red-700 to-red-1 text-base text-white rounded-full font-bold px-9 py-4 cursor-pointer hover:opacity-70">
          Please connect to a wallet
        </span>
      </div>
    </Container>
  )
}

export default RequireConnect
