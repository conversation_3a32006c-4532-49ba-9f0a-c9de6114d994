import BadgeText from 'presentation/components/badge/badgeText'
import SvgIcon from 'presentation/components/svg-icon'
import Image from 'next/image'

type Props = {
  imageSrc: string,
  imageAlt?: string,
  percentage?: number,
  closeable?: boolean,
  name?: string,
  onClick?: () => void,
  onClose?: () => void,
}

const ProductGenerator = (props: Props) => {
  const { imageSrc, imageAlt = '', percentage, closeable = false, name, onClick, onClose } = props

  const handleClose = (e: any) => {
    e.preventDefault()
    onClose?.()
  }

  return (
    <div className="w-full text-center generator-product">
      {percentage && (
        <p className="mb-1">
          <BadgeText size="base" rounded="rounded-25" color="black">{percentage} %</BadgeText>
        </p>
      )}
      <div
        className={`relative w-full pt-[100%] rounded-20 bg-white border border-white hover:shadow-lg transition-shadow duration-150 cursor-pointer
        ${closeable && 'closeable-product'}`}
        onClick={onClick}
      >
        <Image alt={imageAlt} fill priority style={{ objectFit: 'contain' }} src={imageSrc} />
        {closeable && (
          <SvgIcon
            name="plusCircle"
            className="icon-22 stroke-none fill-primary-2 absolute top-1 right-1 cursor-pointer close-icon rotate-45"
            onClick={handleClose}
          />
        )}
      </div>
      {name && (
        <p className="mt-1 font-medium max-w-full truncate">{name}</p>
      )}
    </div>
  )
}

export default ProductGenerator
