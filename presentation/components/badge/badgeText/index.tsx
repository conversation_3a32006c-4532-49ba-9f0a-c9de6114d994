import clsx from 'clsx'
import { ReactNode } from 'react'

type Props = {
  color?: string,
  size?: 'xs' | 'sm' | 'base' | 'lg',
  rounded?: string,
  className?: string,
  children?: ReactNode,
  onClick?: (e: any) => void,
}

const BadgeText = (props: Props) => {
  const { color = 'primary-2', size = 'sm', rounded = 'rounded-10', className, children, onClick } = props

  return (
    <span
      className={clsx(`${rounded} border border-${color} text-${color} text-${size} 
        font-medium px-1 py-[2px] text-center`, className)} 
      onClick={onClick}
    >
      {children}
    </span>
  )
}

export default BadgeText
