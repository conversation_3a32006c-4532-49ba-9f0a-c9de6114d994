import clsx from 'clsx'
import SvgIcon from '../svg-icon'

type Props = {
  text: string | number,
  icon: string,
  className?: string,
  iconClassName?: string,
  viewBox?: string,
}

const Badge = (props: Props) => {
  return (
    <div className={clsx('rounded-full bg-gradient-to-b from-gradient-sky to-gradient-ocean', props.className)}>
      <SvgIcon name={props.icon} className={props.iconClassName} viewBox={props.viewBox} />
      <span className="align-middle">{props.text}</span>
    </div>
  )
}

export default Badge
