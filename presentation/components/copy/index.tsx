import { forwardRef, ReactNode, useState, RefObject, MutableRefObject } from 'react'
import SvgIcon from 'presentation/components/svg-icon'

type Props = {
  children: ReactNode,
  clickIcon?: boolean,
  copyContent?: string
}
const Copy = forwardRef<HTMLElement, Props>((props: Props, ref: any) => {
  const [isCopied, setIsCopied] = useState(false)

  const copyText = (e: any) => {
    let copyValue
    if (props?.copyContent) {
      copyValue = props?.copyContent

      navigator.clipboard.writeText(copyValue)
      setIsCopied(true)

      setTimeout(() => {
        setIsCopied(false)
      }, 1300)
    } else { 
      if (ref?.current) {
        copyValue = ref.current.value || ref.current.innerHTML

        navigator.clipboard.writeText(copyValue)
        setIsCopied(true)

        setTimeout(() => {
          setIsCopied(false)
        }, 1300)
      }
    }
   
  }

  return (
    <div className="relative">
      {isCopied && (
        <p className="rounded-10 px-3 py-2 text-sm text-white font-medium absolute top-[3px] right-0 -translate-y-full bg-dark-primary">
          Copied
        </p>
      )}
      <a onClick={copyText} className="cursor-pointer">{props.children}</a>
      {props.clickIcon && (
        <SvgIcon
          name="copy"
          className="icon-22 stroke-none fill-primary-2 cursor-pointer absolute top-[3px] right-0 -translate-x-1/2 translate-y-1/2"
          onClick={copyText}
        />
      )}
    </div>
  )
})

Copy.displayName = 'Copy'

export default Copy
