import clsx from 'clsx'
import Link from 'next/link'
import { ReactNode } from 'react'

type Props = {
  className?: string;
  style?: React.CSSProperties;
  children?: ReactNode;
  size?: string;
  variant?: string;
  href?: string | { pathname: string; query: any };
  htmltype?: string;
  disable?: boolean;
  target?: string;
  type?: 'button' | 'submit' | 'reset';
  onClick?: (e: any) => void;
};

type Variant = {
  light: string;
  dark: string;
  red: string;
  gray4: string;
  outlineDark: string;
  outlineBlack: string;
};

type Size = {
  xs: string;
  sm: string;
  base: string;
  md: string;
  long: string;
};

const baseClass: string = 'rounded-full font-bold'

const variantStyle: Variant = {
  light:
    'bg-gradient-primary-1-opacity-10 text-primary-2 dark:text-white dark-bg-gradient-primary-1',
  dark: 'bg-gradient-primary-1 text-white',
  red: 'text-red-1 bg-gradient-red-opacity-10',
  gray4: 'dark:text-dark-primary bg-gray-4',
  outlineDark: 'border border-primary-1 bg-white text-primary-1',
  outlineBlack: 'border border-black bg-white text-black',
}

const variantSize: Size = {
  xs: 'btn-xs',
  sm: 'btn-sm',
  base: 'btn-base text-base',
  md: 'text-lg px-6 py-3',
  long: 'py-3 px-8',
}

const Button = ({
  children,
  onClick,
  href,
  variant = 'light',
  size = 'base',
  disable = false,
  type = 'submit',
  className,
  style,
  ...props
}: Props) => {
  return href ? (
    <Link href={href} 
      className={clsx(
        'inline-block text-center',
        baseClass,
        variantStyle[variant as keyof Variant],
        variantSize[size as keyof Size],
        className,
        disable ? 'cursor-not-allowed opacity-30' : 'cursor-pointer'
      )}
      {...props}
    >
      {children} 
    </Link>
  ) : (
    <button
      type={type}
      style={style}
      className={clsx(
        baseClass,
        variantStyle[variant as keyof Variant],
        variantSize[size as keyof Size],
        className,
        disable ? 'cursor-not-allowed opacity-30' : 'cursor-pointer'
      )}
      onClick={onClick}
      {...props}
      disabled={disable}
    >
      {children}
    </button>
  )
}

export default Button
