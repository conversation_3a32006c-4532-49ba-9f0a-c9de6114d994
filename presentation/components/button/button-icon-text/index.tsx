import clsx from 'clsx'
import { ReactNode } from 'react'
import SvgIcon from 'presentation/components/svg-icon'

type Props = {
  className?: string;
  iconClassName?: string;
  variant?: string;
  href?: string | { pathname: string; query: any };
  target?: string;
  svgIcon: string;
  children?: ReactNode;
  onClick?: (e: any) => void;
  onClickIcon?: (e: any) => void;
};

type Variant = {
  light: string;
  dark: string;
  red: string;
};

const variantStyle: Variant = {
  light:
    'bg-gradient-primary-1-opacity-10 text-primary-2 dark:text-white dark-bg-gradient-primary-1 hover:bg-gradient-primary-1-opacity-50 hover:opacity-90',
  dark: 'bg-gradient-primary-1 text-white hover:opacity-90',
  red: 'text-red-1 bg-gradient-red-opacity-10',
}

const ButtonIconText = ({
  className,
  iconClassName,
  variant = 'light',
  svgIcon,
  children,
  onClick,
  onClickIcon,
}: Props) => {
  const baseClass = 'rounded-l-full font-bold transition-all cursor-pointer btn-base text-base border-r-[1px] border-white'
  const iconBaseClass =
    'rounded-r-full font-bold transition-all cursor-pointer min-h-[50px] py-3'

  return (
    <>
      <button
        className={clsx(
          baseClass,
          variantStyle[variant as keyof Variant],
          className
        )}
        onClick={onClick}
      >
        {children}
      </button>
      <button
        className={clsx(
          iconBaseClass,
          iconClassName,
          variantStyle[variant as keyof Variant],
          'shadow-normal'
        )}
        onClick={onClickIcon}
      >
        <SvgIcon
          viewBox="0 0 28.268 29.899"
          name={svgIcon}
          className='w-6 h-6 fill-current dark:text-white -ml-2'
          strokeWidth="2"
        />
      </button>
    </>
  )
}

export default ButtonIconText
