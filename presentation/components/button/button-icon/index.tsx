import { ReactNode } from 'react'
import clsx from 'clsx'
import Link from 'next/link'
import SvgIcon from 'presentation/components/svg-icon'

type Props = {
  svgIcon?: string;
  iconClassName?: string;
  iconViewbox?: string;
  href?: string;
  className?: string;
  children?: ReactNode;
  hasShadow?: boolean;
  strokeWidth?: string;
  onClick?: (e: any) => void;
};

const ButtonIcon = ({
  svgIcon,
  iconClassName,
  iconViewbox,
  href,
  className,
  children,
  hasShadow = true,
  strokeWidth,
  onClick,
}: Props) => {
  const baseClass =
    'p-2 h-fit rounded-full leading-none dark:bg-blue-1 hover:scale-110 transition-all cursor-pointer'

  return href ? (
    <Link
      href={href}
      className={clsx(
        'inline-block',
        hasShadow && 'shadow-normal',
        baseClass,
        className
      )}
      target="_blank"
    >
      {svgIcon && (
        <SvgIcon
          viewBox={iconViewbox}
          name={svgIcon}
          className={iconClassName}
          strokeWidth={strokeWidth}
        />
      )}
      {children}
    </Link>
  ) : (
    <button
      className={clsx(baseClass, className, hasShadow && 'shadow-normal')}
      onClick={onClick}
    >
      {svgIcon && (
        <SvgIcon
          viewBox={iconViewbox}
          name={svgIcon}
          className={iconClassName}
          strokeWidth={strokeWidth}
        />
      )}
      {children}
    </button>
  )
}

export default ButtonIcon
