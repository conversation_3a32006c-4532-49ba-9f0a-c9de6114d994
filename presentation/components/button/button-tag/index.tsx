import clsx from 'clsx'
import { ReactNode, forwardRef } from 'react'

type Props = {
  className?: string,
  shadow?: string,
  background?: string,
  round?: string,
  children: ReactNode,
  active?: boolean,
  activeClass?: string,
  onClick?: (e: any) => void,
}

const ButtonTag = ({
  className,
  shadow = 'shadow-normal',
  background = 'bg-white',
  round = 'rounded-[30px]',
  children,
  active = false,
  activeClass = 'text-primary-2 dark:text-primary-2 active',
  onClick }: Props, ref: any) => {

  const baseClass = `px-3 py-1 xl:px-4 xl:py-[6px] h-fit font-medium text-dark-primary
  dark:text-dark-primary dark:hover:text-primary-2 hover:text-primary-2 transition-colors`

  return (
    <button
      onClick={onClick} ref={ref}
      className={clsx(baseClass, shadow, background, round, className, active && activeClass)}>
      {children}
    </button>
  )
}

const ForwardedButtonTag = forwardRef(ButtonTag)

export default ForwardedButtonTag
