import Head from 'next/head'
import { ReactNode } from 'react'

interface LayoutProps {
  children: ReactNode;
}

const Layout = ({ children }: LayoutProps) => {
  return (
    <>
      <Head>
        <title>NFT Marketplace - Quill</title>
        <meta name="description" content="Quill is a versatile NFT marketplace where you can buy, sell, and explore unique digital assets. Enjoy seamless transactions and a user-friendly platform for all your NFT needs." />
        <meta name="viewport" content="initial-scale=1, width=device-width" />

        <meta property="og:title" content="NFT Marketplace - Quill"/>
        <meta property="og:type" content="website"/>
        <meta property="og:url" content="https://apps.quillnft.net/"/>
        <meta property="og:image" content="https://apps.quillnft.net/ogp-img.png"/>
        <meta property="og:site_name" content="Quill"/>
        <meta property="og:description" content="Quill is a versatile NFT marketplace where you can buy, sell, and explore unique digital assets. Enjoy seamless transactions and a user-friendly platform for all your NFT needs."/>
        <meta property="og:locale" content="en_US"/>

        <link rel="icon" href="/favicon.ico" />
      </Head>
      <main>{children}</main>
    </>
  )
}

export default Layout
