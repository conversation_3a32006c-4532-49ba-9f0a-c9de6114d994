import Link from 'next/link'
import Image from 'next/image'
import SvgIcon from 'presentation/components/svg-icon'
import { FooterMenuLeft, FooterMenuRight } from 'lib/footer'
import {envConfig} from 'presentation/constant/env'
const Footer = () => {
  return (
    <footer className="footer-wrap container-df pt-8 md:pt-14 pb-8">
      <div className="md:flex md:flex-row justify-between">
        <div className="md:basis-1/3">
          <div className="flex items-center md:block">
            <Link href="/" className="logo block relative">
              {/* <SvgIcon
                name="logo"
                viewBox="0 0 47.955 41.043"
                className="w-full h-full light-fill-black dark-fill-white stroke-none"
              /> */}
              <Image width={2090} height={900} src="/asset/images/quill.png" alt="logo" className="" />
            </Link>
            {/* <h2 className="md:mt-7 ml-[30px] md:ml-0 text-2xl font-heading">
              Quill
            </h2> */}
          </div>
          <p className="mt-6">
            {/* The world’s first and largest digital marketplace for crypto
            collectibles and non-fungible tokens (NFTs). Buy, sell, and discover
            exclusive digital items. */}
            Quill is a next-generation NFT marketplace.<br/>
            Easily trade NFTs and create collections with just a few steps.<br/>
            Enjoy a seamless NFT experience with favorites and offer management features!
          </p>
        </div>

        <div className="footer-right mt-8 md:mt-0">
          <div className="flex gap-14 md:gap-4">
            <div className="col-1 w-1/2">
              <h3 className="text-lg">
                <b>Marketplace</b>
              </h3>
              <ul className="mt-5">
                {FooterMenuLeft.map((item, index) => (
                  <li key={index}>
                    <Link
                      href={item.link}
                      className="block hover:text-primary-2"
                    >
                      {item.title}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
            <div className="col-2 w-1/2">
              <h3 className="text-lg">
                <b>My Account</b>
              </h3>
              <ul className="mt-5">
                {FooterMenuRight.map((item, index) => (
                  <li key={index}>
                    <Link
                      href={item.link}
                      className="block hover:text-primary-2"
                    >
                      {item.title}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
      <div className="mt-12 flex flex-col-reverse items-center md:flex-row md:justify-between">
        <p className="text-sm text-gray-1 dark:text-dark-blue mt-5 md:mt-0">
          ©2025 PLANZ / Version 1.1.1
        </p>
        <p className="text-gray-1 dark:text-dark-blue">
          <span>
            <a href={envConfig.privacyPolicyUrl}  target='_blank' rel="noreferrer" className="hover:text-primary-2">
              Privacy Policy
            </a>
          </span>
          <span className="mx-6">|</span>
          <span>
            <a href={envConfig.termsAndConditionsUrl} target='_blank' rel="noreferrer" className="hover:text-primary-2">
              Terms of Services
            </a>
          </span>
        </p>
      </div>
    </footer>
  )
}
export default Footer
