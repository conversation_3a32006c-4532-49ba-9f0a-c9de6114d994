import { watchAccount, watchChainId } from '@wagmi/core'
import { Query as AnnouncementQuery } from 'domain/repositories/announcement_repository/query'
import { Query as CategoryQuery } from 'domain/repositories/category_repository/query'
import Chain from 'domain/value_objects/chain'
import {
  cartItems,
  darkerBgPages,
  menu as menuRaw,
  noneBgImgPages,
  userNav,
} from 'lib/header'
import {
  MenuItemType,
} from 'lib/headerType'
import { useAuthentication } from 'lib/hook/auth_hook'
import { shortenAddress } from 'lib/utils'
import Image from 'next/image'
import Link from 'next/link'
import { useRouter } from 'next/router'
import eventBus from 'lib/eventBus'
import Dropdown from 'presentation/components/dropdown'
import DropdownHead from 'presentation/components/dropdown/dropdown-head'
import DropdownItem from 'presentation/components/dropdown/dropdown-item'
import DropdownWrap from 'presentation/components/dropdown/dropdown-wrap'
import SvgIcon from 'presentation/components/svg-icon'
import ThemeSwitch from 'presentation/components/theme-switch'
import Menu from 'presentation/page_components/global/menu'
import GlobalSearch from 'presentation/page_components/global/search'
import SelectWalletModal from 'presentation/page_components/global/selectWalletModal'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'
import { CartStateProvider } from 'presentation/providers/cart_state_provider'
import AnnouncementUseCase from 'presentation/use_cases/announcement_use_case'
import CategoryUseCase from 'presentation/use_cases/category_use_case'
import MeUseCase from 'presentation/use_cases/me_use_case'
import { useEffect, useState, useRef } from 'react'
import {  toast } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'
import { useAccount, useConfig } from 'wagmi'
import { envConfig } from 'presentation/constant/env'
import { NetworkStateProvider } from 'presentation/providers/network_state_provider'
import { useCustomQuery } from 'lib/hook/useCustomQuery'
const announcementUseCase = new AnnouncementUseCase()
const categoryUseCase = new CategoryUseCase()
const meUseCase = new MeUseCase()

const Header = (props: any) => {
  const initRef = useRef(false)
  const { offset = 0 } = props
  const [miniCartItems, setMiniCartItems] = useState(cartItems)
  const [menu, setMenu] = useState<MenuItemType[]>(menuRaw)
  // const [offset, setOffset] = useState(0)
  const [announcement, setAnnouncement] = useState<any>({})
  const [showSelectWallet, setShowSelectWallet] = useState(false)
  const notLightBgPages = noneBgImgPages.concat(darkerBgPages)
  const [showSearch, setShowSearch] = useState(false)
  const [isHomePage, setIsHomePage] = useState(true)
  const [isAnnouncementPage, setIsAnnouncementPage] = useState(false)
  const router = useRouter()
  const [chains, setChains] = useState<any[]>([])
  const [activeChain, setActiveChain] = useState<any>({})
  // Add state for connected network
  const [connectedNetwork, setConnectedNetwork] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [vendingMachineExpanded, setVendingMachineExpanded] = useState(false)
  const { logoutBE, loginBE, logout, disconnect } = useAuthentication()
  const config = useConfig()
  const { address, isConnected, chainId, connector } = useAccount()
  const authStateProvider = AuthStateProvider((state) => ({
    me: state.me,
    init: state.init,
  }))

  const cartStateProvider = CartStateProvider((state) => ({
    cart: state.cart,
    getCart: state.getCart,
    remove: state.remove,
  }))
  
  // Add network state provider
  const networkStateProvider = NetworkStateProvider((state) => ({
    network: state.network,
    setNetwork: state.setNetwork,
  }))

  const fetchAnnouncements = async () => {
    const query = new AnnouncementQuery({ limit: 1, page: 1 })
    announcementUseCase
      .search(query)
      .then((res) => {
        setAnnouncement(res[0])
      })
      .catch((_error) => {
        toast.error('Failed to fetch announcements')
      })
  }

  const { data: dataCategories = [] } = useCustomQuery(
    ['fetchCategories'],
    async () => {
      try {
        const query = new CategoryQuery()
        const res = await categoryUseCase.search(query)
        return res.map((element) => ({
          title: element.name,
          link: `/categories/${element.slug}`,
        }))
      } catch (error: any) {
        toast.error('Failed to fetch categories')
      }
    },
    { enabled: true, refetchOnWindowFocus: false }
  )

  const fetchLoginStatus =async () => {
    meUseCase
      .loginStatus()
      .then((res) => {
        if (res?.isLoggedIn) {
          authStateProvider.init()
          cartStateProvider.getCart()
        }
      })
      .catch((_error) => {
        toast.error('Failed to login')
      })
  }

  const fetchChains = async () => {
    const chainsData = Chain.getResourceArray().map((chain) => {
      return {
        ...chain,
        title: chain.label,
        link: '#',
        icon: `${chain.name}.png`,
        // onClick: async () => {
        //   switchNetwork(chain).then(() => {
        //     console.log('🚀 ~ switchNetwork ~ chain:', chain)
        //     setActiveChain(chain)
        //   })
        // },
      }
    })
    const all = {
      chainId:'',
      title: 'All',
      link: '#',
      icon: 'all.png',
    }
    const chains = [all, ...chainsData]
    
    // check local storage
    const savedNetwork = localStorage.getItem('selectedNetwork')
    if (savedNetwork) {
      try {
        const parsedNetwork = JSON.parse(savedNetwork)
        networkStateProvider.setNetwork(parsedNetwork)
      } catch (error) {
        console.error('Failed to parse saved network', error)
        networkStateProvider.setNetwork(chains[0])
      }
    } else if (!networkStateProvider.network || !networkStateProvider.network.chainId) {
      // only set active chain if not set
      networkStateProvider.setNetwork(chains[0])
    }
  
    setChains(chains)
  }

 
  const onSelectItem = (item: any) => {
    try {
      console.log('🚀 ~ onSelectItem ~ item:', item)
      setIsLoading(true)
      
      // Save selected network to context
      networkStateProvider.setNetwork(item)
      
      setIsLoading(false)
    } catch (error: any) {
      console.log('🚀 ~ onSelectItem ~ error:', error)
      toast.error(error?.message || error)
      setIsLoading(false)
    }
  }
  const processChangeAccount = async (account: any) => {
    await logoutBE()
    await loginBE(account.address || '')
    window.location.reload()
  }
  
  const openCreatePage = () => {
    router.push('/import-your-nft')
  }

  const gotoAdminPage = () => {
    router.push('/admin/import-nft')
  }

  const onLoginWithGoogle =async () => {
    meUseCase
      .loginWithGoogle()
      .catch((_error) => {
        toast.error('Failed to login')
      })
  }

  useEffect(() => {
    if(initRef.current) return
    fetchChains()
    fetchAnnouncements()
    fetchLoginStatus()
    initRef.current = true
    // const onScroll = () => setOffset(window.scrollY)
    // window.removeEventListener('scroll', onScroll)
    // window.addEventListener('scroll', onScroll, { passive: true })
    // return () => {
    //   window.removeEventListener('scroll', onScroll)
    // }
  }, [])
  
  useEffect(() => {
    if (!dataCategories?.length) return

    setMenu((prevMenu) => {
      const newMenu = [...prevMenu]
      const index = newMenu.findIndex((el) => el.title === 'Categories')
      if (index !== -1) {
        newMenu[index] = { ...newMenu[index], children: dataCategories}
      }
      return newMenu
    })
  }, [dataCategories])

  // Trick to hide newstag on homepage mobile
  useEffect(() => {
    if (router) {
      document.body.classList.remove('darker-bg')
      document.body.classList.remove('light-bg')
      if (darkerBgPages.includes(router.pathname)) {
        document.body.classList.add('darker-bg')
      } else if (!notLightBgPages.includes(router.pathname)) {
        document.body.classList.add('light-bg')
      }
      if (router.pathname === '/announcements') {
        setIsAnnouncementPage(true)
      } else {
        setIsAnnouncementPage(false)
      }
      if (router.pathname === '/') {
        setIsHomePage(true)
      } else {
        setIsHomePage(false)
      }
    }
  }, [router])

  useEffect(() => {
    const unwatch = watchAccount(config, {
      async onChange (account) {
        console.log('🚀 ~ onChange ~ account:', account)
        console.log('🚀 ~ onChange ~ address:', address)
        try {
          if (account.address && address && account.address !== address && account.status === 'connected') {
           
            console.log('🚀 ~ onChange ~ account.address:', account.address)
            console.log('🚀 ~ onChange ~ address:', address)
            processChangeAccount(account)
          } 
        } catch (error) {
          console.log('🚀 ~ onChange ~ error:', error)
        
        }
      },
    })
    
    // Cleanup by calling unwatch to unsubscribe from the account change event
    return () => unwatch()
  }, [address])
  
  useEffect(() => {
    const unwatch = watchChainId(config, {
      onChange (chainId) {
        if (chainId) {
          console.log('Chain ID changed!', chainId)
          setActiveChain(chainId)
          
          // Find matching network from chains
          const network = chains.find((chain) => chain.chainId === chainId)
          if (network) {
            setConnectedNetwork(network)
          } 
        } else { 
          window.location.reload()
        }
      },
    })

    // Cleanup by calling unwatch to unsubscribe from the account change event
    return () => unwatch()
  }, [address, chains])
  
  // Initialize connectedNetwork when component mounts and chains are loaded
  useEffect(() => {
    if (chains.length > 0 && chainId) {
      const network = chains.find((chain) => chain.chainId === chainId)
      if (network) {
        setConnectedNetwork(network)
      } else {
        setConnectedNetwork(null)
      
       
      }
    }
  }, [chains, chainId])
  
  useEffect(() => {
    const openWalletConnectModal = (params: any) => {
    
      setShowSelectWallet(params)
    }

    eventBus.on('openWalletConnectModal', openWalletConnectModal)

    // Cleanup listener on component unmount
    return () => {
      eventBus.off('openWalletConnectModal', openWalletConnectModal)
    }
  }, [])

  // useEffect(() => {
  //   if(address && authStateProvider?.me?.publicAddresses?.length) {
  //     const addressFind = authStateProvider?.me?.publicAddresses.find((i) => i.toLocaleLowerCase() == address.toLocaleLowerCase())
  //     if(!addressFind) {
  //       disconnect()
  //     }
  //   }
  // }, [address, authStateProvider])
  
  return (
    <header
      className={`${
        offset > 10 ? 'fixed top-0 w-full md:h-[98px] h-16' : 'relative'
      } z-[99] bg-white dark:bg-dark-primary drop-shadow-sm`}
    >
      <div
        className={`container-df header-wrapper mx-auto ${
          offset > 10 ? 'scrolled' : ''
        }`}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="logo-wrap">
              <Link href="/" className="logo block relative">
                {/* <SvgIcon
                  name="logo"
                  viewBox="0 0 47.955 41.043"
                  className="w-full h-full light-fill-black dark-fill-white stroke-none absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2"
                /> */}
                <Image width={2090} height={900} src="/asset/images/quill.png" alt="logo" className="" />
              </Link>
            </div>
            <div className="hidden md:block">
              <GlobalSearch />
            </div>
            <div className="block md:hidden ml-2 order-1 mr-5">
              <SvgIcon
                name="search"
                viewBox="0 0 20 20"
                className="w-6 h-6 stroke-none fill-dark-primary dark:fill-white"
                onClick={() => setShowSearch(!showSearch)}
              />
            </div>
          </div>
          <div className="">
            <div className="nav-right flex items-center justify-end flex-row md:basis-1/2">
              
              <div onClick={openCreatePage}  className="btn-primary mr-4 md2:mr-5 font-bold cursor-pointer hover:opacity-90">
                Listing Application
              </div>
              <Menu menu={menu} />
              <div className="coin-header md:mr-5 mr-2 md:w-auto xs:w-5 md:ml-0 ml-2">
                {chains.length > 0 && (
                  <Dropdown
                    parentTitle={networkStateProvider?.network?.title}
                    parentIcon={networkStateProvider?.network?.icon}
                    items={chains}
                    isLoading={isLoading}
                    hideTitleMobile
                    dropdownClass="mt-6 px-6 z-[999]px"
                    onSelectItem={onSelectItem}
                    maxHeight="600px"
                  />
                )}
              </div>
              {authStateProvider.me && (
                <div className="user-header ml-2 mr-4 md:mr-5 xs:mr-2">
                  <DropdownWrap
                    wrapClass='z-[99]'
                    head={
                      <DropdownHead
                        icon={authStateProvider.me?.thumbnail?.url ? '' : 'userCircle'}
                        iconImage={authStateProvider.me?.thumbnail?.url ? authStateProvider.me?.thumbnail?.url : ''}
                        iconViewBox="0 0 30.5 30.5"
                        iconClass="w-6 h-6 xl:w-[30px] xl:h-[30px] stroke-[2]"
                        iconImageClass="w-6 h-6 xl:w-[30px] xl:h-[30px] stroke-[2] rounded-full aspect-square bg-gray-4 object-cover"
                      />
                    }
                    dropdownClass="pt-3.5 pb-4 pl-4 pr-3"
                    content={
                      <ul className="fist-mt-none min-w-[180px]">
                        {userNav.map((item, index) => (
                          <li key={index} className="mt-[18px]">
                            {item.children && item.children.length > 0 ? (
                              // Item with children - use collapse functionality
                              <div>
                                <div className="flex items-center justify-between">
                                  <div 
                                    className="flex items-center cursor-pointer flex-1"
                                    onClick={() => {
                                      if (item.title === 'Vending Machine') {
                                        setVendingMachineExpanded(!vendingMachineExpanded)
                                      }
                                    }}
                                  >
                                    <SvgIcon
                                      name={item.icon}
                                      viewBox={item.viewBox}
                                      className="stroke-none fill-dark-primary dark:fill-white transition w-[20px] h-[20px] relative top-[4px]"
                                    />
                                    <span className="ml-3 font-bold leading-5">{item.title}</span>
                                  </div>
                                  <div className="flex items-center justify-center">
                                    <SvgIcon
                                      name={vendingMachineExpanded && item.title === 'Vending Machine' ? 'arrowUp2' : 'arrowDown2'}
                                      className="w-4 h-4 stroke-none fill-dark-primary dark:fill-white transition-transform cursor-pointer"
                                      onClick={() => {
                                        if (item.title === 'Vending Machine') {
                                          setVendingMachineExpanded(!vendingMachineExpanded)
                                        }
                                      }}
                                    />
                                  </div>
                                </div>
                                {vendingMachineExpanded && item.title === 'Vending Machine' && (
                                  <ul className="ml-6 mt-2 space-y-1">
                                    {item.children.map((child, childIndex) => (
                                      <li key={childIndex}>
                                        <DropdownItem
                                          title={child.title}
                                          href={child.link}
                                          icon="arrow-right"
                                          iconSize="w-3 h-3"
                                          className="font-bold leading-5  text-gray-600 mt-2 dark:text-gray-400"
                                        />
                                      </li>
                                    ))}
                                  </ul>
                                )}
                              </div>
                            ) : (
                              // Regular item without children
                              <DropdownItem
                                title={item.title}
                                icon={item.icon}
                                iconSize={item.iconSize}
                                viewBox={item.viewBox}
                                href={item.link}
                                className="font-bold leading-5"
                              />
                            )}
                          </li>
                        ))}
                        <li className="mt-[18px]">
                          <DropdownItem
                            title="Logout"
                            icon="logout"
                            iconSize="w-[20px] h-[20px]"
                            className="mt-4 font-bold items-center"
                            onClick={() => logout()}
                          />
                        </li>
                        <li className="mt-[18px]">
                          <DropdownItem
                            title="Dark Mode"
                            icon="moon"
                            iconSize="w-[20px] h-[20px]"
                            className="mt-4 font-bold items-center"
                          >
                            <ThemeSwitch />
                          </DropdownItem>
                        </li>
                        {authStateProvider.me?.roleId === envConfig.userRole.operator && <li className="mt-[18px]">
                          <DropdownItem
                            title="Admin"
                            icon="admin"
                            iconSize="w-[20px] h-[20px] !stroke-current"
                            className="mt-4 font-bold items-center"
                            onClick={() => gotoAdminPage()}
                          />
                        </li>}
                      </ul>
                    }
                  />
                </div>
              )}
              <div className="wallet-header lg:mr-5 mr-0">
                <DropdownWrap
                  head={
                    <DropdownHead
                      icon="wallet"
                      iconViewBox="0 0 30.5 26.249"
                      iconClass="w-6 h-6 xl:w-[30px] xl:h-[30px] stroke-[2]"
                    />
                  }
                  dropdownClass="pt-2"
                  content={
                    <div>
                      <div className="text-center align-middle pt-2">
                        {authStateProvider.me ? (
                          <>
                            {/* Display connected network & address */}
                            {address ? (
                              <>
                                <div className="flex items-center mb-2 border-b pb-2 px-4">
                                  <span className="text-md font-bold text-gray-600 dark:text-gray-300 mr-2"> WC:</span>
                                  {connectedNetwork?.icon && (
                                    <Image 
                                      src={`/asset/icons/${connectedNetwork.icon}`} 
                                      alt="Network" 
                                      width={20} 
                                      height={20} 
                                      className="mr-2"
                                    />
                                  )}
                                  <span className="text-md font-bold text-gray-600 dark:text-gray-300">
                                    {connectedNetwork?.title || connectedNetwork?.label || 'Unknown Network'}
                                  </span>
                                </div>
                                <Link
                                  href={`/users/${authStateProvider.me.username}`}
                                  className="inline-flex items-center"
                                >
                                  <SvgIcon
                                    name="user"
                                    className="w-6 h-6 stroke-none fill-primary-2"
                                  />
                                  <span className="ml-2 max-w-[100px] font-bold inline-flex text-primary-2">
                                    {shortenAddress(address)}
                                  </span>
                                </Link>
                              </>
                            ): (
                              <>
                                {
                                  authStateProvider.me.isLinkWallet ? 
                                    (
                                      <div className='text-gray-600 dark:text-gray-300 mb-2'>
                                        <span className='font-medium'>Wallet: </span>{shortenAddress(authStateProvider.me.publicAddresses[0])}
                                      </div>
                                    ) : (
                                      <div className='text-red-400'>You do not have a linked wallet</div>
                                    )
                                }
                                <DropdownItem
                                  icon="metamask"
                                  iconElemment={
                                    <Image
                                      src={'/asset/icons/metamask.png'}
                                      alt=""
                                      className="transition"
                                      width={18}
                                      height={18}
                                    />
                                  }
                                  title="Connect Wallet"
                                  className="mt-2 mb-2 px-4 py-[6px] w-full font-bold rounded-full shadow-normal justify-center"
                                  onClick={() => setShowSelectWallet(true)}
                                />
                              </>
                            )}
                          </>
                        ) : (
                          <>
                            <SvgIcon
                              name="user"
                              className="w-6 h-6 stroke-none fill-dark-primary"
                            />
                            <span className="ml-2 max-w-[100px] font-bold inline-flex flex-center">
                              My Wallet
                            </span>
                          </>
                        )}
                      </div>
                      {!authStateProvider.me && (
                        <>
                          <DropdownItem
                            icon="metamask"
                            iconElemment={
                              <Image
                                src={'/asset/icons/metamask.png'}
                                alt=""
                                className="transition"
                                width={18}
                                height={18}
                              />
                            }
                            title="Connect Wallet"
                            className="px-4 py-[6px] mt-4 font-bold rounded-full shadow-normal"
                            onClick={() => setShowSelectWallet(true)}
                          />
                          <DropdownItem
                            icon="metamask"
                            iconElemment={
                              <Image
                                src={'/asset/icons/google.png'}
                                alt=""
                                className="transition"
                                width={18}
                                height={18}
                              />
                            }
                            title="Login with Google"
                            className="px-4 py-[6px] mt-4 font-bold rounded-full shadow-normal"
                            onClick={onLoginWithGoogle}
                          />
                        </>
                      )}
                    </div>
                  }
                />
              </div>
              {/* {authStateProvider.me && (
                <div className="cart-header mr-5 md:mr-10p">
                  <DropdownWrap
                    head={
                      <DropdownHead
                        icon="cart"
                        iconViewBox="0 0 28.268 29.899"
                        iconClass="w-6 h-6 xl:w-[30px] xl:h-[30px] stroke-[2]"
                      />
                    }
                    dropdownClass="pt-3 md:right-0 -right-[180px]"
                    content={
                      <div className="-mr-1 -mt-1">
                        <p className="flex items-center justify-center">
                          <SvgIcon
                            name="user"
                            className="w-6 h-6 stroke-none fill-dark-primary"
                          />
                          <span className="m-2 font-bold">My Cart</span>
                        </p>
                        <ShowMore
                          height="237px"
                          showMoreClass="mr-16 flex items-center justify-end"
                        >
                          {cartStateProvider.cart.map((item: Asset) => (
                            <MiniItem
                              className="mb-25p"
                              key={item.id}
                              id={item.id}
                              imageSrc={
                                item.thumbnail?.url ||
                                '/asset/images/<EMAIL>'
                              }
                              title={item.name!}
                              name={item.name!}
                              price={0}
                              priceSymbol="ETH"
                              onClose={() => cartStateProvider.remove(item)}
                            />
                          ))}
                        </ShowMore>
                      </div>
                    }
                  >
                    <span
                      className="top-0 right-0 w-5 h-5 translate-x-1/2 -translate-y-1/2 flex justify-center
                      items-center absolute rounded-full text-xs text-white font-bold bg-red-1 border-2 border-white"
                    >
                      {cartStateProvider.cart.length}
                    </span>
                  </DropdownWrap>
                </div>
              )} */}
          
            </div>
          </div>
        </div>
        {/* {!isAnnouncementPage && (
          <div
            className={`news-tag absolute bottom-0 right-0 rounded-tl-[10px] py-1 w-full transition duration-300 text-sm lg:text-base
          justify-between bg-gradient-to-b from-gradient-sky to-gradient-ocean text-white ${
          isHomePage ? 'hidden md:flex' : 'flex'
          }
          ${offset > 10 ? 'scrolled' : ''}
          `}
          >
            {announcement && (
              <Link
                href={`/announcements/${announcement.id}`}
                className="max-w-[70%] truncate"
              >
                {announcement?.title}
              </Link>
            )}
            <Link href="/announcements">
              News
              <SvgIcon
                name="chevronRight"
                className="w-5 h-5 stroke-none fill-white"
              />
            </Link>
          </div>
        )} */}
      </div>
      {showSearch && (
        <div className="fixed top-12 w-full p-4 bg-white dark:bg-dark-primary block md:hidden">
          <GlobalSearch />
        </div>
      )}

      {showSelectWallet && (
        <SelectWalletModal
          isShow={showSelectWallet}
          onClose={() => {
            setShowSelectWallet(false)
            //fetchLoginStatus()
          }}
        />
      )}

    </header>
  )
}

export default Header
