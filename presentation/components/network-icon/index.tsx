import Image from 'next/image'
import { getChainName } from 'lib/utils'
import Chain from 'domain/value_objects/chain'

type NetworkIconProps = {
  chainId: number | string
  width?: number
  height?: number
  className?: string
}

const NetworkIcon = ({ chainId, width = 20, height = 20, className = '' }: NetworkIconProps) => {
  // Convert chainId to string if it's a number
  const chainIdStr = typeof chainId === 'number' ? chainId.toString() : chainId
  
  // Get chain name from chainId
  let chainName = getChainName(Number(chainIdStr))
  if(chainName === 'ethereum-sepolia') {
    chainName = 'ethereum'
  }
  // Get icon path
  let iconPath = `/asset/icons/${chainName}.png`
  
  // Fallback if icon doesn't exist
  const onError = (e: any) => {
    e.target.src = '/asset/icons/unknown.png'
  }

  return (
    <Image
      src={iconPath}
      width={width}
      height={height}
      alt={chainName}
      className={className}
      onError={onError}
    />
  )
}

export default NetworkIcon 