import Image from 'next/image'
import Link from 'next/link'
import { getChainName } from 'lib/utils'
type Props = {
  record?: any;
  imageSize?: number;
  imageRounded?: string;
  maxWidth?: string;
};

const CollectionTableItem = (props: Props) => {
  const {
    record,
    imageSize = 32,
    imageRounded = 'rounded-full',
    maxWidth = 'max-w-[10rem]',
  } = props
  return (
    <Link
      href={`/collections/${getChainName(record.chainId)}/${record.slug}`}
      className={`flex items-center ${maxWidth}`}
    >
      <Image
        className={imageRounded}
        alt={record.name}
        src={
          record.logoImage?.url
            ? record.logoImage?.url
            : '/asset/images/image-slide.png'
        }
        width={imageSize}
        height={imageSize}
      />
      <div className="ml-1 truncate w-full text-gray-1">
        <p className="font-medium text-dark-primary dark:text-white leading-none truncate">
          {record.name}
        </p>
        <span className="text-xs font-normal text-stroke-thin dark:text-gray-3 leading-none">
          @{record.owner?.username}
        </span>
      </div>
    </Link>
  )
}

export default CollectionTableItem
