import { ReactNode, useEffect, useState, useMemo } from 'react'
import clsx from 'clsx'
import SvgIcon from 'presentation/components/svg-icon'
import Tooltip from '../tooltip'
import LoadingData from 'presentation/components/skeleton/loading'

type Props = {
  head?: Array<any> | any,
  data?: Array<any> | any,
  className?: string,
  stickyHeader?: Boolean,
  isLoading?: Boolean,
}

type TableHead = {
  title?: string,
  key?: string,
  sortable?: boolean,
  description?: string,
  options?: SortOptions,
  render: (params?: any) => ReactNode,
}

type SortOptions = {
  arrowRight?: boolean,
  arrowIcon?: string,
  sortDirection?: 'asc' | 'desc',
  sortKey?: string,
  tooltipPosition?: 'top' | 'right' | 'bottom',
}

const renderCustom = (head: any[], data: any, key: any): ReactNode => {
  for (let i = 0; i < head.length; i++) {
    if (head[i]['render'] && head[i].key === key) {
      return head[i]['render'](data)
    }
  }
}

type customObj = {
  [key: string]: any
}

const renderTableCell = (head: Object[], obj: Object) => {
  const headKey: string[] = head.map((element) => element['key' as keyof object])
  const jsx = []

  for (const property in obj) {
    if (typeof obj[property as keyof object] !== 'object') {
      if (headKey.includes(property)) {
        jsx.push(<td className="px-2 py-4 whitespace-nowrap dark:text-white" key={property}>
          {obj[property as keyof object]}
        </td>
        )
      }
    } else {
      const record:customObj = head.find((obj:customObj) => {
        return obj.key === property
      }) || {}
      jsx.push(<td className={clsx(record?.tdClass)} key={property}>
        {renderCustom(head, obj[property as keyof object], property)}
      </td>)
    }
  }
  return jsx
}

const Table = ({ head, data, className, stickyHeader = false, isLoading = false }: Props) => {
  const [sortDirection, setSortDirection] = useState('asc')
  const [activeSortCol, setActiveSortCol] = useState('')
  let beta = data

  // Todo: Sort follow options
  const handleSort = (options: SortOptions, key: string) => {
    setActiveSortCol(key)
    let nestedKey: string

    if (typeof options === 'object' && options?.sortKey) {
      nestedKey = options.sortKey
    }

    if (sortDirection === 'asc') {
      beta.sort((a: any, b: any) => {
        if (nestedKey) {
          return a[key][nestedKey] - b[key][nestedKey]
        }
        return a[key] - b[key]
      })
      setSortDirection('desc')
    } else {
      beta.sort((a: any, b: any) => {
        if (nestedKey) {
          return b[key][nestedKey] - a[key][nestedKey]
        }
        return b[key] - a[key]
      })
      setSortDirection('asc')
    }
  }

  return (
    <>
      <table className={clsx('w-full border-collapse', stickyHeader && 'relative', className)}>
        <thead className={clsx('table-header-group', stickyHeader && 'sticky-header')}>
          <tr className="table-row align-middle text-[#222222] dark:text-white">
            {head.map((item: any, index: number) => (
              <th key={index} className={clsx(
                'table-cell text-left font-medium capitalize border-b',
                item.sortable && 'sortable-col',
                activeSortCol === item.key && 'active',
                stickyHeader ? 'sticky top-0 bg-white dark:bg-dark-primary' : 'p-2',
                item.options?.showFullIcons && 'show-full-icons')}
              >
                {/* Remove w-max */}
                <div className={clsx('flex', stickyHeader && 'px-2 py-2 pt-4 h-full')}>
                  {item.sortable ? (
                    <>
                      {item.options?.showFullIcons ? (
                        <button type="button"
                          className={'inline-flex items-center shrink-0'}
                          onClick={() => handleSort(item.options, item.key)}
                        >
                          {item?.title && item.options?.arrowRight ? (
                            <>
                              <span className="mr-2.5">{item.title}</span>
                              <span className="flex flex-col">
                                <SvgIcon
                                  name={item.options?.arrowIconUp || 'arrowUp2'}
                                  viewBox={item.options?.viewBox || '0 0 24 24'}
                                  className={clsx('stroke-none cursor-pointer',
                                    item.options?.iconSize ? item.options?.iconSize : 'w-5 h-5',
                                    sortDirection === 'asc' ? 'fill-primary-2' : 'fill-gray-6 '
                                  )}
                                />
                                <SvgIcon
                                  name={item.options?.arrowIconDown || 'arrowDown2'}
                                  viewBox={item.options?.viewBox || '0 0 24 24'}
                                  className={clsx('stroke-none cursor-pointer',
                                    item.options?.iconSize ? item.options?.iconSize : 'w-5 h-5',
                                    sortDirection !== 'asc' ? 'fill-primary-2' : 'fill-gray-6 '
                                  )}
                                />
                              </span>
                            </>
                          ) : (
                            <>
                              <span className="flex flex-col">
                                <SvgIcon
                                  name={item.options?.arrowIconUp || 'arrowUp2'}
                                  viewBox={item.options?.viewBox || '0 0 24 24'}
                                  className={clsx('stroke-none fill-gray-6 cursor-pointer', item.options?.iconSize ? item.options?.iconSize : 'w-5 h-5')}
                                />
                                <SvgIcon
                                  name={item.options?.arrowIconDown || 'arrowDown2'}
                                  viewBox={item.options?.viewBox || '0 0 24 24'}
                                  className={clsx('stroke-none fill-gray-6 cursor-pointer', item.options?.iconSize ? item.options?.iconSize : 'w-5 h-5')}
                                />
                              </span>
                              {item.title}
                            </>
                          )}

                        </button>
                      ) : (
                        <button type="button"
                          className={`inline-flex items-center shrink-0 ${sortDirection === 'asc' ? 'arrow-down' : 'arrow-up'}`}
                          onClick={() => handleSort(item.options, item.key)}
                        >
                          {item?.title && item.options?.arrowRight ? (
                            <>
                              <span className="mr-2.5">{item.title}</span>
                              <SvgIcon
                                name={item.options?.arrowIcon || 'arrowDown'}
                                viewBox={item.options?.viewBox || '0 0 24 24'}
                                className={clsx('stroke-none fill-gray-6 cursor-pointer', item.options?.iconSize ? item.options?.iconSize : 'w-5 h-5')}
                              />
                            </>
                          ) : (
                            <>
                              <SvgIcon
                                name={item.options?.arrowIcon || 'arrowDown'}
                                viewBox={item.options?.viewBox || '0 0 24 24'}
                                className={clsx('stroke-none fill-gray-6 cursor-pointer', item.options?.iconSize ? item.options?.iconSize : 'w-5 h-5')}
                              />
                              {item.title}
                            </>
                          )}

                        </button>
                      )}
                    </>
                  ) : (
                    <p className="whitespace-pre-line">{item?.title || '\u00A0'}</p>
                  )}
                  {item.description && (
                    <Tooltip
                      position={item.options?.tooltipPosition}
                      className="-mt-0.5"
                    >{item.description}</Tooltip>
                  )}
                </div>
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="table-row-group">
          {!isLoading && beta?.map((element: object, index: number) => (
            <tr key={index} className={clsx('table-row font-medium text-[#222222] dark:text-white',
              (!stickyHeader || (stickyHeader && index !== 0)) && 'border-t')}
            >
              {renderTableCell(head, element)}
            </tr>
          ))}
        </tbody>
      </table>
      {isLoading && <LoadingData />}
      {
        !isLoading && (!data || data?.length === 0) && (
          <div className="flex justify-center items-center h-40">
            <p>No data found</p>
          </div>
        )
      }
    </>
  )
}

export default Table
