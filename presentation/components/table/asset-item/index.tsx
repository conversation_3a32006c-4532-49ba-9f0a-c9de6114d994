import Image from 'next/image'
import Link from 'next/link'

type Props = {
  asset?: any;
  imageSize?: number;
  imageRounded?: string;
  nameOnly?: boolean;
};

const AssetTableItem = (props: Props) => {
  const {
    asset,
    imageSize = 60,
    imageRounded = 'rounded-[20px]',
    nameOnly = false,
  } = props

  return (
    <Link
      href={`assets/${asset.id}`}
      className="flex items-center relative w-max"
    >
      <Image
        className={imageRounded}
        alt={asset.name}
        src={asset.thumbnail?.url}
        width={imageSize}
        height={imageSize}
      />
      <div className="max-w-[7rem] py-1 ml-2">
        <p className="font-medium truncate">{asset.name}</p>
        {!nameOnly && (
          <span className="text-sm font-medium text-primary-2">
            {asset.collection?.name}
          </span>
        )}
      </div>
    </Link>
  )
}

export default AssetTableItem
