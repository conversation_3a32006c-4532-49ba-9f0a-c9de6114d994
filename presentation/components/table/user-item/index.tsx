import Image from 'next/image'
import Link from 'next/link'
import { shortenAddress } from 'lib/utils'

type Props = {
  record?: any;
  imageSize?: number;
  imageRounded?: string;
  maxWidth?: string;
};

const UserTableItem = (props: Props) => {
  const {
    record,
    imageSize = 32,
    imageRounded = 'rounded-full',
    maxWidth = 'max-w-[10rem]',
  } = props

  return (
    <Link
      href={`account/${record.id}`}
      className={`flex items-center ${maxWidth}`}
    >
      <Image
        className={imageRounded}
        alt={record.name}
        src={record.thumbnail?.url}
        width={imageSize}
        height={imageSize}
      />
      <div className="ml-1 truncate w-full text-gray-1">
        <p className="font-medium text-dark-primary dark:text-white leading-none truncate">
          {record.username}
        </p>
        <span className="text-xs font-normal text-stroke-thin dark:text-gray-3 leading-none">
          {shortenAddress(record.address)}
        </span>
      </div>
    </Link>
  )
}

export default UserTableItem
