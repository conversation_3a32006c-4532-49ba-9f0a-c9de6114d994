import { useEffect, useState } from 'react'
import Button from 'presentation/components/button'
import SvgIcon from 'presentation/components/svg-icon'
import Input from 'presentation/components/input'
import Modal from 'presentation/components/modal'
import { uniqByKeepLast } from 'lib/utils'

type Props = {
  title: string;
  placeholderType?: string;
  levels?: Array<any>;
  isShow: boolean;
  onClose: () => void;
  onSubmit: (levels: any) => void;
};

const AddArrayValueModal = ({
  title = '',
  placeholderType = '',
  levels = [],
  isShow = false,
  onClose = () => {},
  onSubmit = () => {},
}: Props) => {
  const emptyLevel = { name: '', value: 3, max: 5 }
  const [levelsLocal, setLevelsLocal] = useState<any>([emptyLevel])

  const updateForm = (index: number, key: string, value: any) => {
    const newArray = [...levelsLocal]
    newArray[index][key] = key == 'name' ? value : value ? Number(value) : value
    setLevelsLocal(newArray)
  }

  useEffect(() => {
    if (levels.length > 0) {
      setLevelsLocal(levels)
    }
  }, [levels])

  return (
    <Modal
      title={title}
      contentWrapPadding=""
      isShow={isShow}
      onClose={onClose}
    >
      <div className="pl-8 pr-5 pb-[34px] pt-[18px] w-11/12 sm:w-[600px]">
        <div className="flex flex-col w-full max-h-[300px] gap-2 overflow-y-scroll">
          {levelsLocal.map((level: any, index: number) => (
            <div key={index} className="flex gap-4">
              <div className="w-2/4">
                <p className="font-medium">Type</p>
                <div className="mt-10p flex items-center">
                  <SvgIcon
                    name="xCircle"
                    className="icon-22 mr-4 rotate-3 stroke-none fill-primary-2 shrink-0 cursor-pointer"
                    onClick={() => {
                      if (levelsLocal.length > 1) {
                        const newArray = [...levelsLocal]
                        newArray.splice(index, 1)
                        setLevelsLocal(newArray)
                      } else {
                        setLevelsLocal([])
                      }
                    }}
                  />
                  <Input
                    className="min-w-[200px]"
                    name={`type-${index}`}
                    type="text"
                    placeholder={placeholderType}
                    value={level.name}
                    onChange={(e) => updateForm(index, 'name', e.target.value?.trim())}
                  />
                </div>
              </div>
              <div>
                <p className="font-medium">Value</p>
                <div className="mt-10p">
                  <Input
                    className="min-w-[100px]"
                    name={`name-${index}`}
                    type="number"
                    placeholder="Value"
                    value={level.value}
                    valueRange={[0, level.max]}
                    onChange={(e) => {
                      updateForm(index, 'value', e.target.value?.trim())
                    }}
                  />
                </div>
              </div>
              <div>
                <p className="font-medium">Max</p>
                <div className="mt-10p">
                  <Input
                    className="min-w-[100px]"
                    name={`name-${index}`}
                    type="number"
                    placeholder="Max"
                    value={level.max}
                    valueRange={[0, Number.MAX_SAFE_INTEGER]}
                    onChange={(e) => updateForm(index, 'max', e.target.value)}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>

        <Button
          variant="light"
          className="mt-3 px-md items-center"
          onClick={() =>
            setLevelsLocal([...levelsLocal].concat([{ ...emptyLevel }]))
          }
        >
          <SvgIcon
            name="plusCircle"
            className="icon-22 stroke-none fill-primary-2 mr-4"
          />
          Add More
        </Button>

        <div className="text-center mt-5">
          <Button
            variant="dark"
            onClick={() => {
              const newLevels = levelsLocal.filter((level: any) => {
                return level.name !== '' && level.value !== '' && level.max !== 0 && level.value <= level.max 
              })
              onSubmit(uniqByKeepLast(newLevels, 'name'))
            }}
          >
            Save
          </Button>
        </div>
      </div>
    </Modal>
  )
}

export default AddArrayValueModal
