import { useState, useEffect } from 'react'
import { useAuthentication } from 'lib/hook/auth_hook'
import { toast } from 'react-toastify'
import { useAccount } from 'wagmi'
import { formatNumber, getBalanceAmount } from 'lib/number'
import BigNumber from 'bignumber.js'
import { validatePrice } from 'lib/validate'
import { useForm } from 'lib/hook/useForm'

import Loader from 'presentation/components/loader'
import Modal from 'presentation/components/modal'
import Button from 'presentation/components/button'
import Input from 'presentation/components/input'

import { envConfig } from 'presentation/constant/env'
import CardThumbnail from 'presentation/components/asset/card-thumbnail'
import ListingUseCase from 'presentation/use_cases/listing_use_case'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'

import CreateInputItemConverter from 'domain/services/nft_land_contract/converters/create_input_item'
import NftLandContractUtils  from 'domain/external_services/nft_land_contract_utils'
import Listing from 'domain/models/listing'

type Props = {
  listing?: Listing;
  isShow: boolean;
  onClose: () => void;
  onReload: () => void;
};

const listingUseCase = new ListingUseCase()

const BuylListingModal = ({
  listing,
  isShow = false,
  onClose = () => {},
  onReload = () => {},
}: Props) => {
  
  const initFormData = {
    quantity: '1',
  }
  
  /**
   * DATA
   */
  const { formData, setFormValue } = useForm(initFormData)
  const [isDisableForm, setIsDisableForm] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [balanceToken, setBalanceToken] = useState<string|number>(0)
  const [amount, setAmount] = useState<any>({amount: null, amountUsd: null, symbol: '' })
  const [contracts, setContracts] = useState<any>({})
  const [asset, setAsset] = useState<any>({})
  const [error, setError] = useState<string>('')
  const [errorOrder, setErrorOrder] = useState<string>('')
  const [errorQuantity, setErrorQuantity] = useState('')
  const [quantityAvailable, setQuantityAvailable] = useState(1)
  const [isSlashPayment, setIsSlashPayment] = useState(false)
  const [isLoadingSlash, setIsLoadingSlash] = useState(false)

  /**
   * HOOKS
   */
  const { getProviderEthers, switchNetwork} = useAuthentication()
  const { chainId: currentChainId } = useAccount()
  const authStateProvider = AuthStateProvider((state) => ({me: state.me}))

  /**
   * FUNCTIONS
   */
  const initEnvConfig = (chainId:any) => {
    const config: any = envConfig
    setContracts(config.contracts[chainId || 1])
  }

  const getBalanceToken = async () => {
    try {
      const userAddress = authStateProvider?.me?.publicAddresses[0]
      if(!listing?.paymentToken?.address || !userAddress) {
        setBalanceToken(0)
        return
      }
      const contract = new NftLandContractUtils()
      const item = new CreateInputItemConverter()
      let itemToken:any = null
      if(listing?.paymentToken?.isNative) {
        itemToken = item.tokenBalanceItem(item.ItemType.NATIVE, listing.paymentToken?.address)
      } else {
        itemToken = item.tokenBalanceItem(item.ItemType.ERC20, listing.paymentToken?.address)
      }
      
      const provider = await getProviderEthers()
      const res = await contract.getBalanceOf(userAddress, itemToken, provider)
      const balance = getBalanceAmount(res._hex, listing.paymentToken?.decimals)
      setBalanceToken(balance.toFixed())
    } catch (error) {
      setBalanceToken(0)

    }
  }

  const checkOrder = async () => {
    try {
      const assetChainId = (listing as any)?.listingAssets?.[0]?.asset?.chainId || 0
      if (Number(currentChainId) != Number(assetChainId)) {
        return
      }
      const provider = await getProviderEthers()
      const _quantity = await listingUseCase.checkOrderGetQuantityToBuy(
        listing, 
        provider, 
      )
      setQuantityAvailable(_quantity)
    } catch (error: any){
      console.log('------error', error) 
      setErrorOrder(error)
    }
  }

  const submitForm = async (e: any) => {
    try {
      e.preventDefault()
      const success = validateForm()
      if(!success) return 
      
      setIsLoading(true)
      if (!contracts?.domainRegistryAddress || !contracts?.marketplaceAddress) {
        throw new Error('Domain registry or marketplace address is not support in this network')
      }
      if(!listing) {
        throw new Error('Not found listing')
      }
      if(!authStateProvider?.me?.publicAddresses[0]) {
        throw new Error('Please connect wallet')
      }
      const provider = await getProviderEthers()
      await listingUseCase.fulfill(
        listing, 
        formData.quantity,
        provider, 
        authStateProvider?.me.publicAddresses[0],
        contracts,
      )

      onReload()
      onClose()
      toast.success('Purchase successful. It takes a while to update the data. Please waiting')
      setIsLoading(false)
    } catch (error: any) {
      setError(error?.showMsgErrors ? error?.showMsgErrors() : error?.message || error)
      setIsLoading(false)
    }
  }

  const validateForm = () => {
    const quantity = formData.quantity || 0
    const amount = BigNumber(listing?.price || 0).times(quantity).toFixed()
    setAmount({
      amount: amount, 
      amountUsd: BigNumber(listing?.paymentToken?.priceUsd || 0).times(amount).toFixed(), 
      symbol: listing?.paymentToken?.symbol || '',
    })
    
    // validate Quantity
    const quantityError = validatePrice({
      value: formData.quantity, 
      fieldName: 'Quantity', 
      min: 1, 
      maxDecimals: 0, 
      max: quantityAvailable,  
    })
    setErrorQuantity(quantityError?.message || '')

    let errorBalance = ''
    if(BigNumber(balanceToken).lt(amount)) {
      errorBalance = `You don't have enough ${listing?.paymentToken?.symbol || ''}`
    } 
    setError(errorBalance)

    if(quantityError || errorBalance) {
      setIsDisableForm(true)  
      return false
    }
    
    setIsDisableForm(false)
    return true
  }

  const onBuyBySlash = async () => {
    
    setIsLoadingSlash(true)
    try {
      if(!listing) {
        throw new Error('Not found listing')
      }
      if(!authStateProvider?.me?.publicAddresses[0]) {
        throw new Error('Please connect wallet')
      }
      await listingUseCase.buyViaSlash(
        listing, 
        formData.quantity,
        authStateProvider?.me.publicAddresses[0],
        ''
      )
    } catch (error: any) {
      toast.error(error?.showMsgErrors ? error?.showMsgErrors() : error?.message || error)
      setIsLoadingSlash(false)
    }
  }

  /**
   * LIFECYCLES
   */
  useEffect(() => {
    validateForm()  
  }, [listing, formData, quantityAvailable, balanceToken])
  
  useEffect(() => {
    const asset = listing?.listingAssets[0].asset
    setAsset(asset)    
    initEnvConfig(asset?.chainId)
    
    // check Slash
    const slashConfig: any = envConfig.slash
    const symbols = slashConfig[asset?.chainId || 0] || []
    const isSlash = symbols?.includes(listing?.paymentToken?.symbol?.toLocaleLowerCase() || '')
    setIsSlashPayment(isSlash)
  }, [listing])
  
  useEffect(() => {
    getBalanceToken()
    checkOrder()
  }, [listing, contracts, authStateProvider?.me, currentChainId])
  
  return (
    <Modal
      isShow={isShow}
      onClose={onClose}
      title="Approve purchase"
      modalWidthClass="min-w-3/4 md:min-w-3/4 xl:min-w-2/4 sm:w-fit w-11/12"
    >
      <div className="w-full sm:w-[500px] px-4 pt-2">
        <form className="w-full" onSubmit={submitForm}>
          <div className="flex items-center flex-row justify-between">
            {asset && (
              <CardThumbnail asset={asset} />
            )}
            <div>
              {amount.amount !== null && (
                <p className="font-medium text-sm text-gray-1 text-right">
                  {formatNumber(amount.amount, 0, 4)} {amount.symbol} <br/> 
                ${formatNumber(amount.amountUsd)}
                </p>
              )}
              {amount.amount === null && (
                <p className="font-medium text-sm text-gray-1 text-right">
                -- <br/> --
                </p>
              )}
            </div>
          </div>

          <div>
            <p className="mt-5 font-medium dark:text-dark-primary mb-1">Quantity</p>
            <Input
              type="number"
              name="quantity"
              className="no-arrow "
              value={formData.quantity}
              disabled={quantityAvailable <= 1}
              onChange={(e) =>
                setFormValue('quantity', e.target.value)
              }
            />
            {errorQuantity && (
              <div className="text-sm text-red text-stroke-thin mt-1 text-right">
                {errorQuantity}
              </div>
            )}
            <p className="mt-1 text-sm text-gray-1 text-right">
              {quantityAvailable} available
            </p>
          </div>
          
          <div className="flex flex-row justify-between bg-slate-50 p-4 my-4">
            <div className="font-medium flex flex-col flex-center dark:text-dark-primary">
              <p>Balance</p>
              <p>Quantity</p>
              <p>Total price</p>
            </div>
            <div className="font-medium flex flex-col flex-center text-gray-1 text-right">
              <p>{formatNumber(Number(balanceToken), 0, 4)} {listing?.paymentToken?.symbol || ''}</p>
              <p>{formData.quantity || 0}</p>
              <p>{formatNumber(amount.amount, 0, 8)} {listing?.paymentToken?.symbol || ''}</p>
            </div>
          </div>

          {(errorOrder || error) && currentChainId == asset.chainId && (
            <div className="text-sm text-red text-stroke-thin mt-1 text-center break-words">
              { errorOrder || error}
            </div>
          )}
        
          <div className="mt-6 mb-10p flex flex-col sm:flex-row items-center justify-center">
            {(() => {
              if(currentChainId !== asset.chainId) {
                return (
                  <Button
                    variant="dark"
                    type="button"
                    size="sm"
                    className="w-full"
                    onClick={() => switchNetwork({chainId: asset.chainId})}
                  >
                  Switch network
                  </Button> 
                )
              } else if(errorOrder) {
                return (
                  <Button
                    variant="dark"
                    type="button"
                    size="sm"
                    className="w-full"
                    onClick={() => { 
                      onClose()
                      onReload()
                    }}
                  >
                  Refresh
                  </Button> 
                )
              } else {
                return (
                  <Button
                    disable={isLoadingSlash || isLoading || isDisableForm}
                    htmltype="submit"
                    variant="dark"
                    className="flex items-center justify-center w-full"
                    size="sm"
                    onClick={submitForm}
                  >
                    { isLoading && <Loader className='mr-2' /> } {isLoading? 'Please waiting' : 'Complete purchase'}
                  </Button>
                )
              }
            })()}

            {isSlashPayment && currentChainId == asset.chainId ? (
              <Button
                disable={ isLoadingSlash || isLoading || (errorQuantity || errorOrder) ? true : false}
                variant="dark"
                type="button"
                size="sm"
                className="flex items-center justify-center w-full sm:ml-2 sm:mt-0 mt-2"
                onClick={onBuyBySlash}
              >
                { isLoadingSlash && <Loader className='mr-2' /> } {isLoadingSlash? 'Please waiting' : 'Buy via Slash'}
              </Button> 
            ) : (<></>)}
          </div>
        </form>
      </div>
    </Modal>
  )
}

export default BuylListingModal
