import { useEffect, useState } from 'react'
import Button from 'presentation/components/button'
import SvgIcon from 'presentation/components/svg-icon'
import Input from 'presentation/components/input'
import Modal from 'presentation/components/modal'
import { uniqByKeepLast } from 'lib/utils'

type Props = {
  properties?: Array<any>;
  isShow: boolean;
  onClose: () => void;
  onSubmit: (properties: any) => void;
};

const AddPropertiesModal = ({
  properties = [],
  isShow = false,
  onClose = () => {},
  onSubmit = () => {},
}: Props) => {
  const emptyProperty = { type: '', name: '' }
  const [propertiesLocal, setPropertiesLocal] = useState<any>([emptyProperty])

  const updateForm = (index: number, key: string, value: any) => {
    const newArray = [...propertiesLocal]
    newArray[index][key] = value
    setPropertiesLocal(newArray)
  }

  useEffect(() => {
    if (properties.length > 0) {
      setPropertiesLocal(properties)
    }
  }, [])

  return (
    <Modal
      title="Add Properties"
      contentWrapPadding=""
      isShow={isShow}
      onClose={onClose}
    >
      <div className="pl-8 pr-5 pb-[34px] pt-[18px] w-11/12 sm:w-[400px]">
        <div className="flex flex-col w-full max-h-[300px] gap-2 overflow-y-auto">
          {propertiesLocal.map((property: any, index: number) => (
            <div key={index} className="flex gap-4 mb-1">
              <div>
                <p className="font-medium">Type</p>
                <div className="mt-10p flex items-center">
                  <SvgIcon
                    name="xCircle"
                    className="icon-22 mr-4 rotate-3 stroke-none fill-primary-2 shrink-0"
                    onClick={() => {
                      if (propertiesLocal.length > 1) {
                        const newArray = [...propertiesLocal]
                        newArray.splice(index, 1)
                        setPropertiesLocal(newArray)
                      }
                      else {
                        setPropertiesLocal([])
                      }
                    }}
                  />
                  <Input
                    name={`type-${index}`}
                    type="text"
                    placeholder="Ex. Character"
                    value={property.type}
                    onChange={(e) => updateForm(index, 'type', e.target.value?.trim())}
                  />
                </div>
              </div>
              <div>
                <p className="font-medium">Name</p>
                <div className="mt-10p">
                  <Input
                    name={`name-${index}`}
                    type="text"
                    placeholder="Ex. Male"
                    value={property.name}
                    onChange={(e) => updateForm(index, 'name', e.target.value?.trim())}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>

        <Button
          variant="light"
          className="mt-5 px-md items-center"
          onClick={() =>
            setPropertiesLocal(
              [...propertiesLocal].concat([{ ...emptyProperty }])
            )
          }
        >
          <SvgIcon
            name="plusCircle"
            className="icon-22 stroke-none fill-primary-2 mr-4"
          />
          Add More
        </Button>

        <div className="text-center mt-5">
          <Button
            variant="dark"
            onClick={() => {
              const newProperties = propertiesLocal.filter((property: any) => {
                return property.type !== '' && property.name !== ''
              })
              onSubmit(uniqByKeepLast(newProperties, 'type') )
            }}
          >
            Save
          </Button>
        </div>
      </div>
    </Modal>
  )
}

export default AddPropertiesModal
