import { useState, useEffect } from 'react'
import { useAuthentication } from 'lib/hook/auth_hook'
import { toast } from 'react-toastify'
import { useForm } from 'lib/hook/useForm'
import { formatNumber, getDecimalAmount } from 'lib/number'
import { validatePrice } from 'lib/validate'
import BigNumber from 'bignumber.js'

import Link from 'next/link'
import Loader from 'presentation/components/loader'
import Modal from 'presentation/components/modal'
import Input from 'presentation/components/input'
import Button from 'presentation/components/button'
import { envConfig } from 'presentation/constant/env'
import CardThumbnail from 'presentation/components/asset/card-thumbnail'
import RequireConnect from 'presentation/components/require-connect'
import OfferUseCase from 'presentation/use_cases/offer_use_case'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'

import Offer from 'domain/models/offer'
import CreateInputItemConverter from 'domain/services/nft_land_contract/converters/create_input_item'
import NftLandContractUtils  from 'domain/external_services/nft_land_contract_utils'

type Props = {
  offer?: Offer;
  isShow: boolean;
  onClose: () => void;
  onReload: () => void;
};

const offerUseCase = new OfferUseCase()

const AcceptOfferModal = ({
  offer,
  isShow = false,
  onClose = () => {},
  onReload = () => {},
}: Props) => {

  const initFormData = {
    quantity: '1',
  }
  
  /**
   * DATA
   */
  const { formData, setFormValue } = useForm(initFormData)
  const [ feeSales, setFeeSales ] = useState<any>()
  const [isDisableForm, setIsDisableForm] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [amountTotal, setAmountTotal] = useState<any>({amount: null, amountUsd: null, amountEarn: null, amountEarnUsd: null, symbol: '' })
  const [contracts, setContracts] = useState<any>({})
  const [error, setError] = useState<string>('')
  const [errorQuantity, setErrorQuantity] = useState('')
  const [quantityAvailable, setQuantityAvailable] = useState(1)
  const [offerCustom, setOfferCustom] = useState<any>()
  const [creators, setCreators] = useState<any>({})

  /**
   * HOOKS
   */
  const { getProviderEthers, switchNetwork, currentChainId, addressConnect} = useAuthentication()
  const authStateProvider = AuthStateProvider((state) => ({me: state.me}))

  /**
   * FUNCTIONS
   */
  
  const initEnvConfig = (chainId:any) => {
    const config: any = envConfig
    const contracts = config.contracts[chainId || 1]
    setFeeSales({
      address: contracts.feeSalesAddress,
      percent: contracts.feeSalesPercent,
    })
    setContracts(config.contracts[chainId || 1])
  }

  const getQuantityAvailable = async () => {
    try {
      if(!currentChainId || !addressConnect) {
        throw new Error('Wallet not connected')
      }
      const userAddress = addressConnect
      const asset = offer?.asset
      if(!asset?.address || !userAddress) {
        setQuantityAvailable(0)
        return
      }
      const contract = new NftLandContractUtils()
      const item = new CreateInputItemConverter()
      let itemToken = null
      if(asset?.contract?.schema.isErc1155()) {
        itemToken = item.tokenBalanceItem(item.ItemType.ERC1155, asset?.address, asset?.tokenId)
      } else if(asset?.contract?.schema.isErc721()) {
        itemToken = item.tokenBalanceItem(item.ItemType.ERC721, asset?.address, asset?.tokenId)
      }
      if(!itemToken) {
        setQuantityAvailable(0)
        return 
      }

      const provider = await getProviderEthers()
      const res = await contract.getBalanceOf(userAddress, itemToken, provider)
      console.log('-----res', res.toNumber())
      console.log('-----offerCustom.quantity', offerCustom.quantity)
      console.log('-----offer', offer)
      const min = BigNumber.minimum(Number(res), offerCustom.quantity).toNumber()
      setQuantityAvailable(min)
    } catch (error) {
      setQuantityAvailable(0)
    }
  }

  const submitForm = async (e: any) => {
    try {
      e.preventDefault()
      setError('')
      setIsLoading(true)
      if (!contracts?.domainRegistryAddress || !contracts?.marketplaceAddress) {
        throw new Error('Domain registry or marketplace address is not support in this network')
      }
      if(!offer) {
        throw new Error('Not found offer')
      }
      if(!addressConnect) {
        throw new Error('Please connect wallet')
      }
      const provider = await getProviderEthers()
      offer.quantity = formData.quantity
      // calc fee for Creator earnings
      let tips: any = []
      if(creators?.totalRateFee) {
        const addressToken = offer?.paymentToken?.address?.toLowerCase()
        const amountNft = offer.orderParams.offer
          .find((i:any) => i.token.toLowerCase() == addressToken)
          ?.startAmount || 1

        creators.list?.forEach((creator:any) => {
          const percent = getDecimalAmount(creator.percent, -2)
          const amountFee = BigNumber(amountNft).times(percent).toFixed(0)
          tips.push({
            recipient: creator.address,
            token: addressToken,
            amount: amountFee,
          })
        })
      }

      await offerUseCase.fulfill(
        offer,
        provider,
        addressConnect,
        contracts,
        tips
      )
      
      onReload()
      onClose()
      setIsLoading(false)
    } catch (error: any) {
      setError(error?.showMsgErrors ? error?.showMsgErrors() : error?.message || error)
      setIsLoading(false)
    }
  }

  /**
   * LIFECYCLES
   */
  useEffect(() => {    
    initEnvConfig(offer?.asset?.chainId)
    if(offer) {
      const _offers = offerUseCase.mapOffersTable([offer], () => {}, () => {}, null)
      setOfferCustom(_offers[0])
      setCreators(offer?.asset?.getCreatorEarnings(addressConnect))
    }
  }, [offer])
  
  useEffect(() => {
    getQuantityAvailable()
  }, [offerCustom, currentChainId])
  
  useEffect(() => { 
    const amount = BigNumber(offer?.price || 0).times(formData.quantity || 0)
    const fee = BigNumber(creators?.totalRateFee || 0).plus(feeSales?.percent || 0).dividedBy(100).toNumber()
    const amountEarn = amount.times(1 - fee)
    const rate = offer?.paymentToken?.priceUsd || 0
    setAmountTotal({
      amount: amount, 
      amountUsd: amount.times(rate), 
      amountEarn: amountEarn,
      amountEarnUsd: amountEarn.times(rate),
      symbol: offer?.paymentToken?.symbol, 
    })
  }, [offer, formData, creators])

  useEffect(() => {
    // validate Quantity - start
    const quantityError = validatePrice({
      value: formData.quantity, 
      fieldName: 'Quantity', 
      min: 1, 
      maxDecimals: 0, 
      max: quantityAvailable,  
    })
    if(quantityError) {
      setErrorQuantity(quantityError.message)
      setIsDisableForm(true)
    } else {
      setErrorQuantity('')
    }
    // validate Quantity - end

    if(quantityError) {
      setIsDisableForm(true)  
    } else {
      setIsDisableForm(false)
    }
    
  }, [formData, quantityAvailable])
  
  return (
    <Modal
      isShow={isShow}
      onClose={onClose}
      title="Approve sale"
      modalWidthClass="min-w-3/4 md:min-w-3/4 xl:min-w-2/4 sm:w-fit w-11/12"
    >
      <div className="w-full sm:w-[500px] px-4 pt-2">
        <form className="w-full" onSubmit={submitForm}>
          <div className="flex items-center flex-row justify-between">
            {offer?.asset && (
              <CardThumbnail asset={offer?.asset} />
            )}
            <div>
              {amountTotal.amount !== null && (
                <p className="font-medium text-sm text-gray-1 text-right">
                  {formatNumber(amountTotal.amount, 0, 4)} {amountTotal.symbol} <br/> 
                ${formatNumber(amountTotal.amountUsd)}
                </p>
              )}
              {amountTotal.amount === null && (
                <p className="font-medium text-sm text-gray-1 text-right">
                -- <br/> --
                </p>
              )}
            </div>
          </div>

          {quantityAvailable > 1 && (
            <>
              <p className="mt-5 font-medium dark:text-dark-primary mb-1">Quantity</p>
              <Input
                type="number"
                name="quantity"
                className="no-arrow "
                value={formData.quantity}
                onChange={(e) =>
                  setFormValue('quantity', e.target.value)
                }
              />
              {errorQuantity && (
                <div className="text-sm text-red text-stroke-thin mt-1 text-right">
                  {errorQuantity}
                </div>
              )}
              <p className="mt-1 text-sm text-gray-1 text-right">
                {quantityAvailable} available
              </p>
            </>
          )}

          {/* <p className="mt-4 mb-1 font-medium dark:text-dark-primary">Offer details</p>  */}
          <div className="flex flex-row justify-between bg-slate-50 p-4 my-4">
            <div className="font-medium flex flex-col flex-center dark:text-dark-primary">
              <p>Unit price</p>
              <p>Floor difference</p>
              <p>From</p>
              <p>Expiration</p>
            </div>
            <div className="font-medium flex flex-col flex-center text-gray-1 text-right">
              <p>{formatNumber(offerCustom?.unitPrice?.price || 0)} {offerCustom?.unitPrice?.paymentToken?.symbol || ''}</p>
              <p>{`${offerCustom?.floorDifference?.percent || 0}% ${offerCustom?.floorDifference?.text || ''}`}</p>
              <Link href={`users/${offerCustom?.userFrom?.username}`} className="text-primary-2 truncate max-w-[150px]">
                {offer?.user?.id == authStateProvider?.me?.id ? 'You' : offerCustom?.userFrom?.username}
              </Link>
              <p>{offerCustom?.expiration || ''}</p>
            </div>
          </div>
          
          <div className="mt-5">
            <p className="font-medium">Fee</p>
            {feeSales?.address && feeSales?.percent && (
              <div className='flex justify-between text-gray-1 mt-1'>
                <span className="mr-4">Service Fee</span>
                <span>{feeSales.percent}%</span>
              </div>
            )}
            {Number(creators?.totalRateFee) > 0 && (
              <div className='flex justify-between text-gray-1 mt-1'>
                <span className="mr-4">Creator earnings</span>
                <span>{creators?.totalRateFee}%</span>
              </div>
            )}
          </div>
          
          <div className="mt-5 mb-8 flex justify-between">
            <p className="font-medium">Total earnings</p>
            <div className='dark:text-dark-primary text-right'>
              <p className=" font-medium">
                {formatNumber(amountTotal.amountEarn || 0, 0, 4)} {amountTotal.symbol} 
              </p>
              <p className='text-sm'>${formatNumber(amountTotal.amountEarnUsd || 0)}</p> 
            </div>
          </div>
          
          {error && (
            <div className="text-sm text-red text-stroke-thin mt-1 text-center break-words">
              {error}
            </div>
          )}
        
          <div className="mt-6 mb-10p flex items-center	justify-center	">
            {currentChainId !== offer?.asset?.chainId ? 
              ( 
                <>
                  {
                    !currentChainId || !addressConnect ? (
                      <RequireConnect className="!p-0" innerClassName="!p-0"/>
                    ): (
                      <Button
                        variant="dark"
                        type="button"
                        className="mt-6"
                        onClick={() => switchNetwork({chainId: offer?.asset?.chainId})}
                      >
                      Switch network
                      </Button>
                    )
                  }
                </>
              ) : (
                <Button
                  disable={isLoading || isDisableForm}
                  htmltype="submit"
                  variant="dark"
                  className="flex"
                  onClick={submitForm}
                >
                  { isLoading && <Loader className='mr-2' /> } {isLoading? 'Please waiting' : 'Accept'}
                </Button>
              )
            }
            
          </div>
        </form>
      </div>
    </Modal>
  )
}

export default AcceptOfferModal
