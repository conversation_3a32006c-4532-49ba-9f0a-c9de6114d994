import { useState, useEffect } from 'react'
import { DateTime } from 'luxon'
import { useAuthentication } from 'lib/hook/auth_hook'
import { toast } from 'react-toastify'
import { useAccount } from 'wagmi'
import { useForm } from 'lib/hook/useForm'
import { formatNumber, getBalanceAmount } from 'lib/number'
import { validatePrice } from 'lib/validate'
import { envConfig } from 'presentation/constant/env'
import BigNumber from 'bignumber.js'

import Loader from 'presentation/components/loader'
import Modal from 'presentation/components/modal'
import Input from 'presentation/components/input'
import Select from 'presentation/components/select'
import Button from 'presentation/components/button'
import CardThumbnail from 'presentation/components/asset/card-thumbnail'
import { durationDateOptions } from 'presentation/controllers/mockData/options'
import OfferUseCase from 'presentation/use_cases/offer_use_case'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'

import Asset from 'domain/models/asset'
import Offer from 'domain/models/offer'
import Listing from 'domain/models/listing'
import PaymentTokens from 'domain/models/payment_tokens'
import CreateInputItemConverter from 'domain/services/nft_land_contract/converters/create_input_item'
import NftLandContractUtils  from 'domain/external_services/nft_land_contract_utils'
import { contract } from 'web3/lib/commonjs/eth.exports'


type Props = {
  asset?: Asset;
  isShow: boolean;
  listingBid?: Listing|null;
  bestOffer?: Offer|null;
  onClose: () => void;
  onReload: () => void;
};

const offerUseCase = new OfferUseCase()

const MakeOfferModal = ({
  asset,
  isShow = false,
  bestOffer = null,
  listingBid = null,
  onClose = () => {},
  onReload = () => {},
}: Props) => {

  const initFormData = {
    quantity: '1',
    paymentToken: null,
    price: '',
    startAt: DateTime.now(),
    endAt: DateTime.now().plus({days: durationDateOptions[0].value}),
  }

  const isBid = listingBid ? true : false 
  
  /**
   * DATA
   */
  const { formData, setFormValue } = useForm(initFormData)
  const [isDisableForm, setIsDisableForm] = useState(true)
  const [isLoading, setIsLoading] = useState(false)
  const [balanceToken, setBalanceToken] = useState<string|number>(0)
  const [paymentTokens, setPaymentTokens] = useState<PaymentTokens[]>([])
  const [errorPrice, setErrorPrice] = useState('')
  const [errorQuantity, setErrorQuantity] = useState('')
  const [amountOffer, setAmountOffer] = useState<any>({amount: null, amountUsd: null, symbol: '' })
  const [contracts, setContracts] = useState<any>({})
  const [creators, setCreators] = useState<any>({})
  const [error, setError] = useState<string>('')

  /**
   * HOOKS
   */
  const { getProviderEthers, switchNetwork} = useAuthentication()
  const { chainId: currentChainId } = useAccount()
  const authStateProvider = AuthStateProvider((state) => ({me: state.me}))
  console.log('----authStateProvider?.me', authStateProvider?.me)
  /**
   * FUNCTIONS
   */
  const formatPaymentTokens = async () => {
    const list = asset?.collection?.paymentTokens
    setPaymentTokens(list?.filter((i:any) => !i.isNative))
  }
  
  const getBalanceToken = async () => {
    try {
      const userAddress = authStateProvider?.me?.publicAddresses[0] 
      if(!formData.paymentToken?.address || !userAddress) {
        setBalanceToken(0)
        return
      }
      const contract = new NftLandContractUtils()
      const item = new CreateInputItemConverter()
      const itemToken = item.tokenBalanceItem(item.ItemType.ERC20, formData.paymentToken?.address)
      
      const provider = await getProviderEthers()
      const res = await contract.getBalanceOf(userAddress, itemToken, provider)
      const balance = getBalanceAmount(res._hex, formData.paymentToken?.decimals)
      setBalanceToken(balance.toFixed(4, 1))
    } catch (error) {
      setBalanceToken(0)
    }
  }

  const initEnvConfig = (chainId:any) => {
    const config: any = envConfig
    const contract = config.contracts[chainId || 1]
    setContracts(contract)
  }

  const submitForm = async (e: any) => {
    try {
      e.preventDefault()
      setError('')
      setIsLoading(true)
      if (!contracts?.domainRegistryAddress || !contracts?.marketplaceAddress) {
        throw new Error('Domain registry or marketplace address is not support in this network')
      }
      if (!authStateProvider?.me) {
        throw new Error('Not found user')
      }

      let fees:any[] = []
      if(contracts.feeSalesAddress && contracts.feeSalesPercent) {
        fees.push({
          address: contracts.feeSalesAddress,
          percent: contracts.feeSalesPercent,
        })
      }
      // if(creators?.totalRateFee) {
      //   fees.push(...creators.list)
      // }
      const newOffer = Offer.newEmtyOffer()
      newOffer.asset = asset ? asset : null
      newOffer.listingId = listingBid?.id || null
      newOffer.user = authStateProvider?.me ? authStateProvider?.me : null
      newOffer.quantity = formData.quantity
      newOffer.paymentToken = formData.paymentToken
      newOffer.price = formData.price
      newOffer.expiresAt = formData.endAt
      newOffer.fees = fees 
      const provider = await getProviderEthers()

      await offerUseCase.save(
        newOffer, 
        provider, 
        authStateProvider?.me?.publicAddresses[0] ,
        contracts
      )
      onReload()
      onClose()
      toast.success(isBid ? 'Bid successful.' : 'Your offer has been submitted.')
      setIsLoading(false)
    } catch (error: any) {
      setError(error?.showMsgErrors ? error?.showMsgErrors() : error?.message || error)
      setIsLoading(false)
    }
  }

  /**
   * LIFECYCLES
   */
  useEffect(() => {    
    formatPaymentTokens()
    initEnvConfig(asset?.chainId)
    setCreators(asset?.getCreatorEarnings())
  }, [asset])
  
  useEffect(() => {    
    if(listingBid) {
      setFormValue('endAt', listingBid.endAt?.plus({days: 3})) 
    }
  }, [listingBid])
  
  useEffect(() => {
    if (paymentTokens.length > 0) {
      setFormValue('paymentToken', paymentTokens[0]) 
    }
  }, [paymentTokens])
  
  useEffect(() => {
    if(authStateProvider?.me) {
      getBalanceToken()
    } else {
      onClose()
    }
  }, [formData.paymentToken, authStateProvider?.me, currentChainId])
  
  useEffect(() => {
    console.log('----->formData', formData)
    if(formData?.price == '' || formData?.quantity == '') {
      setAmountOffer({
        amount: null, 
        amountUsd: null, 
        symbol: formData.paymentToken?.name || '',
      })
      setErrorPrice('')
      setIsDisableForm(true)
      return
    } 
    setAmountOffer({
      amount: Number(formData?.price || 0)*Number(formData?.quantity || 0), 
      amountUsd: Number(formData?.price || 0)*Number(formData?.quantity || 0)*Number(formData?.paymentToken?.priceUsd || 0), 
      symbol: formData.paymentToken?.name || '',
    })
    
    // validate price - start
    let errPrice = null,
      minPrice:number = 0,
      unit = formData.paymentToken?.name
    
    if(listingBid) {
      if(unit == listingBid?.paymentToken?.name) {
        minPrice = Number(listingBid?.price || 0)
      } else {
        minPrice = BigNumber(listingBid?.paymentToken?.priceUsd || 0)
          .times(listingBid?.price || 0)
          .dividedBy(formData?.paymentToken?.priceUsd)
          .toNumber()
      }
    }
    
    errPrice = validatePrice({
      value: formData.price, 
      fieldName: 'price', 
      min: minPrice, 
      maxDecimals: 4, 
      max: BigNumber(balanceToken).dividedBy(formData?.quantity || 1).toNumber(), 
    })
    if(errPrice) {
      if(listingBid && errPrice.code == 'INVALID_MIN_AMOUNT') {
        setErrorPrice(`Offer must be at least the minimum price of ${formatNumber(minPrice)} ${unit}`)
      } else if(errPrice.code == 'INVALID_MAX_AMOUNT') {
        setErrorPrice(`You don't have enough ${unit}`)
      } 
      else {
        setErrorPrice(errPrice?.message)
      }
      setIsDisableForm(true)
    } else {
      setErrorPrice('')
    }
    // validate price - end

    // validate Quantity - start
    const quantityprice = validatePrice({
      value: formData.quantity, 
      fieldName: 'Quantity', 
      min: 1, 
      maxDecimals: 0, 
      max: asset?.totalSupply || 0, 
    })
    setErrorQuantity(quantityprice?.message || '')
    // validate Quantity - end
    if(errPrice || quantityprice) {
      setIsDisableForm(true)  
    } else {
      setIsDisableForm(false)
    }
  }, [formData, asset?.totalSupply])

  return (
    <Modal
      isShow={isShow}
      onClose={onClose}
      title={isBid? 'Place a bid' : 'Make an offer'}
      modalWidthClass="min-w-3/4 md:min-w-3/4 xl:min-w-2/4 sm:w-fit w-11/12"
    >
      <div className="w-full sm:w-[500px] px-4 pt-2">
        <form className="w-full" onSubmit={submitForm}>
          <div className="flex items-center flex-row justify-between">
            {asset && (
              <CardThumbnail asset={asset} />
            )}
            <div>
              {amountOffer.amount !== null && (
                <p className="font-medium text-sm text-gray-1 text-right">
                  {formatNumber(amountOffer.amount, 0, 4)} {amountOffer.symbol} <br/> 
                ${formatNumber(amountOffer.amountUsd)}
                </p>
              )}
              {amountOffer.amount === null && (
                <p className="font-medium text-sm text-gray-1 text-right">
                -- <br/> --
                </p>
              )}
            </div>
          </div>
          <div className="flex flex-row justify-between bg-slate-50 p-4 my-4">
            <div className="font-medium flex flex-col flex-center dark:text-dark-primary">
              <p>Balance</p>
              <p>Floor Price</p>
              <p>Best Offer</p>
            </div>
            <div className="font-medium flex flex-col flex-center text-gray-1 text-right">
              <p>{formatNumber(Number(balanceToken))} {formData.paymentToken?.name || ''}</p>
              <p>{listingBid ? `${formatNumber(listingBid?.price || 0)} ${listingBid?.paymentToken?.symbol || ''}` : '1 USDC'}</p>
              <p>{bestOffer? `${formatNumber(bestOffer?.price)} ${bestOffer?.paymentToken?.symbol}` : '--'}</p>
            </div>
          </div>
          {/* Price */}
          <p className="mt-5 font-medium dark:text-dark-primary">Price</p>
          <div className="flex sm:flex-nowrap flex-wrap mt-10p w-full">
            {paymentTokens?.length > 0 && formData.paymentToken && (
              <Select
                className="sm:mb-0 mb-2"
                options={paymentTokens}
                defaultSelected={formData.paymentToken}
                showOptionIcon
                rounded
                onSelect={(option) => {
                  setFormValue('paymentToken', option)
                }}
              />
            )}
            {paymentTokens?.length == 0 && (
              <div className="text-sm text-red text-stroke-thin mt-1 text-right">
              Not token yet
              </div>
            )}
            <div className='flex-1 ml-5'>
              <Input
                type="number"
                name="price"
                className="no-arrow "
                value={formData.price}
                onChange={(e) =>
                  setFormValue('price', e.target.value)
                }
              />
              {errorPrice && (
                <div className="text-sm text-red text-stroke-thin mt-1 text-right break-words	">
                  {errorPrice}
                </div>
              )}
              {amountOffer.amount !== null && (
                <p className="mt-1 text-sm text-gray-1 text-right">
                  Total offer amount: {formatNumber(amountOffer.amount, 0, 4)} {amountOffer.symbol} (~${formatNumber(amountOffer.amountUsd)})
                </p>
              )}
            </div>
          </div>

          {/* Quantity */}
          {Number(asset?.totalSupply || 0) > 1 && (
            <>
              <p className="mt-5 font-medium dark:text-dark-primary mb-1">Quantity</p>
              <Input
                type="number"
                name="quantity"
                className="no-arrow "
                valueRange={[1, asset?.totalSupply || 1]}
                value={formData.quantity}
                onChange={(e) =>
                  setFormValue('quantity', e.target.value)
                }
              />
              {errorQuantity && (
                <div className="text-sm text-red text-stroke-thin mt-1 text-right">
                  {errorQuantity}
                </div>
              )}
              <p className="mt-1 text-sm text-gray-1 text-right">
                {asset?.totalSupply} available
              </p>
            </>
          )}

          {/* Duration */}
          {!isBid && (
            <>
              <p className="mt-5 font-medium dark:text-dark-primary">Duration</p>
              <div className="flex sm:flex-nowrap flex-wrap mt-10p w-full">
                <Select
                  options={durationDateOptions.filter((i:any) => i.value)}
                  rounded
                  arrowIconClass="stroke-none fill-primary-2"
                  className="w-full"
                  customWidth={true}
                  onSelect={(option) => {
                    setFormValue('endAt', DateTime.now().plus({days: option.value}))
                  }}
                />
                {/* TODO select day */}
              </div>
            </>
          )}

          {error && (
            <div className="text-sm text-red text-stroke-thin mt-4 text-center">
              {error}
            </div>
          )}
        
          <div className="mt-6 mb-10p flex items-center	justify-center	">
            {currentChainId !== asset?.chainId ? 
              (
                <Button
                  variant="dark"
                  type="button"
                  className="mt-6"
                  onClick={() => switchNetwork({chainId: asset?.chainId})}
                >
                  Switch network
                </Button>
              ) : (
                <Button
                  disable={isLoading || isDisableForm}
                  htmltype="submit"
                  variant="dark"
                  className="flex"
                  onClick={submitForm}
                >
                  { isLoading && <Loader className='mr-2' /> } {isLoading? 'Please waiting' : listingBid? 'Place bid' : 'Make offer'}
                </Button>
              )
            }
            
          </div>
        </form>
      </div>
    </Modal>
  )
}

export default MakeOfferModal
