import { useState, useEffect } from 'react'
import { useAuthentication } from 'lib/hook/auth_hook'
import { toast } from 'react-toastify'
import { formatNumber} from 'lib/number'
import BigNumber from 'bignumber.js'

import Loader from 'presentation/components/loader'
import Modal from 'presentation/components/modal'
import Button from 'presentation/components/button'

import { envConfig } from 'presentation/constant/env'
import CardThumbnail from 'presentation/components/asset/card-thumbnail'
import ListingUseCase from 'presentation/use_cases/listing_use_case'
import RequireConnect from 'presentation/components/require-connect'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'

import ListingOrder from 'domain/models/listing_order'

type Props = {
  order?: ListingOrder;
  isShow: boolean;
  onClose: () => void;
  onReload: () => void;
};

const listingUseCase = new ListingUseCase()

const ClaimNFTModal = ({
  order,
  isShow = false,
  onClose = () => {},
  onReload = () => {},
}: Props) => {
  
  /**
   * DATA
   */
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingCheck, setIsLoadingCheck] = useState(false)
  const [error, setError] = useState<string>('')
  const [errorClaim, setErrorClaim] = useState<string>('')
  const [asset, setAsset] = useState<any>({})
  const [amount, setAmount] = useState<any>({amount: null, amountUsd: null, symbol: '' })
  const [contracts, setContracts] = useState<any>({})
  const [isRefund, setIsRefund] = useState<boolean>(false)
  
  /**
   * HOOKS
   */
  const { getProviderEthers, switchNetwork, currentChainId, addressConnect} = useAuthentication()
  const authStateProvider = AuthStateProvider((state) => ({me: state.me}))

  /**
   * FUNCTIONS
   */
  const initEnvConfig = (chainId:any) => {
    const config: any = envConfig
    setContracts(config.contracts[chainId || 1])
  }

  const checkOrder = async () => {
    try {
      const assetChainId = (order as any)?.listing?.listingAssets?.[0]?.asset?.chainId || 0
      if (Number(currentChainId) != Number(assetChainId)) {
        return
      }
      setIsLoadingCheck(true)
      const provider = await getProviderEthers()
      const quantity = await listingUseCase.checkOrderGetQuantityToBuy(
        order?.listing || null, 
        provider, 
      )
      if(Number(quantity) < Number(order?.quantity || 0)) {
        setErrorClaim('The quantity not enough to claim')
      }
      setIsLoadingCheck(false)
    } catch (error: any){
      setErrorClaim(error)
      setIsLoadingCheck(false)
    }
  }

  const onClaimNft = async (e: any) => {
    try {
      e.preventDefault()
      setError('')
      setIsLoading(true)
      console.log('-----contracts', contracts)
      if (!contracts?.nftPurchaseProxy) {
        throw new Error('Nft Purchase address not support in this network')
      }
      if(!order) {
        throw new Error('Not found order')
      }
      if(!authStateProvider?.me?.publicAddresses[0]) {
        throw new Error('Please connect wallet')
      }
      const provider = await getProviderEthers()
      await listingUseCase.claimNft(
        order, 
        provider, 
        authStateProvider?.me.publicAddresses[0],
        contracts
      )
      onReload()
      onClose()
      toast.success('Claim successful. It takes a while to update the data. Please waiting')
      setIsLoading(false)
    } catch (error: any) {
      console.log('------error', error)
      setIsLoading(false) 
      setError(error?.showMsgErrors ? error?.showMsgErrors() : error?.message || error)
    }
  }
  const onRefund = async (e: any) => {
    try {
      e.preventDefault()
      setError('')
      setIsLoading(true)
      console.log('-----contracts', contracts)
      if (!contracts?.nftPurchaseProxy) {
        throw new Error('Nft Purchase address not support in this network')
      }
      if(!order) {
        throw new Error('Not found order')
      }
      if(!authStateProvider?.me?.publicAddresses[0]) {
        throw new Error('Please connect wallet')
      }
      const provider = await getProviderEthers()
      await listingUseCase.refundToken(
        order, 
        provider, 
        authStateProvider?.me.publicAddresses[0],
        contracts
      )
      onReload()
      onClose()
      toast.success('Refund successful. It takes a while to update the data. Please waiting')
      setIsLoading(false)
    } catch (error: any) {
      console.log('------error', error)
      setIsLoading(false) 
      setError(error?.showMsgErrors ? error?.showMsgErrors() : error?.message || error)
    }
  }
  
  /**
   * LIFECYCLES
   */
  
  useEffect(() => {  
    setAsset(order?.asset)
    initEnvConfig(order?.asset?.chainId)
    const listing = order?.listing
    const amount = new BigNumber(listing?.price || 0).times(Number(order?.quantity || 0)).toNumber()
    if(!listing?.isActiveFixedPrice()) {
      setIsRefund(true)
    } else {
      checkOrder()
    }
    setAmount({
      amount: amount, 
      amountUsd: BigNumber(listing?.paymentToken?.priceUsd || 0).times(amount).toFixed(), 
      symbol: listing?.paymentToken?.symbol || '',
    })
  }, [order, currentChainId])

  return (
    <Modal
      isShow={isShow}
      onClose={onClose}
      title={isRefund ? 'Refund' : 'Claim NFT'}
      modalWidthClass="min-w-3/4 md:min-w-3/4 xl:min-w-2/4 sm:w-fit w-11/12"
    >
      <div className="w-full sm:w-[500px] px-4 pt-2">
        <div className="flex items-center flex-row justify-between">
          {asset && (
            <CardThumbnail asset={asset} />
          )}
        </div>

        <div className="flex flex-row justify-between bg-slate-50 p-4 my-4">
          <div className="font-medium flex flex-col flex-center dark:text-dark-primary">
            <p>Quantity</p>
            <p>Amount</p>
          </div>
          <div className="font-medium flex flex-col flex-center text-gray-1 text-right">
            <p>{Number(order?.quantity || 0)}</p>
            <p>{formatNumber(amount.amount, 0, 8)} {amount?.symbol || ''}</p>
          </div>
        </div>

        {error ? (
          <div className="text-sm text-red text-stroke-thin mt-1 text-center break-words">
            { error }
          </div>
        ): (<></>)}
        
        {errorClaim ? (
          <div className="text-sm text-red text-stroke-thin mt-1 text-center break-words">
            { errorClaim }<br/>
            Click the refund button to claim your refunded amount
          </div>
        ): (<></>)}

        <div className="mt-6 mb-10p flex items-center	justify-center	">
          {currentChainId !== asset?.chainId ? 
            (
              <>
                {
                  !currentChainId || !addressConnect ? (
                    <RequireConnect className="!p-0" innerClassName="!p-0"/>
                  ): (
                    <Button
                      variant="dark"
                      type="button"
                      className="mt-6"
                      onClick={() => switchNetwork({chainId: asset?.chainId})}
                    >
                      Switch network
                    </Button>
                  )
                }
              </>
            ) : (
              <>
                {
                  isRefund || errorClaim ? (
                    <Button
                      disable={isLoading}
                      variant="dark"
                      className="flex"
                      onClick={onRefund}
                    >
                      { isLoading && <Loader className='mr-2' /> } {isLoading? 'Please waiting' : 'Refund'}
                    </Button>
                  ) : (
                    <Button
                      disable={isLoading || isLoadingCheck}
                      variant="dark"
                      className="flex"
                      onClick={onClaimNft}
                    >
                      { isLoading && <Loader className='mr-2' /> } {isLoading? 'Please waiting' : isLoadingCheck? 'Checking order' : 'Claim NFT'}
                    </Button>
                  )
                }
              </>
              
            )
          }
        </div>

      </div>
    </Modal>
  )
}

export default ClaimNFTModal
