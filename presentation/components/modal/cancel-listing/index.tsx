import { useState, useEffect } from 'react'
import { useAuthentication } from 'lib/hook/auth_hook'
import { toast } from 'react-toastify'
import { useAccount } from 'wagmi'
import { formatNumber } from 'lib/number'
import BigNumber from 'bignumber.js'

import Loader from 'presentation/components/loader'
import Modal from 'presentation/components/modal'
import Button from 'presentation/components/button'

import { envConfig } from 'presentation/constant/env'
import CardThumbnail from 'presentation/components/asset/card-thumbnail'
import ListingUseCase from 'presentation/use_cases/listing_use_case'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'

import Listing from 'domain/models/listing'

type Props = {
  listing?: Listing;
  isShow: boolean;
  onClose: () => void;
  onReload: () => void;
};

const listingUseCase = new ListingUseCase()

const CancelListingModal = ({
  listing,
  isShow = false,
  onClose = () => {},
  onReload = () => {},
}: Props) => {

  
  /**
   * DATA
   */
  const [isDisableForm, setIsDisableForm] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [amount, setAmount] = useState<any>({amount: null, amountUsd: null, symbol: '' })
  const [contracts, setContracts] = useState<any>({})
  const [asset, setAsset] = useState<any>({})
  const [error, setError] = useState<string>('')

  /**
   * HOOKS
   */
  const { getProviderEthers, switchNetwork} = useAuthentication()
  const { chainId: currentChainId } = useAccount()
  const authStateProvider = AuthStateProvider((state) => ({me: state.me}))

  /**
   * FUNCTIONS
   */
  
  
  const initEnvConfig = (chainId:any) => {
    const config: any = envConfig
    setContracts(config.contracts[chainId || 1])
  }

  const submitForm = async (e: any) => {
    try {
      e.preventDefault()
      setError('')
      setIsLoading(true)
      if (!contracts?.domainRegistryAddress || !contracts?.marketplaceAddress) {
        throw new Error('Domain registry or marketplace address is not support in this network')
      }
      if(!listing) {
        throw new Error('Not found listing')
      }
      if(!authStateProvider?.me?.publicAddresses[0]) {
        throw new Error('Please connect wallet')
      }
      const provider = await getProviderEthers()
      await listingUseCase.cancel(
        listing, 
        provider, 
        authStateProvider?.me.publicAddresses[0],
        contracts,
      )
      
      onReload()
      onClose()
      setIsLoading(false)
    } catch (error: any) {
      setError(error?.showMsgErrors ? error?.showMsgErrors() : error?.message || error)
      setIsLoading(false)
    }
  }

  /**
   * LIFECYCLES
   */
  useEffect(() => {
    const asset = listing?.listingAssets[0].asset
    const amount = listing?.price || 0
    setAsset(asset)    
    initEnvConfig(asset?.chainId)
    setAmount({
      amount: amount, 
      amountUsd: BigNumber(listing?.paymentToken?.priceUsd || 0).times(amount).toFixed(), 
      symbol: listing?.paymentToken?.symbol || '',
    })
  }, [listing])
  
  return (
    <Modal
      isShow={isShow}
      onClose={onClose}
      title="Cancel listing"
      modalWidthClass="min-w-3/4 md:min-w-3/4 xl:min-w-2/4 sm:w-fit w-11/12"
    >
      <div className="w-full sm:w-[500px] px-4 pt-2">
        <form className="w-full" onSubmit={submitForm}>
          <div className="flex items-center flex-row justify-between">
            {asset && (
              <CardThumbnail asset={asset} />
            )}
            <div>
              {amount.amount !== null && (
                <p className="font-medium text-sm text-gray-1 text-right">
                  {formatNumber(amount.amount, 0, 4)} {amount.symbol} <br/> 
                ${formatNumber(amount.amountUsd)}
                </p>
              )}
              {amount.amount === null && (
                <p className="font-medium text-sm text-gray-1 text-right">
                -- <br/> --
                </p>
              )}
            </div>
          </div>
          
          {error && (
            <div className="text-sm text-red text-stroke-thin mt-1 text-center break-words">
              {error}
            </div>
          )}
        
          <div className="mt-6 mb-10p flex items-center	justify-center	">
            {currentChainId !== asset.chainId ? 
              (
                <Button
                  variant="dark"
                  type="button"
                  className="mt-6"
                  onClick={() => switchNetwork({chainId: asset.chainId})}
                >
                  Switch network
                </Button>
              ) : (
                <Button
                  disable={isLoading || isDisableForm}
                  htmltype="submit"
                  variant="dark"
                  className="flex"
                  onClick={submitForm}
                >
                  { isLoading && <Loader className='mr-2' /> } {isLoading? 'Please waiting' : 'Cancel'}
                </Button>
              )
            }
            
          </div>
        </form>
      </div>
    </Modal>
  )
}

export default CancelListingModal
