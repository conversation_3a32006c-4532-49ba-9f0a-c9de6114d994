import { useState, useEffect } from 'react'
import Modal from 'presentation/components/modal'
import Input from 'presentation/components/input'
import Select from 'presentation/components/select'
import Button from 'presentation/components/button'
import DatetimeRange from 'presentation/components/datetimeRange'
import { durationDateOptions } from 'presentation/controllers/mockData/options'
import CollectionUseCase from 'presentation/use_cases/collection_use_case'
import { Query } from 'domain/repositories/collection_repository/query'
import Collection from 'domain/models/collection'

const collectionUseCase = new CollectionUseCase()

type Props = {
  isShow: boolean;
  onClose: () => void;
  onSubmit: (params: any) => void;
};

const RecommendationModal = ({
  isShow = false,
  onClose = () => {},
  onSubmit = () => {},
}: Props) => {
  const [collections, setCollections] = useState<Collection[]>([])
  const [isFetchingCollections, setIsFetchingCollections] = useState(true)

  const [showDateRange, setShowDateRange] = useState(false)
  const [duration, setDuration] = useState(durationDateOptions[0])

  const fetchCollections = async () => {
    const query = new Query()
    await collectionUseCase
      .search(query)
      .then((res) => {
        setCollections(res)
      })
      .catch((_error) => {
        setCollections([])
      })
    setIsFetchingCollections(false)
  }

  useEffect(() => {
    fetchCollections()
  }, [])

  return (
    <Modal
      isShow={isShow}
      onClose={onClose}
      title="Apply for Recommendation"
      modalWidthClass="min-w-3/4 md:min-w-2/4 xl:min-w-1/4 sm:w-fit w-11/12"
    >
      <div className="w-full sm:w-[466px] xl:px-12 pt-2">
        <p className="text-gray-1">
          Approval will be given within 2 weeks of application. Once approved,
          the listing will be posted after completion of payment of 0.2 ETH per
          week.
        </p>

        {!isFetchingCollections && (
          <>
            <p className="mt-5 mb-10p font-medium dark:text-dark-primary">
              Collection
            </p>
            <Select
              options={collections}
              rounded
              className="w-full"
              selectClassName="justify-between"
              arrowIconClass="stroke-none fill-primary-2"
              customWidth={true}
              placeholder="Select Collection"
              isCollectionList={true}
            />
          </>
        )}

        <p className="mt-5 mb-3 font-medium dark:text-dark-primary">Duration</p>
        <Input
          className="w-full cursor-pointer font-medium"
          type="text"
          value={duration.name}
          onFocus={() => setShowDateRange(true)}
          prefixIcon="calendar"
          prefixIconClassName="absolute left-7 top-3 w-6 h-6 stroke-none fill-primary cursor-pointer"
          suffixIcon="chevronDownSelect"
          suffixIconViewBox="0 0 12.962 7.411"
          suffixIconClassName="absolute right-7 top-5 w-[13px] h-[8px] fill-primary-2 dark:fill-white stroke-none cursor-pointer"
          readOnly
        />
        {showDateRange && (
          <DatetimeRange
            onSelect={(option: any) => setDuration(option)}
            onCloseDateRange={() => {
              setShowDateRange(false)
            }}
            className="custom xs3:max-w-[485px] xs3:min-w-[485px] max-w-[245px] min-w-[245px]"
          />
        )}
        <div className="mt-3 mb-10p text-center">
          <Button variant="dark" onClick={() => onSubmit}>
            Apply
          </Button>
        </div>
      </div>
    </Modal>
  )
}

export default RecommendationModal
