import clsx from 'clsx'
import React, { ReactDOM, MutableRefObject, ReactNode, useRef, useState, useEffect } from 'react'
import { createPortal } from 'react-dom'
import SvgIcon from '../svg-icon'

type Props = {
  title?: string,
  isShow: boolean,
  noModalBg?: boolean,
  contentWrapPadding?: string,
  contentWrapClass?: string,
  children?: ReactNode,
  modalClass?: string,
  modalWidthClass?: string,
  onClose: (e: any) => void,
}

const Modal = ({
  title,
  children,
  noModalBg = false,
  contentWrapPadding = 'p-3',
  contentWrapClass,
  modalClass,
  modalWidthClass = 'min-w-3/4 md:min-w-2/4 xl:min-w-1/4 w-fit',
  onClose,
}: Props) => {
  const contentRef: MutableRefObject<null | any> = useRef(null)
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)

    function closeOnEscapeKeyDown (e: any) {
      if ((e.charCode || e.keyCode) === 27) {
        onClose(e)
      }
    }
    document.addEventListener('keydown', closeOnEscapeKeyDown)
    return () => {
      document.removeEventListener('keydown', closeOnEscapeKeyDown)
    }
  }, [])

  return (
    <>
      {isMounted ?
        createPortal(
          <div className="py-8 md:py-12 bg-stone-800/50 fixed top-0 left-0 right-0 bottom-0 flex z-[999] backdrop-blur-sm overflow-y-scroll" onClick={onClose}>
            {noModalBg ? (
              <>{children}</>
            ) : (
              <div className={clsx('m-auto rounded-10 relative bg-white dark:bg-slate-300', modalClass, modalWidthClass)} ref={contentRef} onClick={(e) => e.stopPropagation()}>
                <p className="py-10p px-14 bg-blue-9 dark:bg-blue-1 text-dark-primary rounded-t-10 text-center text-lg font-medium relative">
                  {title}
                  <SvgIcon
                    onClick={onClose}
                    name="plus"
                    className="w-6 h-6 stroke-none fill-gray-1 rotate-45 absolute top-1/2 right-0 -translate-y-1/2 -translate-x-1/2 cursor-pointer" />
                </p>
                <div className={clsx(contentWrapPadding, contentWrapClass)}>
                  {children}
                </div>
              </div>
            )}
          </div>,
          document.getElementsByTagName('body')[0]
        ) : null
      }
    </>
  )
}

export default Modal
