import Button from 'presentation/components/button'
import Input from 'presentation/components/input'
import Modal from 'presentation/components/modal'
import UserUseCase from 'presentation/use_cases/user_use_case'
import { useState } from 'react'
const userUseCase = new UserUseCase()

type Props = {
  isShow: boolean,
  onClose: () => void,
  onSubmit: (user: any) => void,
}

const AddCollaborator = ({
  isShow = false,
  onClose = () => {},
  onSubmit = () => {},
}: Props) => {
  const [error, setError] = useState('')
  const [inputValue, setInputValue] = useState('')
  const [isValid, setIsValid] = useState(false)

  const checkValid = async () => {
    let rs : any = await userUseCase.findByPublicAddress(inputValue).then((res : any) => {
      if(res?.id) {
        setIsValid(true)
        setError('')
        return res
      } else {
        setIsValid(false)
        setError('This address does not exist.')
        return false
      }
    }).catch((err) => {
      setIsValid(false)
      setError('This address does not exist.')
      return false
    })
    return rs
  }

  const submit = async () => {
    if(inputValue) {
      const res:any = await checkValid()
      if(res.id) {
        onSubmit(res)
      }
    } else {
      setIsValid(false)
      setError('Please input something.')
    }
  }

  return (
    <>
      <Modal isShow={isShow} onClose={onClose} title="Add a Collaborator" modalClass="md:min-w-[400px] min-w-[300px]">
        <div className="md:px-11 px-3 pb-6 pt-[10px] w-full">
          <p className="font-medium mb-[10px]">Address</p>
          <Input
            type="text"
            width="w-full"
            placeholder="e.g. 0x1ed3… or destination.eth"
            value={inputValue}
            onBlur={(e) => {
              setInputValue(e.target.value)
            }}
            className={inputValue && isValid ? 'border-green-1' : (inputValue && !isValid ? 'border-red-1' : '')}
          />
          {inputValue && !isValid && (
            <span className="text-red-1">{ error }</span>
          )}
          <div className="text-center mt-5">
            <Button
              variant="dark" className="min-w-[100px]"
              onClick={() => submit()}
            >
              Submit
            </Button>
          </div>
        </div>
      </Modal>
    </>
  )
}

export default AddCollaborator
