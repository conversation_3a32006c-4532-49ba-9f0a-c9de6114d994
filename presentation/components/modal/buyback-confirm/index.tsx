'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import Image from 'next/image'
import SvgIcon from 'presentation/components/svg-icon'
import Button from 'presentation/components/button'
import BigNumber from 'bignumber.js'
import { formatNumber } from 'lib/number'
interface BuybackConfirmModalProps {
  card: any
  isOpen: boolean
  isLoading?: boolean
  onClose: () => void
  onConfirm: (card: any) => void
}

const BuybackConfirmModal = ({
  card,
  isOpen,
  isLoading = false,
  onClose,
  onConfirm,
}: BuybackConfirmModalProps) => {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
  })
  useEffect(() => {
    if (!isOpen) return

    // Calculate end time (buyBackTimeInDays from card mint date)
    const endTime = new Date(card.buybackExpirationTimestamp)
    const timer = setInterval(() => {
      const now = new Date().getTime()
      const distance = endTime.getTime() - now

      if (distance < 0) {
        clearInterval(timer)
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 })
        return
      }

      const days = Math.floor(distance / (1000 * 60 * 60 * 24))
      const hours = Math.floor(
        (distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
      )
      const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((distance % (1000 * 60)) / 1000)

      setTimeLeft({ days, hours, minutes, seconds })
    }, 1000)

    return () => clearInterval(timer)
  }, [isOpen, card.buybackExpirationTimestamp])

  const handleConfirm = () => {
    onConfirm(card)
  }
  const buybackPrice = BigNumber(card.buyBackPrice).toFixed()

  if (!isOpen) return null

  return (
    <motion.div
      className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-[10000]"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={onClose}
    >
      <motion.div
        className="relative bg-white dark:bg-gray-800 rounded-2xl p-6 max-w-md w-full mx-4"
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
        >
          <SvgIcon name="xCircle" className="w-5 h-5" />
        </button>

        {/* Header */}
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-dark-primary dark:text-white mb-2">
            Confirm Buyback
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Are you sure you want to buy back this card?
          </p>
        </div>

        {/* Card Info */}
        <div className="flex items-center gap-4 mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="relative w-16 h-20 flex-shrink-0">
            <Image
              src={
                card.image ||
                '/asset/images/vending-machine/card.jpg'
              }
              alt={card.name || 'Card'}
              fill
              className="object-cover rounded-md"
              onError={(e) => {
                const target = e.target as HTMLImageElement
                target.src = '/asset/images/vending-machine/card.jpg'
              }}
            />
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-dark-primary dark:text-white mb-1">
              {card.name || 'Unknown Card'}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {card?.attributes?.shop ||
                'Unknown Shop'}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {card?.attributes?.series ||
                'Unknown Series'}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Card
              <span className="font-bold ml-1">
                {card?.attributes?.cardNumber ||
                  'Unknown Card Number'}
              </span>
            </p>
          </div>
        </div>

        {/* Buyback Details */}
        <div className="space-y-4 mb-6">
          <div className="flex justify-between items-center">
            <span className="text-gray-600 dark:text-gray-400">
              Buy Back Price:
            </span>
            <span className="font-bold text-lg text-green-600">
              ${formatNumber(buybackPrice)}
            </span>
          </div>
        </div>

        {/* Countdown Timer */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4 mb-6">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 text-center">
            Buy Back Time Remaining
          </h4>
          <div className="grid grid-cols-4 gap-2">
            {[
              { label: 'Days', value: timeLeft.days },
              { label: 'Hours', value: timeLeft.hours },
              { label: 'Min', value: timeLeft.minutes },
              { label: 'Sec', value: timeLeft.seconds },
            ].map((item, index) => (
              <div key={index} className="text-center">
                <div className="bg-white dark:bg-gray-700 rounded-lg py-2 px-1 shadow-sm">
                  <div className="text-lg font-bold text-dark-primary dark:text-white">
                    {item.value.toString().padStart(2, '0')}
                  </div>
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {item.label}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3">
          <Button
            variant="outline"
            size="md"
            onClick={onClose}
            className="flex-1"
            disable={isLoading}
          >
            Cancel
          </Button>
          <Button
            variant="dark"
            size="md"
            onClick={handleConfirm}
            className="flex-1"
            disable={isLoading}
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                Processing...
              </div>
            ) : (
              'Confirm Buy back'
            )}
          </Button>
        </div>
      </motion.div>
    </motion.div>
  )
}

export default BuybackConfirmModal
