'use client'

import { motion, useAnimation } from 'framer-motion'
import { useEffect, useState } from 'react'
import Image from 'next/image'
import SvgIcon from 'presentation/components/svg-icon'
import { MintedCard } from 'data/datasources/remote/checkout_remote_datasource'
import { shortenAddress } from 'lib/utils'
import { DateTime } from 'luxon'
import Button from 'presentation/components/button'
interface CardViewerModalProps {
  cards: MintedCard[]
  initialIndex: number
  isOpen: boolean
  onClose: () => void
}

const CardViewerModal = ({
  cards,
  initialIndex,
  isOpen,
  onClose,
}: CardViewerModalProps) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex)
  const [isFlipping, setIsFlipping] = useState(false)
  const controls = useAnimation()

  const currentCard = cards[currentIndex]

  useEffect(() => {
    setCurrentIndex(initialIndex)
  }, [initialIndex])

  useEffect(() => {
    if (isOpen && currentCard) {
      // Start the card flip animation
      startFlipAnimation()
    }
  }, [isOpen, currentIndex])


  const startFlipAnimation = async () => {
    setIsFlipping(true)

    // Initial flip animation
    await controls.start({
      rotateY: 180,
      scale: 1,
      transition: { duration: 1.2, ease: 'easeInOut' },
    })

    // Start continuous rotation
    controls.start({
      rotateY: [180, 360],
      transition: {
        duration: 3,
        ease: 'linear',
        repeat: Infinity,
      },
    })
  }

  const goToNext = () => {
    if (currentIndex < cards.length - 1) {
      controls.stop()
      setCurrentIndex(currentIndex + 1)
    }
  }

  const goToPrevious = () => {
    if (currentIndex > 0) {
      controls.stop()
      setCurrentIndex(currentIndex - 1)
    }
  }

  const formatDate = (dateString: string) => {
    try {
      return DateTime.fromISO(dateString).toFormat('MMM dd, yyyy')
    } catch {
      return 'Unknown date'
    }
  }

  const getChainName = (chainId: number) => {
    const chainNames: { [key: number]: string } = {
      1: 'Ethereum',
      137: 'Polygon',
      80002: 'Polygon Amoy',
      11155111: 'Sepolia Ethereum',
    }
    return chainNames[chainId] || `Chain ${chainId}`
  }

  const openTransactionInExplorer = (txHash: string, chainId: number) => {
    const explorers: { [key: number]: string } = {
      1: 'https://etherscan.io',
      137: 'https://polygonscan.com',
      80002: 'https://amoy.polygonscan.com',
      11155111: 'https://sepolia.etherscan.io',
    }

    const baseUrl = explorers[chainId] || 'https://etherscan.io'
    window.open(`${baseUrl}/tx/${txHash}`, '_blank')
  }



  if (!isOpen || !currentCard) return null

  return (
    <motion.div
      className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-[9999]"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={onClose}
    >
      <div
        className="relative bg-white dark:bg-gray-800 rounded-2xl p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 z-10  cursor-pointer dark:hover:bg-gray-600 transition-colors"
        >
          <SvgIcon name="xCircle" className="w-5 h-5" />
        </button>

        {/* Navigation Arrows */}
        {currentIndex > 0 && (
          <button
            onClick={goToPrevious}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 p-3 rounded-full bg-white dark:bg-gray-700 shadow-lg hover:shadow-xl transition-all"
          >
            <SvgIcon name="chevronLeft" className="w-6 h-6" />
          </button>
        )}

        {currentIndex < cards.length - 1 && (
          <button
            onClick={goToNext}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 p-3 rounded-full bg-white dark:bg-gray-700 shadow-lg hover:shadow-xl transition-all"
          >
            <SvgIcon name="chevronRight" className="w-6 h-6" />
          </button>
        )}

        <div className="flex flex-col lg:flex-row gap-8 items-center">
          {/* Card Animation Section */}
          <div className="flex-shrink-0">
            <div style={{ perspective: 1200 }} className="relative">
              <motion.div
                animate={controls}
                initial={{ rotateY: 0, scale: 0.9 }}
                className="relative w-[280px] h-[380px] [transform-style:preserve-3d]"
              >
                {/* FRONT (rotated 180 so it faces viewer at rotateY=180) */}
                <div
                  className="absolute inset-0 [backface-visibility:hidden]"
                  style={{ transform: 'rotateY(180deg)' }}
                >
                  <Image
                    src={currentCard.image}
                    alt={currentCard.name}
                    fill
                    className="object-cover rounded-lg shadow-xl"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement
                      target.src = '/asset/images/vending-machine/card.jpg'
                    }}
                  />
                </div>

                {/* BACK (visible at rotateY=0) */}
                <div className="absolute inset-0 [backface-visibility:hidden]">
                  <Image
                    src={currentCard.image}
                    alt={currentCard.name}
                    fill
                    className="object-cover rounded-lg shadow-xl"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement
                      target.src = '/asset/images/vending-machine/card.jpg'
                    }}
                  />
                </div>
              </motion.div>
            </div>
          </div>

          {/* Card Details Section */}
          <div className="flex-1 space-y-6">
            {/* Card Title and Price */}
            <div className="flex items-start justify-between">
              <div className="w-full">
                <h2 className="text-2xl font-bold text-dark-primary dark:text-white mb-2">
                  {currentCard.name}
                </h2>
                <div className="flex items-center justify-between gap-3 w-full">
                  <span className="px-3 py-2 rounded-full text-sm font-bold bg-gradient-to-r from-yellow-400 to-orange-500 text-white shadow-lg border border-yellow-300">
                    💰{' '}
                    {currentCard.packPrice
                      ? `${currentCard.packPrice} $`
                      : '-- $'}
                  </span>
                </div>
              </div>
              {/* <div className="text-sm text-gray-500 dark:text-gray-400 absolute right-4 top-4 z-20">
                {currentIndex + 1} / {cards.length}
              </div> */}
            </div>

            {/* Card Attributes */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-3">
                <div>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    Series
                  </span>
                  <p className="font-semibold text-dark-primary dark:text-white">
                    {currentCard.attributes?.series || 'Unknown'}
                  </p>
                </div>
                <div>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    Card Number
                  </span>
                  <p className="font-semibold text-dark-primary dark:text-white">
                    {currentCard.attributes?.cardNumber || 'Unknown'}
                  </p>
                </div>
              </div>
              <div className="space-y-3">
                <div>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    Shop
                  </span>
                  <p className="font-semibold text-dark-primary dark:text-white capitalize">
                    {currentCard.attributes?.shop || 'Unknown'}
                  </p>
                </div>
              </div>
            </div>

            {/* Transaction Details */}
            <div className="border-t border-gray-200 dark:border-gray-600 pt-4">
              <h3 className="text-lg font-semibold text-dark-primary dark:text-white mb-3">
                Transaction Details
              </h3>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    TX Hash:
                  </span>
                  <button
                    onClick={() =>
                      openTransactionInExplorer(
                        currentCard.mintTxHash,
                        currentCard.chainId || 80002
                      )
                    }
                    className="text-sm text-primary-2 hover:text-primary-3 font-mono flex items-center gap-1 transition-colors"
                  >
                    {shortenAddress(currentCard.mintTxHash)}
                    <SvgIcon name="externalLink" className="w-3 h-3" />
                  </button>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    Minted:
                  </span>
                  <span className="text-sm text-dark-primary dark:text-white">
                    {formatDate(currentCard.mintBlockchainTimestamp)}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    Token ID:
                  </span>
                  <span className="text-sm text-dark-primary dark:text-white font-mono">
                    {shortenAddress(currentCard.tokenId)}
                  </span>
                </div>
              </div>
            </div>

            {/* Navigation Dots */}
            {cards.length > 1 && (
              <div className="flex justify-center gap-2 pt-4">
                {cards.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      controls.stop()
                      setCurrentIndex(index)
                    }}
                    className={`w-2 h-2 rounded-full transition-colors ${
                      index === currentIndex
                        ? 'bg-primary-2'
                        : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'
                    }`}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

    </motion.div>
  )
}

export default CardViewerModal
