import clsx from 'clsx'
import { ReactNode } from 'react'
import Image from 'next/image'

type Props = {
  children?: ReactNode,
  icon: string,
  isActive?: boolean,
  className?: string,
  onClick?: (e: any) => void,
}

const FilterTag = ({ children, icon, isActive, className, onClick }: Props) => {

  const activeClass = isActive ? 'border-gradient-sky text-gradient-sky' : 'border-gray-3'
  const baseClass = 'filter-tag cursor-pointer table items-center rounded-lg md:rounded-xl px-1 py-1 min-w-[8rem] md:w-32 md:py-3 md:px-3 xl:w-[136px] h-fit border hover:border-gradient-sky hover:text-gradient-sky transition'

  return (
    <div onClick={onClick} className={clsx(activeClass, baseClass, className)}>
      <span className="tag-icon relative icon-wrapper table-cell align-middle">
        {icon && (
          <Image alt='' src={icon} width={40} height={40} />
        )}
      </span>
      <span style={{ height: '3em', lineHeight: '1.4em' }} className="tag-text font-medium table-cell align-middle">
        {children}
      </span>
    </div>
  )
}

export default FilterTag
