import Tooltip from 'presentation/components/tooltip'
import AssetUseCase from 'presentation/use_cases/asset_use_case'
import ButtonIcon from 'presentation/components/button/button-icon'
import { toast } from 'react-toastify'

const assetUseCase = new AssetUseCase()

type Props = {
  collectionId: string;
};

const RefreshMetadata = ({ collectionId }: Props) => {
  const refreshMetadata = () => {
    assetUseCase
      .refreshMetadata({collectionId: collectionId})
      .then((res) => {
        toast.success('We\'ve queued this item for an update! Check back in a minute...')
      })
      .catch((_error) => {
        toast.error('Metadata refresh failed')
      })
  }  

  return (
    <Tooltip position="bottomLeft">
      <Tooltip.Content>
        <div className='font-medium text-base	text-center'>Refresh metadata <br/> of all assets</div> 
      </Tooltip.Content>
      <Tooltip.Body>
        <ButtonIcon
          className="h-fit"
          svgIcon="refresh"
          iconClassName="w-5 h-5 stroke-none fill-primary-2"
          onClick={() => {
            refreshMetadata()
          }}
        />
      </Tooltip.Body>
    </Tooltip>
  )
}

export default RefreshMetadata
