import SvgIcon from 'presentation/components/svg-icon'
import Image from 'next/image'
import Collection from 'domain/models/collection'
import { paymentTokenImages } from 'lib/valueObjects'

import { formatLocalisedCompactNumber, formatNumber } from 'lib/number'
type Props = {
  record: Collection,
  coinSymbolSize?: number,
  showCoinSymbolCorner?: boolean,
  showMoreIcon?: boolean,
  showItemCount?: boolean,
  onClickShowMore?: () => void,
}

const CollectionCommon = ({
  record,
  showCoinSymbolCorner,
  showMoreIcon,
  showItemCount = false,
  onClickShowMore }: Props) => {

  let priceSymbolLabel = 'ETH',
    priceSymbolName = 'eth',
    icon = '/asset/icons/ETH.svg'
  if(record.paymentTokens && record.paymentTokens.length > 0) {
    priceSymbolLabel = record.paymentTokens[0]?.label
    priceSymbolName = record.paymentTokens[0]?.name
    //icon = record.paymentTokens[0]?.icon
  }

  return (
    <div className="rounded-[10px] ring-1 ring-inset ring-gray-3 text-center relative">
      <div className="collection-bg relative w-full">

        <Image
          src={record.bannerImage?.url ? record.bannerImage?.url : '/asset/images/image-slide.png' } className="rounded-[10px]"
          alt={record.name || ''} layout="fill" objectFit="cover"
        />

      </div>
      <div className="relative w-full">
        <div className="collection-character relative mx-auto">

          <Image className="rounded-full object-cover bg-gray-4" src={record.logoImage?.url ? record.logoImage?.url : '/asset/images/character.png' } alt={record.name || ''} layout="fill"/>

        </div>
        {showMoreIcon && (
          <SvgIcon
            name="moreVertical"
            className="w-5 h-5 cursor-pointer stroke-none fill-primary-2 absolute bottom-2.5 right-1.5 translate-y-1/2"
            onClick={onClickShowMore}
          />
        )}
      </div>
      <div className={`collection-info px-4 text-center ${showMoreIcon ? 'pb-5 mt-2' : 'pb-4'}`}>
        <p className="name mt-[6px] font-bold xl:text-lg truncate text-dark-primary dark:text-white">
          {record.name}
        </p>
        {record.owner?.username && (
          <p className="nickname text-xs text-gray-1 dark:text-gray-3 mb-2 truncate text-stroke-thin">
            @{record.owner?.username}
          </p>
        )}
        {!showItemCount ? (
          <p className="font-medium price-symbol flex items-center justify-center">
            <span className="icon-wrapper mr-1.5">
              <Image alt="" src={paymentTokenImages[priceSymbolName?.toLocaleLowerCase()]} width={14} height={14}/>
            </span>
            <span className="align-middle text-xs text-dark-primary dark:text-white text-stroke-thin">
              {formatNumber(record.floorPriceUsd|| 0,0,6)} {priceSymbolLabel}
            </span>
          </p>
        ) : (
          <p className="mt-1 text-xs text-gray-1 dark:text-gray-3 text-stroke-thin">
            {record.numAssets} {record.numAssets > 1 ? 'items' : 'item'}
          </p>
        )}
      </div>
      {showCoinSymbolCorner && (
        <div className="absolute bottom-2 left-2" style={{ width: `${20}px`, height: `${20}px` }}>
          <Image className='object-contain' style={{ height: 'inherit' }} alt={record.name || ''} src={paymentTokenImages[priceSymbolName?.toLocaleLowerCase()]} width={20} height={20}/>
        </div>
      )}
    </div>
  )
}

export default CollectionCommon
