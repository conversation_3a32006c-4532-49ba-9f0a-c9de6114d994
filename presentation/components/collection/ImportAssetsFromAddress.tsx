import React, { useEffect, useState, useCallback } from 'react'
import Container from 'presentation/components/container'
import Image from 'next/image'
import tokenService from 'lib/tokenInfo'
import { getChainId } from 'lib/utils'
import { useAccount, useChainId } from 'wagmi'
import {envConfig} from 'presentation/constant/env'
import Chain from 'domain/value_objects/chain'
import PaymentToken from 'domain/models/payment_tokens'
import { useAuthentication } from 'lib/hook/auth_hook'
import { toast } from 'react-toastify'
import Button from 'presentation/components/button'
import Loader from 'presentation/components/loader'
import RequireConnect from 'presentation/components/require-connect'
import MeUseCase from 'presentation/use_cases/me_use_case'
import Collection from 'domain/models/collection'
import { DateTime } from 'luxon'
import { useRouter } from 'next/router'
import CollectionUseCase from 'presentation/use_cases/collection_use_case'
import PaymentTokensUseCase from 'presentation/use_cases/payment_tokens_use_case'
import Schema from 'domain/value_objects/schemas'
import { useForm } from 'lib/hook/useForm'
import UserUseCase from 'presentation/use_cases/user_use_case'
import CategoryUseCase from 'presentation/use_cases/category_use_case'
import { Query } from 'domain/repositories/category_repository/query'
const meUseCase = new MeUseCase()
const collectionsUseCase = new CollectionUseCase()
const paymentTokensUseCase = new PaymentTokensUseCase()
const categoryUseCase = new CategoryUseCase()
const userUseCase = new UserUseCase()
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'

interface ImportAssetsFromAddressProps {
  chainName: string
  contractAddress: string
}
const initFormData = {
  contractAddress: '',
  creatorTxHash: '',
  name: '',
  symbol: '',
  logoImage: '',
  featuredImage: '',
  bannerImage: '',
  slug: '',
  description: '',
  categories: [],
  paymentTokens: [],
  chainId: '',
  siteUrl: '',
  discordUrl: '',
  instagramUrl: '',
  mediumUrl: '',
  telegramUrl: '',
  owners: [],
  collaborators: [],
  isExplicit: false,
  schema: null,
}
const ImportAssetsFromAddress: React.FC<ImportAssetsFromAddressProps> = ({
  chainName,
  contractAddress,
}) => {
  const [contractInfo, setContractInfo] = useState({
    name: '',
    symbol: '',
    owner: '',
    banner: null,
    icon: null,
    success: false,
  })

  const { formData, setFormValue } = useForm(initFormData)
  const [isLoading, setIsLoading] = useState(true)
  const [isSwitchNetwork, setIsSwitchNetwork] = useState(false)
  const [isConnected, setIsConnected] = useState(false)
  const chainId = useChainId()
  const { getProviderEthers, switchNetwork, addressConnect} = useAuthentication()
  const router = useRouter()
  const [categories, setCategories] = useState([])
  const [paymentTokens, setPaymentTokens] = useState([])
  const [schema, setSchema] = useState(null)
  const [owner, setOwner] = useState('')
  const [collaborators, setCollaborators] = useState([])
  const [isChainSupported, setIsChainSupported] = useState(false)
  const authStateProvider = AuthStateProvider((state) => ({
    me: state.me,
    reload: state.reload,
  }))
  const fetchMe = async () => {
    await meUseCase
      .loginStatus()
      .then((res) => {
        console.log('🚀 ~ .then ~ res:', res)
        setIsConnected(res.isLoggedIn)
        if (res.isLoggedIn && res.user) {
          console.log('🚀 ~ fetchMe ~ res.user:', res.user)
          authStateProvider.reload() // This will update the authStateProvider.me
        }
        // if (res.isLoggedIn) {
        //   setOwners([res.user!])
        //   setCollaborators([{ value: res.user! }])
        // }
      })
      .catch(() => {
        setIsConnected(false)
      })
  }
  const fetchCategories = async () => {
    const query = new Query()
    let categories: any  = await categoryUseCase.search(query).then((res) => {
      return res
    }).catch((_error) => {
      return []
    })
    setCategories(categories)
    setFormValue('categories', [categories[0]])
   
  }
  const switchDefaultNetwork = useCallback(async () => {
    try {
      setIsLoading(true)
      const config: any = envConfig
      const chains = Chain.getResourceArray()
      const chainIdFromName = getChainId(chainName)
      console.log('🚀 ~ switchDefaultNetwork ~ chainIdFromName:', chainIdFromName)
      if (chainIdFromName === 'unknown') {
        throw new Error('Unsupported network')
      }
      const networkSupported = chains.find((network: any) => network.chainId === chainIdFromName)
      await switchNetwork(networkSupported)
      setIsLoading(false)
    } catch (error : any) {
      console.log('🚀 ~ switchDefaultNetwork ~ error:', error)
      toast.error(error?.message || error)
      setIsLoading(false)
    }
  },[chainName, switchNetwork])

  const checkNetwork = useCallback(() => {
    const chainIdFromName = getChainId(chainName)
    if (chainId !== chainIdFromName) {
      setIsSwitchNetwork(true)
    } else { 
      setIsSwitchNetwork(false)
    }

  }, [chainId, chainName])

  const getOwnerFromAddress = async (address : string) => {
    let rs : any = await userUseCase.findByPublicAddress(address)
    return rs
  }

  const submitRequest = async () => {
    try {
      setIsLoading(true)
      const ownersMap : any = []
      const collaboratorsMap : any = []
      const accountAddress = authStateProvider.me?.publicAddresses[0] || ''
      console.log('🚀 ~ submitRequest ~ accountAddress:', accountAddress)
      if(contractInfo.owner && contractInfo.owner.toLowerCase() == accountAddress.toLowerCase()){
        const user = await getOwnerFromAddress(contractInfo.owner)
        if (user) { 
          ownersMap.push(user)
          collaboratorsMap.push(user)
        }
      }
      
      const collectionData = {
        ...formData,
        schemaId: formData.schema?.id,
        owner: ownersMap[0],
        owners: ownersMap,
        collaborators: collaboratorsMap,
      }

      const newCollection = new Collection(
        undefined,
        collectionData.slug,
        collectionData.name,
        collectionData.symbol,
        collectionData.description,
        collectionData.siteUrl,
        collectionData.discordUrl,
        collectionData.telegramUrl,
        collectionData.twitterUrl,
        collectionData.mediumUrl,
        null,
        collectionData.instagramUrl,
        collectionData.isExplicit,
        0,
        0,
        0,
        0,
        0,
        0,
        DateTime.now(),
        DateTime.now(),
        collectionData.logoImage,
        collectionData.featuredImage,
        collectionData.bannerImage,
        collectionData.categories,
        collectionData.owner,
        collectionData.paymentTokens,
        [],
        collectionData.collaborators,
        collectionData.collectionAddress,
        ownersMap,
        collectionData.chainId,
        collectionData.schemaId,
        null,
        null,
        []
      )

      const importCollection = Object.assign(newCollection, { 
        contractAddress: formData.contractAddress,
      })
      console.log('🚀 ~ submitRequest ~ importCollection:', importCollection)
      
      await collectionsUseCase.importFromContractAddress(importCollection)
      toast.success('The collection has been imported successfully.')
      router.push('/collections')
      setIsLoading(false)
    } catch (err : any) {
      const errorMessage = err.getErrors?.()?.join(', ') || err.getMessage?.() || err.message || 'Failed to import collection'
      toast.error(errorMessage)
      setIsLoading(false)
    }
  }
  const formatPaymentTokens = async (chainIdSelect ?: number) => {
    try {

      const paymentTokens:PaymentToken[] = await paymentTokensUseCase.getPaymentTokens(chainIdSelect)
      const fixedOptions = paymentTokens.filter((token: any) => {
        return token.isNative || token.isWrappedEther
      })
      setFormValue('paymentTokens', fixedOptions) // set default value
    } catch (error) {
      console.log('🚀 ~ formatPaymentTokens ~ error:', error)
      
    }
  }
  const formatSchemas = async (contractType : string) => {
    let schemas = Schema.getResourceArray()
    schemas = schemas.map((schema : any) => {
      return {
        ...schema,
        value: schema.name, // for set checked  dropdown
      }
    })

    // set default schemaId selected
    if (schemas && schemas.length > 0) {
      let schemaSelect = schemas.find((schema : any) => {
        return schema.name.toUpperCase() === contractType
      })
      setFormValue('schema',schemaSelect)
    }
  }
  const formatFormValue = (info: any) => {
    setFormValue('slug', `${chainName}-${contractAddress}`)
    setFormValue('name', info.name)
    setFormValue('symbol', info.symbol)
    setFormValue('description', `Automatically imported ${info.name}`)
    setFormValue('logoImage', info.icon || null)
    setFormValue('featuredImage', null)
    setFormValue('bannerImage', info.banner || null)
    setFormValue('categories', [])
    setFormValue('chainId', chainId)
    setFormValue('contractAddress', contractAddress)
    setFormValue('twitterUrl', '')
  }
  const checkIsChainSupported = () => { 
    const chainIdFromName = getChainId(chainName)
    if (chainIdFromName !== 'unknown') {
      setIsChainSupported(true)
    } else {
      setIsChainSupported(false)
      toast.error('Unsupported Chain')
    }
  }
  useEffect(() => {
    const fetchContractInfo = async () => {
      try {
        setIsLoading(true)
        const chainIdFromName: any = getChainId(chainName)
        console.log('🚀 ~ fetchContractInfo ~ chainIdFromName:', chainIdFromName)
        const info = await tokenService.getContractInfo(chainIdFromName, contractAddress)
        console.log('🚀 ~ fetchContractInfo ~ info:', info)
        setContractInfo(info)
        formatFormValue(info)
        formatPaymentTokens(chainIdFromName)
        formatSchemas(info.contractType || '')
        await fetchCategories()

      } catch (error : any) {
        toast.error('Failed to fetch contract info')
        console.error('Error fetching contract info:', error)
      } finally {
        setIsLoading(false)
      }
    }

    if (chainName && contractAddress) {
      fetchContractInfo()
    }
  }, [chainName,chainId, contractAddress])
  
  useEffect(() => {
    fetchMe()
    checkNetwork()
  }, [chainName, chainId, checkNetwork])

  useEffect(() => {
    checkIsChainSupported()
  }, [chainName])
 
  return (
    <Container className="pt-9">
        
      <div className="relative flex flex-col items-center">
        {/* Banner */}
        <div className="relative w-full h-52">
          <Image
            className="rounded-lg"
            alt="NFT banner"
            src={contractInfo.banner || '/asset/images/NFT-banner.png'}
            fill
            style={{ objectFit: 'cover' }}
          />
        </div>
       
        {/* Logo */}
        <div className="absolute -bottom-16 left-1/2 transform -translate-x-1/2">
          <div className="relative h-32 w-32 overflow-hidden rounded-full border-4 border-white bg-white">
            <Image
              className="rounded-full"
              src={contractInfo.icon || '/asset/images/character.png'}
              alt={contractInfo.name || 'Quill'}
              fill
            />
          </div>
        </div>
      </div>

      <div className="mt-20 mb-8 text-center">
        <h1 className="text-2xl font-bold text-center">
          {contractInfo.name || 'Unknown Collection'}
        </h1>
        {contractInfo.symbol && <h2 className="text-xl font-bold text-center">
          ({contractInfo.symbol})
        </h2>}
      </div>

      {/* Add Submit Request button and other content here */}
      <div className="flex justify-center mt-8"> 
        {
          !isConnected || !addressConnect ? (
            <RequireConnect className="!p-0 !m-0 require-connect" innerClassName="!py-0"/>
          ) : <div>
            {isSwitchNetwork ? (
              <Button
                variant="dark"
                className="mt-6 flex"
                disable={isLoading || !isChainSupported}
                type="button"
                onClick={() => switchDefaultNetwork()}
              > 
                { isLoading && <Loader className='mr-2' /> }  Switch network
              </Button>
            ) : (
              <div className="flex flex-col items-center">
                <Button
                  disable={isLoading || !contractInfo.name || !contractInfo.symbol}
                  onClick={submitRequest}
                  variant="dark"
                  className="mt-6 flex"
                > 
                  { isLoading && <Loader className='mr-2' /> } Submit Request
                </Button>
                {!isLoading && (!contractInfo.name || !contractInfo.symbol) && <div className="error text-red-500 text-sm text-center mt-2">
                    Unable to retrieve the contract name or symbol. This collection cannot be imported.
                </div>} 
              </div>
            )}
          </div>}
        
                
      </div>
    </Container>
  )
}

export default ImportAssetsFromAddress