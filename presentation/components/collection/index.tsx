import Image from 'next/image'
import PriceSymbol from 'presentation/components/price-symbol'
import Badge from 'presentation/components/badge'
import ButtonIcon from '../button/button-icon'
import clsx from 'clsx'
import { formatNumberPostfix } from 'lib/number'
import BigNumber from 'bignumber.js'
import NetworkIcon from 'presentation/components/network-icon'

type Props = {
  record: any,
  name?: string,
  nickname?: string,
  bgImage: string,
  characterImage: string,
  rank?: number,
  price?: number,
  priceSymbol?: string,
  coinSymbolSize?: number,
}

const CollectionComponent = ({ price, priceSymbol, coinSymbolSize = 14, record = {} }: Props) => {
  return (
    <div className="rounded-[10px] ring-1 ring-inset ring-gray-3 text-center relative">
      <div className="collection-bg relative w-full">
        <Image
          src={record.bannerImage?.url || '/asset/images/image-slide.png'} className="rounded-[10px]"
          alt={record.name || ''} layout="fill" objectFit="cover"
        />
        {/* <Badge
          icon="cup" text={1} className="badge absolute md:hidden"
          iconClassName="w-3.5 stroke-none fill-white"
          viewBox="0 0 14.667 12"
        /> */}
        <ButtonIcon
          svgIcon="plus"
          iconClassName="w-4 h-4 stroke-[2] stroke-white"
          className="add-btn rounded-md md:hidden"
        />
      </div>
      <div className="collection-character relative mx-auto">
        <Image className="rounded-full object-cover bg-gray-4" src={record.logoImage?.url || '/asset/images/image-slide.png'} alt={record.name || ''} layout="fill"/>
      </div>
      <div className="collection-info px-1 xl:px-6 pb-4 text-center">
        <p className="name md:mt-[6px] mt-3 font-bold xl:text-lg truncate w-100 flex items-center justify-center">
          {record.chainId && <NetworkIcon chainId={record.chainId} width={16} height={16} className="mr-1" />}
          {record.name}
        </p>
        <p className="price md:mt-[6px] mt-0.5 text-sm font-bold md:hidden">
          {price?.toLocaleString() || 0} {priceSymbol || 'ETH'}
        </p>
        <p className="nickname mt-[6px] xl:text-lg hidden md:block text-gray-2 truncate">
          @{record.owner?.username}
        </p>
    
        <PriceSymbol
          icon='/asset/icons/dollar.png'
          iconSize={coinSymbolSize}
          className={clsx('mt-1 text-sm xl:text-lg hidden md:block', coinSymbolSize >= 22 ? 'mt-0.5' : '')} // Adjusted margin
          text="Floor Price" price={record.floorPriceUsd ?
            (BigNumber(record.floorPriceUsd).lt(0.000001) ? 
              BigNumber(record.floorPriceUsd).toFixed(8) : 
              formatNumberPostfix(BigNumber(record.floorPriceUsd).toFixed(8))) : 0}
          symbol="USD"
          priceCustom={record.floorPriceUsd ?
            (BigNumber(record.floorPriceUsd).lt(0.000001) ? 
              BigNumber(record.floorPriceUsd).toFixed(8) : 
              formatNumberPostfix(BigNumber(record.floorPriceUsd).toFixed(8))) : 0}
        />
        {/* Volume display */}
        <PriceSymbol
          icon='/asset/icons/dollar.png'
          iconSize={coinSymbolSize}
          className={clsx('mt-1 text-sm xl:text-lg hidden md:block', coinSymbolSize >= 22 ? 'mt-0.5' : '')} // Adjusted margin
          text="Volume" price={record.totalVolumeUsd ?
            formatNumberPostfix(record.totalVolumeUsd) : 0} symbol="USD"
          priceCustom={record.totalVolumeUsd ?
            formatNumberPostfix(record.totalVolumeUsd) : 0}
        />
      </div>
    </div>
  )
}

export default CollectionComponent
