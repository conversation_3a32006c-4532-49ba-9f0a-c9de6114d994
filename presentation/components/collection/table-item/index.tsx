import Image from 'next/image'
import { CollectionTableItemProps } from 'types/type'

const CollectionTableItem = (props: CollectionTableItemProps) => {
  return (
    <div className="flex items-center w-max" {...props}>
      <Image src={props.imagesrc} alt="" width={32} height={32} />
      <div className="ml-1 shrink-0 max-w-[10rem]">
        <p className="font-medium truncate">{props.name}</p>
        <p className="font-normal text-xs text-gray-1 dark:text-gray-3 text-stroke-thin truncate">
          {props.nickname}
        </p>
      </div>
    </div>
  )
}

export default CollectionTableItem
