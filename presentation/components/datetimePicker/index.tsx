import { useEffect, useRef, useState } from 'react'
import DatePicker from 'react-datepicker'
import { formatDate } from 'lib/utils'
import Input from 'presentation/components/input'
import SvgIcon from 'presentation/components/svg-icon'
import 'react-datepicker/dist/react-datepicker.css'
import Timepicker from 'presentation/components/timepicker'
import clsx from 'clsx'

type Props = {
  onCloseDateRange?: () => void
  dateFormat?: string
  monthsShown?: number
  inline?: boolean
  showTimeInput?: boolean
  className?: string
  selectedDate?: string
  onSelectTime?: (option: any) => void
  onChangeDate?: (date: Date) => void
}

const DatetimePicker = (props: Props) => {
  const {
    onCloseDateRange,
    monthsShown = 2,
    dateFormat = 'MM/dd/yyyy',
    inline = false,
    showTimeInput = false,
    className = '',
    selectedDate = null,
    onSelectTime = () => {},
    onChangeDate = () => {},
  } = props

  const [startDate, setStartDate] = useState<Date | null>(new Date())

  const dateRangeRef = useRef(null)
  useEffect(() => {
    if (selectedDate) {
      console.log("🚀 ~ useEffect ~ selectedDate:", selectedDate)
      setStartDate(new Date(selectedDate))
    } else if(selectedDate === ''){
      setStartDate(null);
    }
  }, [selectedDate])
  return (
    <div ref={dateRangeRef} className={clsx('duration-daterange', className)}>
      {/* {!inline && (
        <SvgIcon
          onClick={onCloseDateRange}
          name="plus"
          className="w-6 h-6 stroke-none fill-gray-1 rotate-45 absolute top-0 right-0 translate-y-1/2 -translate-x-1/2 cursor-pointer"
        />
      )} */}
      {inline && <div className="flex flex-col px-4 pt-5">
        <p className="font-medium mb-3 text-dark-primary dark:text-white">
          Date and Time
        </p>
        <div className="xs3:inline-flex xs3:flex-row flex flex-col xs3:space-x-4 xs3:space-y-0 space-y-4">
          <Input width="w-[147px]" value={ formatDate(startDate)} />
          {showTimeInput && (
            <Timepicker onSelect={(option) => onSelectTime(option)} />
          )}
        </div>
      </div>}
      <div className="mt-5 flex center">
        <DatePicker
          selected={startDate}
          dateFormat={dateFormat}
          onChange={(date: Date) => {
            console.log("🚀 ~ DatetimePicker ~ date:", date)
            setStartDate(date)
            onChangeDate(date)
          }}
          monthsShown={monthsShown}
          inline={inline}
        />
      </div>
    </div>
  )
}

export default DatetimePicker
