import clsx from 'clsx'
import { useState } from 'react'
import SvgIcon from 'presentation/components/svg-icon'
import DatetimeRange from 'presentation/components/datetimeRange'

type Props = {
  options: any,
  className?: string,
  defaultSelected?: any,
  showDatepicker?: <PERSON>olean,
  onSelect: (option: any) => void,
}

const FilterTime = ({options, className, defaultSelected, showDatepicker = false, onSelect, ...props}: Props) => {
  const [showDateRange, setShowDateRange] = useState(false)
  const [selected, setSelected] = useState(defaultSelected || {})
  const handleSelect = (option: any) => {
    onSelect(option)
    setSelected(option)
  }

  return (
    <div className={clsx('filter-time', className)}>
      <ul className="flex items-center gap-x-[2px]">
        {options.map((option: any, index: number) => (
          <li
            key={index} className={clsx('filter-time-item', {
              active: selected.value === option.value,
            })}
            onClick={() => handleSelect(option)}
          >
            {option.name}
          </li>
        ))}
        {showDatepicker && (
          <li className="flex relative w-10 h-10 ml-1.5 z-50">
            <span className="flex items-center h-full">
              <SvgIcon
                name="calendar"
                className="w-6 h-6 stroke-none fill-primary"
                onClick={() => setShowDateRange(!showDateRange)}
              />
            </span>
            {showDateRange && (
              <div className="absolute top-10 right-0">
                <DatetimeRange
                  showTimeInput={false}
                  showDuration={false}
                  onCloseDateRange={() => {setShowDateRange(false)}}
                />
              </div>
            )}
          </li>
        )}
      </ul>
    </div>
  )
}

export default FilterTime
