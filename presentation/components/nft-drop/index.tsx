import Image from 'next/image'
import { Fragment } from 'react'
import { CountDown } from 'types/type'
import SvgIcon from 'presentation/components/svg-icon'
import ShowMore from 'presentation/components/show-more'
import PriceSymbol from 'presentation/components/price-symbol'
import SocialNetworks from 'presentation/page_components/socialNetworks'

type Props = {
  className?: string,
  record?: any,
}

const NftDropCard = ({
  className = '',
  record = {},
}: Props) => {

  return (
    <div className={`nft-card-wrap ${className}`}>
      <SvgIcon
        name="calendarPlus"
        className="w-5 h-22 stroke-none fill-primary-2 cursor-pointer absolute top-2 right-10p"
      />
      <p className="category-badge">{record.category?.shortenedNamae}</p>
      <Image
        src={record.logoImage?.url}
        width={56} height={56}
        alt={record.name || ''}
        className="rounded-full"
      />
      <p className="mt-1 text-sm font-bold">{record.name}</p>
      <ShowMore
        height="auto"
        iconClass="w-3 h-3 stroke-primary-2 fill-none stroke-[2]"
        textClass="text-xs text-primary-2"
        showMoreClass="flex items-center justify-center"
      >
        <p className="mt-1 text-center text-xs text-gray-1 dark:text-gray-3 text-stroke-thin">
          {record.description}
        </p>
      </ShowMore>
      <div className="mt-[5px]">
        <PriceSymbol
          className="text-xs leading-5" text="Price"
          price={record.floorPrice}
          symbol={record.paymentToken ? record.paymentToken.label : 'ETH'}
        />
        <PriceSymbol
          className="text-xs leading-5"
          text="Volume"
          price={record.totalVolume} symbol="K"
        />
        <p className="text-xs font-medium leading-5">Date: {record.date}</p>
        <p className="text-xs font-medium leading-5 text-center">{record.time}</p>
      </div>

      <hr className="w-full border-t border-gray-3 my-1" />
      {record.countDown && (
        <div className="w-full flex justify-center gap-[7px]">
          {Object.keys(record.countDown).map((item, i) => (
            <Fragment key={i}>
              <div key={i} className="shrink-0">
                <p className="text-sm font-bold text-blue-4 text-center">
                  {String(record.countDown[item as keyof CountDown]).padStart(2, '0')}
                </p>
                <p className="text-xxs text-gray-1 dark:text-gray-3 capitalize text-stroke-thin">{item}</p>
              </div>
              {i < Object.keys(record.countDown).length - 1 && (
                <div className="text-sm font-bold text-blue-4">:</div>
              )}
            </Fragment>
          ))}
        </div>
      )}
      <div className="flex mt-10p">
        <SocialNetworks {...record} iconSize="icon-21" spacing="mr-0" hasShadow={false} />
      </div>

    </div>
  )
}

export default NftDropCard
