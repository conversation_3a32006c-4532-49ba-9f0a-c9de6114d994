import SvgIcon from 'presentation/components/svg-icon'
import clsx from 'clsx'

type Props = {
  title?: string,
  subtitle?: string,
  className?: string,
}

const NoData = ({ title = 'No data yet', subtitle = 'Check back later', className }: Props) => {
  return (
    <div className={clsx('py-2 text-center', className)}>
      <SvgIcon name="noSymbol"/>
      <p className="mt-2 text-lg font-medium">{title}</p>
      <p className="mt-2">{subtitle}</p>
    </div>
  )
}

export default NoData
