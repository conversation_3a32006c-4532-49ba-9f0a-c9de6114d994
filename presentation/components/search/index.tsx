import SvgIcon from 'presentation/components/svg-icon'
import { useDebounce } from 'lib/hook/customHook'
import { useState , useEffect, useRef} from 'react'
type Props = {
  inputClass?: string;
  placeholder?: string;
  onChange?: (e: any) => void;
  onFocus?: (e: any) => void;
  handleEnter?: (e: any) => void;
};

const Search = ({
  inputClass,
  placeholder = 'Search items, collections and accounts',
  onChange,
  onFocus,
  handleEnter,
  ...props
}: Props) => {
  const [value, setValue] = useState('')
  const debouncedValue = useDebounce(value, 300)
  const previousDebouncedValue = useRef(debouncedValue)

  useEffect(() => {
    if (onChange && debouncedValue !== previousDebouncedValue.current) {
      onChange({ target: { value: debouncedValue } })
    }
    previousDebouncedValue.current = debouncedValue
  }, [debouncedValue, onChange])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setValue(e.target.value)
  }

  const onKeyDown = async (e: any) => {
    if (e.key === 'Enter' && handleEnter) {
      handleEnter!(e)
    }
  }

  return (
    <div className="search-wrap relative" {...props}>
      <input
        className={`px-10 py-2 lg:px-11 lg:py-[15px] bg-gray-4 w-full dark:bg-blue-1 text-sm rounded-full outline-none dark:text-slate-300
        focus:shadow-[0_0_12px_1px_rgba(0,0,0,0.05)] focus:px-10 focus:lg:px-11 transition duration-150 ${inputClass}`}
        placeholder={placeholder}
        value={value}
        onChange={handleInputChange}
        onFocus={onFocus}
        onKeyDown={onKeyDown}
      />
      <span className="top-1/2 left-4 -translate-y-1/2 absolute leading-none">
        <SvgIcon
          name="search"
          className="w-5 h-5 stroke-none fill-dark-primary dark:fill-white"
          viewBox="0 0 20 20"
        />
      </span>
    </div>
  );
};

export default Search;
