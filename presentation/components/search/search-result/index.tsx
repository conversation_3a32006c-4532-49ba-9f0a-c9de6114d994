import clsx from 'clsx'
import Image from 'next/image'
import Link from 'next/link'
import React, { useEffect, useState } from 'react'
import Collection from 'domain/models/collection'
import { getChainName } from 'lib/utils'
type Props = {
  loading: boolean;
  results?: Collection[];
  showResult?: boolean;
  className?: string;
  inlineMode?: boolean;
  isLink?: boolean;
  onClickOutside?: () => void;
  onClickItem?: (item: Collection) => void;
};

const SearchResult = React.forwardRef<HTMLDivElement, Props>(
  (
    {
      loading,
      results,
      showResult,
      className,
      inlineMode = false,
      isLink = true,
      onClickOutside,
      onClickItem = () => {},
    }: Props,
    ref: any
  ) => {
    useEffect(() => {
      function handleClickOutside (e: any) {
        if (ref?.current && !ref.current.contains(e.target)) {
          onClickOutside && onClickOutside()
        }
      }
      document.addEventListener('mousedown', handleClickOutside)
      return () => {
        document.removeEventListener('mousedown', handleClickOutside)
      }
    }, [ref.current])

    return (
      <div
        ref={ref}
        className={clsx(
          `w-full rounded-lg p-3 ${
            inlineMode ? '' : 'absolute drop-shadow-md'
          } z-20 dark:bg-slate-800 space-y-3`,
          className
        )}
      >
        {loading ? (
          <p className="text-slate-500">Loading ...</p>
        ) : (
          <>
            {results?.length ? (
              results.map((item, index) => (
                <div
                  key={index}
                  className="block cursor-pointer"
                  onClick={() => onClickItem(item)}
                >   
                  {isLink ? <Link href={`/collections/${getChainName(item.chainId)}/${item.slug}`}>
                    <div className="flex gap-2 items-center">
                      {item.logoImage?.url && (
                        <Image
                          className="rounded-full"
                          src={item.logoImage?.url}
                          alt={item.name || ''}
                          width={30}
                          height={30}
                        />
                      
                      )}
                      <div className="w-full truncate">
                        <p className="font-medium dark:text-slate-300">
                          {item.name}
                        </p>
                        <p className="text-xs text-gray-2 w-full truncate">
                          @{item.owner?.username}
                        </p>
                      </div>
                    </div>
                  </Link> :   
                    <div className="flex gap-2 items-center">
                      {item.logoImage?.url && (
                        <Image
                          className="rounded-full"
                          src={item.logoImage?.url}
                          alt={item.name || ''}
                          width={30}
                          height={30}
                        />
                      
                      )}
                      <div className="w-full truncate">
                        <p className="font-medium dark:text-slate-300">
                          {item.name}
                        </p>
                        <p className="text-xs text-gray-2 w-full truncate">
                          @{item.owner?.username}
                        </p>
                      </div>
                    </div>}
                </div>
              ))
            ) : (
              <p className="dark:text-slate-500">Not found</p>
            )}
          </>
        )}
      </div>
    )
  }
)

SearchResult.displayName = 'SearchResult'

export default SearchResult
