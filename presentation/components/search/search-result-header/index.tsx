import clsx from 'clsx'
import Image from 'next/image'
import Link from 'next/link'
import React, { useEffect, useState } from 'react'
import Collection from 'domain/models/collection'
import Asset from 'domain/models/asset'
import User from 'domain/models/user'
import { getChainName } from 'lib/utils'
type Props = {
  loading: boolean;
  searchResultAssets: Asset[];
  searchResultCollections: Collection[];
  searchResultUsers: User[];
  showResult?: boolean;
  className?: string;
  inlineMode?: boolean;
  onClickOutside?: () => void;
  onClickItem?: (item: Collection | Asset) => void;
};

const SearchResultHeader = React.forwardRef<HTMLDivElement, Props>(
  (
    {
      loading,
      searchResultAssets,
      searchResultCollections,
      searchResultUsers,
      showResult,
      className,
      inlineMode = false,
      onClickOutside,
      onClickItem = () => {},
    }: Props,
    ref: any
  ) => {
    useEffect(() => {
      function handleClickOutside (e: any) {
        if (ref?.current && !ref.current.contains(e.target)) {
          onClickOutside && onClickOutside()
        }
      }
      document.addEventListener('mousedown', handleClickOutside)
      return () => {
        document.removeEventListener('mousedown', handleClickOutside)
      }
    }, [ref.current])

    return (
      <div
        ref={ref}
        className={clsx(
          `w-full rounded-lg p-3 ${
            inlineMode ? '' : 'absolute drop-shadow-md'
          } z-20 dark:bg-slate-800 space-y-3`,
          className
        )}
      >
        {loading ? (
          <p className="text-slate-500">Loading ...</p>
        ) : (
          <>
            {(searchResultAssets && searchResultCollections) ||
            searchResultUsers ? (
                <>
                  {searchResultCollections &&
                  searchResultCollections.length > 0 && (
                    <p className="font-bold text-gray-1">Collections</p>
                  )}
                  {searchResultCollections &&
                  searchResultCollections.length > 0 &&
                  searchResultCollections.map((item, index) => (
                    <div
                      key={index}
                      className="block cursor-pointer"
                      onClick={() => onClickItem(item)}
                    >
                      <Link href={`/collections/${getChainName(item.chainId)}/${item.slug}`}>
                        <div className="flex gap-2 items-center">
                          {item.logoImage?.url && (
                            <Image
                              className="rounded-full"
                              src={item.logoImage?.url}
                              alt={item.name || ''}
                              width={30}
                              height={30}
                            />
                          
                          )}
                          <div className="w-full truncate">
                            <p className="font-medium dark:text-slate-300">
                              {item.name}
                            </p>
                            <p className="text-xs text-gray-2 w-full truncate">
                              @{item.owner?.username}
                            </p>
                          </div>
                        </div>
                      </Link>
                    </div>
                  ))}

                  {searchResultAssets &&
                  searchResultAssets.length > 0 &&
                  searchResultCollections &&
                  searchResultCollections.length > 0 &&
                  searchResultUsers.length > 0 && (
                    <hr className="border-gray-200 dark:border-slate-700" />
                  )}
                  {searchResultAssets && searchResultAssets.length > 0 && (
                    <p className="font-bold text-gray-1">Assets</p>
                  )}

                  {searchResultAssets &&
                  searchResultAssets.length > 0 &&
                  searchResultAssets.map((item, index) => (
                    <div
                      key={index}
                      className="block cursor-pointer"
                      onClick={() => onClickItem(item)}
                    >
                      <Link href={`/assets/${item.id}`}>
                        <div className="flex gap-2 items-center">
                          {item.thumbnail?.url && (
                            <Image
                              className="rounded-full"
                              src={item.thumbnail?.url}
                              alt={item.name || ''}
                              width={30}
                              height={30}
                            />
                        
                          )}
                          <div className="w-full truncate">
                            <p className="font-medium dark:text-slate-300">
                              {item.name}
                            </p>
                            <p className="text-xs text-gray-2 w-full truncate">
                            @{item.address}
                            </p>
                          </div>
                        </div>
                      </Link>
                    </div>
                  ))}
                  {searchResultAssets &&
                  searchResultAssets.length === 0 &&
                  searchResultCollections &&
                  searchResultCollections.length === 0 &&
                  searchResultUsers.length === 0 && (
                    <p className="dark:text-slate-500">Not found</p>
                  )}

                  {searchResultUsers && searchResultUsers.length > 0 && (
                    <p className="font-bold text-gray-1">Users</p>
                  )}
                  {searchResultUsers.map((user, index) => (
                    <div className="block cursor-pointer" key={index}>
                      <Link href={`/users/${user.username}`}>
                        <div className="flex gap-2 items-center">
                          {user.thumbnail?.url && (
                            <Image
                              className="rounded-full"
                              src={user.thumbnail?.url}
                              alt={user.username || ''}
                              width={30}
                              height={30}
                            />
                          )}
                          <div className="w-full truncate">
                            <p className="font-medium dark:text-slate-300">
                              {user.username}
                            </p>
                          </div>
                        </div>
                      </Link>
                    </div>
                  ))}
                </>
              ) : (
                <p className="dark:text-slate-500">Not found</p>
              )}
          </>
        )}
      </div>
    )
  }
)

SearchResultHeader.displayName = 'SearchResultHeader'

export default SearchResultHeader
