import React, { useState } from 'react'
import Switch from 'presentation/components/switch'
import { toast } from 'react-toastify'
import { useCustomQuery } from 'lib/hook/useCustomQuery'
import SettingUseCase from 'presentation/use_cases/setting_use_case'

const settingUseCase = new SettingUseCase()

const SettingsBuy = () => {
  const [isLoading, setIsLoading] = useState(false)

  const { data: enableBuy = false, refetch } = useCustomQuery(
    ['SalesSetting'],
    async () => {
      try {
        const res = await settingUseCase.getSettingVMSale()
        return res.enable
      } catch (error: any) {
        toast.error(
          error?.showMsgErrors
            ? error?.showMsgErrors()
            : error?.message || error
        )
      }
    },
    { enabled: true }
  )

  const handleToggleSalesSetting = async (enable: boolean) => {
    try {
      setIsLoading(true)
      await settingUseCase.updateSettingVMSale({ enable })
      await refetch()
      setIsLoading(false)
      toast.success('Update successful')
    } catch (error: any) {
      toast.error(
        error?.showMsgErrors ? error?.showMsgErrors() : error?.message || error
      )
      setIsLoading(false)
    }
  }

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">Buying Settings</h2>
      <div className="w-full flex md:items-center items-start ">
        <p className="block text-sm font-medium text-gray-700 dark:text-white min-w-24">
          Enable Buy
        </p>
        <Switch
          defaultActive={enableBuy}
          value={enableBuy}
          loading={isLoading}
          onClick={(active) => handleToggleSalesSetting(active)}
        />
      </div>
    </div>
  )
}

export default SettingsBuy
