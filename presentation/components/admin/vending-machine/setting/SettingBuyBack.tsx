import React, { useState, useCallback, useEffect } from 'react'
import Switch from 'presentation/components/switch'
import { toast } from 'react-toastify'
import { useForm } from 'lib/hook/useForm'
import Button from 'presentation/components/button'
import Input from 'presentation/components/input'
import Loader from 'presentation/components/loader'
import { useCustomQuery } from 'lib/hook/useCustomQuery'
import SettingUseCase from 'presentation/use_cases/setting_use_case'
import { validatePrice } from 'lib/validate'

const settingUseCase = new SettingUseCase()

const SettingBuyBack = () => {
  const [isLoading, setIsLoading] = useState(false)
  const { formData, setFormValue } = useForm({
    enable: false,
    rate: '',
    timeByDay: '',
  })
  const [isDisableForm, setIsDisableForm] = useState(false)
  const [errorRate, setErrorRate] = useState('')
  const [errorTime, setErrorTime] = useState('')

  const { data, isFetched, refetch } = useCustomQuery(
    ['SettingBuyBack'],
    async () => {
      try {
        const res = await settingUseCase.getSettingVMBuyBack()
        return res
      } catch (error: any) {
        toast.error(
          error?.showMsgErrors
            ? error?.showMsgErrors()
            : error?.message || error
        )
      }
    },
    { enabled: true }
  )

  const validateForm = useCallback(() => {
    const rateError = validatePrice({
      value: formData.rate,
      fieldName: 'Rate',
      min: 1,
      maxDecimals: 2,
      max: 100,
    })
    setErrorRate(rateError?.message || '')

    const timeError = validatePrice({
      value: formData.timeByDay,
      fieldName: 'Time',
      min: 1,
      maxDecimals: 0,
      max: 1000,
    })
    setErrorTime(timeError?.message || '')

    if (rateError || timeError) {
      setIsDisableForm(true)
      return false
    }

    setIsDisableForm(false)
    return true
  }, [formData])

  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault()
      try {
        const success = validateForm()
        if (!success) return
        setIsLoading(true)
        await settingUseCase.updateSettingVMBuyBack(formData)
        await refetch()
        setIsLoading(false)
        toast.success('Update successful')
      } catch (error: any) {
        toast.error(
          error?.showMsgErrors
            ? error?.showMsgErrors()
            : error?.message || error
        )
      }
      setIsLoading(false)
    },
    [formData]
  )

  useEffect(() => {
    if (data) {
      setFormValue('enable', data?.enable || false)
      setFormValue('rate', `${data?.rate || ''}`)
      setFormValue('timeByDay', `${data?.timeByDay || ''}`)
    }
  }, [data])

  useEffect(() => {
    if (isFetched) {
      validateForm()
    }
  }, [formData, isFetched])

  const wrapInputClass = 'w-full flex items-center'
  const labelClass =
    'block text-sm font-medium text-gray-700 dark:text-white min-w-24'
  const errorClass = 'text-sm text-red text-stroke-thin mt-1 text-left'

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">Buyback Settings</h2>

      <form
        onSubmit={handleSubmit}
        className="flex flex-col gap-4 max-w-[400px]"
      >
        <div className={wrapInputClass}>
          <label className={labelClass}>Enable</label>
          <Switch
            defaultActive={formData?.enable}
            value={formData?.enable}
            onClick={(active) => setFormValue('enable', active)}
          />
        </div>
        <div className={wrapInputClass}>
          <label className={labelClass}>Rate (%)</label>
          <div className="w-full">
            <Input
              wrapClass="w-full"
              type="text"
              value={formData.rate}
              maxLength={50}
              disabled={isLoading}
              placeholder="Ex. 90"
              deleteIcon
              onChange={(e) => setFormValue('rate', e.target.value)}
            />
            {errorRate && <div className={errorClass}>{errorRate}</div>}
          </div>
        </div>

        <div className={wrapInputClass}>
          <label className={labelClass}>Time (days)</label>
          <div className="w-full">
            <Input
              wrapClass="w-full"
              type="text"
              value={formData.timeByDay}
              maxLength={50}
              disabled={isLoading}
              placeholder="Ex. 7"
              deleteIcon
              onChange={(e) => setFormValue('timeByDay', e.target.value)}
            />
            {errorTime && <div className={errorClass}>{errorTime}</div>}
          </div>
        </div>

        <div>
          <Button
            type="submit"
            variant="dark"
            className="flex items-center justify-center "
            disable={isLoading || isDisableForm}
          >
            {isLoading ? <Loader /> : 'Save'}
          </Button>
        </div>
      </form>
    </div>
  )
}

export default SettingBuyBack
