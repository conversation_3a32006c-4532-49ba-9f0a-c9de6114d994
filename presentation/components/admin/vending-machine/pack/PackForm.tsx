import React, { useState, useEffect } from 'react'
import Image from 'next/image'
import SvgIcon from 'presentation/components/svg-icon'
import OddsTable from './OddsTable'
import Select from 'presentation/components/select'
import Input from 'presentation/components/input'
import Button from 'presentation/components/button'
import Loader from 'presentation/components/loader'

interface OddsRow {
  id: number;
  fromPrice: string;
  toPrice: string;
  rate: string;
}

export interface PackFormData {
  id?: number;
  name: string;
  price: string | number;
  description: string;
  categoryId: string;
  image: File | null;
  imageUrl?: string;
  odds: OddsRow[];
}

interface PackFormProps {
  initialData: PackFormData;
  onSubmit: (data: PackFormData) => Promise<void>;
  isSubmitting: boolean;
  submitLabel: string;
  onImageUpload?: (file: File) => Promise<string>; // Returns imageUrl
  categories: Array<{ id: string; name: string; }>; // Categories from API
  errorMessage?: string;
}

const PackForm: React.FC<PackFormProps> = ({
  initialData,
  onSubmit,
  isSubmitting,
  submitLabel,
  onImageUpload,
  categories,
  errorMessage,
}) => {
  const [formData, setFormData] = useState<PackFormData>(() => initialData)
  const [imagePreview, setImagePreview] = useState<string>(() => initialData.imageUrl || '')
  const [validationError, setValidationError] = useState<string>('')
  const [isUploadingImage, setIsUploadingImage] = useState<boolean>(false)
  const [hasPriceRangeErrors, setHasPriceRangeErrors] = useState<boolean>(false)

  // Only update form data when switching to edit mode with different data
  useEffect(() => {
    // Only reset form if we're switching to edit mode with actual data and different ID
    if (initialData.id && initialData.id !== formData.id) {
      setFormData(initialData)
      setImagePreview(initialData.imageUrl || '')
    }
  }, [initialData.id, formData.id, initialData.imageUrl])

  // Transform categories for Select component
  const categoryOptions = categories.map((cat) => ({
    id: cat.id,
    name: cat.name,
    value: cat.id,
  }))
  
  // Validate odds total
  useEffect(() => {
    validateOdds()
  }, [formData.odds])
  
  const validateOdds = () => {
    // Check for empty required fields
    if (formData.odds.length === 0) {
      setValidationError('')
      return true
    }

    // Check for empty fields in any row - safely check for undefined values
    const hasEmptyFields = formData.odds.some((row) =>
      !row.fromPrice?.trim() || !row.toPrice?.trim() || !row.rate?.trim()
    )

    if (hasEmptyFields) {
      setValidationError('All fields in the odds table are required')
      return false
    }

    // Check price range validation (To Price > From Price)
    const hasPriceRangeErrors = formData.odds.some((row) => {
      const fromPrice = parseFloat(row.fromPrice) || 0
      const toPrice = parseFloat(row.toPrice) || 0
      return toPrice <= fromPrice
    })

    if (hasPriceRangeErrors) {
      setValidationError('To Price must be greater than From Price in all rows')
      return false
    }

    // Validate total percentage equals 100%
    const total = formData.odds.reduce((sum, odd) => {
      return sum + (parseFloat(odd.rate) || 0)
    }, 0)

    if (total !== 100) {
      setValidationError(`Total percentage must be 100%. Current total: ${total.toFixed(0)}%`)
      return false
    } else {
      setValidationError('')
      return true
    }
  }
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData({
      ...formData,
      [name]: value,
    })
  }
  
  const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      // Create a preview URL immediately
      const reader = new FileReader()
      reader.onloadend = () => {
        setImagePreview(reader.result as string)
      }
      reader.readAsDataURL(file)

      // Upload image if onImageUpload is provided
      if (onImageUpload) {
        setIsUploadingImage(true)
        try {
          const imageUrl = await onImageUpload(file)
          // Update formData with the uploaded imageUrl
          setFormData({
            ...formData,
            image: file,
            imageUrl: imageUrl,
          })
        } catch (error) {
          console.error('Error uploading image:', error)
          // Keep the file in formData even if upload fails
          setFormData({
            ...formData,
            image: file,
          })
        } finally {
          setIsUploadingImage(false)
        }
      } else {
        // If no upload handler, just set the file
        setFormData({
          ...formData,
          image: file,
        })
      }
    }
  }
  
  const handleOddsChange = (index: number, field: string, value: string) => {
    const newOdds = [...formData.odds]
    console.log('🚀 ~ handleOddsChange ~ newOdds:', newOdds)
    newOdds[index] = { 
      ...newOdds[index], 
      [field]: value,
    }
    setFormData({
      ...formData,
      odds: newOdds,
    })
  }
  
  const addOddsRow = () => {
    const newRow: OddsRow = {
      id: Date.now(), // Use timestamp as unique ID
      fromPrice: '',
      toPrice: '',
      rate: '',
    }
    setFormData({
      ...formData,
      odds: [...formData.odds, newRow],
    })
  }
  
  const removeOddsRow = (index: number) => {
    const newOdds = [...formData.odds]
    newOdds.splice(index, 1)
    setFormData({
      ...formData,
      odds: newOdds,
    })
  }
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateOdds()) {
      return
    }
    
    await onSubmit(formData)
  }
  
  const calculateTotalPercentage = () => {
    return formData.odds.reduce((total, odd) => {
      return total + (parseFloat(odd.rate) || 0)
    }, 0)
  }

  const handlePriceRangeValidation = (hasErrors: boolean) => {
    setHasPriceRangeErrors(hasErrors)
  }
  
  const totalPercentage = calculateTotalPercentage()
  
  // Check if the form is valid - safely check for undefined values
  const hasEmptyOddsFields = formData.odds.length > 0 && formData.odds.some((row) => 
    !row.fromPrice?.trim() || !row.toPrice?.trim() || !row.rate?.trim()
  )
  
  const isFormValid =
    !validationError &&
    formData.name &&
    formData.price &&
    formData.description &&
    formData.categoryId &&
    (formData.image || formData.imageUrl) &&
    !hasEmptyOddsFields &&
    !hasPriceRangeErrors &&
    (formData.odds.length === 0 || totalPercentage === 100) &&
    !isUploadingImage
  
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-6">
        {/* Image upload */}
        <div>
          <div className="flex items-center mb-4">
            <label className="block text-sm font-medium text-gray-700 w-24">
              Image: <span className="text-red-500">*</span>
            </label>
            <div className="relative w-fit">
              <div className="absolute top-[-10px] right-[-10px] z-10 cursor-pointer">
                <input
                  className="w-5 h-5 opacity-0 absolute z-10"
                  type="file"
                  id="img"
                  name="image"
                  accept="image/*"
                  onChange={handleImageChange}
                />
                <SvgIcon
                  
                  name="plusCircle"
                  className="w-5 h-5 z-10 stroke-none fill-primary-2 cursor-pointer"
                />
              </div>
              <div className="upload-image-square bg-gray-5 dark:bg-blue-1 h-40 w-40 rounded-md flex items-center justify-center overflow-hidden relative">
                {isUploadingImage ? (
                  <div className="flex flex-col items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mb-2"></div>
                    <span className="text-sm text-gray-500">Uploading...</span>
                  </div>
                ) : imagePreview ? (
                  <div className="relative w-full h-full">
                    <Image
                      alt="Preview"
                      src={imagePreview}
                      fill
                      style={{ objectFit: 'cover' }}
                      className="rounded-md"
                    />
                  </div>
                ) : (
                  <span className="text-gray-400">Preview Image</span>
                )}
              </div>
            </div>
          </div>
        </div>
        
        {/* Category selection */}
        <div className="flex items-center">
          <label className="block text-sm font-medium text-gray-700 w-24">
            Category: <span className="text-red-500">*</span>
          </label>
          <Select
            options={categoryOptions}
            className="w-60"
            defaultSelected={categoryOptions.find((cat) => cat.value === formData.categoryId) || { name: 'Select a category', value: '' }}
            onSelect={(option) => setFormData({
              ...formData,
              categoryId: option.value,
            })}
          />
        </div>
        
        {/* Price input */}
        <div className="flex items-center">
          <label className="block text-sm font-medium text-gray-700 w-24">
            Price (USD): <span className="text-red-500">*</span>
          </label>
          <Input
            type="number"
            name="price"
            value={formData.price}
            onChange={(e) => setFormData({
              ...formData,
              price: e.target.value,
            })}
            className="no-arrow w-60"
            placeholder="e.g., 19.99"
            valueRange={[0, Number.MAX_SAFE_INTEGER]}
          />
        </div>
        
        {/* Name input */}
        <div className="flex items-center">
          <label className="block text-sm font-medium text-gray-700 w-24">
            Name <span className="text-red-500">*</span>
          </label>
          <Input
            type="text"
            name="name"
            value={formData.name}
            onChange={(e) => setFormData({
              ...formData,
              name: e.target.value,
            })}
            className="w-full"
            placeholder="Enter pack name"
          />
        </div>
        
        {/* Description textarea */}
        <div className="flex">
          <label className="block text-sm font-medium text-gray-700 w-24">
            Description <span className="text-red-500">*</span>
          </label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            className="border border-gray-300 rounded-md px-3 py-2 w-full h-24"
            placeholder="Enter pack description"
          />
        </div>
        
        {/* Odds Table Component */}
        <OddsTable
          odds={formData.odds}
          handleOddsChange={handleOddsChange}
          addOddsRow={addOddsRow}
          removeOddsRow={removeOddsRow}
          totalPercentage={totalPercentage}
          validationError={validationError}
          onPriceRangeValidation={handlePriceRangeValidation}
        />
      </div>
      {errorMessage && (
        <div className="text-red-500">{errorMessage}</div>
      )}
      <div className="flex justify-end pt-4">
        <Button
          type="submit"
          variant="dark"
          size="base"
          disable={isSubmitting || !isFormValid}
          className="flex items-center justify-center"
        >
          {isSubmitting && <Loader className='mr-2' />}
          {isSubmitting ? 'Loading...' : submitLabel}
        </Button>
      </div>
    </form>
  )
}

export default PackForm 