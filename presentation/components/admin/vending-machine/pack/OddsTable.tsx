import React, { useState, useCallback, useEffect } from 'react'
import Input from 'presentation/components/input'

interface OddsRow {
  id: number;
  fromPrice: string;
  toPrice: string;
  rate: string;
}

interface OddsTableProps {
  odds: OddsRow[];
  handleOddsChange: (index: number, field: string, value: string) => void;
  addOddsRow: () => void;
  removeOddsRow: (index: number) => void;
  totalPercentage: number;
  validationError: string;
  onPriceRangeValidation?: (hasErrors: boolean) => void;
}

const OddsTable: React.FC<OddsTableProps> = ({
  odds,
  handleOddsChange,
  addOddsRow,
  removeOddsRow,
  totalPercentage,
  validationError,
  onPriceRangeValidation,
}) => {
  // Track which fields have been changed by the user
  const [changedFields, setChangedFields] = useState<Record<string, boolean>>({})
  
  // Local state for inputs to get immediate control over them
  const [inputValues, setInputValues] = useState<Record<string, string>>({})
  
  // Get field unique identifier
  const getFieldId = (index: number, field: string) => `${index}-${field}`
  // Initialize input values from props
  useEffect(() => {
    const values: Record<string, string> = {}
    odds.forEach((row, index) => {
      values[getFieldId(index, 'fromPrice')] = row.fromPrice || ''
      values[getFieldId(index, 'toPrice')] = row.toPrice || ''
      values[getFieldId(index, 'rate')] = row.rate || ''
    })
    setInputValues(values)
  }, [odds])
  
  // Check if field has been changed by user
  const hasFieldChanged = (index: number, field: string) => {
    return changedFields[getFieldId(index, field)] === true
  }
  
  // Check if a field is empty
  const isFieldEmpty = (value?: string) => {
    return !value?.trim()
  }
  
  // Process input by filtering out non-numeric characters and normalizing decimal points
  const handleInputChange = useCallback((index: number, field: string, inputValue: string) => {
   
    // Update local state
    const fieldId = getFieldId(index, field)
    setInputValues((prev) => ({
      ...prev,
      [fieldId]: inputValue,
    }))
    
    // Mark field as changed
    setChangedFields((prev) => ({
      ...prev,
      [fieldId]: true,
    }))
    
    // Notify parent component
    handleOddsChange(index, field, inputValue)
  }, [handleOddsChange])
  
  // Get current value for a field
  const getInputValue = (index: number, field: string) => {
    const fieldId = getFieldId(index, field)
    return inputValues[fieldId] || ''
  }
  
  // Check if To Price is greater than From Price
  const isToGreaterThanFrom = (index: number) => {
    const fromPrice = parseFloat(getInputValue(index, 'fromPrice')) || 0
    const toPrice = parseFloat(getInputValue(index, 'toPrice')) || 0
    return toPrice > fromPrice
  }

  // Should show validation error for empty fields
  const shouldShowValidationError = (index: number, field: string) => {
    return hasFieldChanged(index, field) && isFieldEmpty(getInputValue(index, field))
  }

  // Should show validation error for price range
  const shouldShowPriceRangeError = (index: number) => {
    const fromChanged = hasFieldChanged(index, 'fromPrice')
    const toChanged = hasFieldChanged(index, 'toPrice')
    const fromValue = getInputValue(index, 'fromPrice')
    const toValue = getInputValue(index, 'toPrice')

    // Only show error if both fields have values and at least one has been changed
    return (fromChanged || toChanged) &&
           !isFieldEmpty(fromValue) &&
           !isFieldEmpty(toValue) &&
           !isToGreaterThanFrom(index)
  }

  // Should validation message be shown
  const shouldShowValidation = Object.keys(changedFields).length > 0

  // Get price range validation errors
  const getPriceRangeErrors = () => {
    const errors: string[] = []
    odds.forEach((row, index) => {
      if (shouldShowPriceRangeError(index)) {
        const fromPrice = getInputValue(index, 'fromPrice')
        const toPrice = getInputValue(index, 'toPrice')
        errors.push(`Row ${index + 1}: To Price ($${toPrice}) must be greater than From Price ($${fromPrice})`)
      }
    })
    return errors
  }

  const priceRangeErrors = getPriceRangeErrors()

  // Notify parent component about price range validation
  useEffect(() => {
    if (onPriceRangeValidation) {
      onPriceRangeValidation(priceRangeErrors.length > 0)
    }
  }, [priceRangeErrors.length, onPriceRangeValidation])

  return (
    <div>
      {/* Header with Add button */}
      <div className="flex items-center justify-between mb-2">
        <label className="block text-sm font-medium text-gray-700">
          Odds <span className="text-red-500">*</span>
        </label>
        <button 
          type="button" 
          className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          onClick={addOddsRow}
        >
          Add
        </button>
      </div>
      
      {/* Odds table */}
      <table className="w-full border-collapse border border-gray-300 mb-2">
        <thead>
          <tr className="bg-gray-50">
            <th className="py-2 px-4 border border-gray-300 text-left">
              From Price <span className="text-red-500">*</span>
            </th>
            <th className="py-2 px-4 border border-gray-300 text-left">
              To Price <span className="text-red-500">*</span>
            </th>
            <th className="py-2 px-4 border border-gray-300 text-left">
              Rate <span className="text-red-500">*</span>
            </th>
            <th className="py-2 px-4 border border-gray-300 w-16"></th>
          </tr>
        </thead>
        <tbody>
          {odds.map((row, index) => (
            <tr key={row.id}>
              <td className="border border-gray-300 p-2">
                <div className="flex items-center w-full px-2">
                  <span className="flex-shrink-0 mr-2">$</span>
                  <Input
                    type="number"
                    step="any"
                    value={getInputValue(index, 'fromPrice')}
                    onChange={(e) => handleInputChange(index, 'fromPrice', e.target.value)}
                    className={`p-2 w-full ${shouldShowValidationError(index, 'fromPrice') || shouldShowPriceRangeError(index) ? 'border border-red-500' : ''}`}
                    noBaseClass
                    wrapClass="w-full"
                    valueRange={[0,9999999]}
                  />
                </div>
              </td>
              <td className="border border-gray-300 p-2">
                <div className="flex items-center w-full px-2">
                  <span className="flex-shrink-0 mr-2">$</span>
                  <Input
                    type="number"
                    step="any"
                    value={getInputValue(index, 'toPrice')}
                    onChange={(e) => handleInputChange(index, 'toPrice', e.target.value)}
                    className={`p-2 w-full ${shouldShowValidationError(index, 'toPrice') || shouldShowPriceRangeError(index) ? 'border border-red-500' : ''}`}
                    noBaseClass
                    wrapClass="w-full"
                    valueRange={[0,9999999]}
                  />
                </div>
              </td>
              <td className="border border-gray-300 p-2">
                <div className="flex items-center w-full px-2">
                  <Input
                    type="number"
                    value={getInputValue(index, 'rate')}
                    onChange={(e) => handleInputChange(index, 'rate', e.target.value)}
                    className={`p-2 w-full ${shouldShowValidationError(index, 'rate') ? 'border border-red-500' : ''}`}
                    noBaseClass
                    wrapClass="w-full"
                  />
                  <span className="flex-shrink-0 ml-2">%</span>
                </div>
              </td>
              <td className="border border-gray-300 text-center p-2">
                {odds.length > 1 && <button
                  type="button"
                  onClick={() => removeOddsRow(index)}
                  className="text-gray-500 hover:text-red-500"
                  aria-label="Remove row"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mx-auto" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </button>
                }
              </td>
            </tr>
          ))}
          {odds.length === 0 && (
            <tr>
              <td colSpan={4} className="border border-gray-300 p-4 text-center text-gray-500">
                No odds defined yet. Click &quot;Add&quot; to create an odds row.
              </td>
            </tr>
          )}
        </tbody>
      </table>
      
      {/* Footer with field info and total */}
      <div className="flex justify-between items-center">
        <div className="text-sm text-gray-500">
          <span className="text-red-500">*</span> Required fields
        </div>
        <div className="text-right text-sm">
          {odds.length > 0 && shouldShowValidation && (
            totalPercentage === 100 ? (
              <span className="text-green-600 font-medium">Total: 100%</span>
            ) : (
              <span className="text-red-500 font-medium">Total: {totalPercentage}% (must be 100%)</span>
            )
          )}
        </div>
      </div>
      
      {/* Validation error messages */}
      {shouldShowValidation && (
        <div className="mt-1">
          {validationError && (
            <p className="text-red-500 text-sm">{validationError}</p>
          )}
          {priceRangeErrors.length > 0 && (
            <div className="text-red-500 text-sm">
              {priceRangeErrors.map((error, index) => (
                <p key={index}>{error}</p>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default OddsTable 