import Button from 'presentation/components/button'
import React from 'react'
import Select from 'presentation/components/select'
import Input from 'presentation/components/input'

interface SearchFormState {
  name: string;
  category: string;
}

interface SearchFormProps {
  categories: any[];
  formState: SearchFormState;
  onChangeForm: (field: keyof SearchFormState, value: string) => void;
  onSearch: (e: React.FormEvent) => void;
}

const SearchForm: React.FC<SearchFormProps> = ({
  categories,
  formState,
  onChangeForm,
  onSearch,
}) => {
  // Format categories for the Select component
  const categoryOptions = [
    { name: 'All Categories', value: '' },
    ...categories.map((category) => ({ name: category.name, value: category.id })),
  ]
  
  // Find the currently selected category option
  const selectedCategory = categoryOptions.find((option) => option.value === formState.category) || categoryOptions[0]
  
  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 mb-6">
      <form onSubmit={onSearch} className="flex flex-wrap items-end gap-4">
        <div className="flex-1 min-w-[200px]">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Category:
          </label>
          <Select
            options={categoryOptions}
            defaultSelected={selectedCategory}
            className="w-full"
            customWidth={true}
            onSelect={(option) => onChangeForm('category', option.value)}
          />
        </div>
        
        <div className="flex-1 min-w-[200px]">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Name
          </label>
          <Input
            type="text"
            className="w-full"
            placeholder="Search by name"
            value={formState.name}
            onChange={(e) => onChangeForm('name', e.target.value)}
            wrapClass="w-full"
          />
        </div>
        
        <div>
          <Button
            type="submit"
            variant="light"
            className="flex items-center justify-center">
            Search
          </Button>

        </div>
      </form>
    </div>
  )
}

export default SearchForm 