import React, { useState, useEffect } from 'react'
import Button from 'presentation/components/button'
import Input from 'presentation/components/input'
import Loader from 'presentation/components/loader'

export interface CategoryFormData {
  id?: number;
  name: string;
  description?: string;
}

interface CategoryFormProps {
  initialData: CategoryFormData;
  onSubmit: (data: CategoryFormData) => Promise<void>;
  isSubmitting: boolean;
  submitLabel: string;
}

const CategoryForm: React.FC<CategoryFormProps> = ({
  initialData,
  onSubmit,
  isSubmitting,
  submitLabel,
}) => {
  const [formData, setFormData] = useState<CategoryFormData>(initialData)
  
  // Update form data when initialData changes (useful for edit mode)
  useEffect(() => {
    setFormData(initialData)
  }, [initialData])
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData({
      ...formData,
      [name]: value,
    })
  }
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    await onSubmit(formData)
  }
  
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Name
        </label>
        <Input
          type="text"
          name="name"
          value={formData.name}
          onChange={(e) => handleInputChange(e)}
          className="w-full"
          placeholder="Enter category name"
          wrapClass="w-full"
        />
      </div>
      
      <div className="flex justify-end pt-4">
        <Button
          type="submit"
          variant="dark"
          disable={isSubmitting || !formData.name.trim()}
          className="w-24 flex items-center justify-center"
        >
          {isSubmitting && <Loader className='mr-2' />}
          {submitLabel}
        </Button>
      </div>
    </form>
  )
}

export default CategoryForm 