import React, { useMemo, useState } from 'react'
import { useForm } from 'lib/hook/useForm'
import But<PERSON> from 'presentation/components/button'
import Input from 'presentation/components/input'
import Loader from 'presentation/components/loader'
import Select from 'presentation/components/select'
import DatetimeRange from 'presentation/components/datetimeRange'
import 'react-datepicker/dist/react-datepicker.css'
import SvgIcon from 'presentation/components/svg-icon'
import clsx from 'clsx'
import SelectCategory from '../card/SelectCategory'

interface SearchBuybackFormProps {
  onSearch: (e: React.FormEvent) => void
  onExport?: (e: React.FormEvent) => void
  isLoading: boolean
  isLoadingExport?: boolean
}

const SearchBuybackForm: React.FC<SearchBuybackFormProps> = ({
  onSearch,
  onExport,
  isLoading = false,
  isLoadingExport = false,
}) => {
  const { formData, setFormValue } = useForm({
    category: '',
    cardId: '',
    tokenId: '',
    duration: null,
    startAt: null,
    endAt: null,
  })
  const [showDateRange, setShowDateRange] = useState(false)

  const onSubmitSearch = async (e: React.FormEvent) => {
    e.preventDefault()
    await onSearch(formData)
  }

  const onExportCsv = async (e: React.FormEvent) => {
    e.preventDefault()
    if (onExport) {
      await onExport(formData)
    }
  }

  return (
    <div className="border border-gray-200 rounded-lg p-6">
      <form onSubmit={onSubmitSearch} className="flex flex-col gap-4">
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-4">
          <div className="">
            <label className="block text-sm font-medium text-gray-700 dark:text-white mb-1">
              Category
            </label>
            <SelectCategory
              isSelectEmpty={true}
              value={formData.category}
              onChange={(value) => setFormValue('category', value)}
            />
          </div>
          
          <div className="">
            <label className="block text-sm font-medium text-gray-700 dark:text-white mb-1">
              Card ID
            </label>
            <Input
              wrapClass="w-full"
              type="text"
              value={formData.cardId}
              maxLength={50}
              disabled={isLoading}
              placeholder="Enter Card ID"
              deleteIcon
              onChange={(e) => setFormValue('cardId', e.target.value)}
            />
          </div>

          <div className="">
            <label className="block text-sm font-medium text-gray-700 dark:text-white mb-1">
              Token ID
            </label>
            <Input
              wrapClass="w-full"
              type="text"
              value={formData.tokenId}
              maxLength={50}
              disabled={isLoading}
              placeholder="Enter Token ID"
              deleteIcon
              onChange={(e) => setFormValue('tokenId', e.target.value)}
            />
          </div>
        </div>

        <div className="flex justify-end items-center gap-4">
          {onExport && (
            <Button
              type="button"
              variant="outlineDark"
              className="flex items-center justify-center h-[50px] w-44 !px-2"
              disable={isLoadingExport}
              onClick={onExportCsv}
            >
              {isLoadingExport ? (
                <Loader className="border-gray" />
              ) : (
                'Export CSV'
              )}
            </Button>
          )}
          <Button
            type="submit"
            variant="dark"
            className="flex items-center justify-center h-[50px] w-44 !px-2"
            disable={isLoading}
          >
            {isLoading ? <Loader className="border-gray" /> : 'Search'}
          </Button>
        </div>
      </form>
    </div>
  )
}

export default SearchBuybackForm
