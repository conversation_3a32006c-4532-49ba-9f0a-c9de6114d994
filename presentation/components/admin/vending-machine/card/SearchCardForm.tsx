import React, { useMemo } from 'react'
import { useForm } from 'lib/hook/useForm'
import Button from 'presentation/components/button'
import Input from 'presentation/components/input'
import Loader from 'presentation/components/loader'
import SelectCategory from './SelectCategory'
import clsx from 'clsx'

interface SearchFormProps {
  onSearch: (formData: any) => void
  isLoading: boolean
  selectCategory?: boolean
  selectCategoryEmpty?: boolean
  className?: string
}

const SearchCardForm: React.FC<SearchFormProps> = ({
  onSearch,
  isLoading = false,
  selectCategory = false,
  selectCategoryEmpty = false,
  className = '',
}) => {
  const { formData, setFormValue } = useForm({ search: '', categoryId: '' })

  const onSubmitSearch = async (e: React.FormEvent) => {
    e.stopPropagation()
    e.preventDefault()
    await onSearch(formData)
  }

  const wrapInputClass =
    'w-full md:max-w-[450px] flex-1 flex max-md:items-center md:flex-col'
  const labelClass =
    'block text-sm font-medium text-gray-700 dark:text-white mb-1 max-md:w-[80px]'

  return (
    <div
      className={clsx(
        'border border-gray-200 rounded-lg p-6 max-md:p-2',
        className
      )}
    >
      <form
        onSubmit={onSubmitSearch}
        className="flex max-md:flex-col items-end gap-4 max-md:gap-3"
      >
        {selectCategory && (
          <div className={wrapInputClass}>
            <label className={labelClass}>Category</label>
            <SelectCategory
              isSelectEmpty={selectCategoryEmpty}
              onChange={(value) => setFormValue('categoryId', value)}
              value={formData.categoryId}
            />
          </div>
        )}
        <div className={wrapInputClass}>
          <label className={labelClass}>Card ID</label>
          <Input
            wrapClass="w-full"
            type="text"
            value={formData.search}
            maxLength={50}
            disabled={isLoading}
            placeholder="Search by Card Id"
            deleteIcon
            onChange={(e) => setFormValue('search', e.target.value)}
          />
        </div>

        <div>
          <Button
            type="submit"
            variant="light"
            className="flex items-center justify-center "
            disable={isLoading}
          >
            {isLoading ? <Loader className="border-gray" /> : 'Search'}
          </Button>
        </div>
      </form>
    </div>
  )
}

export default SearchCardForm
