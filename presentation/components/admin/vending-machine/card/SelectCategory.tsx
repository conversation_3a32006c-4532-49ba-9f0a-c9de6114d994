import React, { useMemo } from 'react'
import Select from 'presentation/components/select'
import { useCustomQuery } from 'lib/hook/useCustomQuery'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'
import { Query as CategoryQuery } from 'domain/repositories/vending_machine_category_repository/query'
import VendingMachineCategoryUseCase from 'presentation/use_cases/vending_machine_category_use_case'

const categoryUseCase = new VendingMachineCategoryUseCase()

interface SelectCategoryProps {
  onChange: (value: string) => void
  isSelectEmpty?: boolean
  value: string
}

const SelectCategory: React.FC<SelectCategoryProps> = ({
  onChange,
  isSelectEmpty = false,
  value,
}) => {
  const authStateProvider = AuthStateProvider((state) => ({
    me: state.me,
  }))
  const { data: categoryOptions = [] } = useCustomQuery(
    ['CategoryOptions', authStateProvider?.me?.id],
    async () => {
      try {
        const query = new CategoryQuery({ limit: 100 })
        const result = await categoryUseCase.list(query)
        return result.items.map((i) => {
          return { value: i.id, name: i.name }
        })
      } catch (error: any) {
        return []
      }
    },
    {
      enabled: authStateProvider.me?.isOperator,
      refetchOnWindowFocus: false,
    }
  )

  const options = useMemo(() => {
    let tmp = [{ value: '', name: 'All Categories' }]
    if (isSelectEmpty) {
      tmp.push({ value: 'n/a', name: 'N/A' })
    }
    return [...tmp, ...categoryOptions]
  }, [categoryOptions, isSelectEmpty])

  const selectedValue = useMemo(() => {
    return options.find((i: any) => i?.value === value)
  }, [options, value])

  return (
    <Select
      options={options}
      className="w-full"
      selectClassName="py-2.5"
      customWidth={true}
      defaultSelected={selectedValue}
      onSelect={(option) => onChange(option.value)}
    />
  )
}

export default SelectCategory
