import React, { useMemo, useState, useCallback } from 'react'
import { useForm } from 'lib/hook/useForm'
import Button from 'presentation/components/button'
import Select from 'presentation/components/select'
import Input from 'presentation/components/input'
import Loader from 'presentation/components/loader'
import DatetimeRange from 'presentation/components/datetimeRange'
import 'react-datepicker/dist/react-datepicker.css'
import SvgIcon from 'presentation/components/svg-icon'
import clsx from 'clsx'
import SelectCategory from './SelectCategory'
import { StatusCheckout } from 'domain/models/card_vending_machine/status_checkout'

interface SearchFormProps {
  onSearch: (dataSearch: any) => void
  onExport?: (e: React.FormEvent) => void
  isLoading: boolean
  isLoadingExport?: boolean
}

const SearchCardForm: React.FC<SearchFormProps> = ({
  onSearch,
  onExport,
  isLoading = false,
  isLoadingExport = false,
}) => {
  const optionsStatus = [
    { value: '', name: 'All Status' },
    ...StatusCheckout.getResourceArray().map((i) => {
      return { value: i.name, name: i.label }
    }),
  ]
  const initForm = {
    cardId: '',
    categoryId: '',
    status: '',
    duration: null,
    startDate: null,
    endDate: null,
  }
  const { formData, setFormValue, setFormData } = useForm(initForm)
  const [showDateRange, setShowDateRange] = useState(false)

  const selectedStatus = useMemo(() => {
    return optionsStatus.find((i: any) => i?.value === formData.status)
  }, [formData.status])

  const onSubmitSearch = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault()
      await onSearch(formData)
    },
    [formData]
  )

  const resetForm = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault()
      setFormData(initForm)
      await onSearch(initForm)
    },
    [formData]
  )

  const onExportCsv = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault()
      if (onExport) {
        await onExport(formData)
      }
    },
    [formData]
  )
  const wrapInputClass = 'w-full flex max-sm:items-center sm:flex-col'
  const labelClass =
    'block text-sm font-medium text-gray-700 dark:text-white sm:mb-1 max-sm:w-[65px] flex-shrink-0'
  return (
    <div className="border border-gray-200 rounded-lg md:p-6 p-3">
      <form onSubmit={onSubmitSearch} className="flex flex-col gap-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4">
          <div className={wrapInputClass}>
            <label className={labelClass}>Category</label>
            <SelectCategory
              onChange={(value) => setFormValue('categoryId', value)}
              value={formData.categoryId}
            />
          </div>
          <div className={wrapInputClass}>
            <label className={labelClass}>Status</label>
            <Select
              options={optionsStatus}
              className="w-full"
              selectClassName="py-2.5"
              customWidth={true}
              defaultSelected={selectedStatus}
              onSelect={(option) => setFormValue('status', option.value)}
            />
          </div>
          <div className={wrapInputClass}>
            <label className={labelClass}>Card ID</label>
            <Input
              wrapClass="w-full"
              type="text"
              value={formData.cardId}
              maxLength={50}
              disabled={isLoading}
              placeholder="Enter Card Id"
              deleteIcon
              onChange={(e) => setFormValue('cardId', e.target.value)}
            />
          </div>

          {/* Date */}
          <div className={wrapInputClass}>
            <label className={labelClass}>Date</label>
            <div className="relative z-50 w-full">
              <Button
                type="button"
                variant="gray4"
                className="w-full !px-5 font-normal flex justify-between items-center dark:bg-blue-8"
                disable={isLoading}
                onClick={() => setShowDateRange(!showDateRange)}
              >
                {formData.startDate && formData.endDate ? (
                  <>
                    <span className="dark:text-white text-sm sm:text-base">
                      {formData.startDate.toFormat('yyyy-MM-dd')} ~{' '}
                      {formData.endDate.toFormat('yyyy-MM-dd')}
                    </span>
                    <SvgIcon
                      onClick={(e) => {
                        e.stopPropagation()
                        setFormValue('startDate', '', true)
                        setFormValue('endDate', '', true)
                      }}
                      name="plus"
                      className={clsx(
                        'w-5 h-5 stroke-none fill-gray-1 rotate-45 cursor-pointer'
                      )}
                    />
                  </>
                ) : (
                  <span className="text-[#9da5b0]">Start date ~ End date</span>
                )}
              </Button>
              {showDateRange && (
                <div className="absolute top-[50px] right-0">
                  <DatetimeRange
                    classNameTopInput="!hidden"
                    showTimeInput={false}
                    showDuration={false}
                    onChangeStartDatetime={(value: any) =>
                      setFormValue('startDate', value)
                    }
                    onChangeEndDatetime={(value: any) =>
                      setFormValue('endDate', value)
                    }
                    onCloseDateRange={() => {
                      setShowDateRange(false)
                    }}
                  />
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="flex justify-end items-center gap-4">
          <Button
            type="button"
            variant="gray4"
            className="flex items-center justify-center h-[50px] w-44 !px-2"
            disable={isLoading}
            onClick={resetForm}
          >
            Reset
          </Button>

          {onExport && (
            <Button
              type="button"
              variant="outlineDark"
              className="flex items-center justify-center h-[50px] w-44 !px-2"
              disable={isLoadingExport}
              onClick={onExportCsv}
            >
              {isLoadingExport ? (
                <Loader className="border-gray" />
              ) : (
                'Export CSV'
              )}
            </Button>
          )}

          <Button
            type="submit"
            variant="dark"
            className="flex items-center justify-center h-[50px] w-44 !px-2"
            disable={isLoading}
          >
            {isLoading ? <Loader className="border-gray" /> : 'Search'}
          </Button>
        </div>
      </form>
    </div>
  )
}

export default SearchCardForm
