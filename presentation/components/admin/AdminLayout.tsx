import React, { ReactNode } from 'react'
import AdminSidebar from './AdminSidebar'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'
import RequireConnect from 'presentation/components/require-connect'
interface AdminLayoutProps {
  children: ReactNode
  title?: string
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children, title }) => {
  const authStateProvider = AuthStateProvider((state) => ({
    me: state.me,
  }))

  return (
    <div className="container-df mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row gap-8">
        {/* Sidebar */}
        <div className="md:w-1/4 lg:w-1/5">
          <AdminSidebar />
        </div>

        {/* Main content */}
        <div className="md:w-3/4 lg:w-4/5">
          <h1 className="text-2xl font-bold mb-6 text-center">
            {title || 'Admin Dashboard'}
          </h1>
          {(() => {
            if (!authStateProvider?.me) {
              return <RequireConnect />
            } else if (!authStateProvider.me?.isOperator) {
              return (
                <div className="text-red text-left mt-4">
                  Please log in with an admin account.
                </div>
              )
            } else {
              return <div>{children}</div>
            }
          })()}
        </div>
      </div>
    </div>
  )
}

export default AdminLayout
