import React from 'react'
import { useRouter } from 'next/router'
import Link from 'next/link'
import clsx from 'clsx'
import ToggleVertical from '../../components/toggle-wrap/toggle-vertical'
import ToggleWrap from '../../components/toggle-wrap'
import { adminSidebar } from 'presentation/controllers/mockData/admin_mock_data'

interface AdminSidebarProps {
  className?: string
}

const AdminSidebar: React.FC<AdminSidebarProps> = ({ className }) => {
  const router = useRouter()

  return (
    <div className={clsx('admin-sidebar', className)}>
      <ToggleWrap contentClass="rounded-t-10">
        {adminSidebar.map((item, index) => {
          // Get current path from router
          const currentPath = router.pathname
          // Check if current path matches item slug or is a child path
          const isActive = currentPath === item.slug || 
            (item.children && Array.isArray(item.children) && 
              item.children.some((child) => currentPath === child.slug || currentPath.startsWith(child.slug)))
          
          return (
            <ToggleVertical
              key={index}
              toggleTitle={item.name}
              titleLink={Array.isArray(item.children) ? undefined : item.slug}
              titleColor={isActive ? '!text-primary-2' : ''} // Change color when active
              toggleTitleWrapClass={clsx(
                index === 0 ? 'rounded-t-10' : '',    
                index === adminSidebar.length - 1 && !Array.isArray(item.children) ? 'rounded-b-10' : ''
              )}
              toggleTitleWrapClassWhenClose={
                index === adminSidebar.length - 1 ? 'rounded-b-10' : ''
              }
              contentWrapClass={
                index === adminSidebar.length - 1 ? 'rounded-b-10' : ''
              }
              defaultShow={isActive && Array.isArray(item.children)}
            >
              {Array.isArray(item.children) && (
                <div className="pl-2 py-2 space-y-2">
                  {item.children.map((child, childIndex) => {
                    const isChildActive = currentPath === child.slug || currentPath.startsWith(child.slug)
                    
                    return (
                      <Link 
                        href={child.slug} 
                        key={childIndex}
                        className={clsx(
                          'block py-1 px-2 rounded hover:bg-gray-100 hover:dark:bg-slate-500 transition-colors',
                          isChildActive ? 'text-primary-2 font-medium' : 'text-gray-700 dark:text-white'
                        )}
                      >
                        {child.name}
                      </Link>
                    )
                  })}
                </div>
              )}
            </ToggleVertical>
          )
        })}
      </ToggleWrap>
    </div>
  )
}

export default AdminSidebar 