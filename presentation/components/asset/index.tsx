import Image from 'next/image'
import Link from 'next/link'
import clsx from 'clsx'
import SvgIcon from 'presentation/components/svg-icon'
import AssetThumbnail from 'presentation/components/asset/thumbnail'
import AssetPrice from 'presentation/components/asset/price'
import AssetBundleThumbnail from 'presentation/components/asset/bundle-thumbnail'
import LikeAsset from 'presentation/components/asset/like-asset'

type Props = {
  record: any;
  className?: string;
  showOfferText?: boolean;
  showDaysLeft?: boolean;
  showAddToCart?: boolean;
  isBundle?: boolean;
  isConnected?: boolean;
  user ?: any,
};

const AssetCard = (props: Props) => {
  const {
    record,
    className = '',
    showOfferText = true,
    showDaysLeft = true,
    showAddToCart = false,
    isBundle = false,
  } = props

  const asset = { ...record }

  const handleClickCart = (e: any, id: any) => {
    e.preventDefault()
    // TODO
  }

  const handleClickRank = (e: any, id: any) => {
    e.preventDefault()
    // TODO
  }
  
  return (
    <div className={clsx('product-card', className)}>
      <div className="flex max-w-full truncate mb-2">
        <div className="w-8 flex items-center flex-none">
          <Image
            alt=""
            src={asset?.collection?.logoImage?.url || '/asset/images/character-bird.png'}
            width={32}
            height={32}
            className="rounded-full aspect-square bg-gray-4 object-cover"
          />
        </div>
        <div className="flex-grow truncate">
          <div className="flex items-end ml-2">
            <p className="font-medium truncate w-full cursor-pointer text-dark-primary dark:text-white">
              <Link href={`/assets/${asset.id}`}>{asset.collection?.name}</Link>
            </p>

            {asset.id && <LikeAsset assetId={asset.id} className="ml-2" />}

          </div>
        </div>
      </div>

      <Link href={`/assets/${asset.id}`} className="cursor-pointer">
        {isBundle ? (
          <AssetBundleThumbnail
            thumbnail={
              asset.thumbnail?.url
                ? asset.thumbnail?.url
                : '/asset/images/character-ship.png'
            }
            watermarkIcon={asset.watermarkIcon}
          />
        ) : (
          <AssetThumbnail asset={asset}/>
        )}
        {showAddToCart && (
          <div
            className="w-11 h-5 -mt-5 mr-2 float-right translate-y-1/2 rounded-10 border border-primary-2 bg-white text-center"
            onClick={(e) => handleClickCart(e, asset.id)}
          >
            <SvgIcon
              name="cart1"
              className="w-18p h-14p mb-[3px] stroke-none fill-primary-2"
            />
          </div>
        )}
        
        <AssetPrice asset={asset}/>
      </Link>

      {/* {(showOfferText || showDaysLeft) && (
        <div className="mt-1 text-xs text-gray-1 dark:text-gray-3 text-right">
          {showOfferText && (
            <div className="flex flex-wrap items-center justify-end gap-0.5">
              <span>Offer for{isBundle ? ' min' : ''}</span>
              <Image alt="" src="/asset/icons/ETH.svg" width={12} height={12} />
              <span>
                {asset.offerPrice?.toLocaleString()} {asset.offerSymbol}
              </span>
              <br />
            </div>
          )}
          {showDaysLeft && (
            <span>{asset.remainDay?.toLocaleString()} days left</span>
          )}
        </div>
      )} */}
    </div>
  )
}

export default AssetCard
