import Image from 'next/image'
import clsx from 'clsx'
import { useState, useEffect } from 'react'

type Props = {
  file?: any,
  className?: string,  
  classNameImage?: string,  
  classNameAudio?: string,  
  classNameVideo?: string,  
}

const AssetThumbnailFile = ({
  file = undefined,
  className = '',
  classNameImage= 'rounded-10',
  classNameAudio= 'absolute top-0 bottom-0 left-0 right-0 w-full h-full flex flex-col justify-center',
  classNameVideo= 'absolute top-0 bottom-0 left-0 right-0 w-full h-full rounded-10',
}: Props) => {
  const [ fileSrc, setFileSrc ] = useState('')
  const [ isImage, setIsImage ] = useState(false)
  const [ isAudio, setIsAudio ] = useState(false)
  const [ isVideo, setIsVideo ] = useState(false)
  const [ isReady, setIsReady ] = useState(false)

  const readImageFile = (_file:any) => {
    const reader = new FileReader()
    reader.readAsDataURL(_file)
    reader.onload = async (e:any) => {
      let url = e.target.result
      setFileSrc(url)
      setIsReady(true)
    }
  }
  
  const readFile = (_file:any) => {
    const fileReader:any = new FileReader()
    fileReader.onload = async () => {
      const blob = new Blob([fileReader.result], { type: _file.type })
      const url:any = URL.createObjectURL(blob)  
      setFileSrc(url)
      setIsReady(true)
    }
    fileReader.readAsArrayBuffer(_file)
  }

  useEffect(() => {
    setIsReady(false)
    const type = file.type.split('/')[0]
    const _isImage = type === 'image'
    if(_isImage) {
      readImageFile(file)
    } else {
      readFile(file)
    }
    setIsImage(_isImage)
    setIsAudio(type === 'audio')
    setIsVideo(type === 'video')
    
  }, [file])

  return (
    <>
      {isReady && (
        <div className={clsx('relative', className)}>
          {isImage && (
            <Image
              src={fileSrc || '/asset/images/character-ship.png'}
              alt=""
              fill
              style={{ objectFit: 'cover' }}
              className={clsx(classNameImage)}
            />
          )}
      
          {isAudio && (
            <div className={clsx(classNameAudio)}>
              <audio controls>
                <source src={fileSrc} type="audio/mpeg" />
                {'Sorry, your browser doesn\'t support embedded audio'}
              </audio>
            </div>
          )}
      
          {isVideo && (
            <video controls className={clsx(classNameVideo)}>
              <source src={fileSrc} type="video/mp4" />
              {'Sorry, your browser doesn\'t support embedded video'}
            </video>
          )}
      
        </div>
      )}
    </>
  )
}

export default AssetThumbnailFile
