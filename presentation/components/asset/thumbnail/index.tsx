import Asset from 'domain/models/asset'
import Image from 'next/image'
import clsx from 'clsx'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'

type Props = {
  asset: Asset;
  onlyShowImage?: boolean,
  autoPlayVideo?: boolean,
  className?: string,
  classNameImage?: string,  
  classNameAudio?: string,  
  classNameVideo?: string,  
  onClick?: Function | null,
}

const AssetThumbnail = ({
  asset,
  onlyShowImage = false,
  autoPlayVideo = false,
  className = '',
  classNameImage = '',
  classNameVideo = 'object-cover bg-gray-4',
  classNameAudio = '',
  onClick = null,
}: Props) => {
  const [ playVideo, setPlayVideo ] = useState(false)
  const [ fileSrc, setFileSrc ] = useState('')
  const [ filePoster, setFilePoster ] = useState('')
  const [ watermarkIcon, setWatermarkIcon ] = useState('')
  const [ isImage, setIsImage ] = useState(false)
  const [ isAudio, setIsAudio ] = useState(false)
  const [ isVideo, setIsVideo ] = useState(false)

  const router = useRouter()

  const handlePlayVideo = (e: any) => {
    e.preventDefault()
    if(onClick == null) {
      setPlayVideo(!playVideo)
    }
  }
  
  const handleClick = (e: any) => {
    e.preventDefault()
    if(onClick) {
      onClick()
    } else{
      router.push(`/assets/${asset.id}`)
    }
  }

  useEffect(() => {
    if (asset?.thumbnail) {
      const type: string = asset.thumbnail?.mimeType || ''
      setIsImage(type.indexOf('image') > -1)
      setIsAudio(type.indexOf('audio') > -1)
      setIsVideo(type.indexOf('video') > -1)
      setFileSrc(asset?.thumbnail?.url || '')
      setFilePoster(asset?.collection?.featuredImage?.url || asset?.collection?.logoImage?.url || '/asset/images/character-ship.png' )
      if(autoPlayVideo ) {
        setPlayVideo(true)
      }
    }
  }, [asset?.thumbnail])
   

  return (
    <div 
      className={clsx('relative w-full product-image rounded-20 overflow-hidden with-video ', className)} 
      
    >
      {isImage && (
        <Image
          className={clsx('duration-500 transition-transform hover:scale-110 border rounded-20', classNameImage)}
          src={fileSrc || '/asset/images/character-ship.png'}
          layout="fill"
          objectFit="cover"
          alt=""
          onClick={handleClick}
        />
      )}
      {isVideo && (
        <>
          {!playVideo ? (
            <Image
              className={clsx('duration-500 transition-transform hover:scale-110 border rounded-20', classNameImage)}
              src={filePoster}
              layout="fill"
              objectFit="cover"
              alt=""
              onClick={handleClick}
            />
          ) : (
            <video 
              autoPlay
              controls
              playsInline
              loop
              controlsList="nodownload" 
              preload="metadata"
              poster={filePoster}
              className={clsx('absolute top-0 bottom-0 left-0 right-0 w-full h-full flex flex-col justify-center ', classNameVideo)}
            >
              <source src={fileSrc} type="video/mp4" />
              {'Sorry, your browser doesn\'t support embedded video'}
            </video>
          )}
          
        </>
      )}
      {isAudio && (
        <div>
          <Image
            className={clsx('duration-500 transition-transform hover:scale-110 border rounded-20 relative z-1', classNameImage)}
            src={'/asset/images/character-ship.png'}
            layout="fill"
            objectFit="cover"
            alt=""
            onClick={handleClick}
          />
          {playVideo && (
            <div className={clsx('absolute bottom-0 left-0 right-0 w-full flex flex-col justify-center', classNameAudio)}>
              <audio 
                autoPlay
                controls
                playsInline
                loop
                controlsList="nodownload" 
                preload="none"
                className='w-full'
              >
                <source src={fileSrc} type="audio/mpeg" />
                {'Sorry, your browser doesn\'t support embedded audio'}
              </audio>
            </div>
          )}
          
        </div>
      )}
      
      {(isVideo || isAudio) && !onlyShowImage && !autoPlayVideo && (
        <div className="absolute bottom-4 right-4 z-10" onClick={handlePlayVideo}>
          <Image
            className='bg-gray-3 rounded-full cursor-pointer'
            alt='controls'
            src={playVideo ? '/asset/icons/pause-btn-white.png' : '/asset/icons/play-btn-white.png'}
            width={30}
            height={30}
          />
        </div>
      )}
      
      {watermarkIcon && (
        <div className="absolute top-[2px] left-[2px] rounded-20 overflow-hidden">
          <Image src={watermarkIcon} alt="" width={25} height={25}/>
        </div>
      )}
      {!isImage && !isAudio && !isVideo && !watermarkIcon && (
        <Image
          className={clsx('duration-500 transition-transform hover:scale-110 border rounded-20', classNameImage)}
          src={fileSrc || '/asset/images/character-ship.png'}
          layout="fill"
          objectFit="cover"
          alt=""
          onClick={handleClick}
        />
      )}
    </div>
  )
}

export default AssetThumbnail
