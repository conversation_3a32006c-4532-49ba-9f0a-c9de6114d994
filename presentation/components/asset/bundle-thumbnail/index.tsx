import Image from 'next/image'
import clsx from 'clsx'

type Props = {
  thumbnail?: string,
  watermarkIcon?: string,
  className?: string,
}

const BundleThumbnail = ({
  thumbnail = '',
  watermarkIcon = '',
  className = '',
}: Props) => {

  return (
    <div className="relative">
      <div className={clsx('relative w-full product-image rounded-20 overflow-hidden shadow-normal', className)}>
        <Image
          className="duration-500 transition-transform hover:scale-110"
          src={thumbnail}
          layout="fill"
          objectFit="cover"
          alt=""
        />
        {watermarkIcon && (
          <div className="absolute top-[2px] left-[2px] rounded-20 overflow-hidden">
            <Image src={watermarkIcon} alt="" width={25} height={25}/>
          </div>
        )}
      </div>
      <div className="absolute top-[5px] left-[5px] w-full h-full bg-white rounded-20 border border-gray-3 -z-1 shadow-normal"></div>
      <div className="absolute top-[10px] left-[10px] w-full h-full bg-white rounded-20 border border-gray-3 -z-2 shadow-normal"></div>
    </div>
  )
}

export default BundleThumbnail