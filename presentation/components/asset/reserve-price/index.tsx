import Image from 'next/image'
import Link from 'next/link'
import clsx from 'clsx'
import Asset from 'domain/models/asset'
import AssetThumbnail from 'presentation/components/asset/thumbnail'
import LikeAsset from 'presentation/components/asset/like-asset'
import AssetPrice from 'presentation/components/asset/price'

type Props = {
  record: Asset;
  className?: string;
  onPlayVideo?: (src?: string) => void;
};

const AssetReservePrice = (props: Props) => {
  const { record, className = '' } = props

  const asset = {...record}

  return (
    <div className={clsx('mr-6 min-w-[210px]', className)}>
      <div className="flex max-w-full truncate mb-2">
        <div className="w-8 flex items-center flex-none">
          <Image
            alt=""
            src={asset?.collection?.logoImage?.url || '/asset/images/character-bird.png'}
            width={32}
            height={32}
            className="rounded-full aspect-square bg-gray-4 object-cover"
          />
        </div>
        <div className="flex-grow truncate">
          <div className="flex items-end ml-2">
            <p className="font-medium text-sm truncate w-full cursor-pointer text-dark-primary dark:text-white">
              <Link href={`/assets/${asset.id}`}>{asset.collection?.name}</Link>
            </p>

            {asset.id && <LikeAsset assetId={asset.id} className="ml-2" />}
          </div>
        </div>
      </div>
      
      <Link
        href={`/assets/${asset.id}`}
        className="w-full h-[210px] relative block rounded-20"
      >
        <AssetThumbnail className="h-full" asset={record}/>
      </Link>

      <AssetPrice asset={record}/>
    </div>
  )
}

export default AssetReservePrice
