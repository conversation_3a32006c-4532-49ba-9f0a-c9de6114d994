import Image from 'next/image'
import Link from 'next/link'
import AssetThumbnail from 'presentation/components/asset/thumbnail'
import { getChainName } from 'lib/utils'
const ProductSingle = (props: any) => {
  const {
    record = {},
    paymentToken = null,
    price = '',
  } = props


  return (
    <div className={`product-single ${props.className}`}>
      <Link href={`/collections/${getChainName(record.collection.chainId)}/${record.collection.slug}`}>
        <div className="flex max-w-full mb-3">
          <div className="w-8 rounded-full items-center">
            {record.collection?.logoImage?.url ? (
              <Image
                alt={record.collection?.name || ''}
                src={record.collection?.logoImage?.url}
                width={32}
                height={32}
                className="rounded-full aspect-square bg-gray-4 object-cover"
              />
            ) : (
              <div className="w-[32px] h-[32px] rounded-full bg-gray-5 dark:bg-blue-1" />
            )}
          </div>
          <div className="w-3/4">
            <div className="ml-2">
              <p className="font-medium truncate w-full cursor-pointer">
                {record.collection?.name || 'Collection\'s name'}
              </p>
            </div>
          </div>
        </div>
      </Link>
      <AssetThumbnail className="h-[210px]" asset={record} onlyShowImage={true} />
      <p className="mt-10p font-medium truncate">{record.name}</p>
      
      <div className="mt-1 flex justify-between">
        <div className="inline-flex items-center">
          {paymentToken?.icon && (
            <div className="icon-18 relative mr-2">
              <Image
                alt={paymentToken?.name || ''}
                src={paymentToken?.icon}
                fill
                style={{ objectFit: 'contain' }}
              />
            </div>
          )}
          <span className="uppercase font-medium">
            {price || '--'} {paymentToken?.name || ''}
          </span>
        </div>
      </div>
      
    </div>
  )
}

export default ProductSingle
