import AssetThumbnail from 'presentation/components/asset/thumbnail'

const CardThumbnail = (props: any) => {
  const { asset, isShowChain = true } = props

  return (
    <div className="flex items-center">
      {asset && (
        <AssetThumbnail 
          className="h-[60px] !w-[60px] !rounded-10" 
          classNameImage="!rounded-10"
          onlyShowImage={true}
          asset={asset}/>
      )}
      <div className="ml-10p">
        <p className="font-medium text-sm text-gray-1">
          {asset?.collection?.name}
        </p>
        <p className="font-bold text-dark-primary dark:text-white">
          {asset?.name}
        </p>
        <p className="font-medium text-sm text-gray-1">
         Chain: {asset?.chain?.getLabel()}
        </p>
      </div>
    </div>
  )
}

export default CardThumbnail
