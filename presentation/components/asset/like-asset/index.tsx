import clsx from 'clsx'
import { useState, useEffect } from 'react'
import { toast } from 'react-toastify'
import SvgIcon from 'presentation/components/svg-icon'
import { Query as AssetLikeQuery } from 'domain/repositories/asset_like_repository/query'
import TargetEntityType from 'domain/value_objects/target_entity_type'
import AssetLikeUseCase from 'presentation/use_cases/asset_like_use_case'
import LikeUseCase from 'presentation/use_cases/like_use_case'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'
import { useCustomQuery } from 'lib/hook/useCustomQuery'

const assetLikeUseCase = new AssetLikeUseCase()
const likeUseCase = new LikeUseCase()

const LikeAsset = (props: any) => {
  const { assetId, className = '', emitTotal = () => {} } = props
  
  const authStateProvider = AuthStateProvider((state) => ({
    me: state.me,
  }))

  const { data: likeCount = 0, refetch: fetchTotalLikes } = useCustomQuery(
    ['like-asset', assetId],
    async () => {
      try {
        const query = new AssetLikeQuery({assetId: assetId})
        const res =  await assetLikeUseCase.totalCount(query)
        return res
      } catch (error) {
        return 0
      }
    },
    { enabled: !!assetId, staleTime: 30000 * 60 }
  )

  const { data: isLiked = false, refetch: fetchLike } = useCustomQuery(
    ['user-like-asset', authStateProvider?.me?.id, assetId],
    async () => {
      try {
        const query = new AssetLikeQuery({
          assetId: assetId,
          userId: authStateProvider?.me?.id,
        })
        const res =  await await assetLikeUseCase.search(query)
        return res.length > 0
      } catch (error) {
        return false
      }
    },
    {
      enabled: !!(authStateProvider?.me?.id && assetId),
      staleTime: 30000 * 60,
    }
  )

  /**
   * FUNCTIONS
   */
  const toggleLike = async () => {
    
    if (authStateProvider.me?.id && assetId) {
      if (isLiked) {
        await likeUseCase
          .delete(TargetEntityType.asset(), assetId)
          .catch((_error) => {
            toast.error('Failed to unlike asset')
          })
      } else {
        await likeUseCase
          .create(TargetEntityType.asset(), assetId)
          .catch((_error) => {
            toast.error('Failed to like asset')
          })
      }
      fetchTotalLikes()
      fetchLike()
   
    } else {
      toast.error('Please connect wallet')
    }
  }
  
  useEffect(() => {
    emitTotal(likeCount)
  }, [likeCount])
  
  return (
    <p className={clsx('text-sm text-gray-1', className)}>
      <span className="icon-wrapper">
        {isLiked ? (
          <SvgIcon
            name="heart"
            className="w-6 h-6 stroke-red-600 fill-red-600 cursor-pointer"
            onClick={toggleLike}
          />
        ) : (
          <SvgIcon
            name="heart"
            className="w-6 h-6 stroke-none fill-gray-2 cursor-pointer"
            onClick={toggleLike}
          />
        )}
      </span>
      <span className="ml-0.5 align-middle font-medium text-base">
        {likeCount}
      </span>
    </p>
  )
}

export default LikeAsset
