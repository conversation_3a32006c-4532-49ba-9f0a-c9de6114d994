import Asset from 'domain/models/asset'
import Image from 'next/image'
import clsx from 'clsx'
import { useState, useEffect } from 'react'
import { formatNumber, showFormatNumber } from 'lib/number'
import NetworkIcon from 'presentation/components/network-icon'
type Props = {
  asset: Asset;
  isShowIconSymbol?: Boolean,
  isShowName?: Boolean,
  classNamePrice?: String,
  classNamePriceUsd?: String,
}

const AssetPrice = ({
  asset,
  isShowName = true,
  isShowIconSymbol = true,
  classNamePrice = 'text-base text-dark-primary dark:text-white',
  classNamePriceUsd = 'text-xs font-medium text-gray-1 dark:text-gray-3',
  
}: Props) => {
  
  const iconSize = 18
  const [ price, setPrice ] = useState<any>(null)
  const [ auction, setAuction ] = useState<any>(null)

  useEffect(() => {
    // if(asset.currentPrice) {
    //   setPrice({
    //     amount: Number(asset.currentPrice || 0),
    //     symbol: asset?.currentPaymentToken?.symbol || '',
    //     icon: asset?.currentPaymentToken?.icon || '',
    //   })
    // } else {
    //   setPrice(null) 
    // }
    if(asset?.minListingType?.isFixedPrice()){
      setPrice({
        amount: Number(asset.minListingPrice || 0),
        amountUsd: Number(asset.minListingPrice || 0) * Number(asset?.minListingPaymentToken?.priceUsd || 0),
        symbol: asset?.minListingPaymentToken?.symbol || '',
        icon: asset?.minListingPaymentToken?.icon || '',
      })
    } else {
      setPrice(null)
    }
    
    if(asset?.minListingType?.isTimedAuction() 
      && (asset.minListingExpiresAt?.diffNow()?.valueOf() || 0) > 0
    ){

      setAuction({
        amount: Number(asset.minListingPrice || 0),
        symbol: asset?.minListingPaymentToken?.symbol || '',
        icon: asset?.minListingPaymentToken?.icon || '',
        amountUsd: Number(asset.minListingPrice || 0) * Number(asset?.minListingPaymentToken?.priceUsd || 0),
        remainDay: asset.minListingExpiresAt?.diffNow(['days']).toObject().days,
      })
    } else {
      setAuction(null)
    }
  
  }, [asset])

  return (
    <>
      {isShowName && (
        <div className="flex flex-wrap truncate font-medium mt-10p ">
          <span className="mr-1 leading-none pt-0.5 truncate text-dark-primary dark:text-white w-full pb-1 flex items-center">
            {asset.chainId && <NetworkIcon chainId={asset.chainId} width={16} height={16} className="mr-1" />}
            {asset.name}
          </span>
        </div>
      )}
      {price && (
        <div className="inline-flex items-center w-full mt-1">
          {isShowIconSymbol && price.icon &&  (
            <span className="icon-wrapper relative mr-1">
              <Image alt="" src={price.icon} width={iconSize} height={iconSize} style={{height: iconSize, width: 'auto'}} />
            </span>
          )}
          <span className={clsx('font-medium leading-none pt-0.5 ', classNamePrice)}>
            { showFormatNumber(price.amount, 0, 3)} {price.symbol }
          </span>
          <Image
            alt=""
            src="/asset/icons/dollar.png"
            width={14}
            height={14}
            className='mt-0.5 ml-1 mr-0.5'
          />
          <span className={clsx('leading-none pt-1 ', classNamePriceUsd)}>
            { showFormatNumber(price.amountUsd, 2 , 2)}
          </span>
        </div>

      )}
      {auction && (
        <div className="text-xs text-gray-1 dark:text-gray-3 text-right">
          <div className="flex flex-wrap items-center justify-end gap-0.5 font-medium">
            {/* <span>Offer for{isBun dle ? ' min' : ''}</span> */}
            <span className='mr-1'>Offer for</span>
            {auction.icon && <Image alt=""  src={auction.icon} width={18} height={18} style={{height: 18, width: 'auto'}} />}
            <span className='text-base text-dark-primary dark:text-white pt-0.5'>
              { showFormatNumber(auction.amount, 0, 3)} {auction.symbol }
            </span>
            <Image
              alt=""
              src="/asset/icons/dollar.png"
              width={14}
              height={14}
              className='mt-0.5 ml-1 mr-0.5'
            />
            <span className={clsx('text-xs font-medium text-gray-1 dark:text-gray-3 leading-none pt-1')}>
              { showFormatNumber(auction.amountUsd, 2 , 2)}
            </span>
            <br />
          </div>
          <span className='text-xs'>{formatNumber(auction.remainDay, 0, 1)} days left</span>
          
        </div>
      )}
    </>
  )
}

export default AssetPrice
