import Image from 'next/image'
import SvgIcon from 'presentation/components/svg-icon'
import { formatNumber } from 'lib/number'
import { DateTime } from 'luxon'
import PaymentToken from 'domain/models/payment_tokens'

type Props = {
  paymentToken: PaymentToken|null;
  price: Number|null,
  endTime?: DateTime|null,
  minimumbid?: Boolean
}

const PriceAssetDetail = ({
  paymentToken,
  price,
  endTime,
  minimumbid = false,
}: Props) => {
  
  
  return (
    <div>
      {endTime && (
        <p className="inline-flex lg:justify-start justify-center w-full">
          <SvgIcon
            name="adjustmentVertical"
            viewBox="0 0 15.086 22.63"
            stroke="#777E90"
            strokeWidth="1"
            className="w-3.5 dark:stroke-white"
          />
          <span className="ml-2 text-lg align-middle text-stroke-thin">
          Sale ends {endTime?.toFormat('yyyy-MM-dd mm:ss')}
          </span>
        </p>
      )}
      {minimumbid && (
        <p className="inline-flex lg:justify-start justify-center mt-4 w-full">Minimum bid</p>
      )}
      <div className="mt-2 flex flex-wrap-reverse lg:justify-start justify-center w-full">
        <div className="mr-4 flex items-center">
          <span className="mr-1.5">
            {paymentToken?.icon && (
              <Image
                alt="coin"
                src={paymentToken?.icon}
                width={30}
                height={30}
                style={{height: 30, width: 'auto'}}
              />
            )}
                              
          </span>
          <span className="text-lg lg:text-3xl font-medium text-primary-2">
            {formatNumber(Number(price || 0), 0, 8 )} {paymentToken?.symbol}
          </span>
        </div>
        <div className="h-fit flex items-center pb-1.5">
          <span className="mr-1.5">
            <Image
              alt=""
              src="/asset/icons/dollar.png"
              width={18}
              height={18}
            />
          </span>
          <span className="text-base align-middle font-medium">
            {formatNumber(Number(price || 0) * (paymentToken?.priceUsd || 0))}
          </span>
        </div>
      </div>
    </div>
  )
}

export default PriceAssetDetail
