import Asset from 'domain/models/asset'
import AssetThumbnail from 'presentation/components/asset/thumbnail'
import { useState } from 'react'
import clsx from 'clsx'

type Props = {
  asset: Asset;
  className?: string;
};

const AssetThumbnailPreview = ({
  asset,
  className = '',
}: Props) => {
  const [fullScreen, setFullScreen] = useState(false)

  const openFullScreen = (e: any) => {
    setFullScreen(true)
  }

  const closeFullScreen = (e: any) => {
    setFullScreen(false)
  }

  return (
    <>
      <AssetThumbnail 
        className={clsx(className, 'cursor-pointer')} 
        asset={asset} 
        onlyShowImage={false} 
        onClick={openFullScreen}
      />
      
      {fullScreen && (
        <div
          className="z-[999] grid overflow-hidden fixed top-0 left-0 right-0 bottom-0 justify-center bg-black bg-opacity-50 backdrop-filter backdrop-blur-sm"
          
        >
          <button className="absolute top-6 right-6 text-white text-4xl" onClick={closeFullScreen}>
            &times;
          </button>
          <div className="p-8 content-thumbnail-preview">
            <AssetThumbnail 
              className="h-full flex"
              classNameImage="!relative"
              classNameVideo=""
              classNameAudio="z-2"
              asset={asset} 
              autoPlayVideo={true}
            />

          </div>
        </div>
      )}
    </>
  )
}

export default AssetThumbnailPreview
