import ButtonTag from 'presentation/components/button/button-tag'

const ButtonTabs = ({
  tabs = [],
  activeTab = '',
  onChangeTab = () => {},
  className = 'flex flex-wrap justify-center lg:justify-start lg:flex-nowrap gap-2 md:gap-4 xl:gap-5',
}:any) => {

  return (
    <>
      {tabs.length > 0 && (
        <div className={className}>
          {tabs.map((tab:any, key:number) => (
            <ButtonTag
              key={key}
              className="shrink-0"
              active={activeTab === tab.slug}
              onClick={() => onChangeTab(tab.slug)}
            >
              {tab.name}
            </ButtonTag>
          ))}
        </div>
      )}
    </>
  )
}

export default ButtonTabs
