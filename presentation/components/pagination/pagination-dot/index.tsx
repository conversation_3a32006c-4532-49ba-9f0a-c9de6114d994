import clsx from 'clsx'
import { usePagination, DOTS } from 'lib/hook/customHook'

const PaginationDot = (props: any) => {
  const {
    totalCount,
    siblingCount = 1,
    currentPage,
    pageSize,
    onPageChange,
  } = props

  const paginationRange: any = usePagination({
    currentPage,
    totalCount,
    siblingCount,
    pageSize,
  })

  if (currentPage === 0 || paginationRange?.length < 2) {
    return null
  }

  return (
    <>
      <ul className="flex pagination-dot">
        {paginationRange?.map((pageNumber: number | string, index: number) => {
          if (pageNumber === DOTS) {
            return <li key={index} className="pagination-item dots">&#8230;</li>
          }

          return (
            <li
              key={index}
              className={clsx('pagination-item', {
                selected: pageNumber === currentPage,
              })}
              onClick={() => onPageChange(pageNumber)}
            >
              &#8226;
            </li>
          )
        })}
      </ul>
    </>
  )
}

export default PaginationDot
