import { useEffect, useState } from 'react'
import clsx from 'clsx'
import { usePagination, DOTS, pageSizes } from 'lib/hook/customHook'
import { rowOptions } from 'presentation/controllers/mockData/options'
import Select from 'presentation/components/select'
import SvgIcon from 'presentation/components/svg-icon'

const Pagination = (props: any) => {
  const {
    totalCount,
    siblingCount = 1,
    currentPage,
    pageSize = pageSizes.pageSize10,
    className,
    enableSelectRow = true,
    showCountText = true,
    selectRowOptions = rowOptions,
    onPageChange,
    onChangePageSize,
    isShortened = false,
  } = props

  const [currentPageSize, setCurrentPageSize] = useState(pageSize)
  const [defaultSelected, setDefaultSelected] = useState(selectRowOptions[0])
  const paginationRange: any = usePagination({
    currentPage,
    totalCount,
    siblingCount,
    pageSize: currentPageSize,
    isShortened,
  })
  useEffect(() => {
    const option = selectRowOptions.find((item: any) => item.value === pageSize)
    setDefaultSelected(option)
  }, [pageSize, setDefaultSelected, selectRowOptions])
  
  
  if (currentPage === 0) {
    return null
  }

  const onNext = () => {
    onPageChange(currentPage + 1)
  }

  const onPrevious = () => {
    onPageChange(currentPage - 1)
  }

  let lastPage = paginationRange[paginationRange.length - 1]
  return (
    <div className={clsx('pagination-wrap gap-4 flex flex-wrap justify-center md:justify-between items-center', isShortened && 'xl:gap-2')}>
      <div className="shrink-0">
        {showCountText && (
          <p className="text-gray-1 dark:text-gray-3">
            Showing {(currentPageSize * currentPage) - currentPageSize + 1}-{currentPageSize * currentPage > totalCount ? totalCount : currentPageSize * currentPage} out of {totalCount.toLocaleString()}
          </p>
        )}
      </div>
      <div className="pagination">
        <ul className="flex items-center gap-1">
          <li>
            <SvgIcon
              name="chevronRight"
              className={clsx('w-5 h-5 stroke-none fill-primary-2 cursor-pointer rotate-180 pagination-arrow', {
                disabled: currentPage === 1,
              })}
              onClick={onPrevious}
            />
          </li>
          {paginationRange?.map((pageNumber: number | string, index: number) => {
            if (pageNumber === DOTS) {
              return <li key={index} className={clsx('pagination-item dots', isShortened && 'shortened')}>&#8230;</li>
            }

            return (
              <li
                key={index}
                className={clsx('pagination-item', {
                  selected: pageNumber === currentPage,
                }, isShortened && 'shortened')}
                onClick={() => onPageChange(pageNumber)}
              >
                {pageNumber}
              </li>
            )
          })}
          <li>
            <SvgIcon
              name="chevronRight"
              className={clsx('w-5 h-5 stroke-none fill-primary-2 cursor-pointer pagination-arrow', {
                disabled: currentPage === lastPage,
              })}
              onClick={onNext}
            />
          </li>
        </ul>
      </div>
      <div className="">
        {enableSelectRow && (
          <Select  defaultSelected={defaultSelected} options={selectRowOptions} onSelect={(option) => {
            if (currentPageSize !== option.value) {
              setCurrentPageSize(option.value)
              onChangePageSize(option.value)
            }
          }} />
        )}
      </div>
    </div>
  )
}

export default Pagination
