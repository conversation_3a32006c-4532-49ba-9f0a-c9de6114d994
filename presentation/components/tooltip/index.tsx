import clsx from 'clsx'
import { ReactNode, useState, Children } from 'react'
import SvgIcon from '../svg-icon'

type Props = {
  children?: ReactNode,
  contentWidth?: string,
  iconSize?: string,
  position?: 'top' | 'bottom' | 'left' | 'right' | 'bottomLeft' | 'bottomRight' | 'topCenter' | 'topRight';
  className?: string,
}

type content = {
  children?: ReactNode,
}

const Tooltip = (props: Props) => {
  const [showTooltip, setShowTooltip] = useState(false)
  const { iconSize = 'w-4 h-4', position = 'top' } = props
  let _body, _content

  const contentPosition = {
    top: 'top-0 left-0 -translate-y-full -translate-x-1/2',
    right: 'top-0 right-0 translate-x-full',
    bottomLeft: 'bottom-0 -translate-x-1/2 translate-y-full',
    bottomRight: 'bottom-0 -translate-x-full translate-y-full',
    
    topCenter: 'bottom-full mb-1 left-1/2 -translate-x-1/2',
    bottom: 'top-full mt-1 left-1/2 -translate-x-1/2',
    left: 'right-full mr-1 top-1/2 -translate-y-1/2',
    topRight: 'left-full ml-1 top-1/2 -translate-y-1/2',
  }

  Children.forEach(props.children, (child: any) => {
    if (child && child.type === TooltipBody) {
      return _body = child
    }
    if (child && child.type === TooltipContent) {
      return _content = child
    }
  })
  
  if (!_content) _content = <>{props.children}</>
  if (!_body) _body = <SvgIcon name="info" className={`ml-1 stroke-none fill-primary-2 ${iconSize}`}/>

  return (
    <div className={clsx('relative', props.className)}>
      <span onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
      >
        {/* <SvgIcon name="info" className={`ml-1 stroke-none fill-primary-2 ${iconSize}`}/> */}
        {_body}
      </span>
      {showTooltip && (
        <div className={clsx('absolute min-w-[200px] min-h-fit py-2 px-4 text-xs bg-green-2 dark:bg-blue-3 rounded-lg z-50',
          contentPosition[position],
          props.contentWidth)}>
          {/* {props.children} */}
          {_content}
        </div>
      )}
    </div>
  )
}
const TooltipBody = (props: content) => <>{props.children}</>
const TooltipContent = (props: content) => <>{props.children}</>

Tooltip.Body = TooltipBody
Tooltip.Content = TooltipContent

export default Tooltip
