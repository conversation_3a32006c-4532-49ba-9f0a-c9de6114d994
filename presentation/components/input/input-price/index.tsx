import Image from 'next/image'
import Input from 'presentation/components/input'
import clsx from 'clsx'

type Props = {
  symbolIconSrc?: string;
  priceSymbol?: string;
  inputClass?: string;
  type?: string;
  name?: string;
  value?: string | number;
  wrapperClassName?: string;
  onChange?: (e: any) => void;
};

const InputPrice = (props: Props) => {
  const {
    symbolIconSrc,
    priceSymbol,
    type,
    name,
    value,
    inputClass = 'w-14 mr-6 pr-2 ml-7',
    wrapperClassName,
    onChange,
  } = props
  return (
    <div
      className={clsx(
        'rounded-25 pl-4 max-h-50p py-3.5 w-fit flex items-center bg-gray-5 dark:bg-blue-1',
        wrapperClassName
      )}
    >
      {priceSymbol && (
        <p className="mr-1 font-medium uppercase">{priceSymbol}</p>
      )}
      {symbolIconSrc && (
        <Image
          alt=""
          src={symbolIconSrc}
          width={25}
          height={25}
          className="mr-1"
        />
      )}
      <Input
        noBaseClass
        width=""
        className={clsx(
          'max-h-full no-arrow rounded-r-25 bg-transparent outline-none',
          inputClass
        )}
        type={type}
        name={name}
        value={value}
        onChange={onChange}
      />
    </div>
  )
}

export default InputPrice
