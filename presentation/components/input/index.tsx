import clsx from 'clsx'
import { forwardRef, useEffect, useState } from 'react'
import SvgIcon from '../svg-icon'

type Props = {
  disabled?: boolean;
  type?: string;
  step?: string;
  placeholder?: string;
  name?: string;
  value?: any;
  width?: string;
  className?: string;
  wrapClass?: string;
  noBaseClass?: boolean;
  suffixIcon?: string;
  suffixIconClassName?: string;
  suffixIconViewBox?: string;
  prefixIcon?: string;
  prefixIconClassName?: string;
  prefixIconViewBox?: string;
  readOnly?: boolean;
  deleteIcon?: boolean;
  maxLength?: number;
  valueRange?: Array<number>;
  onChange?: (e: any) => void;
  onFocus?: () => void;
  onBlur?: (e: any) => void;
  onClick?: () => void;
};

const Input = forwardRef<HTMLInputElement, Props>(
  (
    {
      disabled,
      placeholder,
      name,
      value,
      className,
      wrapClass,
      noBaseClass = false,
      type = 'text',
      step = 'any',
      width = 'w-full',
      suffixIcon,
      maxLength,
      valueRange = [0, 100],
      suffixIconClassName,
      suffixIconViewBox,
      prefixIcon,
      prefixIconClassName,
      prefixIconViewBox,
      readOnly = false,
      deleteIcon = false,
      onChange,
      onFocus,
      onBlur,
      onClick,
      ...props
    }: Props,
    ref
  ) => {
    const [inputValue, setInputValue] = useState(value)

    const baseClass = `px-5 py-3 ${
      readOnly
        ? 'bg-gray-5 dark:bg-blue-8 dark:text-white'
        : 'bg-gray-4 dark:bg-blue-8'
    } outline-0 rounded-3xl`

    const handleFocus = () => {
      onFocus && onFocus()
    }
    const handleBlur = (e: any) => {
      onBlur && onBlur(e)
    }
    const handleChange = (e: any) => {
      setInputValue(e.target.value)
      onChange && onChange(e)
    }
    const handleClick = () => {
      onClick && onClick()
    }
    const handleDelete = () => {
      setInputValue('')
      if (onChange) {
        const event = {
          target: { value: '' },
        } as any
        onChange(event)
      }
    }

    useEffect(() => {
      setInputValue(value)
    }, [value])

    return (
      <div
        className={clsx(
          'relative',
          suffixIcon ? 'input-with-suffix-icon' : '',
          prefixIcon ? 'input-with-prefix-icon' : '',
          wrapClass
        )}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onClick={handleClick}
      >
        {prefixIcon && (
          <SvgIcon
            name={prefixIcon}
            viewBox={prefixIconViewBox}
            className={prefixIconClassName}
          />
        )}
        <input
          step={step}
          disabled={disabled}
          ref={ref}
          className={clsx(noBaseClass ? '' : baseClass, width, className)}
          type={type}
          name={name}
          value={inputValue}
          placeholder={placeholder}
          onChange={handleChange}
          readOnly={readOnly}
          maxLength={maxLength}
          min={valueRange[0]}
          max={valueRange[1]}
          {...props}
        />
        {suffixIcon && (
          <SvgIcon
            name={suffixIcon}
            viewBox={suffixIconViewBox}
            className={suffixIconClassName}
          />
        )}
        {deleteIcon && inputValue && (
          <SvgIcon
            onClick={(e) => { 
              e.stopPropagation()
              handleDelete()
            }}
            name="plus"
            className={clsx(
              'absolute top-3.5 right-4 w-5 h-5 stroke-none fill-gray-1 rotate-45 cursor-pointer',
            )}
          />

        )}
      </div>
    )
  }
)

Input.displayName = 'Input'

export default Input
