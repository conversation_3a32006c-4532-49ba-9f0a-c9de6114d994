import { useState, useEffect } from 'react'
import Input from 'presentation/components/input'
import Asset from 'domain/models/asset'
import Image from 'next/image'

import SvgIcon from 'presentation/components/svg-icon'
import { MetadataStatus } from 'domain/models/asset/metadata_status'
import { DateTime } from 'luxon'
import Chain from 'domain/value_objects/chain'

type Props = {
  defaultValue?: Number;
  asset: Asset;
  canDelete: boolean;
  onChange: (option: any) => void;
  onDelete: (option: any) => void;
};

const emptyAsset = Asset.newEmtyAsset()

const InputListingAssetQuantity = ({
  defaultValue = 1,
  asset = emptyAsset,
  canDelete = true,
  onChange = () => {},
  onDelete = () => {},
}: Props) => {
  const [readOnly, setReadOnly] = useState(false)
  const [collectionAsset] = useState(asset)

  useEffect(() => {
    if (collectionAsset != null) {
      if (collectionAsset.contract?.schema.getName() == 'erc1155') {
        setReadOnly(true)
      } else {
        setReadOnly(false)
      }
    }
  }, [collectionAsset])

  return (
    <>
      <div className="w-fit flex justify-between border border-gray-7 rounded-lg p-2 my-4 mx-auto">
        <div className="flex justify-start">
          <Image
            className="rounded-10"
            src="/asset/images/<EMAIL>"
            objectFit="contain"
            alt=""
            width={50}
            height={50}
          />
          <div className="ml-10p my-auto">
            <p className="text-sm text-dark-primary dark:text-white">
              {asset.name}
            </p>
            <p className="text-sm text-gray-1">{asset.collection?.name}</p>
          </div>
        </div>
        <Input
          className="w-[120px] ml-1 my-auto"
          value={defaultValue}
          readOnly={readOnly}
          type="number"
        />
        {canDelete ? (
          <SvgIcon
            name="trash"
            className="w-5 h-5 stroke-red my-auto mx-2 cursor-pointer"
            onClick={() => {
              onDelete(asset)
            }}
          />
        ) : (
          <div className="w-5 h-5 mx-2" />
        )}
      </div>
    </>
  )
}

export default InputListingAssetQuantity
