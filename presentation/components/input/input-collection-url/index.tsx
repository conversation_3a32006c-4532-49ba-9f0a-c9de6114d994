import clsx from 'clsx'
import Input from 'presentation/components/input'
import SvgIcon from 'presentation/components/svg-icon'
import CollectionUseCase from 'presentation/use_cases/collection_use_case'
import { useEffect, useState } from 'react'
import { getChainName } from 'lib/utils'
import { NetworkStateProvider } from 'presentation/providers/network_state_provider'
const collectionUseCase = new CollectionUseCase()

type Props = {
  defaultValue?: string;
  isUpdate?: boolean;
  chainId?: number; // Keep for backward compatibility
  onChange: (option: any) => void;
};

const InputCollectionURL = ({
  defaultValue = '',
  isUpdate = false,
  chainId = 1,
  onChange = () => {},
}: Props) => {
  const [error, setError] = useState('')
  const [inputValue, setInputValue] = useState(defaultValue)
  const [currentValue] = useState(defaultValue)
  const [isValid, setIsValid] = useState(false)
  const [domain, setDomain] = useState('')
  
  // Get network from NetworkStateProvider
  const networkState = NetworkStateProvider((state) => ({
    network: state.network,
  }))
  

  const checkValid = async () => {
    await collectionUseCase
      .findBySlug(inputValue)
      .then((res) => {
        if (isUpdate && currentValue === inputValue) {
          setIsValid(true)
          return
        } else {
          setIsValid(false)
          setError('This URL name is already taken.')
        }
      })
      .catch((_error) => {
        const regex = /^[a-z0-9-]*$/
        if (regex.test(inputValue)) {
          setIsValid(true)
        } else {
          setIsValid(false)
          setError('This URL name is invalid.')
        }
      })
  }
  const getDomain = async () => {
    let domain  = ''
    if (typeof window !== 'undefined') {
      domain = window.location.origin // Get the domain name
    }
    if(domain.includes('localhost')) {
      domain = 'https://stg.nft-land.me'
    }
    const chainName = getChainName(chainId) || 'unknown'
    domain += '/' + chainName
    setDomain(domain)
  }
  useEffect(() => {
    const delayInputTimeoutId = setTimeout(() => {
      if (inputValue) {
        checkValid()
      }
    }, 500)
    return () => clearTimeout(delayInputTimeoutId)
  }, [inputValue])

  useEffect(() => {
    getDomain()
  }, [chainId])
  return (
    <>
      <div className="mt-5">
        <p className="font-medium text-dark-primary dark:text-gray-3">
          URL{defaultValue == '' && <span className="text-red-1">{' *'}</span>}
        </p>
        <p className="mt-1 text-sm text-gray-1 mb-1.5">
          Customize your URL. Must only contain lowercase letters, numbers, and
          hyphens.
        </p>
        <div className="flex items-center flex-wrap">
          <p>{domain}/</p>
          <Input
            type="text"
            name="url"
            value={inputValue}
            placeholder={inputValue}
            onChange={(e) => {
              setInputValue(e.target.value)
              onChange(e.target.value)
            }}
            className={clsx(
              'w-48 border mr-3',
              inputValue && isValid
                ? 'border-green-1'
                : inputValue && !isValid
                  ? 'border-red-1'
                  : ''
            )}
          />
          {inputValue && isValid && (
            <div className="inline-flex items-center">
              <SvgIcon name="check" className="w-5 h-5 stroke-green-1 mr-1" />
              <span className="text-sm text-gray-1">This URL is valid.</span>
            </div>
          )}
          {inputValue && !isValid && (
            <div className="inline-flex items-center">
              <SvgIcon
                name="plus"
                className="w-7 h-7 stroke-none fill-red-1 rotate-45 cursor-pointer"
              />
              <span className="text-sm text-red-1">{error}</span>
            </div>
          )}
        </div>
      </div>
    </>
  )
}

export default InputCollectionURL
