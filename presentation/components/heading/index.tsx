import clsx from 'clsx'
import { ReactNode } from 'react'

type Props = {
  children: ReactNode
  className?: string
  textColor?: string
}

const Heading = ({
  children,
  className,
  textColor = 'text-dark-primary dark:text-white',
  ...props
}: Props) => {
  return (
    <p
      className={clsx(
        'text-xl md:text-3xl xl:text-4xl font-medium md:italic text-center text-dark-primary dark:text-white ',
        className
      )}
      {...props}
    >
      {children}
    </p>
  )
}

export default Heading
