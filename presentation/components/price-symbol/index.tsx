import clsx from 'clsx'
import Image from 'next/image'
import { formatNumber } from 'lib/number'

type Props = {
  text?: string,
  price?: number | string,
  priceCustom?: number | string,
  symbol?: string,
  icon?: string | '/asset/icons/ETH.svg',
  iconSize?: number,
  className?: string,
  separateTitle?: boolean,
  rank?: string | number,
  isBundle?: boolean,
  isSimilar?: boolean,
  onClickRank?: (e: any) => void,
}

const PriceSymbol = ({
  text,
  price,
  priceCustom,
  symbol,
  icon = '/asset/icons/ETH.svg',
  iconSize = 20,
  className,
  separateTitle,
  rank,
  isBundle = false,
  isSimilar = false,
  onClickRank,
}: Props) => {

  return (
    <>
      {isSimilar ? (
        <div className={clsx('font-medium price-symbol', className)}>
          <div className="flex flex-wrap truncate px-1">
            {text && (
              <span className="leading-none truncate w-full pt-0.5 pb-1">
                {text}
              </span>
            )}
            <span className="leading-none pt-0.5 text-primary-2">
              { priceCustom ? priceCustom : formatNumber(Number(price || 0))} {symbol || 'ETH'}
            </span>
          </div>
        </div>
      ) : (
        <div className={clsx('font-medium price-symbol', className)}>
          <div className={separateTitle ? 'flex flex-wrap truncate' : 'inline-flex items-center'}>
            {text && (
              <span className={clsx('mr-1 leading-none pt-0.5 truncate text-dark-primary dark:text-white', 
                separateTitle ? 'w-full pb-1' : '')}
              >
                {text}
              </span>
            )}
            {separateTitle ? (
              <div className="inline-flex items-center justify-between w-full">
                {isBundle && (
                  <span className="mr-0.5 font-normal text-dark-blue dark:text-white">Min</span>
                )}
                <span className="icon-wrapper relative">
                  <Image alt="" src={icon} width={iconSize} height={iconSize} style={{height: iconSize, width: 'auto'}} />
                </span>
                <span className={clsx(!rank && 'ml-1', 'leading-none pt-0.5 text-dark-primary dark:text-white flex-grow')}>
                  { priceCustom ? priceCustom : formatNumber(Number(price || 0))} {symbol || 'ETH'}
                </span>
                {rank && (
                  <span className="mt-1 py-[2px] px-1 float-right leading-[initial] text-xs rounded-10 border border-primary-2 text-primary-2 cursor-pointer"
                    onClick={onClickRank}
                  >
                    # {rank?.toLocaleString()}
                  </span>
                )}
              </div>
            ) : (
              <>
                {isBundle && (
                  <span className="mr-0.5">Min</span>
                )}
                <span className="icon-wrapper relative">
                  <Image alt="" src={icon} width={iconSize} height={iconSize} style={{height: iconSize, width: 'auto'}} />
                </span>
                <span className="ml-1 leading-none pt-1">{priceCustom ? priceCustom : formatNumber(Number(price || 0))} {symbol || 'ETH'}</span>
              </>
            )}
          </div>
        </div>
      )}
    </>
  )
}

export default PriceSymbol
