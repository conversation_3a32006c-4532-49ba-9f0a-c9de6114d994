import { useRef, useEffect, useState } from 'react'
import ShowMore from './index'

type Props = {
  description?: string
}

const CollectionDescription = ({ description }: Props) => {
  const [hasMoreContent, setHasMoreContent] = useState(false)
  const textRef = useRef<HTMLParagraphElement>(null)

  useEffect(() => {
    const checkContentHeight = () => {
      if (textRef.current) {
        const contentHeight = textRef.current.scrollHeight
        console.log('🚀 ~ checkContentHeight ~ textRef.current:', textRef.current)
        console.log('🚀 ~ checkContentHeight ~ contentHeight:', contentHeight)
        setHasMoreContent(contentHeight > 24)
      }
    }

    checkContentHeight()
    window.addEventListener('resize', checkContentHeight)
    
    return () => {
      window.removeEventListener('resize', checkContentHeight)
    }
  }, [description])

  if (!description) return null

  return (
    <ShowMore
      height="22px"
      className="mt-2 mb-1"
      showMoreClass={hasMoreContent ? 'flex items-center justify-center' : 'hidden'}
    >
      <p 
        ref={textRef}
        className="text-gray-1 dark:text-gray-3 max-w-[600px] break-all whitespace-pre-line"
      >
        {description}
      </p>
    </ShowMore>
  )
}

export default CollectionDescription 