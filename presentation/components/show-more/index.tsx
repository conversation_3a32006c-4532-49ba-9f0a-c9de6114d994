import clsx from 'clsx'
import { ReactNode, useState } from 'react'
import SvgIcon from '../svg-icon'

type Props = {
  height?: string,
  className?: string,
  showMoreText?: string,
  showMoreClass?: string,
  arrowPosition?: 'left' | 'right',
  textClass?: string,
  iconClass?: string,
  wrapClass?: string,
  children?: ReactNode,
}

const ShowMore = ({
  height, className,
  showMoreText = 'More',
  arrowPosition = 'left',
  showMoreClass,
  textClass = 'text-sm text-gray-1 dark:text-gray-3 text-stroke-thin',
  children,
  wrapClass,
  iconClass = 'w-5 h-5 fill-none stroke-gray-1 dark:stroke-gray-3 stroke-[2]',
  ...props}: Props) => {

  const [expanded, setExpand] = useState(false)

  return (
    <div className={wrapClass}>
      <div className={clsx('transition-all', className)} {...props}>
        <div className="inline-block" style={expanded ? {height: '100%'} : {height: height , overflow: 'hidden'}}>
          {children}
        </div>
      </div>
      <div className={clsx(showMoreClass)}>
        <button className={clsx('flex items-center cursor-pointer', arrowPosition === 'right' && 'flex-row-reverse')} onClick={() => setExpand(!expanded)}>
          <SvgIcon
            name="chevronUp"
            className={clsx('transition', iconClass, expanded ? 'rotate-0' : 'rotate-180')}/>
          <span className={clsx(textClass, arrowPosition === 'right' ? 'mr-7' : 'ml-2')}>
            {expanded ? 'Less' : showMoreText}
          </span>
        </button>
      </div>
    </div>
  )
}

export default ShowMore
