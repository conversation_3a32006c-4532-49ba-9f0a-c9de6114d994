import clsx from 'clsx'
import Image from 'next/image'

type Props = {
  label?: string,
  name: string,
  value: string | number | null,
  icon?: string,
  iconSize?: number,
  className?: string,
  defaultChecked?: boolean,
  checked?: boolean,
  onChange?: (e: any) => void,
}

const RadioTag = ({ label, name, value, icon, iconSize = 16, className, defaultChecked, checked, onChange }: Props) => {

  return (
    <div className={clsx('rounded-full px-3 py-3 border flex items-center radio-tag-wrapper min-w-[106px]', className)}>
      <input type="radio" name={name} id={`${name}-${value}`} value={value || ''} onChange={onChange} defaultChecked={defaultChecked} checked={checked}/>
      <label className="ml-1 font-medium" htmlFor={`${name}-${value}`}>{label}</label>
      {icon && (
        <span className="ml-1 leading-none icon-wrapper flex-none">
          <Image style={className?.includes('radio-height-20') ? { width: 'auto', height: '20px' } : {}} alt={name} src={`/asset/icons/${icon}`} width={iconSize} height={iconSize}/>
        </span>
      )}
    </div>
  )
}

export default RadioTag
