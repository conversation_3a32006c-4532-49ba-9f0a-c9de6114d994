import Link from 'next/link'
import SvgIcon from '../svg-icon'

type Props = {
  tabs: any;
  className?: string;
};

const BreadCrumb = ({
  tabs = [],
  className = 'inline-flex items-center space-x-1 md:space-x-3',
}: Props) => {
  return (
    <>
      <nav className="flex mt-10 mb-10" aria-label="Breadcrumb">
        <ol className={className}>
          {tabs.map((tab: any, key: number) => (
            <li className="inline-flex items-center" key={key}>
              <Link href={tab.url}>
                <span className="cursor-pointer hover:text-primary-1 transition-colors">
                  {tab.name}
                </span>
              </Link>
              {key < tabs.length - 1 && (
                <SvgIcon
                  name="arrowRight"
                  stroke="none"
                  className="w-6 h-6 text-gray-400"
                />
              )}
            </li>
          ))}
        </ol>
      </nav>
    </>
  )
}

export default BreadCrumb
