import { useState } from 'react'
import ToggleVertical from 'presentation/components/toggle-wrap/toggle-vertical'
import FilterStatus from 'presentation/controllers/abstract/value_object'
import { FilterParams } from 'types/type'
import classNames from 'classnames'

type Props = {
  defaultShow?: boolean
  filterStatues: FilterStatus[]
  changeFilterParam?: (param: keyof FilterParams, value: any, activeState?: boolean) => void
}

const ToggleVerticalFilterStatuesV2 = ({
  defaultShow = true,
  filterStatues = [],
  changeFilterParam = () => {},
}: Props) => {
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([])

  const handleStatusClick = (statusName: string) => {
    let newSelectedStatuses: string[]

    if (statusName === 'all') {
      // If clicking "All", clear all selections
      newSelectedStatuses = []
      // Clear all filter statuses
      selectedStatuses.forEach((status: string) => {
        changeFilterParam('filterStatuses', status, false)
      })
      changeFilterParam('filterStatuses', null, true)
    } else {
      if (selectedStatuses.includes(statusName)) {
        // If status is already selected, remove it
        newSelectedStatuses = selectedStatuses.filter((s: string) => s !== statusName)
        changeFilterParam('filterStatuses', statusName, false)
      } else {
        // If status is not selected, add it
        newSelectedStatuses = [...selectedStatuses, statusName]
        changeFilterParam('filterStatuses', statusName, true)
      }
    }

    setSelectedStatuses(newSelectedStatuses)
  }

  return (
    <ToggleVertical toggleTitle="Status" defaultShow={defaultShow}>
      <div className="grid grid-cols-2 gap-2 mt-4">
        <button
          onClick={() => handleStatusClick('all')}
          className={classNames(
            'px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-200',
            selectedStatuses.length === 0
              ? 'bg-primary-2 text-white shadow-md'
              : 'bg-gray-4 text-gray-1 hover:bg-gray-3'
          )}
        >
          All
        </button>
        {filterStatues.map((status) => (
          <button
            key={status.name}
            onClick={() => handleStatusClick(status.name)}
            className={classNames(
              'px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-200',
              selectedStatuses.includes(status.name)
                ? 'bg-primary-2 text-white shadow-md'
                : 'bg-gray-4 text-gray-1 hover:bg-gray-3'
            )}
          >
            {status.label}
          </button>
        ))}
      </div>
    </ToggleVertical>
  )
}

export default ToggleVerticalFilterStatuesV2
