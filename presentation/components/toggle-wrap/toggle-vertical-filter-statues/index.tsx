import { useEffect } from 'react'
import ToggleVertical from 'presentation/components/toggle-wrap/toggle-vertical'
import FilterStatus from 'presentation/controllers/abstract/value_object'
import Switch from 'presentation/components/switch'
import { FilterParams } from 'types/type'

type Props = {
  defaultShow?: boolean,
  isActiveAll?: boolean
  filterStatues: FilterStatus[],
  changeFilterParam?: (param: keyof FilterParams, value: any, activeState?: boolean) => void,
}

const ToggleVerticalFilterStatues = ({
  defaultShow = true,
  filterStatues = [],
  isActiveAll = false,
  changeFilterParam = () => {},
}: Props) => {


  return (
    <ToggleVertical toggleTitle="Status" defaultShow={defaultShow}>
      <ul className="fist-mt-none">
        {filterStatues.map((status, index) => (
          <li key={index} className="mt-3.5 font-medium flex items-center justify-between">
            {status.label}
            <Switch defaultActive={isActiveAll} onClick={(activeState) => changeFilterParam('filterStatuses', status.name, activeState)}/>
          </li>
        ))}
      </ul>
    </ToggleVertical>
  )
}

export default ToggleVerticalFilterStatues
