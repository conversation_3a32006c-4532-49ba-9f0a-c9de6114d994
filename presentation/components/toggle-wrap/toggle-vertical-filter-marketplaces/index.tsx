import ToggleVertical from 'presentation/components/toggle-wrap/toggle-vertical'
import Image from 'next/image'
import Switch from 'presentation/components/switch'
import { FilterParams } from 'types/type'
import Marketplace from 'domain/value_objects/marketplace'

type Props = {
  defaultShow?: boolean,
  marketplaces: Marketplace[],
  changeFilterParam?: (param: keyof FilterParams, value: any, activeState?: boolean) => void,
}

const ToggleVerticalFilterMarketplaces = ({
  defaultShow = true,
  marketplaces = [],
  changeFilterParam = () => { },
}: Props) => {

  return (
    <ToggleVertical toggleTitle="Marketplace" defaultShow={defaultShow}>
      <ul className="fist-mt-none">
        {marketplaces.map((marketplace, index) => (
          <li key={index} className="mt-15p font-medium flex items-center justify-between">
            <p className="flex items-center">
              <Image src={marketplace.getIconPath()} width={25} height={25} alt="" />
              <span className="ml-3">{marketplace.getLabel()}</span>
            </p>
            <Switch onClick={(activeState) => changeFilterParam('marketplaces', marketplace, activeState)} />
          </li>
        ))}
      </ul>
    </ToggleVertical>
  )
}

export default ToggleVerticalFilterMarketplaces
