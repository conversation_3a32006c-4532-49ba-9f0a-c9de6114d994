import Link from 'next/link'
import {
  MutableRefObject,
  ReactNode,
  useEffect,
  useRef,
  useState,
} from 'react'
import clsx from 'clsx'
import SvgIcon from 'presentation/components/svg-icon'

type Props = {
  toggleTitle?: string;
  titleLink?: string;
  defaultShow?: boolean;
  heightFit?: boolean;
  children?: ReactNode;
  contentClass?: string;
  contentWrapClass?: string;
  className?: string;
  toggleTitleWrapClass?: string;
  toggleTitleWrapClassWhenClose?: string;
  titleColor?: string;
};

const ToggleVertical = ({
  toggleTitle,
  titleLink,
  defaultShow,
  heightFit = false,
  children,
  contentClass,
  contentWrapClass,
  className,
  toggleTitleWrapClass,
  toggleTitleWrapClassWhenClose,
  titleColor,
}: Props) => {
  const contentRef: MutableRefObject<null | any> = useRef(null)

  const [isShow, setIsShow] = useState(defaultShow)
  const [height, setHeight] = useState(0)
  const titleDefaultColor = titleColor
    ? titleColor
    : titleLink
      ? 'text-dark-primary hover:text-primary-2 dark:text-white'
      : 'text-dark-primary dark:text-white'

  useEffect(() => {
    if (isShow) {
      setHeight(
        heightFit
          ? 'fit-content'
          : contentRef.current?.getBoundingClientRect().height
      )
    } else {
      setHeight(0)
    }
  }, [isShow])

  return (
    <div className={clsx('w-full', className)}>
      <div
        className={clsx(
          'pl-5 pr-8 py-10p flex justify-between bg-blue-7 dark:bg-slate-800',
          toggleTitleWrapClass,
          !isShow && toggleTitleWrapClassWhenClose
        )}
      >
        {titleLink ? (
          <Link
            href={titleLink}
            className={clsx(
              titleDefaultColor,
              'text-lg font-bold cursor-pointer transition-colors dark:text-white'
            )}
          >
            {toggleTitle}
          </Link>
        ) : (
          <p className={clsx(titleDefaultColor, 'text-lg font-bold')}>
            {toggleTitle}
          </p>
        )}
        {children && (
          <button>
            <SvgIcon
              className={
                isShow
                  ? 'w-4 h-2 fill-primary-2 stroke-none'
                  : 'w-6 h-6 -mr-1 fill-primary-2 stroke-none'
              }
              name={isShow ? 'minus' : 'plus'}
              viewBox={isShow ? '0 0 16 2' : '0 0 24 24'}
              strokeWidth={isShow ? '2' : '1.5'}
              onClick={() => setIsShow(!isShow)}
            />
          </button>
        )}
      </div>
      {children && (
        <div
          className={clsx(
            'overflow-hidden transition-all ease-in-out duration-200 bg-white dark:bg-dark-primary',
            contentWrapClass
          )}
          style={{ height }}
        >
          <div
            ref={contentRef}
            className={clsx('toggle-content', contentClass)}
          >
            {children}
          </div>
        </div>
      )}
    </div>
  )
}

export default ToggleVertical
