import clsx from 'clsx'
import Collection from 'domain/models/collection'
import Search from 'presentation/components/search'
import SearchResult from 'presentation/components/search/search-result'
import ToggleVertical from 'presentation/components/toggle-wrap/toggle-vertical'
import CollectionUseCase from 'presentation/use_cases/collection_use_case'
import { MutableRefObject, useEffect, useRef, useState } from 'react'
import { FilterParams } from 'types/type'
import { NetworkStateProvider } from 'presentation/providers/network_state_provider'

const collectionUseCase = new CollectionUseCase()

type Props = {
  defaultShow?: boolean;
  isExact?: boolean;
  changeFilterParam?: (
    param: keyof FilterParams,
    value: any,
    activeState?: boolean
  ) => void;
};

const ToggleVerticalSearchCollection = ({
  defaultShow = true,
  isExact = false,
  changeFilterParam = () => {},
}: Props) => {
  // Add NetworkStateProvider
  const networkStateProvider = NetworkStateProvider((state) => ({
    network: state.network,
  }))

  const [isLoading, setIsLoading] = useState(false)
  const [showResult, setShowResult] = useState(false)
  const [searchResult, setSearchResult] = useState<any>([])
  const [selectedCollection, setSelectedCollection] = useState<Collection | null>(null)
  const searchResultRef: MutableRefObject<null | any> = useRef(null)

  const handleSearch = async (e: any) => {
    const keyword = e.target.value
    keyword.trim().length === 0 ? setShowResult(false) : setShowResult(true)
    setIsLoading(true)
    
    // Get chainId from network provider
    const chainId = networkStateProvider.network?.chainId || ''
    
    await collectionUseCase
      .findBySlug(keyword, isExact, chainId) // Pass chainId as third parameter
      .then((res) => {
        if (isExact) {
          setSearchResult([res])
        } else { 
          setSearchResult(res)
        }
      })
      .catch((_error) => {
        setSearchResult([])
      })
  }

  useEffect(() => {
    setIsLoading(false)
  }, [searchResult])

  return (
    <ToggleVertical
      toggleTitle="Collections"
      contentWrapClass={showResult ? 'overflow-visible' : ''}
      defaultShow={defaultShow}
      heightFit={true}
    >
      <div className={`relative ${showResult ? 'max-h-none' : 'max-h-[50px]'}`}>
        <Search onChange={handleSearch} placeholder="Search" />
        <SearchResult
          results={searchResult}
          ref={searchResultRef}
          loading={isLoading}
          isLink={false}
          className={clsx(
            'bg-slate-50 max-h-[145px] overflow-y-scroll transition duration-200 ease-in',
            showResult
              ? 'visible translate-y-0 opacity-100'
              : 'invisible -translate-y-2 opacity-0'
          )}
          inlineMode={true}
          onClickItem={(item: Collection) => {
            setSelectedCollection(item)
            changeFilterParam('collectionIds', item.id, true)
          }}
        />
        {selectedCollection && (
          <button
            onClick={() => {
              setSelectedCollection(null)
              changeFilterParam('collectionIds', null, false)
            }}
            className="absolute right-4 top-4 text-sm text-gray-500 hover:text-gray-700"
          >
            Clear
          </button>
        )}
      </div>
    </ToggleVertical>
  )
}

export default ToggleVerticalSearchCollection
