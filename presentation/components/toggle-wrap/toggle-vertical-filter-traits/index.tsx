import { useState, useEffect, useCallback, memo, useRef } from 'react'
import ToggleVertical from 'presentation/components/toggle-wrap/toggle-vertical'
import { FilterParams } from 'types/type'
import SvgIcon from 'presentation/components/svg-icon'
import clsx from 'clsx'

type TraitValue = {
  value: string;
  count: number;
  checked?: boolean;
}

type Trait = {
  name: string;
  values: TraitValue[];
  collapsed: boolean;
  searchQuery: string;
  filteredValues: TraitValue[];
}

type TraitValueWithName = {
  name: string;
  value: string;
}

type Props = {
  defaultShow?: boolean;
  traits?: Omit<Trait, 'collapsed' | 'searchQuery' | 'filteredValues'>[];
  onToggleTrait?: (traitValues: TraitValueWithName[]) => void;
}

const ToggleVerticalFilterTraits = ({
  defaultShow = true,
  traits = [],
  onToggleTrait = () => {},
}: Props) => {
  const [traitsList, setTraitsList] = useState<Trait[]>([])
  
  // Add ref to track previous selected values to prevent loops
  const prevSelectedValuesRef = useRef<string>('')
  
  // Initialize traits when the component mounts or when traits changes
  useEffect(() => {
    // Safety check
    if (!traits || !Array.isArray(traits)) {
      console.error('TraitsComponent: Invalid traits data', traits)
      return
    }
    
    if (traits && traits.length > 0) {
      // Initialize traits with collapsed state and empty search query
      const initializedTraits = traits.map((trait) => ({
        ...trait,
        collapsed: true,
        searchQuery: '',
        filteredValues: trait.values || [],
        values: (trait.values || []).map((val) => ({
          ...val,
          checked: false,
        })),
      }))
      
      setTraitsList(initializedTraits)
    }
  }, [traits]) // Just use the traits directly, React will handle the deep comparison

  // Add a useEffect to check when traitsList changes
  useEffect(() => {
    // Find all checked traits and notify parent
    const checkedTraits = traitsList.filter((trait) => 
      trait.values.some((value) => value.checked)
    )
    
    if (checkedTraits.length > 0) {
      
      // Convert to a flat array of trait values with trait names
      const flatTraitValues = checkedTraits.flatMap((trait) => 
        trait.values
          .filter((value :any) => value.checked)
          .map((value :any) => ({
            name: trait.name,
            value: value.value,
          }))
      )
            
      // Check if values have changed to prevent loops
      const valuesString = JSON.stringify(flatTraitValues)
      if (valuesString !== prevSelectedValuesRef.current) {
        // Store current values for next comparison
        prevSelectedValuesRef.current = valuesString
        
        // Pass the flattened array to parent
        onToggleTrait(flatTraitValues)
      } else {
        console.log('Values have not changed, no need to notify')
      }
    } else if (prevSelectedValuesRef.current !== '') {
      // If we had values before but now empty, update that
      prevSelectedValuesRef.current = ''
      onToggleTrait([])
    }
  }, [traitsList, onToggleTrait])

  // Memoize handlers to prevent recreating functions on each render
  const toggleTraitCollapse = useCallback((traitIndex: number) => {
    setTraitsList((prevTraits) => {
      const updatedTraits = [...prevTraits]
      updatedTraits[traitIndex] = {
        ...updatedTraits[traitIndex],
        collapsed: !updatedTraits[traitIndex].collapsed,
      }
      return updatedTraits
    })
  }, [])

  const toggleTraitValue = (traitIndex: number, valueIndex: number) => {
    setTraitsList((prevTraits) => {
      const updatedTraits = [...prevTraits]
      const trait = { ...updatedTraits[traitIndex] }
      updatedTraits[traitIndex] = trait
      
      const filteredValueIndex = valueIndex
      const actualValueIndex = trait.values.findIndex(
        (value) => value.value === trait.filteredValues[filteredValueIndex].value
      )
      
      if (actualValueIndex !== -1) {
        const updatedValues = [...trait.values]
        trait.values = updatedValues
        
        const currentChecked = updatedValues[actualValueIndex].checked
        updatedValues[actualValueIndex] = {
          ...updatedValues[actualValueIndex],
          checked: !currentChecked,
        }
        
        // Also update in filtered values for UI consistency
        const updatedFilteredValues = [...trait.filteredValues]
        trait.filteredValues = updatedFilteredValues
        
        const filteredValueToUpdate = updatedFilteredValues[filteredValueIndex]
        if (filteredValueToUpdate) {
          updatedFilteredValues[filteredValueIndex] = {
            ...filteredValueToUpdate,
            checked: !currentChecked,
          }
        }
        
        // Log the toggle action
        const traitValue = trait.values[actualValueIndex]
        console.log(
          'Toggling trait:',
          !currentChecked ? 'ADD' : 'REMOVE',
          `${trait.name}: ${traitValue.value}`
        )
      }
      
      return updatedTraits
    })
  }

  const handleTraitSearch = useCallback((traitIndex: number, query: string) => {
    setTraitsList((prevTraits) => {
      const updatedTraits = [...prevTraits]
      const trait = {...updatedTraits[traitIndex]}
      updatedTraits[traitIndex] = trait
      
      trait.searchQuery = query
      
      // Filter values based on search query
      if (query.trim() === '') {
        trait.filteredValues = trait.values
      } else {
        trait.filteredValues = trait.values.filter((value) => 
          value.value.toLowerCase().includes(query.toLowerCase())
        )
      }
      
      return updatedTraits
    })
  }, [])

  const clearTraitSearch = useCallback((traitIndex: number) => {
    setTraitsList((prevTraits) => {
      const updatedTraits = [...prevTraits]
      const trait = {...updatedTraits[traitIndex]}
      updatedTraits[traitIndex] = trait
      
      trait.searchQuery = ''
      trait.filteredValues = trait.values
      
      return updatedTraits
    })
  }, [])

  // This is a pure function, doesn't need to be recreated on every render
  const getTraitValuesCount = (trait: Trait) => {
    return trait.values.length
  }

  // Render without any state updates
  return (
    <ToggleVertical toggleTitle="Traits" heightFit={true} defaultShow={defaultShow}>
      <div className="">
        {traitsList.length > 0 ? (
          traitsList.map((trait, traitIndex) => (
            <div key={traitIndex} className={clsx(
              'mb-3 overflow-hidden',
              !trait.collapsed ? 'border border-blue-600 rounded-xl' : 'bg-gray-100 dark:bg-blue-1 rounded-3xl'
            )}>
              {/* Trait header */}
              <div 
                className={clsx(
                  'flex justify-between items-center px-5 py-4 font-semibold cursor-pointer',
                  trait.collapsed ? 'bg-gray-100 dark:bg-blue-1' : 'bg-white dark:bg-blue-7'
                )}
                onClick={() => toggleTraitCollapse(traitIndex)}
              >
                <div className="flex justify-between items-center w-full">
                  <span>{trait.name}</span>
                  <div className="flex items-center">
                    <div className="mr-2 text-gray-500">{getTraitValuesCount(trait)}</div>
                    <div className="flex items-center">
                      <SvgIcon
                        name="arrowDown2"
                        className={clsx(
                          'w-4 h-4 stroke-none fill-gray-500 transition-transform ml-1 mt-2',
                          trait.collapsed ? '' : 'transform rotate-180 mt-[-12px]'
                        )}
                      />
                    </div>
                 
                  </div>
                </div>
              </div>
              
              {/* Trait content (only shown when expanded) */}
              {!trait.collapsed && (
                <div className="bg-white dark:bg-blue-7">
                  {/* Trait-specific search box */}
                  <div className="px-4 py-3">
                    <div className="relative">
                      <input
                        type="text"
                        placeholder="Search"
                        className="w-full py-2.5 px-10 rounded-full bg-gray-100 dark:bg-blue-1 text-sm outline-none"
                        value={trait.searchQuery}
                        onChange={(e) => handleTraitSearch(traitIndex, e.target.value)}
                      />
                      <span className="absolute left-3.5 top-1/2 transform -translate-y-1/2">
                        <SvgIcon
                          name="search"
                          className="w-4 h-4 stroke-none fill-gray-500"
                          viewBox="0 0 20 20"
                        />
                      </span>
                      {trait.searchQuery && (
                        <button 
                          className="absolute right-3.5 top-1/2 transform -translate-y-1/2"
                          onClick={() => clearTraitSearch(traitIndex)}
                        >
                          <SvgIcon
                            name="xSquare"
                            className="w-4 h-4 stroke-none fill-gray-500"
                          />
                        </button>
                      )}
                    </div>
                  </div>
                  
                  {/* Trait values with checkboxes */}
                  <div className="px-4 pb-4">
                    {trait.filteredValues.length > 0 ? (
                      trait.filteredValues.map((value, valueIndex) => (
                        <div 
                          key={valueIndex}
                          className="flex justify-between items-center py-3 cursor-pointer border-b border-gray-100 last:border-0"
                          onClick={() => toggleTraitValue(traitIndex, valueIndex)}
                        >
                          <div className="flex items-center">
                            <div className={clsx(
                              'w-5 h-5 border rounded flex items-center justify-center mr-3',
                              value.checked 
                                ? 'bg-primary-2 border-primary-2' 
                                : 'border-gray-300'
                            )}>
                              {value.checked && (
                                <SvgIcon
                                  name="check"
                                  className="w-3.5 h-3.5 stroke-white fill-none"
                                />
                              )}
                            </div>
                            <span className="font-medium">{value.value}</span>
                          </div>
                          <span className="text-gray-500">{value.count.toLocaleString()}</span>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-3 text-gray-500">
                        No matching traits found
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          ))
        ) : (
          <div className="text-center py-4 text-gray-500">
            Loading traits...
          </div>
        )}
      </div>
    </ToggleVertical>
  )
}

export default ToggleVerticalFilterTraits