import ToggleVertical from 'presentation/components/toggle-wrap/toggle-vertical'
import Switch from 'presentation/components/switch'
import { FilterParams } from 'types/type'
import { useEffect } from 'react'

type Props = {
  defaultShow?: boolean,
  isActiveAll?: boolean,
  tokens: any[],
  title?: string,
  heightFit?: boolean,
  changeFilterParam?: (param: keyof FilterParams, value: any, activeState?: boolean) => void,
}

const ToggleVerticalFilterTokens = ({
  defaultShow = true,
  isActiveAll = false,
  tokens = [],
  title = 'On Sale In',
  heightFit = false,
  changeFilterParam = () => {},
}: Props) => {
  
  // Add useEffect to trigger changeFilterParam when isActiveAll changes
  useEffect(() => {
    tokens.forEach((token) => {
      changeFilterParam('paymentTokens', token, isActiveAll)
    })
  }, [isActiveAll, tokens])

  return (
    <ToggleVertical toggleTitle={title} defaultShow={defaultShow} heightFit={heightFit} contentWrapClass="rounded-b-lg">
      <ul className="fist-mt-none">
        {tokens.map((token, index) => (
          <li key={index} className="mt-5 font-medium flex items-center justify-between">
            {token.name}
            <Switch defaultActive={isActiveAll} onClick={(activeState) => changeFilterParam('paymentTokens', token, activeState)}/>
          </li>
        ))}
      </ul>
    </ToggleVertical>
  )
}

export default ToggleVerticalFilterTokens
