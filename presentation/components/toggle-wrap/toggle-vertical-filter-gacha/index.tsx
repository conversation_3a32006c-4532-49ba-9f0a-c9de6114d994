import ToggleVertical from 'presentation/components/toggle-wrap/toggle-vertical'
import Switch from 'presentation/components/switch'
import ValueObject from 'presentation/controllers/abstract/value_object'
import { FilterParams } from 'types/type'

type Props = {
  defaultShow?: boolean,
  filterGacha: ValueObject[],
  changeFilterParam?: (param: keyof FilterParams, value: any, activeState?: boolean) => void,
}

const ToggleVerticalFilterMarketplaces = ({
  defaultShow = true,
  filterGacha = [],
  changeFilterParam = () => {},
}: Props) => {

  return (
    <ToggleVertical toggleTitle="Gacha" defaultShow={defaultShow}>
      <ul className="fist-mt-none">
        {filterGacha.map((item, index) => (
          <li key={index} className="mt-5 font-medium flex items-center justify-between">
            {item.label}
            {/* <Switch onClick={(activeState) => changeFilterParam('gacha', item.name, activeState)}/> */}
          </li>
        ))}
      </ul>
    </ToggleVertical>
  )
}

export default ToggleVerticalFilterMarketplaces
