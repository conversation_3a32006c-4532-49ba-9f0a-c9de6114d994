import { useEffect } from 'react'
import ToggleVertical from 'presentation/components/toggle-wrap/toggle-vertical'
import Image from 'next/image'
import Switch from 'presentation/components/switch'
import { FilterParams } from 'types/type'
import Chain from 'domain/value_objects/chain'

type Props = {
  defaultShow?: boolean,
  isActiveAll?: boolean,
  chains: Chain[],
  changeFilterParam?: (param: keyof FilterParams, value: any, activeState?: boolean) => void,
}

const ToggleVerticalFilterChains = ({
  defaultShow = true,
  isActiveAll = false,
  chains = [],
  changeFilterParam = () => { },
}: Props) => {


  return (
    <ToggleVertical toggleTitle="Chains" defaultShow={defaultShow}>
      <ul className="fist-mt-none">
        {chains.map((chain, index) => (
          <li key={index} className="mt-5 font-medium flex items-center justify-between">
            <p className="flex items-center">
              <span className="mr-3">{chain.getLabel()}</span>
              <Image src={chain.getIconPath() || ''} width={25} height={25} alt={chain.getName()} />
            </p>
            <Switch defaultActive={isActiveAll} onClick={(activeState) => {
              changeFilterParam('chains', chain, activeState)
            }} />
          </li>
        ))}
      </ul>
    </ToggleVertical>
  )
}

export default ToggleVerticalFilterChains
