import { FC, useEffect } from 'react'
import ToggleVertical from 'presentation/components/toggle-wrap/toggle-vertical'
import RadioTag from 'presentation/components/radio-tag'
import { FilterParams } from 'types/type'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'

interface Props {
  defaultShow?: boolean
  changeFilterParam?: (param: keyof FilterParams, value: any, activeState?: boolean) => void,
}

const ToggleVerticalMyNFTs: FC<Props> = ({
  defaultShow = false,
  changeFilterParam = () => {},
}) => {
  const { me, init } = AuthStateProvider()


  useEffect(() => {
    // Initialize auth state if me is undefined
    if (me === undefined) {
      init()
    }
  }, [me, init])

  const handleChange = (e: any) => {
    const value = e.target.value
    if (value === 'me' && !me?.id) {
      // If user selects "Me" but me.id is not available, don't update the filter
      console.warn('User ID not available')
      return
    }
    changeFilterParam('ownerId', value === 'all' ? '' : me?.id)
  }

  return (
    <>
      {me?.id && <ToggleVertical toggleTitle="Owner" defaultShow={defaultShow}>
        <ul className="fist-mt-none space-y-2">
          <li>
            <RadioTag
              name="owner"
              value="all"
              label="All"
              defaultChecked={true}
              onChange={handleChange}
            />
          </li>
          <li>
            <RadioTag
              name="owner"
              value="me"
              label="Me"
              onChange={handleChange}
            />
          </li>
        </ul>
      </ToggleVertical>}
    </>

  )
}

export default ToggleVerticalMyNFTs
