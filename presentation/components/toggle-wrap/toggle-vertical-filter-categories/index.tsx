import Category from 'domain/models/category'
import Image from 'next/image'
import Switch from 'presentation/components/switch'
import ToggleVertical from 'presentation/components/toggle-wrap/toggle-vertical'
import { FilterParams } from 'types/type'

type Props = {
  defaultShow?: boolean,
  categories: Category[],
  changeFilterParam?: (param: keyof FilterParams, value: any, activeState?: boolean) => void,
}

const mapIcons: any = {
  all: '/asset/icons/app.png',
  limit: '/asset/icons/handLimit.png',
  digitalArt: '/asset/icons/digitalArt.png',
  music: '/asset/icons/music.png',
  gaming: '/asset/icons/gaming.png',
  virtualWorlds: '/asset/icons/virtualWorld.png',
  tradingCards: '/asset/icons/tradingCard.png',
  photo: '/asset/icons/photo.png',
  utility: '/asset/icons/utility.png',
  sports: '/asset/icons/sport.png',
  domainNames: '/asset/icons/domainNames.png',
  collect: '/asset/icons/collect.png',
  ai: '/asset/icons/ai.png',
  defi: '/asset/icons/defi.png',
}

const ToggleVerticalCategories = ({
  defaultShow = true,
  categories = [],
  changeFilterParam = () => { },
}: Props) => {

  return (
    <ToggleVertical toggleTitle="Categories" heightFit={true} defaultShow={defaultShow}>
      <ul className="fist-mt-none">
        {categories.map((category, index) => (
          <li key={index} className="mt-5 font-medium flex items-center justify-between">
            <p className="flex items-center">
              <span className="mr-3">{category.name}</span>
              <Image src={mapIcons[category.slug]} width={25} height={25} alt="" />
            </p>
            <Switch onClick={(activeState) => changeFilterParam('categoryIds', category.id, activeState)} />
          </li>
        ))}
      </ul>
    </ToggleVertical>
  )
}

export default ToggleVerticalCategories
