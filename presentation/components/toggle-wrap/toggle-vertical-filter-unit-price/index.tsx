import Input from 'presentation/components/input'
import RadioTag from 'presentation/components/radio-tag'
import ToggleVertical from 'presentation/components/toggle-wrap/toggle-vertical'
import { FilterParams } from 'types/type'
import { useEffect, useState } from 'react'
import Button from 'presentation/components/button'

type TokenOption = {
  label: string;
  value: string;
  icon?: string;
}

type Props = {
  defaultShow?: boolean;
  minPrice: number|string;
  maxPrice: number|string;
  tokenList: TokenOption[];
  changeFilterParam?: (param: keyof FilterParams, value: any, activeState?: boolean) => void;
}

const ToggleVerticalFilterUnitPrice = ({
  defaultShow = true,
  minPrice = '',
  maxPrice = '',
  tokenList = [],
  changeFilterParam = () => {},
}: Props) => {
  // Local state
  const [selectedToken, setSelectedToken] = useState<string | null>(null)
  const [_minPrice, setMinPrice] = useState(minPrice)
  const [_maxPrice, setMaxPrice] = useState(maxPrice)

  // Initialize from current filters if available
  useEffect(() => {
  
    // Initialize price values
    if (minPrice) {
      setMinPrice(minPrice)
    }
    if (maxPrice) {
      setMaxPrice(maxPrice)
    }
  }, [minPrice, maxPrice])

  // Handle token selection
  const handleTokenChange = (value: string) => {
    setSelectedToken(value)
    console.log('Selected token:', value)
  }

  // Reset filter values
  const handleReset = () => {
    setSelectedToken(null)
    setMinPrice('')
    setMaxPrice('')
    changeFilterParam('unitPriceFrom', '')
    changeFilterParam('unitPriceTo', '')
    changeFilterParam('currencyId', '')
  }

  // Apply filters
  const handleApply = () => {
    if (selectedToken) {
      changeFilterParam('currencyId', selectedToken)
    }
    
    // Update price filters if values exist
    if (_minPrice || _maxPrice) {
      changeFilterParam('unitPriceFrom', _minPrice)
      changeFilterParam('unitPriceTo', _maxPrice)
      console.log('Applied price filter - min:', _minPrice, 'max:', _maxPrice)
    }
  }

  return (
    <ToggleVertical toggleTitle="Unit Price" heightFit={true} defaultShow={defaultShow}>
      <div className="flex justify-end">
        <Button
          style={{
            background: 'transparent',
          }}
          variant="light"
          className="mb-2 text-center !p-0 mr-4"
          type="button"
          size="xs"
          onClick={handleReset}
        > 
          Reset 
        </Button>
      </div>
      
      {/* Token Selection */}
      <div className="mb-4">
        <h4 className="font-medium mb-2">Select Token:</h4>
        <ul className="flex flex-wrap gap-2">
          {tokenList.map((token, index) => (
            <li key={index}>
              <RadioTag 
                className="radio-height-20"
                label={token.label} 
                name={token.label} 
                value={token.value}
                icon={token.icon}
                iconSize={20}
                checked={selectedToken?.toString() === token.value?.toString()}
                onChange={(e) => handleTokenChange(e.target.value)}
              />
            </li>
          ))}
        </ul>
      </div>
      
      {/* Price Range */}
      <div>
        <h4 className="font-medium mb-2">Price Range:</h4>
        <div className="flex items-center mt-4 md:justify-around justify-center">
          <Input 
            type="number" 
            width="w-[104px]" 
            className="no-arrow h-[50px]" 
            placeholder="Min"
            value={_minPrice} 
            onChange={(e) => setMinPrice(e.target.value)} 
          />
          <span className="font-medium align-middle ml-3 mr-10p">To</span>
          <Input 
            type="number" 
            width="w-[104px]" 
            className="no-arrow h-[50px]" 
            placeholder="Max"
            value={_maxPrice} 
            onChange={(e) => setMaxPrice(e.target.value)}
          />
        </div>
      </div>
      
      {/* Apply Button */}
      <div className="btn-apply">
        <Button
          variant="dark"
          className="mt-4 w-full text-center"
          disable={(!selectedToken && !_minPrice && !_maxPrice) || (!!_minPrice && !!_maxPrice && Number(_minPrice) > Number(_maxPrice))}
          type="button"
          onClick={handleApply}
        > 
          Apply
        </Button>
      </div>
    </ToggleVertical>
  )
}

export default ToggleVerticalFilterUnitPrice 