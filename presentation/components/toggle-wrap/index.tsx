import clsx from 'clsx'
import { ReactNode } from 'react'

type Props = {
  title?: string,
  contentClass?: string,
  className?: string,
  children?: ReactNode,
  backgroundTitleClassName?: string,
}

const ToggleWrap = ({ 
  title, 
  className, 
  contentClass, 
  children,
  backgroundTitleClassName='bg-gradient-to-b from-gradient-sky to-gradient-ocean',
}: Props) => {
  return (
    <div className={clsx('rounded-10 overflow-hidden', className)}>
      {title && (
        <p className={clsx('px-5 pt-2 pb-[11px] text-xl font-medium rounded-t-10 text-white', backgroundTitleClassName)}>
          {title}
        </p>
      )}
      <div className={clsx('border border-gray-3 rounded-b-10', contentClass)}>
        {children}
      </div>
    </div>
  )
}

export default ToggleWrap
