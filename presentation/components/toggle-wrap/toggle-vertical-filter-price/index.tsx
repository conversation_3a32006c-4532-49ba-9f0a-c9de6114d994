import Input from 'presentation/components/input'
import RadioTag from 'presentation/components/radio-tag'
import ToggleVertical from 'presentation/components/toggle-wrap/toggle-vertical'
import { FilterParams } from 'types/type'
import { useEffect, useState } from 'react'
import { useAccount } from 'wagmi'
import { chains } from 'presentation/constant/chains'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'
import Button from 'presentation/components/button'
import PaymentTokensUseCase from 'presentation/use_cases/payment_tokens_use_case'
import { NetworkStateProvider } from 'presentation/providers/network_state_provider'
const paymentTokensUseCase = new PaymentTokensUseCase()
type Props = {
  defaultShow?: boolean,
  minPrice: number|string,
  maxPrice: number|string,
  changeFilterParam?: (param: keyof FilterParams, value: any, activeState?: boolean) => void,
}

const ToggleVerticalFilterPrice = ({
  defaultShow = true,
  minPrice = '',
  maxPrice = '',
  changeFilterParam = () => {},
}: Props) => {
  /**
   * STATES
   */
  const [tokenUnit, setTokenUnit] = useState('eth')
  const [tokenUnitValue, setTokenUnitValue] = useState<number | null>(null)
  const [_priceTokenUnit, setPriceTokenUnit] = useState<number | null>(null)
  const [_minPrice, setMinPrice] = useState(minPrice)
  const [_maxPrice, setMaxPrice] = useState(maxPrice)
  /**
   * HOOKS
   */
 
  const authStateProvider = AuthStateProvider((state) => ({
    me: state.me,
  }))
  // Add network state provider
  const networkStateProvider = NetworkStateProvider((state) => ({
    network: state.network,
    setNetwork: state.setNetwork,
  }))
  /**
   * FUNCTIONS
   */
  const getTokenUnitFromWC = async () => {
    const chainSelected = chains.find((chain: any) => chain.id === networkStateProvider?.network?.chainId)
    console.log('🚀 ~ getTokenUnitFromWC ~ chainSelected:', chainSelected)
    if (chainSelected) {
      const paymentTokens = await paymentTokensUseCase.getPaymentTokens(networkStateProvider?.network?.chainId)
      console.log('🚀 ~ getTokenUnitFromWC ~ chainSelected:', chainSelected)
      const symbol = chainSelected.nativeCurrency.symbol
      setTokenUnit(symbol)
      const paymentTokenId: number | null = paymentTokens.find((paymentToken: any) => paymentToken.symbol === symbol)?.id || null
      setPriceTokenUnit(paymentTokenId)
      setTokenUnitValue(paymentTokenId)
      // changeFilterParam('priceTokenUnit', chainSelected.nativeCurrency.symbol)
    } else { 
      setTokenUnit('')
      setPriceTokenUnit(null)
      setTokenUnitValue(null)
      // changeFilterParam('priceTokenUnit', '')
    }
  }

  /**
   * USE EFFECTS
   */
  useEffect(() => {
    getTokenUnitFromWC()
  }, [networkStateProvider?.network?.chainId])
  return (
    <ToggleVertical toggleTitle="Price" defaultShow={defaultShow}>
      <div className="flex justify-end">
        <Button
          style={{
            background: 'transparent',
          }}
          variant="light"
          className="mb-2 text-center !p-0 mr-4"
          type="button"
          size="xs"
          onClick={() => {
            changeFilterParam('priceTokenUnit', '')
            changeFilterParam('minPrice', '')
            changeFilterParam('maxPrice', '')
          }}
        > 
            Reset 
        </Button>
      </div>
      <ul className="filter-by-price-wrapper xl:pl-10p xl:pr-[5px]">
        <li className="flex align-middle md:justify-between justify-center md:gap-x-0 gap-x-4">
          {tokenUnit && <RadioTag label={tokenUnit} name="coin" value={tokenUnitValue} icon={`${tokenUnit?.toLocaleLowerCase() ||''}.png`} iconSize={20}
            checked={_priceTokenUnit === tokenUnitValue}
            onChange={(e) => { 
              // Convert string value to number or null
              const value = e.target.value !== '' ? Number(e.target.value) : null
              console.log('🚀 ~  e.target.value:',  e.target.value, e)
              setPriceTokenUnit(value)
              console.log('Setting token unit to:', value)
            }}
          />}
          <RadioTag label="USD" name="coin" value={0} icon="dollar.png" iconSize={20}
            checked={_priceTokenUnit === 0}
            onChange={(e) => { 
              // Always set to 0 for USD
              setPriceTokenUnit(0)
              console.log('Setting token unit to USD:', 0)
            }}
          />
        </li>
        <li className="flex items-center mt-4 md:justify-around justify-center">
          <Input type="number" width="w-[104px]" className="no-arrow h-[50px]" placeholder="Min"
            value={minPrice} onChange={(e) => { setMinPrice(e.target.value) }}
          />
          <span className="font-medium align-middle ml-3 mr-10p">To</span>
          <Input type="number" width="w-[104px]" className="no-arrow h-[50px]" placeholder="Max"
            value={maxPrice} onChange={(e) => {setMaxPrice(e.target.value)}}
          />
        </li>
      </ul>
      <div className="btn-apply">
          
        <Button
          variant="dark"
          className="mt-2 w-full text-center"
          disable={(!_minPrice && !_maxPrice) || (!!_minPrice && !!_maxPrice && Number(_minPrice) > Number(_maxPrice))}
          type="button"
          onClick={() => {
            if (_minPrice || _maxPrice) {
              console.log('🚀 ~ _priceTokenUnit:', _priceTokenUnit)
              changeFilterParam('priceTokenUnit', _priceTokenUnit)
              changeFilterParam('minPrice', _minPrice)
              changeFilterParam('maxPrice', _maxPrice)
            }
          }}
        > 
            Apply
        </Button>
      </div>
    </ToggleVertical>

  )
}

export default ToggleVerticalFilterPrice
