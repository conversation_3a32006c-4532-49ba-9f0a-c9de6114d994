import ToggleVertical from 'presentation/components/toggle-wrap/toggle-vertical'
import Switch from 'presentation/components/switch'
import { FilterParams } from 'types/type'

//Todo remove mock data
import { filterRarityScore } from 'presentation/controllers/mockData/common_mock_data'
//End todo remove mock data

type Props = {
  defaultShow?: boolean,
  changeFilterParam?: (param: keyof FilterParams, value: any, activeState?: boolean) => void,
}

const ToggleVerticalFilterRarityScore = ({
  defaultShow = true,
  changeFilterParam = () => {},
}: Props) => {

  return (
    <ToggleVertical toggleTitle="Rarity Score" defaultShow={defaultShow}>
      <ul className="fist-mt-none">
        {filterRarityScore.map((status, index) => (
          <li key={index} className="mt-5 font-medium flex items-center justify-between">
            <p className="flex items-center">
              <span className={`dot inline-block mr-10p dot-${status.color}`}></span>
              <span>{status.name}</span>
            </p>
            <Switch onClick={(activeState) => changeFilterParam('rarityScore', status.value, activeState)}/>
          </li>
        ))}
      </ul>
    </ToggleVertical>
  )
}

export default ToggleVerticalFilterRarityScore
