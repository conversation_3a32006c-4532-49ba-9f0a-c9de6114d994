import SvgIcon from 'presentation/components/svg-icon'
import { useClickOutside } from 'lib/hook/customHook'
import clsx from 'clsx'
import { useEffect, useRef, useState } from 'react'

type Props = {
  options?: Array<any>[],
  className?: string,
  defaultOption?: any,
  onChange?: any,
}

const SelectTab = ({
  className,
  options = [],
  defaultOption = {},
  onChange = () => { },
}: Props) => {

  const [expand, setExpand] = useState(false)
  const selectRef = useRef(null)
  const isClickOutside = useClickOutside(selectRef)
  const [selected, setSelected] = useState<any>({})

  useEffect(() => {
    if (isClickOutside) {
      setExpand(false)
    }
  }, [isClickOutside])

  useEffect(() => {
    if (defaultOption) {
      setSelected(defaultOption)
    } else {
      if (options.length > 0) {
        setSelected(options[0])
      }
    }
  }, [options])

  return (
    <>
      {options.length > 0 && (
        <div ref={selectRef} className={clsx('rounded-25 bg-gray-4', className)}>
          <a className="cursor-pointer" onClick={() => setExpand(!expand)}>
            <div className="px-4 py-3 flex items-center justify-between">
              <p className="font-medium">
                <SvgIcon name={selected.icon} className="w-4 h-4 stroke-none fill-dark-primary" />
                <span className="align-middle ml-2">{selected.name}</span>
              </p>
              <SvgIcon name="chevronDown" className="w-5 h-5 stroke-none fill-primary-2" />
            </div>
          </a>

          <ul className={expand ? 'block' : 'hidden'}>
            {options.map((option: any, index: number) => (
              <li
                key={index}
                className={`px-4 py-3 flex items-center justify-between cursor-pointer
                  ${option.value === selected.value && 'hidden'}`}
                onClick={() => {
                  onChange(option)
                  setSelected(option)
                }}
              >
                <p className="font-medium">
                  <SvgIcon name={option.icon} className="w-4 h-4 stroke-none fill-dark-primary" />
                  <span className="align-middle ml-2">{option.name}</span>
                </p>
              </li>
            ))}
          </ul>
        </div>
      )}
    </>
  )
}

export default SelectTab
