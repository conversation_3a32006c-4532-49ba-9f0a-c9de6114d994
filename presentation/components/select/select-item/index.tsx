import clsx from 'clsx'
import { ReactNode } from 'react'

type Props = {
  value: any,
  className?: string,
  children?: ReactNode,
  onSelect: (e: any) => void,
}

const SelectItem = ({ value, className, children, onSelect}: Props) => {

  return (
    <li
      onClick={() => onSelect(value)}
      className={clsx('px-3 py-1 cursor-pointer hover:bg-primary-1/5 transition', className)}>
      {children}
    </li>
  )
}

export default SelectItem
