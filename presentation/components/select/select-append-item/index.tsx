import { useEffect, useState, useRef, MutableRefObject } from 'react'
import clsx from 'clsx'
import Image from 'next/image'
import { useClickOutside } from 'lib/hook/customHook'
import SvgIcon from 'presentation/components/svg-icon'
import Switch from 'presentation/components/switch'

type Option = {
  title: string;
  name: string;
  value: string;
};

type Props = {
  options: any;
  className?: string;
  customWidth?: boolean;
  selectClassName?: string;
  rounded?: boolean;
  defaultSelected?: any;
  showOptionIcon?: boolean;
  arrowIconClass?: string;
  placeholder?: string;
  isCollectionList?: boolean;
  isActivityFilter?: boolean;
  dropdownClassName?: string;
  onSelect?: (option: any) => void;
  onSelected?: (option: any) => void;
};

const SelectAppendItem = ({
  options,
  className = 'w-max',
  customWidth = false,
  selectClassName,
  rounded = false,
  defaultSelected = options[0],
  showOptionIcon = false,
  arrowIconClass = 'fill-dark-primary dark:fill-white stroke-none ',
  placeholder,
  isCollectionList = false,
  isActivityFilter = false,
  dropdownClassName,
  onSelect,
  onSelected,
}: Props) => {
  const [selected, setSelected] = useState<any>(
    placeholder ? { name: placeholder } : defaultSelected
  )
  const [active, setActive] = useState(false)
  const selectRef: MutableRefObject<null | any> = useRef(null)
  const optionWrapRef: MutableRefObject<null | any> = useRef(null)
  const isClickOutside = useClickOutside(selectRef)

  const handleSelect = (option: any) => {
    onSelect?.(option)
  }

  useEffect(() => {
    if (isClickOutside) {
      setActive(false)
    }
    onSelected?.(selected)
  }, [isClickOutside])

  useEffect(() => {
    if (!customWidth) {
      selectRef.current.style.width =
        optionWrapRef.current?.getBoundingClientRect().width + 'px'
    }
  }, [])

  const roundedClass = 'px-7 py-3 rounded-3xl bg-gray-4 dark:bg-blue-8'

  return (
    <div
      ref={selectRef}
      className={clsx('relative', className)}
      onClick={() => (isActivityFilter ? {} : setActive(!active))}
    >
      {isActivityFilter && (
        <>
          <div
            className={clsx(
              'flex items-center cursor-pointer justify-start px-7 py-3.25 bg-gray-4 dark:bg-blue-8 md:min-w-[293px]',
              active ? 'rounded-t-3xl' : 'rounded-3xl',
              selectClassName
            )}
            onClick={() => setActive(!active)}
          >
            <SvgIcon
              name="chevronDownSelect"
              viewBox="0 0 12.962 7.411"
              className={clsx(
                'w-[13px] h-[8px] transition duration-300 shrink-0',
                active ? 'rotate-180' : '',
                arrowIconClass
              )}
            />
            <div className="mr-[22px] flex ml-4">
              <div className="inline-flex justify-start items-center space-x-1.5">
                <span className="text-dark-blue dark:text-gray-3 text-stroke-thin">
                  {placeholder}
                </span>
              </div>
            </div>
          </div>
          <div
            className={clsx(
              `absolute md:-right-[3px] right-0 w-full md:min-w-[293px] z-10 bg-gray-4 dark:bg-blue-8 rounded-b-3xl overflow-hidden transition px-3 py-2
            ${active
          ? 'opacity-100 translate-y-0 visible'
          : 'opacity-0 -translate-y-2 invisible'
        }`,
              dropdownClassName
            )}
          >
            <ul>
              {options.map((option: any, index: number) => (
                <li
                  key={index}
                  value={option.value}
                  className="px-4 py-1 cursor-pointer inline-flex justify-between items-center transition hover:bg-gray-4 dark:hover:bg-black w-full"
                >
                  <span className="text-dark-primary dark:text-white font-medium">
                    {option.name}
                  </span>
                  <Switch defaultActive={option.value} onClick={() => { }} />
                </li>
              ))}
            </ul>
          </div>
        </>
      )}
      {isCollectionList && (
        <>
          <div
            className={clsx(
              'flex items-center cursor-pointer justify-between px-7 py-2.5 bg-gray-4 dark:bg-blue-8',
              active ? 'rounded-t-3xl' : 'rounded-3xl',
              selectClassName
            )}
          >
            <div className="mr-[22px] flex">
              <div className="inline-flex justify-start items-center space-x-1.5">
                {selected.logoImage?.url && (
                  <Image
                    src={
                      selected.logoImage?.url
                        ? selected.logoImage?.url
                        : options[0].logoImage?.url
                    }
                    alt={selected.name ? selected.name : options[0].name}
                    className="rounded-full"
                    width={30}
                    height={30}
                  />
                )}
                <span
                  className={clsx(
                    selected?.name === placeholder
                      ? 'text-dark-blue dark:text-gray-3'
                      : 'text-dark-primary dark:text-white',
                    'text-stroke-thin',
                    'truncate'
                  )}
                >
                  {selected.name ? selected.name : options[0].name}
                </span>
              </div>
              {selected.icon && showOptionIcon && (
                <div className="icon-25 relative ml-10p">
                  <Image
                    alt=""
                    src={selected.icon}
                    fill
                    style={{ objectFit: 'contain' }}
                  />
                </div>
              )}
            </div>
            <SvgIcon
              name="chevronDownSelect"
              viewBox="0 0 12.962 7.411"
              className={clsx(
                'w-[13px] h-[8px] transition duration-300 shrink-0',
                active ? 'rotate-180' : '',
                arrowIconClass
              )}
            />
          </div>
          <div
            ref={optionWrapRef}
            className={`absolute right-0 w-full z-10 bg-gray-4 dark:bg-blue-8 rounded-b-3xl overflow-hidden transition px-3 py-2
            ${active
          ? 'opacity-100 translate-y-0 visible'
          : 'opacity-0 -translate-y-2 invisible'
        }`}
          >
            <ul>
              {options.map((option: any, index: number) => (
                <li
                  key={index}
                  className="px-4 py-1 cursor-pointer inline-flex justify-between items-center transition hover:bg-gray-4 dark:hover:bg-black w-full"
                  onClick={() => handleSelect(option)}
                >
                  <div className="inline-flex justify-start items-center space-x-1.5">
                    {option.logoImage?.url && (
                      <Image
                        src={option.logoImage?.url}
                        alt={option.name}
                        className="rounded-full"
                        width={30}
                        height={30}
                      />
                    )}
                    <span className="text-dark-primary dark:text-white text-stroke-thin truncate">
                      {option.name}
                    </span>
                  </div>
                  {selected.name === option.name ? (
                    <SvgIcon
                      name="check"
                      className="w-5 h-5 stroke-none dark:fill-white fill-gray-3"
                    />
                  ) : (
                    <span className="w-5"></span>
                  )}
                </li>
              ))}
            </ul>
          </div>
        </>
      )}
      {!isCollectionList && !isActivityFilter && (
        <>
          <div
            className={clsx(
              'flex items-center cursor-pointer justify-between',
              rounded
                ? roundedClass
                : 'px-[17px] py-1.5 border-gray-3 rounded-[5px] border',
              selectClassName
            )}
          >
            <div className="mr-[22px] flex">
              <span
                className={clsx(
                  'text-ellipsis overflow-hidden inline-block align-middle shrink-0 font-medium',
                  selected?.name === placeholder
                    ? 'text-dark-blue dark:text-gray-3'
                    : 'text-dark-primary dark:text-white'
                )}
              >
                {selected.name
                  ? selected.name.substring(0, 35)
                  : options[0].name}
                {selected.name.length > 35 && '...'}
              </span>
              {selected.icon && showOptionIcon && (
                <div className="icon-25 relative ml-10p">
                  <Image
                    alt=""
                    src={selected.icon}
                    fill
                    style={{ objectFit: 'contain' }}
                  />
                </div>
              )}
            </div>
            <SvgIcon
              name="chevronDownSelect"
              viewBox="0 0 12.962 7.411"
              className={clsx(
                'w-[13px] h-[8px] transition duration-300 shrink-0',
                active ? 'rotate-180' : '',
                arrowIconClass
              )}
            />
          </div>
          <div
            ref={optionWrapRef}
            className={`absolute right-0 min-w-max w-full z-10 bg-white dark:bg-blue-3 border rounded-lg overflow-hidden transition
            ${active
          ? 'opacity-100 translate-y-0 visible'
          : 'opacity-0 -translate-y-2 invisible'
        }`}
          >
            <ul>
              {options.map((option: any, index: number) => (
                <li
                  key={index}
                  value={option.value}
                  className="px-4 py-1 cursor-pointer flex justify-between items-center transition hover:bg-gray-4 dark:hover:bg-black"
                  onClick={() => handleSelect(option)}
                >
                  <p className="text-stroke-thin text-dark-primary dark:text-white">
                    {option.name}
                  </p>
                  {selected.value === option.value ? (
                    <SvgIcon
                      name="check"
                      className="w-5 h-5 stroke-none dark:fill-white fill-gray-3"
                    />
                  ) : (
                    <span className="w-5"></span>
                  )}
                </li>
              ))}
            </ul>
          </div>
        </>
      )}
    </div>
  )
}

export default SelectAppendItem
