import Select from 'presentation/components/select'
import ValueObject from 'presentation/controllers/abstract/value_object'
import { useState, useEffect } from 'react'

type Props = {
  periodRanges: ValueObject[],
  defaultSelected?: ValueObject,
  onSelect?: (option: any) => void,
  onSelected?: (option: any) => void,
  shortenTitle?: boolean,
}

const mapPeriodRange:any = {
  lastSevenDays: 7,
  lastFourteenDays: 14,
  lastThirtyDays: 30,
  lastSixtyDays: 60,
  lastNinetyDays: 90,
  allTime: 'All time',
}

const PeriodRangesSelect = ({
  periodRanges = [],
  defaultSelected,
  onSelect = () => {},
  onSelected = () => {},
  shortenTitle = false,
}: Props) => {
  const [ranges , setRanges] = useState([])
  const [defaultRange , setDefaultRange] = useState<any>(null)

  const formatPeriodRanges  = () => {
    const ranges:any = periodRanges.map((periodRange:any) => {
      return {
        id: periodRange.id,
        name: shortenTitle ? (periodRange.label).replace('last ', '') : periodRange.label,
        value: periodRange.name,
        subValue: mapPeriodRange[periodRange.name],
      }
    })
    setRanges(ranges)
  }

  useEffect(() => {
    formatPeriodRanges()
    if(defaultSelected) {
      setDefaultRange({
        id: defaultSelected.id,
        name: shortenTitle ? (defaultSelected.label).replace('last ', '') : defaultSelected.label,
        value: defaultSelected.name,
        subValue: mapPeriodRange[defaultSelected.name],
      })
    }
  }, [])

  return (
    <>
      {ranges.length > 0 && (
        <Select
          options={ranges}
          defaultSelected={defaultRange}
          className="h-fit"
          onSelect={(option) => onSelect(option)}
          onSelected={(option) => onSelected(option)}
        />
      )}
    </>
  )
}

export default PeriodRangesSelect
