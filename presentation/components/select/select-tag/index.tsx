import clsx from 'clsx'
import { useClickOutside } from 'lib/hook/customHook'
import Image from 'next/image'
import SvgIcon from 'presentation/components/svg-icon'
import { useEffect, useRef, useState } from 'react'

type Props = {
  options: Array<any>,
  className?: string,
  defaultSelected?: any,
  fixedOptions?: Array<any>,
  length?: number,
  isHideSelected?: boolean,
  onSelect: (item: any) => void,
}

const SelectTag = ({ options, className, defaultSelected, fixedOptions, length, isHideSelected, onSelect }: Props) => {
  const [active, setActive] = useState(false)
  const [selected, setSelected] = useState<any[]>(defaultSelected ? defaultSelected instanceof Array ? defaultSelected  :[defaultSelected] : [])
  const selectRef = useRef(null)
  const isClickOutside = useClickOutside(selectRef)

  const handleSelect = (option: any) => {
    let dupFixedOptions = []
    if (fixedOptions) {
      dupFixedOptions = fixedOptions.filter((fixedOption) => fixedOption.value === option.value)
    }
   
    let duplicateArr = selected.filter((selectedItem) => selectedItem.value === option.value)
    if (duplicateArr.length === 0 && dupFixedOptions.length === 0) {
      if ((length && selected.length < length) || !length) {
        setSelected([...selected, option])
      }
    }
  }

  const removeSelected = (item: object) => {
    let filteredArr = selected.filter((selectedItem) => JSON.stringify(selectedItem) !== JSON.stringify(item))
    setSelected(filteredArr)
  }

  useEffect(() => {
    onSelect(selected)
    if (isClickOutside) {
      setActive(false)
    }
  }, [selected, isClickOutside])

  return (
    <div ref={selectRef} className={clsx(`mt-4 relative w-fit h-fit flex items-center ${fixedOptions ? 'sm:flex-nowrap flex-wrap' : ''}`, className)}>
      <div className="relative py-3">
        {!isHideSelected && <SvgIcon name="plusCircle" className="w-5 h-5 mr-8 stroke-none fill-primary-2 cursor-pointer"
          onClick={() => setActive((active) => !active)}
        />}
        <div className={`absolute top-12 left-0 z-10 bg-white dark:bg-blue-3 border rounded-lg transition overflow-hidden
          ${active ? 'opacity-100 translate-y-0 visible' : 'opacity-0 -translate-y-2 invisible'}`}>
          <ul className="min-w-max max-h-[250px] overflow-y-auto">
            {options.map((option: any, index: number) => (
              <li key={'option' + index} value={option.value}
                className="px-4 py-2 cursor-pointer flex justify-between items-center transition hover:bg-gray-4 dark:hover:text-dark-primary"
                onClick={() => handleSelect(option)}
              >
                <p className="font-medium mr-3">{option.name}</p>
                {option.icon && (
                  <Image alt="" src={option.icon} width={25} height={25} style={{ objectFit: 'contain', height: '25px' }} />
                )}
              </li>
            ))}
          </ul>
        </div>
      </div>
      {fixedOptions && (
        <ul className="flex flex-wrap gap-5 mr-8 sm:mb-0 mb-5 sm:w-auto w-full">
          {fixedOptions.map((item: any, index) => (
            <li key={'fixedOption' + index} className="px-4 py-3 relative flex items-center font-medium bg-blue-2 dark:bg-blue-1 rounded-3xl">
              <span className="mr-[10px]">{item?.name}</span>
              {item?.icon && (
                <Image alt="" src={item?.icon} width={25} height={25} style={{ objectFit: 'contain', height: '25px' }} />
              )}
            </li>
          ))}
        </ul>
      )}
      <ul className="flex flex-wrap gap-5">
        {selected.length ? selected.map((item: any, index) => (
          <li key={'selected' + index} className="px-4 py-3 relative flex items-center font-medium bg-blue-2 dark:bg-blue-1 rounded-3xl">
            <span className="mr-[10px]">{item?.name}</span>
            {item?.icon && (
              <Image alt="" src={item?.icon} width={25} height={25} style={{ objectFit: 'contain', height: '25px' }} />
            )}
            <SvgIcon name="plusCircle" className="w-5 h-5 absolute -top-1 right-0 rotate-45 stroke-none fill-primary-2 cursor-pointer"
              onClick={() => removeSelected(item)}
            />
          </li>
        )) : null}
      </ul>
    </div>
  )
}

export default SelectTag
