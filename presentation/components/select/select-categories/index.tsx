import Category from 'domain/models/category'
import { Query } from 'domain/repositories/category_repository/query'
import SelectTag from 'presentation/components/select/select-tag'
import CategoryUseCase from 'presentation/use_cases/category_use_case'
import { useEffect, useState } from 'react'

const categoryUseCase = new CategoryUseCase()

type Props = {
  length?: number;
  defaultSelected?: Category;
  onSelect: (option: any) => void;
};

const SelectCategories = ({ length = 1, onSelect = () => {}, defaultSelected }: Props) => {
  const [isReady, setIsReady] = useState(false)
  const [categories, setCategories] = useState<Category[]>([])


  const fetchCategories = async () => {
    const query = new Query()
    let categories = await categoryUseCase.search(query).then((res) => {
      return res;
    }).catch((_error) => {
      console.log("🚀 ~ categories ~ _error:", _error)
      return [];
    })
    setCategories(categories)
    if (categories?.length > 0) {
      onSelect(categories[0])// set Default value
    }
   
  }

  useEffect(() => {
    fetchCategories()
    setIsReady(true)  

  }, [])

  useEffect(() => {
    if (categories?.length > 0) {
      setIsReady(true)
    }
  }, [categories])

  return (
    <>
      <div className="mt-5">
        <p className="font-medium text-[#222222]">Category</p>
        <p className="text-sm text-gray-1 dark:text-gray-3">
          Adding a category will help make your item discoverable.
        </p>
      </div>
      {isReady && categories?.length > 0 && (
      
        <SelectTag
          options={categories}
          defaultSelected={defaultSelected || categories[0]}
          length={length}
          onSelect={(option) => {
            onSelect(option)}}
          />
          
      )}
    </>
  )
}

export default SelectCategories
