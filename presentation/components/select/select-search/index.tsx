import clsx from 'clsx'
import { Fragment, ReactNode, useState } from 'react'
import SvgIcon from 'presentation/components/svg-icon'
import SelectItem from '../select-item'

type Option = {
  title: string,
  name: string,
  percentage: string,
  value: string,
}

type Props = {
  options: Option[],
  nonSelectText?: string,
  className?: string,
  optionListClass?: string,
  childNode?: (childProps: any, index: number) => ReactNode,
}

const SelectSearch = ({ options, className, nonSelectText = 'Properties', optionListClass = 'w-full', childNode }: Props) => {
  const [selected, setSelected] = useState(null)
  const [active, setActive] = useState(false)

  const handleMouseEnter = () => setActive(true)
  const handleMouseLeave = () => setActive(false)

  const handleSelect = (value: any) => {
    if (selected === value) {
      return setSelected(null)
    }
    setSelected(value)
  }

  // TODO: handle Search
  const handleSearch = (keyword: string) => {}

  return (
    <div className={clsx('relative', className)}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="pl-6 pr-1 py-3 flex items-center rounded-full bg-gray-4 dark:bg-blue-1">
        <p className="cursor-pointer mr-6 shrink-0">
          <span className="md:mr-1.5 mr-0.5 w-[75px] text-ellipsis font-medium overflow-hidden inline-block align-middle capitalize">
            {selected ? selected : nonSelectText}
          </span>
          <SvgIcon
            name="chevronDown"
            className={`w-6 h-6 stroke-none fill-primary-2 transition duration-300 ${active ? 'rotate-180' : ''}`}
          />
        </p>
        <div className="shrink-0">
          <SvgIcon name="magnifyingGlass" className="w-[18px] h-[18px] stroke-none fill-dark-primary dark:fill-white"/>
          <input
            type="text" onChange={(e) => handleSearch(e.target.value)} placeholder="Value"
            className="md:w-[168px] w-[120px] px-4 outline-none bg-transparent text-sm text-gray-2"
          />
        </div>
      </div>
      <div className={`absolute left-0 z-10 py-3 bg-white border rounded-lg dark:bg-slate-700 transition
        ${active ? 'opacity-100 translate-y-0 visible' : 'opacity-0 -translate-y-2 invisible'}
        ${optionListClass}`}>
        <ul>
          {options.map((option, index) => (
            <Fragment key={index}>
              {childNode ?
                childNode(option, index) : (
                  <SelectItem key={index} value={option.value} className="flex justify-between items-center"
                    onSelect={(value) => handleSelect(value)}>
                    <p className="w-[30%] font-medium">{option.title}</p>
                    <p className="w-[30%] font-medium text-center">{option.name}</p>
                    <p className="w-[30%] font-medium text-center">{option.percentage}</p>
                    {selected === option.value ?
                      (<SvgIcon name="check" className="w-5 h-5 stroke-primary-1"/>)
                      : (<span className="w-5"></span>)
                    }
                  </SelectItem>
                )}
            </Fragment>
          ))}
        </ul>
      </div>
    </div>
  )
}

export default SelectSearch
