import { useRef, useState, useEffect } from 'react'
import clsx from 'clsx'
import Input from 'presentation/components/input'

const hours: any[] = []
for(let i = 0; i <= 23; i++) {
  hours.push(i < 10 ? `0${i}` : i)
}

const minutes: any[] = []
for(let i = 0; i <= 55;) {
  minutes.push(i < 10 ? `0${i}` : i)
  i = i+5
}

type Props = {
  defaultHour?: number | string,
  defaultMinute?: number | string,
  position?: 'top' | 'bottom',
  onSelect?: (option: any) => void,
}

const Timepicker = ({
  defaultHour = '00',
  defaultMinute = '00',
  position = 'bottom',
  onSelect,
}: Props) => {

  const timepickereRef = useRef<HTMLInputElement>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  const [selectedHour, setSelectedHour] = useState<number | string>(defaultHour)
  const [selectedMinute, setSelectedMinute] = useState<number | string>(defaultMinute)
  const [selectedTime, setSelectedTime] = useState<number | string>(`${selectedHour} : ${selectedMinute}`)

  const handleSelect = (option: any) => {
    onSelect?.(option)
  }

  useEffect(() => {
    setSelectedTime(`${selectedHour} : ${selectedMinute}`)
    handleSelect({
      hour: selectedHour,
      minute: selectedMinute,
    })
  }, [selectedHour, selectedMinute])

  useEffect(() => {
    const checkIfClickedOutside = (e: any) => {
      if (isModalOpen && timepickereRef.current && !timepickereRef.current?.contains(e.target)) {
        setIsModalOpen(false)
      }
    }

    document.addEventListener('mousedown', checkIfClickedOutside)
    return () => {
      document.removeEventListener('mousedown', checkIfClickedOutside)
    }
  }, [isModalOpen])

  return (
    <div className="relative">
      <Input
        className="w-[142px] cursor-pointer"
        type="text"
        value={selectedTime}
        prefixIcon="clock"
        prefixIconViewBox="0 0 22 22"
        prefixIconClassName="absolute left-5 top-3 w-6 h-6 stroke-none fill-primary-2 cursor-pointer"
        readOnly wrapClass="sm"
        onClick={() => {
          setIsModalOpen(!isModalOpen)
        }}
      />
      {isModalOpen && (
        <div
          ref={timepickereRef}
          className={clsx('absolute left-0 overflow-hidden py-3 rounded-lg drop-shadow-xl',
            'grid grid-cols-2 gap-2 w-full h-64 z-20',
            'bg-white dark:bg-dark-primary dark:text-white',
            position === 'bottom' ? 'top-10' : '-top-64'
          )}
        >
          <ul className="flex flex-col w-full h-full overflow-y-auto space-y-2 px-3">
            {hours.map((hour, index) => (
              <li
                key={index} onClick={() => setSelectedHour(hour)}
                className={clsx('w-full text-center hover:bg-green-2 dark:hover:bg-blue-1 cursor-pointer',
                  selectedHour === hour ? 'bg-green-2 dark:bg-blue-1' : '')}
              >
                {hour}
              </li>
            ))}
          </ul>
          <ul className="flex flex-col w-full h-full overflow-y-auto space-y-2 px-3">
            {minutes.map((minute, index) => (
              <li
                key={index} onClick={() => setSelectedMinute(minute)}
                className={clsx('w-full text-center hover:bg-green-2 dark:hover:bg-blue-1 cursor-pointer',
                  selectedMinute === minute ? 'bg-green-2 dark:bg-blue-1' : '')}
              >
                {minute}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  )
}

export default Timepicker
