import clsx from 'clsx'
import { ReactNode } from 'react'
import SvgIcon from 'presentation/components/svg-icon'
import Link from 'next/link'

type Props = {
  title?: string;
  href?: string;
  children?: ReactNode;
  icon: string;
  iconSize?: ReactNode;
  viewBox?: string;
  iconElemment?: ReactNode;
  className?: string;
  onClick?: (e: any) => void;
};

const DropdownItem = ({
  title,
  href,
  children,
  icon,
  iconSize = 'w-[24px] h-[24px]',
  viewBox,
  iconElemment,
  className,
  onClick,
}: Props) => {
  return (
    <>
      {children ? (
        <div className={clsx('flex justify-between cursor-pointer', className)}>
          {href ? (
            <Link href={href}>
              <div className="flex items-center transition svg-wapper">
                {iconElemment ? (
                  iconElemment
                ) : (
                  <SvgIcon
                    name={icon}
                    viewBox={viewBox}
                    className={clsx(
                      'stroke-none fill-dark-primary dark:fill-white transition',
                      iconSize
                    )}
                  />
                )}
                <span className="ml-3">{title}</span>
              </div>
            </Link>
          ) : (
            <div
              className="flex items-center transition cursor-pointer svg-wapper"
              onClick={onClick}
            >
              {iconElemment ? (
                iconElemment
              ) : (
                <SvgIcon
                  name={icon}
                  viewBox={viewBox}
                  className={clsx(
                    'stroke-none fill-dark-primary dark:fill-white transition',
                    iconSize
                  )}
                />
              )}
              <span className="ml-3">{title}</span>
            </div>
          )}
          {children}
        </div>
      ) : (
        <>
          {href ? (
            <Link href={href}>
              <div
                className={clsx(
                  'flex items-center transition svg-wapper cursor-pointer',
                  className
                )}
              >
                {iconElemment ? (
                  iconElemment
                ) : (
                  <SvgIcon
                    name={icon}
                    viewBox={viewBox}
                    className={clsx(
                      'stroke-none fill-dark-primary dark:fill-white transition',
                      iconSize
                    )}
                  />
                )}
                <span className="ml-3">{title}</span>
              </div>
            </Link>
          ) : (
            <div
              className={clsx(
                'flex items-center transition svg-wapper cursor-pointer',
                className
              )}
              onClick={onClick}
            >
              {iconElemment ? (
                iconElemment
              ) : (
                <SvgIcon
                  name={icon}
                  viewBox={viewBox}
                  className={clsx(
                    'stroke-none fill-dark-primary dark:fill-white transition',
                    iconSize
                  )}
                />
              )}
              <span className="ml-3">{title}</span>
            </div>
          )}
        </>
      )}
    </>
  )
}

export default DropdownItem
