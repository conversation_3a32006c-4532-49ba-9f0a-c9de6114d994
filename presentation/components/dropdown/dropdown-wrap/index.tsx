import { ReactNode, useState, useRef, useEffect } from 'react'
import clsx from 'clsx'

type Props = {
  head: ReactNode;
  content: ReactNode;
  wrapClass?: string;
  wrapHeadClass?: string;
  wrapContentClass?: string;
  dropdownClass?: string;
  children?: ReactNode;
};

const DropdownWrap = ({ head, content, children, dropdownClass }: Props) => {
  const [active, setActive] = useState(false)
  const dropdownRef = useRef(null)

  const handleMouseEnter = () => setActive(true)
  const handleMouseLeave = () => setActive(false)
  const handleMouseClick = () => setActive(!active)
  const handleMouseClickInside = (e: { stopPropagation: () => any; }) => e.stopPropagation()

  // Detect clicks outside the dropdown and close it
  // useEffect(() => {
  //   const handleClickOutside = () => {
  //     setActive(false)
  //   }
  //   document.addEventListener('mousedown', handleClickOutside)
  //   return () => {
  //     document.removeEventListener('mousedown', handleClickOutside)
  //   }
  // }, [])

  return (
    <div
      className="dropdown-wrap relative"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={handleMouseClick}
    >
      <div className="flex items-center cursor-pointer">{head}</div>
      <div
        ref={dropdownRef}
        onClick={handleMouseClickInside}
        className={clsx(
          'w-max bg-white dark:bg-dark-primary text-dark-primary dark:text-white',
          'absolute rounded-lg border border-gray-3 p-4 right-0 transition duration-100 ease-in z-10',
          active ? 'opacity-100 translate-y-0 visible' : 'opacity-0 -translate-y-2 invisible',
          dropdownClass
        )}
      >
        {content}
      </div>
      {children}
    </div>
  )
}

export default DropdownWrap
