import clsx from 'clsx'
import SvgIcon from 'presentation/components/svg-icon'
import Image from 'next/image'

type Props = {
  title?: string,
  icon?: string,
  iconClass?: string,
  iconImage?: string,
  iconImageClass?: string,
  iconViewBox?: string,
  hideTitleMobile?: boolean,
  className?: string,
}

const DropdownHead = ({ title, icon, iconClass, iconImage, iconImageClass, iconViewBox, hideTitleMobile, className, ...props }: Props) => {
  return (
    <div className={clsx('flex items-center cursor-pointer hhh', className)} {...props}>
      {icon && !iconImage ? ( <SvgIcon name={icon} className={iconClass} viewBox={iconViewBox}/> ) : ''}
      {!icon && iconImage ? ( <Image className={iconImageClass} alt="" src={iconImage} width={60} height={60} /> ) : ''}
      {title ? ( <span className={`ml-2.5 font-bold leading-5 ${hideTitleMobile ? 'hidden md:block' : ''}`}>{title}</span> ) : ''}
    </div>
  )
}

export default DropdownHead
