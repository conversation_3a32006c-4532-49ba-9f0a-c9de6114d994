import clsx from 'clsx'
import Image from 'next/image'
import Link from 'next/link'
import { ReactNode, useState } from 'react'
type Props = {
  items: Item[],
  parentTitle?: string,
  parentIcon: string,
  children?: ReactNode,
  hideTitleMobile?: boolean,
  dropdownClass?: string,
  isLoading?: boolean,
  maxHeight?: string,
  onSelectItem?: (item: any) => void
}

type Item = {
  icon?: string,
  title: string,
  link: string,
  hasChild?: boolean,
  child?: Child[],
  onClick?: () => void,
}

type Child = {
  id: number,
  icon: string,
  title: string,
  link: string,
}

const Dropdown = ({ parentTitle, parentIcon, items, children, hideTitleMobile, dropdownClass ,isLoading, onSelectItem, maxHeight }: Props) => {
  const [active, setActive] = useState(false)

  const handleMouseEnter = () => setActive(true)
  const handleMouseLeave = () => setActive(false)
  const selectItem = (item: any) => {
    if(isLoading) return
    onSelectItem?.(item)
  }
  return (
    <div
      className="dropdown-wrap relative"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}>
      <div className="flex items-center cursor-pointer">
        <Image src={`/asset/icons/${parentIcon}`} alt="" width={20} height={20} />
        {parentTitle && (
          <span className={`ml-2.5 font-bold leading-5 ${hideTitleMobile ? 'hidden md:block' : ''}`}>{parentTitle}</span>
        )}
      </div>
      <div 
        style={{ maxHeight: maxHeight ? maxHeight : '' }}
        className={clsx(`w-max bg-white dark:bg-dark-primary text-dark-primary dark:text-white 
        absolute rounded-lg border border-gray-3 p-4 -top-1 right-0 transition duration-100 ease-in z-10
        ${maxHeight ? 'overflow-x-hidden' : ''}
        ${active ? 'opacity-100 translate-y-0 visible' : 'opacity-0 -translate-y-2 invisible'}`, dropdownClass)}>
        {children}
        <ul>
          {items.map((item, index) => (
            
            item.link && item.link !== '#' ? <Link href={item.link} key={index}>
              <li className={`flex items-center cursor-pointer font-bold ${index > 0 ? 'mt-5' : ''}`} onClick={item.onClick}>
                {item.icon ? (
                  <Image src={`/asset/icons/${item.icon}`} alt="" width={20} height={20} />
                ) : <></>}
                <div className="ml-2.5 hover:text-primary-1 transition">{item.title}</div>
              </li>
            </Link>
              :
              <li key={index} onClick={() => selectItem(item)} className={`flex items-center cursor-pointer font-bold ${index > 0 ? 'mt-5' : ''}`} >
                {item.icon ? (
                  <Image src={`/asset/icons/${item.icon}`} alt="" width={20} height={20} />
                ) : <></>}
                <div className="ml-2.5 hover:text-primary-1 transition">{item.title}</div>
              </li>
            
          ))}
        </ul>
      </div>
    </div>
  )
}

export default Dropdown
