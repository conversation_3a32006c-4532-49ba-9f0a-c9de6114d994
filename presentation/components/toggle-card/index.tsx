import SvgIcon from 'presentation/components/svg-icon'
import Link from 'next/link'
import { useState } from 'react'
import Image from 'next/image'
import clsx from 'clsx'

const ToggleCard = () => {
  const [toggle, setToggle] = useState(false)

  return (
    <div className="notify-card fixed bottom-2 z-10 right-0 w-max rounded-tl-[10px] rounded-bl-[10px] shadow-normal bg-white dark:text-dark-primary">
      <div
        className={clsx(
          toggle ? 'pl-7 pr-10p' : 'px-2 xl:pl-[9px] xl:pr-10p',
          'flex pt-3 pb-3'
        )}
      >
        <div
          className={clsx(
            'flex items-center transition-all overflow-hidden',
            toggle ? 'show' : 'hidden'
          )}
        >
          <div className="mr-3 relative card-image">
            <Image
              src="/asset/images/<EMAIL>"
              alt=""
              fill
              className="object-contain"
            />
          </div>
          <div className="card-text">
            <p className="mb-1 text-red-1 text-lg font-medium align-middle">
              <SvgIcon
                name="cart1"
                className="w-[24px] h-[18px] mr-1 fill-red-1 stroke-none"
              />
              Now Sold
            </p>
            <p className="font-medium text-dark-primary">
              Collection Name：Alex Smith
            </p>
            <p className="font-medium text-dark-primary">
              Asset Name：By the Digital Art
            </p>
            <p className="font-medium text-dark-primary">
              Selling Price：0.005 ETH
            </p>
            <p className="font-medium text-dark-primary">Purchased by Ahhhh</p>
          </div>
        </div>
        <div className="min-h-[150px] flex flex-col justify-between">
          <SvgIcon
            name={toggle ? 'minus' : 'plus'}
            viewBox={toggle ? '0 0 16 2' : '0 0 24 24'}
            className={`-mt-1 ${
              toggle ? 'w-4 h-6' : 'w-6 h-6'
            } cursor-pointer fill-primary-2 stroke-none`}
            onClick={() => setToggle(!toggle)}
          />
          <Link
            href="/setting"
            className="cursor-point -mb-1 flex justify-center items-center"
          >
            <SvgIcon
              name="setting"
              fill="#02AACF"
              className="icon-21 stroke-none"
            />
          </Link>
        </div>
      </div>
    </div>
  )
}

export default ToggleCard
