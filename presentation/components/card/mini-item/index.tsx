import ButtonIcon from 'presentation/components/button/button-icon'
import SvgIcon from 'presentation/components/svg-icon'
import clsx from 'clsx'
import Image from 'next/image'
import Link from 'next/link'

type Props = {
  title: string;
  name: string;
  id?: string;
  imageSrc?: string;
  price?: number;
  priceSymbol?: string;
  className?: string;
  onClose?: (e: any) => void;
};

const MiniItem = (props: Props) => {
  return (
    <div className={clsx('flex items-center relative', props.className)}>
      <Image
        className="rounded-[20px]"
        alt=""
        src={props.imageSrc || ''}
        width={60}
        height={60}
      />
      <div className="max-w-[7rem] py-1 ml-2">
        <p className="font-medium truncate">{props.title}</p>
        <Link href="/" className="text-sm font-medium text-primary-2">
          {props.name}
        </Link>
      </div>
      <p className="ml-2 mr-1 font-medium">
        {props.price} {props.priceSymbol}
      </p>
      <SvgIcon
        name="xCircle"
        className="w-5 h-5 fill-primary-2 stroke-none absolute top-0 -right-1 cursor-pointer"
        onClick={props.onClose}
      />
    </div>
  )
}

export default MiniItem
