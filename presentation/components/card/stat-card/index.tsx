import clsx from 'clsx'
import SvgIcon from 'presentation/components/svg-icon'

type Props = {
  icon?: string,
  title: string | number,
  subtitle: string,
  className?: string,
  widthClasses?: string,
  iconSizeClasses?: string,
}

const StatCard = ({ icon, title, subtitle, className, widthClasses = 'lg:w-28', iconSizeClasses = 'w-4 h-4' }: Props) => {
  return (
    <div className={clsx('px-1 py-1 md:py-3 min-w-fit bg-white dark:bg-slate-800 rounded-md flex flex-col items-center border border-gray-3 dark:border-none',
      className, widthClasses)}>
      {icon && (
        <SvgIcon name={icon} className={clsx('stroke-none fill-primary-2', iconSizeClasses)} />
      )}
      <p className="mt-1 text-2xl font-bold text-primary-2">{title}</p>
      <p className="text-sm text-gray-1 text-stroke-thin">{subtitle}</p>
    </div>
  )
}

export default StatCard
