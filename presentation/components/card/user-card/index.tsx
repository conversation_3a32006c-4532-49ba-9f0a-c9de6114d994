import { ReactNode } from 'react'
import Link from 'next/link'
import clsx from 'clsx'
import SvgIcon from 'presentation/components/svg-icon'

type Props = {
  href?: string;
  name?: string;
  nickname?: string;
  children: ReactNode;
  className?: string;
};

const UserCard = ({ href, name, nickname, children, className }: Props) => {
  return href ? (
    <Link href={href} className={clsx('block mt-2', className)}>
      <div className="flex gap-2 items-center">
        {children}
        <div>
          <p className="text-xl font-medium dark:text-slate-300">
            {name}
            <SvgIcon
              name="verify"
              className="w-5 h-5 ml-1 stroke-none fill-primary-2"
            />
          </p>
          <p className="text-gray-2 text-stroke-thin">@{nickname}</p>
        </div>
      </div>
    </Link>
  ) : (
    <div className={clsx('flex gap-2 items-center', className)}>
      {children}
      <div>
        <p className="text-xl font-medium dark:text-slate-300">
          {name}
          <SvgIcon
            name="verify"
            className="w-5 h-5 ml-1 stroke-none fill-primary-2"
          />
        </p>
        <p className="text-gray-2 text-stroke-thin">@{nickname}</p>
      </div>
    </div>
  )
}

export default UserCard
