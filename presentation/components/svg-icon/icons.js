const Icons = {
  userCircle: `<g transform="translate(0.25 0.25)">
    <path d="M25.607,4.393A15,15,0,0,0,4.393,25.607,15,15,0,0,0,25.607,4.393ZM15,28.344A13.344,13.344,0,1,1,28.344,15,13.361,13.361,0,0,1,15,28.344Z" fill="#02aacf" stroke="#02aacf" stroke-width="0.5" />
    <path d="M184.606,83.319a3.709,3.709,0,1,0-3.706-3.712A3.712,3.712,0,0,0,184.606,83.319Zm0-5.845a2.133,2.133,0,1,1-2.133,2.133A2.134,2.134,0,0,1,184.606,77.473Z" transform="translate(-169.606 -71.161)" fill="#02aacf" stroke="#02aacf" stroke-width="0.5" />
    <path d="M131.009,233.885a8.071,8.071,0,0,0-12.369,0,11.694,11.694,0,0,0-2.014,5.752.8.8,0,0,0,.77.862.844.844,0,0,0,.949-.7c.545-4.8,2.726-7.226,6.479-7.226s5.934,2.43,6.479,7.226a.841.841,0,0,0,.86.705.465.465,0,0,0,.09-.006.808.808,0,0,0,.77-.862A11.673,11.673,0,0,0,131.009,233.885Z" transform="translate(-109.825 -216.473)" fill="#02aacf" stroke="#02aacf" stroke-width="0.5" />
  </g>`,
  user: `<g transform="translate(0 0)">
    <path d="M17,20a1,1,0,0,1-1-1V17a3,3,0,0,0-3-3H5a3,3,0,0,0-3,3v2a1,1,0,0,1-2,0V17a5.006,5.006,0,0,1,5-5h8a5.006,5.006,0,0,1,5,5v2A1,1,0,0,1,17,20ZM9,10a5,5,0,1,1,5-5A5.006,5.006,0,0,1,9,10ZM9,2a3,3,0,1,0,3,3A3,3,0,0,0,9,2Z" transform="translate(3 2)"/>
  </g>`,
  userFluid: `<g id="Group_4895" data-name="Group 4895" transform="translate(-1100 -588)">
    <path id="Path" d="M4.2,9.807a4.207,4.207,0,0,0,4.2-4.2V4.2A4.2,4.2,0,0,0,0,4.2V5.6A4.207,4.207,0,0,0,4.2,9.807Z" transform="translate(1108.107 592)"/>
    <path id="Path-2" data-name="Path" d="M14.543,2.8A3.473,3.473,0,0,1,11.15,0h-6.3A4.882,4.882,0,0,0,0,4.9v.7H16.62V4.9a4.907,4.907,0,0,0-.641-2.426A3.394,3.394,0,0,1,14.543,2.8Z" transform="translate(1104 603.016)"/>
    <path id="Path-3" data-name="Path" d="M2.8,0H1.4V1.4H0V2.8H1.4V4.2H2.8V2.8H4.2V1.4H2.8Z" transform="translate(1116.417 600.278)"/>
  </g>`,
  userPlus:
    '<path stroke-linecap="round" stroke-linejoin="round" d="M19 7.5v3m0 0v3m0-3h3m-3 0h-3m-2.25-4.125a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zM4 19.235v-.11a6.375 6.375 0 0112.75 0v.109A12.318 12.318 0 0110.374 21c-2.331 0-4.512-.645-6.374-1.766z" />',
  bell: `<defs>
    <clipPath id="bellA">
      <path d="M11,22A3.008,3.008,0,0,1,8.405,20.5,1,1,0,0,1,9.27,19h3.46a1,1,0,0,1,.866,1.5A3.011,3.011,0,0,1,11,22Zm10-5H1a1,1,0,0,1,0-2,2,2,0,0,0,2-2V8A8,8,0,0,1,19,8v5a2,2,0,0,0,2,2,.959.959,0,0,1,1,1,1.029,1.029,0,0,1-.25.687A.97.97,0,0,1,21,17ZM11,2A6.007,6.007,0,0,0,5,8v5a4,4,0,0,1-.536,2h13.07A4,4,0,0,1,17,13V8A6.007,6.007,0,0,0,11,2Z" transform="translate(1 1)"/>
    </clipPath>
    <filter id="b" x="-61" y="-43" width="144" height="144" filterUnits="userSpaceOnUse">
      <feOffset dy="18" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="20" result="c"/>
      <feFlood flood-color="#6b67d2" flood-opacity="0.349"/>
      <feComposite operator="in" in2="c"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g transform="translate(-1 -1)">
    <path d="M13.595,20.5a3,3,0,0,1-5.19,0A1,1,0,0,1,9.27,19h3.46A1,1,0,0,1,13.595,20.5Z" transform="translate(1 1)"/>
    <g clip-path="url(#bellA)">
      <g transform="matrix(1, 0, 0, 1, 1, 1)" filter="url(#b)">
        <rect width="24" height="24" transform="translate(-1 -1)"/>
      </g>
    </g>
  </g>`,
  tag: `
  <defs>
    <clipPath id="tagA">
      <path d="M11.005,21.166a2.981,2.981,0,0,1-2.122-.878L.293,11.707A1,1,0,0,1,0,11V1A1,1,0,0,1,1,0H11a1.007,1.007,0,0,1,.707.292L20.3,8.885a3.008,3.008,0,0,1,0,4.23l-7.172,7.172A3.019,3.019,0,0,1,11.005,21.166ZM2,2v8.586l8.3,8.287a1,1,0,0,0,.707.293.994.994,0,0,0,.707-.293L18.882,11.7a1,1,0,0,0,0-1.408L10.586,2Z" transform="translate(1 1)"/>
    </clipPath>
    <filter id="tagB" x="-61" y="-43" width="144" height="144" filterUnits="userSpaceOnUse">
      <feOffset dy="18" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="20" result="c"/>
      <feFlood flood-color="#6b67d2" flood-opacity="0.349"/>
      <feComposite operator="in" in2="c"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g transform="translate(1 1)">
    <g clip-path="url(#tagA)">
      <g transform="matrix(1, 0, 0, 1, 1, 1)" filter="url(#tagB)">
        <rect width="24" height="24" transform="translate(-1 -1)"/>
      </g>
    </g>
  </g>`,
  alertOctagon: `
  <path d="M6.86,22a1.006,1.006,0,0,1-.707-.293L.293,15.847A1.006,1.006,0,0,1,0,15.14V6.86a1.006,1.006,0,0,1,.293-.707L6.153.293A1.006,1.006,0,0,1,6.86,0h8.28a1.006,1.006,0,0,1,.707.293l5.86,5.861A1.006,1.006,0,0,1,22,6.86v8.28a1.006,1.006,0,0,1-.293.707l-5.86,5.86A1.006,1.006,0,0,1,15.14,22ZM2,7.274v7.452L7.274,20h7.452L20,14.726V7.274L14.726,2H7.274Zm8.294,7.433A1,1,0,1,1,11,15,1,1,0,0,1,10.293,14.707ZM10,11V7a1,1,0,1,1,2,0v4a1,1,0,0,1-2,0Z" transform="translate(1 1)"/>
  `,
  wallet: `<g transform="translate(0.25 -34.3)">
    <path d="M28.32,45.748h-.714V41.459a3.179,3.179,0,0,0-3.175-3.175h-.658l.006-1.594a2.143,2.143,0,0,0-2.141-2.141H2.6A2.6,2.6,0,0,0,0,37.116V58.157A2.143,2.143,0,0,0,2.141,60.3h22.3a3.179,3.179,0,0,0,3.175-3.175V52.829h.714A1.675,1.675,0,0,0,30,51.156V47.421A1.684,1.684,0,0,0,28.32,45.748ZM2.6,36.027H21.639a.664.664,0,0,1,.664.658l-.006,1.6H2.6a1.129,1.129,0,0,1,0-2.258ZM26.13,57.118a1.7,1.7,0,0,1-1.7,1.7H2.135a.666.666,0,0,1-.664-.664V39.5a2.591,2.591,0,0,0,1.132.258H24.432a1.7,1.7,0,0,1,1.7,1.7v4.288H21.091a1.675,1.675,0,0,0-1.674,1.674v3.735a1.675,1.675,0,0,0,1.674,1.674H26.13Zm2.387-5.962a.194.194,0,0,1-.2.2H21.1a.194.194,0,0,1-.2-.2V47.421a.2.2,0,0,1,.2-.2H28.32a.2.2,0,0,1,.2.2v3.735Z" transform="translate(0 0)" fill="#02aacf" stroke="#02aacf" stroke-width="0.5" />
  </g>`,
  //viewBox="0 0 20.003 18.999"
  heart: `<g transform="translate(0)">
    <path d="M10.008,19a.736.736,0,0,1-.376-.1l-.245-.147a34.756,34.756,0,0,1-6-4.674A12.491,12.491,0,0,1,.407,9.329a7.755,7.755,0,0,1,.2-5.5A6.3,6.3,0,0,1,4.484.314,6.572,6.572,0,0,1,9.735.856L10,1.013l.269-.157a6.568,6.568,0,0,1,4.981-.62l.266.078A6.308,6.308,0,0,1,19.4,3.834a7.774,7.774,0,0,1,.2,5.511,12.5,12.5,0,0,1-2.976,4.737,34.675,34.675,0,0,1-6,4.665l-.229.143A.736.736,0,0,1,10.008,19ZM6.494,1.461a5.1,5.1,0,0,0-1.56.244c-2.812.906-4.162,3.984-3.14,7.16a11.013,11.013,0,0,0,2.615,4.168A33.3,33.3,0,0,0,10.145,17.5L10,17.41l.468-.294a33.2,33.2,0,0,0,4.6-3.574l.528-.5a10.993,10.993,0,0,0,2.614-4.159c1.025-3.183-.326-6.268-3.142-7.176a5.108,5.108,0,0,0-4.628.78.733.733,0,0,1-.865.009l-.2-.14A5.077,5.077,0,0,0,6.494,1.461Zm9.025,6.31a.737.737,0,0,1-.729-.671,1.973,1.973,0,0,0-1.363-1.724.732.732,0,0,1,.445-1.392,3.432,3.432,0,0,1,2.376,3,.732.732,0,0,1-.668.79Z" transform="translate(1 3)"/>
  </g>`,
  moon: `<defs>
    <clipPath id="moonA">
      <path d="M10,19.962c-.1,0-.208,0-.311,0A10,10,0,0,1,9.08,0c.033,0,.066,0,.1,0a.992.992,0,0,1,.887.551.982.982,0,0,1-.089,1.044,6,6,0,0,0,8.391,8.392.988.988,0,0,1,.59-.2,1.016,1.016,0,0,1,.748.333.982.982,0,0,1,.252.761A10,10,0,0,1,10,19.962ZM7.277,2.438a8,8,0,0,0,2.477,15.52c.082,0,.166,0,.249,0a8.017,8.017,0,0,0,7.521-5.278A8,8,0,0,1,7.277,2.438Z" transform="translate(2 2)"/>
    </clipPath>
    <filter id="moonB" x="-62" y="-44" width="144" height="144" filterUnits="userSpaceOnUse">
      <feOffset dy="18" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="20" result="c"/>
      <feFlood flood-color="#6b67d2" flood-opacity="0.349"/>
      <feComposite operator="in" in2="c"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g transform="translate(1 1)">
    <g clip-path="url(#moonA)">
      <g transform="matrix(1, 0, 0, 1, 2, 2)" filter="url(#moonB)">
        <rect width="24" height="24" transform="translate(-2 -2)"/>
      </g>
    </g>
  </g>`,
  cart: `<g transform="translate(-14.057 0.302)">
      <path d="M41.68,5.669A1.507,1.507,0,0,0,40.5,5.09H20.625l-.677-2.405h0A3.013,3.013,0,0,0,17.723.556L15.294.017a.763.763,0,0,0-.9.606.789.789,0,0,0,.585.936L17.4,2.1a1.47,1.47,0,0,1,1.084,1.037l4.44,15.785a3.3,3.3,0,0,0-.29.158,3.142,3.142,0,0,0-1.12,1.193,3.38,3.38,0,0,0-.389,1.518h0v.082h0a3.354,3.354,0,0,0,.246,1.19A3.257,3.257,0,0,0,22.519,24.5a3.071,3.071,0,0,0,1.765.558H38.565a.789.789,0,0,0,0-1.577H24.284a1.571,1.571,0,0,1-.635-.132,1.65,1.65,0,0,1-.719-.613A1.65,1.65,0,0,1,23,20.806a1.594,1.594,0,0,1,.42-.374,1.9,1.9,0,0,1,.591-.235L38.365,17.78a2.324,2.324,0,0,0,1.873-1.864l1.756-8.933h0a1.665,1.665,0,0,0,.03-.314A1.614,1.614,0,0,0,41.68,5.669ZM38.745,15.6a.774.774,0,0,1-.625.622l-13.714,2.31L21.069,6.667H40.5Z" transform="translate(0 0)" fill="#02aacf" stroke="#02aacf" stroke-width="0.6" />
      <path d="M163.839,438.8a1.751,1.751,0,1,0,.634.771A1.751,1.751,0,0,0,163.839,438.8Zm-.329,1.728a.713.713,0,0,1-.256.311.707.707,0,0,1-.67.065.711.711,0,0,1-.311-.257.71.71,0,0,1-.065-.669.712.712,0,0,1,.257-.311.7.7,0,0,1,.394-.121.707.707,0,0,1,.652.982Z" transform="translate(-138.086 -412.711)" fill="#02aacf" stroke="#02aacf" stroke-width="0.6" />
      <path d="M365.372,438.8a1.751,1.751,0,1,0,.634.771A1.751,1.751,0,0,0,365.372,438.8Zm-.33,1.728a.708.708,0,0,1-.257.311.706.706,0,0,1-.669.065.712.712,0,0,1-.312-.257.71.71,0,0,1-.065-.669.709.709,0,0,1,.257-.311.7.7,0,0,1,.394-.121.707.707,0,0,1,.651.982Z" transform="translate(-327.053 -412.711)" fill="#02aacf" stroke="#02aacf" stroke-width="0.6" />
    </g>`,
  cart1: '<path d="M10,19.5A1.5,1.5,0,1,1,8.5,18,1.5,1.5,0,0,1,10,19.5ZM13.5,18A1.5,1.5,0,1,0,15,19.5,1.5,1.5,0,0,0,13.5,18Zm1.336-5,1.977-7H0l2.938,7h11.9ZM19.805,3,16.373,15H3.776l.839,2H17.854L21.328,5h1.929L24,3Z" transform="translate(0 -3)" />',
  eye: `<g transform="translate(0 0)">
      <g transform="translate(0 0)">
        <path d="M12,18c-3.19,0-6.169-1.452-8.854-4.316A20.54,20.54,0,0,1,.895,10.793,15.006,15.006,0,0,1,.105,9.447a1.008,1.008,0,0,1,0-.895A15,15,0,0,1,.895,7.207,20.54,20.54,0,0,1,3.145,4.316C5.831,1.452,8.81,0,12,0s6.169,1.452,8.855,4.316a20.54,20.54,0,0,1,2.251,2.891,15,15,0,0,1,.788,1.345,1,1,0,0,1,0,.895,15,15,0,0,1-.788,1.345,20.54,20.54,0,0,1-2.251,2.891C18.169,16.548,15.19,18,12,18ZM12,2C9.385,2,6.9,3.239,4.6,5.683a18.536,18.536,0,0,0-2.03,2.609c-.162.251-.307.489-.433.707.122.213.268.451.433.707A18.536,18.536,0,0,0,4.6,12.316C6.9,14.761,9.385,16,12,16s5.1-1.239,7.4-3.684a18.434,18.434,0,0,0,2.03-2.609c.161-.25.307-.488.434-.707-.128-.222-.274-.46-.434-.707A18.434,18.434,0,0,0,19.4,5.683C17.1,3.239,14.615,2,12,2Zm0,11a4,4,0,1,1,4-4A4,4,0,0,1,12,13Zm0-6a2,2,0,1,0,2,2A2,2,0,0,0,12,7Z" transform="translate(0 3)"/>
        <g transform="matrix(1, 0, 0, 1, -60, -18)" filter="url(#a)">
          <path d="M24,24h0Z" transform="translate(60 18)"/>
        </g>
      </g>
    </g>`,
  square:
    '<path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6A2.25 2.25 0 016 3.75h2.25A2.25 2.25 0 0110.5 6v2.25a2.25 2.25 0 01-2.25 2.25H6a2.25 2.25 0 01-2.25-2.25V6zM3.75 15.75A2.25 2.25 0 016 13.5h2.25a2.25 2.25 0 012.25 2.25V18a2.25 2.25 0 01-2.25 2.25H6A2.25 2.25 0 013.75 18v-2.25zM13.5 6a2.25 2.25 0 012.25-2.25H18A2.25 2.25 0 0120.25 6v2.25A2.25 2.25 0 0118 10.5h-2.25a2.25 2.25 0 01-2.25-2.25V6zM13.5 15.75a2.25 2.25 0 012.25-2.25H18a2.25 2.25 0 012.25 2.25V18A2.25 2.25 0 0118 20.25h-2.25A2.25 2.25 0 0113.5 18v-2.25z" />',
  cog: '<path stroke-linecap="round" stroke-linejoin="round" d="M10.343 3.94c.09-.542.56-.94 1.11-.94h1.093c.55 0 1.02.398 1.11.94l.149.894c.07.424.384.764.78.93.398.164.855.142 1.205-.108l.737-.527a1.125 1.125 0 011.45.12l.773.774c.39.389.44 1.002.12 1.45l-.527.737c-.25.35-.272.806-.107 1.204.165.397.505.71.93.78l.893.15c.543.09.94.56.94 1.109v1.094c0 .55-.397 1.02-.94 1.11l-.893.149c-.425.07-.765.383-.93.78-.165.398-.143.854.107 1.204l.527.738c.32.447.269 1.06-.12 1.45l-.774.773a1.125 1.125 0 01-1.449.12l-.738-.527c-.35-.25-.806-.272-1.203-.107-.397.165-.71.505-.781.929l-.149.894c-.09.542-.56.94-1.11.94h-1.094c-.55 0-1.019-.398-1.11-.94l-.148-.894c-.071-.424-.384-.764-.781-.93-.398-.164-.854-.142-1.204.108l-.738.527c-.447.32-1.06.269-1.45-.12l-.773-.774a1.125 1.125 0 01-.12-1.45l.527-.737c.25-.35.273-.806.108-1.204-.165-.397-.505-.71-.93-.78l-.894-.15c-.542-.09-.94-.56-.94-1.109v-1.094c0-.55.398-1.02.94-1.11l.894-.149c.424-.07.765-.383.93-.78.165-.398.143-.854-.107-1.204l-.527-.738a1.125 1.125 0 01.12-1.45l.773-.773a1.125 1.125 0 011.45-.12l.737.527c.35.25.807.272 1.204.107.397-.165.71-.505.78-.929l.15-.894z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />',
  setting: `
  <path d="M12.08,24a3,3,0,0,1-3-3,.7.7,0,0,0-.484-.685.652.652,0,0,0-.265-.056.64.64,0,0,0-.445.178l-.059.059a3,3,0,0,1-4.244,0A3.022,3.022,0,0,1,2.7,18.375a2.982,2.982,0,0,1,.879-2.122l.052-.052a.655.655,0,0,0,.126-.727.648.648,0,0,0-.591-.393H3a3,3,0,0,1,0-6A.7.7,0,0,0,3.686,8.6a.648.648,0,0,0-.122-.709L3.5,7.827A3,3,0,1,1,7.747,3.583l.052.052a.647.647,0,0,0,.454.185.639.639,0,0,0,.262-.056,1.053,1.053,0,0,1,.2-.064A.646.646,0,0,0,9,3.17V3a3,3,0,0,1,6,0v.086a.671.671,0,0,0,.668.655.637.637,0,0,0,.445-.178l.059-.059a2.983,2.983,0,0,1,2.123-.88,3.018,3.018,0,0,1,2.121.879,3,3,0,0,1,0,4.244l-.052.052a.646.646,0,0,0-.13.716,1,1,0,0,1,.064.2A.646.646,0,0,0,20.83,9H21a3,3,0,1,1,0,6h-.086a.663.663,0,0,0-.477,1.112l.059.059a2.983,2.983,0,0,1,.88,2.123,3,3,0,0,1-3,3,2.982,2.982,0,0,1-2.122-.879l-.052-.052a.644.644,0,0,0-.453-.184.68.68,0,0,0-.274.059.645.645,0,0,0-.393.59V21A3,3,0,0,1,12.08,24ZM8.343,18.253a2.455,2.455,0,0,1,1,.209A2.65,2.65,0,0,1,11.08,20.91V21a1,1,0,1,0,2,0v-.174a2.662,2.662,0,0,1,4.527-1.883l.061.06a1,1,0,0,0,1.416,0,1,1,0,0,0,0-1.416l-.067-.067A2.664,2.664,0,0,1,20.91,13H21a1,1,0,1,0,0-2h-.174A2.649,2.649,0,0,1,18.4,9.394a1,1,0,0,1-.072-.265,2.641,2.641,0,0,1,.615-2.737l.06-.06a1.008,1.008,0,0,0,.293-.707,1,1,0,0,0-1.71-.708l-.067.068a2.642,2.642,0,0,1-1.856.757,2.606,2.606,0,0,1-1.057-.223A2.652,2.652,0,0,1,13,3.09V3a1,1,0,1,0-2,0v.175A2.647,2.647,0,0,1,9.394,5.6a.955.955,0,0,1-.265.073,2.653,2.653,0,0,1-2.737-.615L6.332,5A1,1,0,0,0,5.625,4.7.993.993,0,0,0,4.917,5a1,1,0,0,0,0,1.415l.068.068a2.621,2.621,0,0,1,.553,2.865A2.65,2.65,0,0,1,3.09,11.08H3a1,1,0,1,0,0,2h.175a2.663,2.663,0,0,1,1.883,4.527L5,17.668a1,1,0,1,0,1.416,1.415l.068-.067A2.686,2.686,0,0,1,8.343,18.253ZM12,16a4,4,0,1,1,4-4A4,4,0,0,1,12,16Zm0-6a2,2,0,1,0,2,2A2,2,0,0,0,12,10Z"/>`,
  arrowRightOnRect:
    '<path stroke-linecap="round" stroke-linejoin="round" d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15m3 0l3-3m0 0l-3-3m3 3H9" />',
  verify:
    '<path d="M9.5,19A9.5,9.5,0,0,1,2.783,2.783,9.5,9.5,0,1,1,16.218,16.218,9.438,9.438,0,0,1,9.5,19ZM4.6,8.461a.594.594,0,0,0-.42.171l-.839.826a.576.576,0,0,0,0,.826l3.859,3.8a.6.6,0,0,0,.84,0l6.828-6.715a.578.578,0,0,0,0-.826l-.839-.826a.6.6,0,0,0-.84,0L7.62,11.189,5.02,8.632A.6.6,0,0,0,4.6,8.461Z" fill="#02aacf"/>',
  minus: `<g id="Group_173" data-name="Group 173" transform="translate(-4 -11)">
    <path id="Shape-2" data-name="Shape" d="M1,2H15a1,1,0,0,0,0-2H1A1,1,0,0,0,1,2Z" transform="translate(4 11)"></path>
  </g>`,
  plus: '<path d="M7,15V9H1A1,1,0,1,1,1,7H7V1A1,1,0,1,1,9,1V7h6a1,1,0,0,1,0,2H9v6a1,1,0,1,1-2,0Z" transform="translate(4 4)"/>',
  plusCircle: `<g transform="translate(0 0)">
    <path d="M3.222,18.778A11,11,0,1,1,18.778,3.222,11,11,0,1,1,3.222,18.778ZM2,11a9,9,0,1,0,9-9A9.01,9.01,0,0,0,2,11Zm8,4V12H7a1,1,0,1,1,0-2h3V7a1,1,0,1,1,2,0v3h3a1,1,0,0,1,0,2H12v3a1,1,0,0,1-2,0Z" transform="translate(1 1)" fill="#02aacf"/>
  </g>`,
  hand: `<g transform="translate(-41.394)">
    <path d="M116.536,239.917h-7.155a.663.663,0,0,0-.663.663v2.043a.663.663,0,0,0,.663.663h7.155a.663.663,0,0,0,.663-.663V240.58A.663.663,0,0,0,116.536,239.917Z" transform="translate(-62.516 -222.782)"/>
    <path d="M55.127,3.523a1.072,1.072,0,0,0-1.264.835l-.51,2.5a.181.181,0,0,1-.358-.052l.387-4.548a1.177,1.177,0,0,0-2.346-.2l-.33,3.873a.19.19,0,0,1-.38-.016V1.177a1.178,1.178,0,0,0-2.355,0V5.84a.157.157,0,0,1-.314.018l-.446-3.785a1.178,1.178,0,1,0-2.339.276l.919,7.8a.236.236,0,0,1-.4.191L43.419,8.3a1.177,1.177,0,0,0-1.7,1.633l4.672,4.852a2.945,2.945,0,0,0,2.346,1.166h2.824a3.122,3.122,0,0,0,3.119-3.118,17.593,17.593,0,0,1,.355-3.518l.924-4.526A1.071,1.071,0,0,0,55.127,3.523Z" transform="translate(0)"/>
  </g>`,
  xCircle:
    '<path d="M3.222,18.778A11,11,0,1,1,18.778,3.222,11,11,0,1,1,3.222,18.778ZM2,11a9,9,0,1,0,9-9A9.01,9.01,0,0,0,2,11Zm11.293,3.707L11,12.414,8.708,14.707a1,1,0,1,1-1.415-1.414L9.586,11,7.293,8.708A1,1,0,0,1,8.708,7.293L11,9.586l2.293-2.293a1,1,0,1,1,1.414,1.415L12.414,11l2.293,2.293a1,1,0,1,1-1.414,1.414Z" transform="translate(1 1)"/>',
  xSquare: ` <defs>
    <clipPath id="xsquareA">
      <path d="M17,20H3a3,3,0,0,1-3-3V3A3,3,0,0,1,3,0H17a3,3,0,0,1,3,3V17A3,3,0,0,1,17,20ZM3,2A1,1,0,0,0,2,3V17a1,1,0,0,0,1,1H17a1,1,0,0,0,1-1V3a1,1,0,0,0-1-1ZM13,14a.994.994,0,0,1-.707-.293L10,11.415,7.707,13.707a1,1,0,0,1-1.414-1.414L8.586,10,6.293,7.707A1,1,0,1,1,7.707,6.293L10,8.586l2.293-2.293a1,1,0,0,1,1.414,1.414L11.414,10l2.293,2.293A1,1,0,0,1,13,14Z" transform="translate(2 2)"/>
    </clipPath>
    <filter id="xsquareB" x="-62" y="-44" width="144" height="144" filterUnits="userSpaceOnUse">
      <feOffset dy="18" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="20" result="c"/>
      <feFlood flood-color="#6b67d2" flood-opacity="0.349"/>
      <feComposite operator="in" in2="c"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g transform="translate(-2 -2)">
    <path d="M10,8.586,7.707,6.293A1,1,0,0,0,6.293,7.707L8.586,10,6.293,12.293a1,1,0,0,0,1.414,1.414L10,11.414l2.293,2.293a1,1,0,0,0,1.414-1.414L11.414,10l2.293-2.293a1,1,0,1,0-1.414-1.414Z" transform="translate(2 2)"/>
    <g clip-path="url(#xsquareA)">
      <g transform="matrix(1, 0, 0, 1, 2, 2)" filter="url(#xsquareB)">
        <rect width="24" height="24" transform="translate(-2 -2)" fill="#02aacf"/>
      </g>
    </g>
  </g>`,
  globe: `<g transform="translate(0 0)">
    <path d="M11,22h-.032A11,11,0,0,1,3.222,3.222,10.926,10.926,0,0,1,10.976,0h.047a11,11,0,0,1,7.754,18.778A10.925,10.925,0,0,1,11.031,22Zm2.276-2.291A9.008,9.008,0,0,0,19.944,12H15.95A16.278,16.278,0,0,1,13.276,19.708Zm-4.551,0A16.322,16.322,0,0,1,6.053,12h-4A9.006,9.006,0,0,0,8.725,19.708ZM11,19.449A14.371,14.371,0,0,0,13.945,12H8.059A14.406,14.406,0,0,0,11,19.449ZM19.944,10a9.006,9.006,0,0,0-6.669-7.708A16.331,16.331,0,0,1,15.947,10Zm-6,0A14.409,14.409,0,0,0,11,2.551,14.374,14.374,0,0,0,8.056,10ZM6.05,10A16.265,16.265,0,0,1,8.725,2.291,9.006,9.006,0,0,0,2.055,10Z" transform="translate(1 1)"/>
  </g>`,
  discord:
    '<path d="M20.336,13.321a19.552,19.552,0,0,0-4.953-1.556,14.521,14.521,0,0,0-.634,1.317,18.185,18.185,0,0,0-5.489,0,14.079,14.079,0,0,0-.642-1.317,19.487,19.487,0,0,0-4.956,1.56A20.771,20.771,0,0,0,.1,27.233a19.8,19.8,0,0,0,6.074,3.113,15.106,15.106,0,0,0,1.3-2.143,12.792,12.792,0,0,1-2.049-1c.172-.127.34-.26.5-.4a14,14,0,0,0,12.145,0c.164.137.332.27.5.4a12.76,12.76,0,0,1-2.052,1,15.04,15.04,0,0,0,1.3,2.143A19.761,19.761,0,0,0,23.9,27.233,20.751,20.751,0,0,0,20.336,13.321ZM8.015,24.433a2.33,2.33,0,0,1-2.158-2.455,2.318,2.318,0,0,1,2.158-2.457,2.3,2.3,0,0,1,2.158,2.457A2.315,2.315,0,0,1,8.015,24.433Zm7.976,0a2.33,2.33,0,0,1-2.158-2.455,2.318,2.318,0,0,1,2.158-2.457,2.3,2.3,0,0,1,2.158,2.457A2.316,2.316,0,0,1,15.991,24.433Z" transform="translate(0 -9)"/>',
  twitter:
    //'<path d="M24,4.557a9.831,9.831,0,0,1-2.828.775,4.932,4.932,0,0,0,2.165-2.724A9.864,9.864,0,0,1,20.21,3.8a4.927,4.927,0,0,0-8.391,4.49A13.978,13.978,0,0,1,1.671,3.149,4.93,4.93,0,0,0,3.194,9.723,4.9,4.9,0,0,1,.965,9.107,4.927,4.927,0,0,0,4.914,14a4.935,4.935,0,0,1-2.224.084A4.928,4.928,0,0,0,7.29,17.5,9.9,9.9,0,0,1,0,19.54a13.939,13.939,0,0,0,7.548,2.212A13.925,13.925,0,0,0,21.543,7.106,10.025,10.025,0,0,0,24,4.557Z" transform="translate(0 0)"/>',
    '<path d="M12.4208 9.03655L20.1466 0H18.3195L11.6013 7.85632L6.15568 0H0L8.20245 11.8347L0.0810599 21.3325H1.90815L9.02115 13.0133L14.7877 21.3325H20.9434L12.4208 9.03655ZM2.64336 1.40639H5.46181L18.3001 19.9261H15.4824L2.64336 1.40639Z"  />',
  telegram:
    '<path d="M18.384,22.779a1.189,1.189,0,0,0,1.107.145,1.16,1.16,0,0,0,.724-.84C21.084,18,23.192,7.663,23.983,3.948a.781.781,0,0,0-.26-.758.8.8,0,0,0-.8-.14C18.733,4.6,5.82,9.447.542,11.4a.822.822,0,0,0,.051,1.563c2.367.708,5.474,1.693,5.474,1.693s1.452,4.385,2.209,6.615a.876.876,0,0,0,.6.576.866.866,0,0,0,.811-.207l3.1-2.923s3.572,2.619,5.6,4.062ZM7.374,14.1,9.053,19.64l.373-3.507L19.611,6.947a.277.277,0,0,0,.033-.377.284.284,0,0,0-.376-.064L7.374,14.1Z" transform="translate(0 -1)" fill-rule="evenodd"/>',
  hamburger:
    '<path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />',
  hamburger1: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 19.743 18.205">
    <path id="menu" d="M18.076,18.205H1.667a1.667,1.667,0,1,1,0-3.334h16.41a1.667,1.667,0,1,1,0,3.334Zm0-7.436H1.667a1.667,1.667,0,1,1,0-3.334h16.41a1.667,1.667,0,1,1,0,3.334Zm0-7.436H1.667A1.667,1.667,0,1,1,1.667,0h16.41a1.667,1.667,0,1,1,0,3.334Z" transform="translate(0 0)" fill="currentColor"/>
  </svg>`,
  // magnifyingGlass: '<path d="M18.292,19.707l-4.533-4.534a8.509,8.509,0,1,1,1.414-1.414l4.534,4.533a1,1,0,0,1-1.415,1.415ZM2,8.5a6.5,6.5,0,0,0,11.059,4.629l.034-.036.036-.034A6.5,6.5,0,1,0,2,8.5Z" transform="translate(2 2)" />',
  magnifyingGlass: `<defs>
    <clipPath id="magnifyingGlassA">
      <path d="M18.292,19.707l-4.533-4.534a8.509,8.509,0,1,1,1.414-1.414l4.534,4.533a1,1,0,0,1-1.415,1.415ZM2,8.5a6.5,6.5,0,0,0,11.059,4.629l.034-.036.036-.034A6.5,6.5,0,1,0,2,8.5Z" transform="translate(2 2)"/>
    </clipPath>
    <filter id="magnifyingGlassB" x="-60" y="-42" width="144" height="144" filterUnits="userSpaceOnUse">
      <feOffset dy="18" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="20" result="c"/>
      <feFlood flood-color="#6b67d2" flood-opacity="0.349"/>
      <feComposite operator="in" in2="c"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <path d="M18.292,19.707l-4.533-4.534a8.509,8.509,0,1,1,1.414-1.414l4.534,4.533a1,1,0,0,1-1.415,1.415ZM2,8.5a6.5,6.5,0,0,0,11.059,4.629l.034-.036.036-.034A6.5,6.5,0,1,0,2,8.5Z" transform="translate(2 2)"/>
  <g clip-path="url(#magnifyingGlassA)">
    <g transform="matrix(1, 0, 0, 1, 0, 0)" filter="url(#magnifyingGlassB)">
      <rect width="24" height="24"/>
    </g>
  </g>`,
  // 0 0 20 20
  search: `<defs>
    <clipPath id="clip-path">
      <path id="_Icon_Сolor" data-name="🎨 Icon Сolor" d="M14.167,15a.829.829,0,0,1-.59-.245l-2.83-2.829a6.6,6.6,0,0,1-4.082,1.407,6.667,6.667,0,1,1,6.667-6.667,6.6,6.6,0,0,1-1.407,4.081l2.829,2.83A.834.834,0,0,1,14.167,15ZM6.666,1.667a5,5,0,1,0,5,5A5.005,5.005,0,0,0,6.666,1.667Z"/>
    </clipPath>
  </defs>
  <rect id="Icons_Line_search_Background_" data-name="Icons Line/search (Background)" width="20" height="20" fill="none"/>
  <path id="_Icon_Сolor-2" data-name="🎨 Icon Сolor" d="M14.167,15a.829.829,0,0,1-.59-.245l-2.83-2.829a6.6,6.6,0,0,1-4.082,1.407,6.667,6.667,0,1,1,6.667-6.667,6.6,6.6,0,0,1-1.407,4.081l2.829,2.83A.834.834,0,0,1,14.167,15ZM6.666,1.667a5,5,0,1,0,5,5A5.005,5.005,0,0,0,6.666,1.667Z" transform="translate(2.5 2.5)"/>
  <path id="_Icon_Сolor-3" data-name="🎨 Icon Сolor" d="M14.167,15a.829.829,0,0,1-.59-.245l-2.83-2.829a6.6,6.6,0,0,1-4.082,1.407,6.667,6.667,0,1,1,6.667-6.667,6.6,6.6,0,0,1-1.407,4.081l2.829,2.83A.834.834,0,0,1,14.167,15ZM6.666,1.667a5,5,0,1,0,5,5A5.005,5.005,0,0,0,6.666,1.667Z" transform="translate(2.5 2.5)"/>`,
  // share: '<path stroke-linecap="round" stroke-linejoin="round" d="M7.217 10.907a2.25 2.25 0 100 2.186m0-2.186c.18.324.283.696.283 1.093s-.103.77-.283 1.093m0-2.186l9.566-5.314m-9.566 7.5l9.566 5.314m0 0a2.25 2.25 0 103.935 2.186 2.25 2.25 0 00-3.935-2.186zm0-12.814a2.25 2.25 0 103.933-2.185 2.25 2.25 0 00-3.933 2.185z" />',
  share: `<g transform="translate(0 0)">
    <path d="M12,18a3.989,3.989,0,0,1,.152-1.092L6.841,13.813a4,4,0,1,1,0-5.627l5.31-3.1a4.01,4.01,0,1,1,1.01,1.726l-5.31,3.1a4.01,4.01,0,0,1,0,2.174l5.313,3.1A4,4,0,1,1,12,18Zm2,0a2,2,0,1,0,.334-1.105,1,1,0,0,1-.116.2A1.987,1.987,0,0,0,14,18ZM2,11a2,2,0,0,0,3.705,1.044l.021-.037.025-.04a2,2,0,0,0,0-1.938l-.021-.035L5.708,9.96A2,2,0,0,0,2,11ZM16,6a2,2,0,1,0-1.75-1.033l.024.038.021.038A2,2,0,0,0,16,6Z" transform="translate(2 1)"/>
  </g>`,
  ellipsisVertical:
    '<path stroke-linecap="round" stroke-linejoin="round" d="M12 6.75a.75.75 0 110-********* 0 010 1.5zM12 12.75a.75.75 0 110-********* 0 010 1.5zM12 18.75a.75.75 0 110-********* 0 010 1.5z" />',
  // refresh: '<path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />',
  // arrowTriangle: '<path stroke-linecap="round" stroke-linejoin="round" d="M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5" />',
  spin: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
    <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
  </svg>`,
  refresh: `<defs>
    <clipPath id="refreshA">
      <path d="M-8.788,21.751a10,10,0,0,0,7.221-6.418,1,1,0,0,0-.609-1.276,1,1,0,0,0-1.276.609A8,8,0,0,1-9.229,19.8a8.015,8.015,0,0,1-7.447-2.169L-19.475,15H-16a1,1,0,0,0,1-1,1,1,0,0,0-1-1h-6a1,1,0,0,0-.928.625l0,.013A1,1,0,0,0-23,14v6a1,1,0,0,0,1,1,1,1,0,0,0,1-1V16.312l2.932,2.755A10,10,0,0,0-10.994,22,9.993,9.993,0,0,0-8.788,21.751ZM-1,9a1,1,0,0,0,.927-.626l.005-.013A1.018,1.018,0,0,0,0,8V2A1,1,0,0,0-1,1,1,1,0,0,0-2,2V5.686L-4.933,2.931A10,10,0,0,0-14.213.247a10,10,0,0,0-7.22,6.418,1,1,0,0,0,.609,1.276,1,1,0,0,0,1.276-.609A8,8,0,0,1-13.771,2.2,8.012,8.012,0,0,1-6.325,4.367L-3.524,7H-7A1,1,0,0,0-8,8,1,1,0,0,0-7,9Z" transform="translate(23 1)"/>
    </clipPath>
    <filter id="refreshB" x="-60" y="-42" width="144" height="144" filterUnits="userSpaceOnUse">
      <feOffset dy="18" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="20" result="c"/>
      <feFlood flood-color="#6b67d2" flood-opacity="0.349"/>
      <feComposite operator="in" in2="c"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g transform="translate(0)">
    <path d="M-8.788,21.751a10,10,0,0,0,7.221-6.418,1,1,0,0,0-.609-1.276,1,1,0,0,0-1.276.609A8,8,0,0,1-9.229,19.8a8.015,8.015,0,0,1-7.447-2.169L-19.475,15H-16a1,1,0,0,0,1-1,1,1,0,0,0-1-1h-6a1,1,0,0,0-.928.625l0,.013A1,1,0,0,0-23,14v6a1,1,0,0,0,1,1,1,1,0,0,0,1-1V16.312l2.932,2.755A10,10,0,0,0-10.994,22,9.993,9.993,0,0,0-8.788,21.751ZM-1,9a1,1,0,0,0,.927-.626l.005-.013A1.018,1.018,0,0,0,0,8V2A1,1,0,0,0-1,1,1,1,0,0,0-2,2V5.686L-4.933,2.931A10,10,0,0,0-14.213.247a10,10,0,0,0-7.22,6.418,1,1,0,0,0,.609,1.276,1,1,0,0,0,1.276-.609A8,8,0,0,1-13.771,2.2,8.012,8.012,0,0,1-6.325,4.367L-3.524,7H-7A1,1,0,0,0-8,8,1,1,0,0,0-7,9Z" transform="translate(23 1)"/>
    <g clip-path="url(#refreshA)">
      <g transform="matrix(1, 0, 0, 1, 0, 0)" filter="url(#refreshB)">
        <rect width="24" height="24" transform="translate(0)" fill="#02aacf"/>
      </g>
    </g>
  </g>`,
  arrowTriangle: `<defs>
    <clipPath id="arrowTriangleA">
      <path d="M15,21a.992.992,0,0,1-.5-.135L8,17.152,1.5,20.868A.994.994,0,0,1,1,21a1.01,1.01,0,0,1-.825-.438.98.98,0,0,1-.112-.911l7-19a1,1,0,0,1,1.876,0l7,19a.981.981,0,0,1-.113.911A1.011,1.011,0,0,1,15,21ZM8,3.892,2.891,17.77l4.616-2.638a1,1,0,0,1,.993,0l4.616,2.638Z" transform="translate(8 3)"/>
    </clipPath>
    <filter id="arrowTriangleB" x="-63.997" y="-44" width="144" height="144" filterUnits="userSpaceOnUse">
      <feOffset dy="18" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="20" result="c"/>
      <feFlood flood-color="#6b67d2" flood-opacity="0.349"/>
      <feComposite operator="in" in2="c"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g transform="translate(-3.997 -2)" clip-path="url(#arrowTriangleA)">
    <g transform="matrix(1, 0, 0, 1, 4, 2)" filter="url(#arrowTriangleB)">
      <rect width="24" height="24" transform="translate(-4 -2)" fill="#02aacf"/>
    </g>
  </g>`,
  arrowDown:
    '<path d="M6.6,17.918h0l-.018-.008-.008,0L6.562,17.9l-.014-.007-.005,0-.019-.01h0a1,1,0,0,1-.231-.172l-6-6a1,1,0,0,1,1.415-1.414L6,14.586V1A1,1,0,0,1,8,1V14.586l4.293-4.293a1,1,0,0,1,1.414,1.414l-6,6-.042.04h0l-.017.015-.007.006-.011.009-.012.01-.006,0L7.6,17.8l0,0-.016.012,0,0-.017.012,0,0-.018.012,0,0-.017.011,0,0-.016.01-.005,0-.015.008-.008,0-.013.007-.011.005-.009,0-.016.008,0,0a1,1,0,0,1-.817.009Z" transform="translate(5 3)"/>',
  alertTriangle: `
  <defs>
    <clipPath id="alertA">
      <path d="M2.966,20.1A3,3,0,0,1,.41,15.589l8.47-14.14a3,3,0,0,1,5.135,0L22.493,15.6a3,3,0,0,1-2.576,4.5ZM10.593,2.48,2.133,16.6a1,1,0,0,0,.844,1.5H19.906a1,1,0,0,0,.858-.5.991.991,0,0,0,.005-.981L12.3,2.481A1.008,1.008,0,0,0,11.447,2,.993.993,0,0,0,10.593,2.48Zm.147,12.33a1,1,0,1,1,.707.293A1,1,0,0,1,10.74,14.81ZM10.447,11.1v-4a1,1,0,1,1,2,0v4a1,1,0,1,1-2,0Z" transform="translate(1 2)"/>
    </clipPath>
    <filter id="alertB" x="-60" y="-42" width="144" height="144" filterUnits="userSpaceOnUse">
      <feOffset dy="18" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="20" result="c"/>
      <feFlood flood-color="#6b67d2" flood-opacity="0.349"/>
      <feComposite operator="in" in2="c"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <path d="M2.966,20.1A3,3,0,0,1,.41,15.589l8.47-14.14a3,3,0,0,1,5.135,0L22.493,15.6a3,3,0,0,1-2.576,4.5ZM10.593,2.48,2.133,16.6a1,1,0,0,0,.844,1.5H19.906a1,1,0,0,0,.858-.5.991.991,0,0,0,.005-.981L12.3,2.481A1.008,1.008,0,0,0,11.447,2,.993.993,0,0,0,10.593,2.48Zm.147,12.33a1,1,0,1,1,.707.293A1,1,0,0,1,10.74,14.81ZM10.447,11.1v-4a1,1,0,1,1,2,0v4a1,1,0,1,1-2,0Z" transform="translate(1 2)"/>
  <g clip-path="url(#alertA)">
    <g transform="matrix(1, 0, 0, 1, 0, 0)" filter="url(#alertB)">
      <rect width="24" height="24"/>
    </g>
  </g>`,
  global:
    '<path stroke-linecap="round" stroke-linejoin="round" d="M12 21a9.004 9.004 0 008.716-6.747M12 21a9.004 9.004 0 01-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 017.843 4.582M12 3a8.997 8.997 0 00-7.843 4.582m15.686 0A11.953 11.953 0 0112 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0121 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0112 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 013 12c0-1.605.42-3.113 1.157-4.418" />',
  adjustmentVertical: `<g id="Group_4897" data-name="Group 4897" transform="translate(-589.333 -521)">
    <path id="Shape" d="M9.276,0a.943.943,0,0,1,.943.943V2.829a.943.943,0,1,1-1.886,0V.943A.943.943,0,0,1,9.276,0Z" transform="translate(592.315 521)" fill="#777e90"/>
    <path id="Shape-2" data-name="Shape" d="M9.276,10a.943.943,0,0,1,.943.943V16.6a.943.943,0,1,1-1.886,0V10.943A.943.943,0,0,1,9.276,10Z" transform="translate(592.315 522.315)" fill="#777e90"/>
    <path id="Shape-3" data-name="Shape" d="M.943,3.333a.943.943,0,0,1,.943.943v7.543a.943.943,0,0,1-1.886,0V4.276A.943.943,0,0,1,.943,3.333Z" transform="translate(591.219 521.438)" fill="#777e90"/>
    <path id="Shape-4" data-name="Shape" d="M.943,15a.943.943,0,0,1,.943.943v3.772a.943.943,0,1,1-1.886,0V15.943A.943.943,0,0,1,.943,15Z" transform="translate(591.219 522.972)" fill="#777e90"/>
    <path id="Shape-5" data-name="Shape" d="M8.333,2.829a2.829,2.829,0,0,1,5.657,0V8.486a2.829,2.829,0,0,1-5.657,0Zm3.772,0V8.486a.943.943,0,1,1-1.886,0V2.829a.943.943,0,0,1,1.886,0Z" transform="translate(590.429 522.886)" fill="#777e90"/>
    <path id="Shape-6" data-name="Shape" d="M0,11.162a2.829,2.829,0,0,1,5.657,0v1.886a2.829,2.829,0,1,1-5.657,0Zm3.772,0v1.886a.943.943,0,1,1-1.886,0V11.162a.943.943,0,1,1,1.886,0Z" transform="translate(589.333 523.982)" fill="#777e90"/>
  </g>`,
  noSymbol:
    '<path stroke-linecap="round" stroke-linejoin="round" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636" />',
  chevronUp:
    '<path stroke-linecap="round" stroke-linejoin="round" d="M4.5 15.75l7.5-7.5 7.5 7.5" />',
  chevronDown: `<g transform="translate(0 0)">
    <path d="M7,5.586,1.707.293A1,1,0,0,0,.293,1.707l6,6a1,1,0,0,0,1.414,0l6-6A1,1,0,0,0,12.293.293Z" transform="translate(5 8)"/>
  </g>`,
  //0 0 12.962 7.411
  chevronDownSelect:
    '<path id="Icon_ionic-ios-arrow-down" data-name="Icon ionic-ios-arrow-down" d="M12.671,16.424l4.9-4.9a.922.922,0,0,1,1.308,0,.934.934,0,0,1,0,1.312l-5.553,5.557a.925.925,0,0,1-1.277.027l-5.592-5.58a.926.926,0,0,1,1.308-1.312Z" transform="translate(-6.188 -11.247)"/>',
  chevronRight:
    '<path d="M5.586,7,.293,12.293a1,1,0,0,0,1.414,1.414l6-6a1,1,0,0,0,0-1.414l-6-6A1,1,0,0,0,.293,1.707Z" transform="translate(8 5)"/>',
  chevronLeft:
    '<path d="M12.293,1.707a1,1,0,0,0-1.414,0l-6,6a1,1,0,0,0,0,1.414l6,6a1,1,0,0,0,1.414-1.414L6.707,7Z" transform="translate(4 5)"/>',
  chevronUpDown:
    '<path stroke-linecap="round" stroke-linejoin="round" d="M8.25 15L12 18.75 15.75 15m-7.5-6L12 5.25 15.75 9" />',
  check: `<g transform="translate(0 0)">
    <path d="M1.707,6.293A1,1,0,0,0,.293,7.707l5,5a1,1,0,0,0,1.414,0l11-11A1,1,0,0,0,16.293.293L6,10.586Z" transform="translate(3 6)" fill="#02aacf"/>
  </g>`,
  calendar: `<defs>
    <clipPath id="calendarA">
      <path d="M3,22a3,3,0,0,1-3-3V5A3,3,0,0,1,3,2H5V1A1,1,0,0,1,7,1V2h6V1a1,1,0,0,1,2,0V2h2a3,3,0,0,1,3,3V19a3,3,0,0,1-3,3ZM2,19a1,1,0,0,0,1,1H17a1,1,0,0,0,1-1V10H2ZM18,8V5a1,1,0,0,0-1-1H15V5a1,1,0,0,1-2,0V4H7V5A1,1,0,0,1,5,5V4H3A1,1,0,0,0,2,5V8Z" transform="translate(2 1)"/>
    </clipPath>
    <filter id="calendarB" x="-60" y="-42" width="144" height="144" filterUnits="userSpaceOnUse">
      <feOffset dy="18" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="20" result="c"/>
      <feFlood flood-color="#6b67d2" flood-opacity="0.349"/>
      <feComposite operator="in" in2="c"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <path d="M3,22a3,3,0,0,1-3-3V5A3,3,0,0,1,3,2H5V1A1,1,0,0,1,7,1V2h6V1a1,1,0,0,1,2,0V2h2a3,3,0,0,1,3,3V19a3,3,0,0,1-3,3ZM2,19a1,1,0,0,0,1,1H17a1,1,0,0,0,1-1V10H2ZM18,8V5a1,1,0,0,0-1-1H15V5a1,1,0,0,1-2,0V4H7V5A1,1,0,0,1,5,5V4H3A1,1,0,0,0,2,5V8Z" transform="translate(2 1)"/>
  <g clip-path="url(#calendarA)">
    <g transform="matrix(1, 0, 0, 1, 0, 0)" filter="url(#calendarB)">
      <rect width="24" height="24" fill="#02aacf"/>
    </g>
  </g>`,
  calendarPlus: `defs>
    <clipPath id="calendarPlus-clip-path">
      <path id="calendarPlus_Combined_Shape" data-name="Combined Shape" d="M3,22a3,3,0,0,1-3-3V5A3,3,0,0,1,3,2H5V1A1,1,0,0,1,7,1V2h6V1a1,1,0,0,1,2,0V2h2a3,3,0,0,1,3,3V19a3,3,0,0,1-3,3ZM2,19a1,1,0,0,0,1,1H17a1,1,0,0,0,1-1V10H2ZM18,8V5a1,1,0,0,0-1-1H15V5a1,1,0,0,1-2,0V4H7V5A1,1,0,0,1,5,5V4H3A1,1,0,0,0,2,5V8Z" transform="translate(2 1)" fill="#02aacf"/>
    </clipPath>
    <filter id="calendarPlus_Icon_Color" x="-62" y="-43" width="144" height="144" filterUnits="userSpaceOnUse">
      <feOffset dy="18" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="20" result="blur"/>
      <feFlood flood-color="#6b67d2" flood-opacity="0.349"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="calendarPlus_Group_25213" data-name="Group 25213" transform="translate(-578 -283)">
    <g id="Group_25212" data-name="Group 25212" transform="translate(-165.012 -300)">
      <path id="Shape" d="M0,.8v6.42a.8.8,0,0,0,1.6,0V.8A.8.8,0,0,0,0,.8Z" transform="translate(752.21 594)" fill="#02aacf"/>
      <path id="Shape-2" data-name="Shape" d="M.8,1.6h6.42a.8.8,0,0,0,0-1.6H.8a.8.8,0,0,0,0,1.6Z" transform="translate(749 597.21)" fill="#02aacf"/>
    </g>
    <g id="calendarPlus_Group_148" data-name="Group 148" transform="translate(572 289)">
      <g id="calendarPlus_Mask_Group_148" data-name="Mask Group 148" transform="translate(4 -7)" clip-path="url(#calendarPlus-clip-path)">
        <g transform="matrix(1, 0, 0, 1, 2, 1)" filter="url(#calendarPlus_Icon_Color)">
          <rect id="_Icon_Color-2" data-name="↳ Icon Color" width="24" height="24" transform="translate(-2 -1)" fill="#02aacf"/>
        </g>
      </g>
    </g>
  </g>`,
  logout: `<g transform="translate(0 0)">
    <path d="M3,22a3,3,0,0,1-3-3V3A3,3,0,0,1,3,0H8A1,1,0,0,1,8,2H3A1,1,0,0,0,2,3V19a1,1,0,0,0,1,1H8a1,1,0,0,1,0,2Zm11.293-6.293a1,1,0,0,1,0-1.414L16.586,12H7a1,1,0,1,1,0-2h9.586L14.293,7.707a1,1,0,1,1,1.414-1.414l4,4,.039.04a1,1,0,0,1-.034,1.379l-4,4a1,1,0,0,1-1.414,0Z" transform="translate(2 1)"/>
  </g>`,
  info: '<path d="M3.222,18.778A11,11,0,1,1,18.778,3.222,11,11,0,1,1,3.222,18.778ZM2,11a9,9,0,1,0,9-9A9.01,9.01,0,0,0,2,11Zm8,4V11a1,1,0,1,1,2,0v4a1,1,0,0,1-2,0Zm.293-6.293A1,1,0,1,1,11,9,1,1,0,0,1,10.293,8.708Z" transform="translate(1 1)"/>',
  moreVertical: `<g transform="translate(0 -1)">
    <path d="M3,22a3,3,0,1,1,3-3A3,3,0,0,1,3,22Zm0-4a1,1,0,1,0,1,1A1,1,0,0,0,3,18Zm0-4a3,3,0,1,1,3-3A3,3,0,0,1,3,14Zm0-4a1,1,0,1,0,1,1A1,1,0,0,0,3,10ZM3,6A3,3,0,1,1,6,3,3,3,0,0,1,3,6ZM3,2A1,1,0,1,0,4,3,1,1,0,0,0,3,2Z" transform="translate(9 1)" fill="#02aacf"/>
  </g>`,
  arrowDownLeft:
    '<path d="M1,14a1,1,0,0,1-.611-.209h0l-.019-.015h0L.349,13.76h0q-.029-.025-.056-.052h0l0,0-.015-.016L.27,13.683l-.011-.012-.008-.009-.008-.009-.009-.011-.006-.007-.01-.013,0-.006L.2,13.6l0-.005-.011-.015,0,0-.011-.015,0,0-.01-.015,0-.005-.009-.015,0-.007L.134,13.5l0-.008-.007-.013-.005-.01-.005-.01-.007-.013,0-.008-.008-.016,0,0L.083,13.4h0A1,1,0,0,1,0,13V4A1,1,0,1,1,2,4v6.586L12.293.293a1,1,0,0,1,1.414,1.415L3.415,12H10a1,1,0,0,1,0,2Z" transform="translate(5 5)"/>',
  arrowUpRight:
    '<path d="M.293,13.707a1,1,0,0,1,0-1.414L10.586,2H4A1,1,0,1,1,4,0h9a1,1,0,0,1,.4.083h0l.021.009,0,0L13.44.1l.008,0,.013.007.01.005.01.005.013.007.008,0,.014.008.007,0,.015.009.005,0,.015.01,0,0,.015.011,0,0L13.6.2,13.6.2l.014.011.006,0,.013.01.007.006.011.009.009.008.009.008.012.011.006.005L13.7.29l0,0h0q.027.027.052.056h0l.016.019h0l.015.019h0A1,1,0,0,1,14,1v9a1,1,0,0,1-2,0V3.415L1.707,13.707a1,1,0,0,1-1.415,0Z" transform="translate(5 5)"/>',
  logo: `
    <path d="M0,41.043a4.36,4.36,0,0,0,4.36-4.36V22.859L5.1,23.6a4.362,4.362,0,0,0,6.169,0L4.36,16.69V10.525L17.439,23.6l3.085-3.081L0,0Z" fill="#474747" />
    <path d="M274.831,0l0,.009V0h-8.719a4.36,4.36,0,0,0-4.36,4.36h13.079l0,0V30.52l-6.914-6.914-3.081,3.085,14.354,14.354V4.362h8.716A4.36,4.36,0,0,0,292.266,0Z" transform="translate(-244.311 -0.002)" fill="#474747" />`,
  all: `<defs><linearGradient id="a" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#6fdef8" stop-opacity="0.6"/><stop offset="1" stop-color="#2e86d2"/></linearGradient></defs><g transform="translate(-87 -2008)"><g transform="translate(87 2008)"><rect width="40" height="40" rx="20" opacity="0.1" fill="url(#a)"/></g><g transform="translate(97.604 2017.953)"><path d="M55.212,29.466a.908.908,0,0,1-.912-.912V22.712a.908.908,0,0,1,.912-.912h5.944a.908.908,0,0,1,.912.912v5.843a.908.908,0,0,1-.912.912Z" transform="translate(-43.459 -21.268)" fill="url(#a)"/><path d="M57.481,54.352,54,58.1a.572.572,0,0,0,0,.777l3.479,3.749a.591.591,0,0,0,.844,0L61.8,58.878a.572.572,0,0,0,0-.777l-3.479-3.749A.591.591,0,0,0,57.481,54.352Z" transform="translate(-43.161 -42.709)" fill="url(#a)"/><circle cx="3.918" cy="3.918" r="3.918" transform="translate(0 11.745)" fill="url(#a)"/><path d="M22.673,22.074l3.006-1.722a.883.883,0,0,1,.912,0L29.6,22.074a.936.936,0,0,1,.439.777V26.3a.936.936,0,0,1-.439.777L26.59,28.795a.883.883,0,0,1-.912,0l-3.006-1.722A.972.972,0,0,1,22.2,26.3V22.851A.883.883,0,0,1,22.673,22.074Z" transform="translate(-22.2 -20.225)" fill="url(#a)"/></g></g>
  `,
  award:
    '<path d="M12.486,23.857,8,21.166,3.515,23.857a1,1,0,0,1-1.506-.989L3.14,14.35a8,8,0,1,1,9.721,0l1.131,8.518A1,1,0,0,1,13,24,.992.992,0,0,1,12.486,23.857ZM8.515,19.142l3.221,1.933-.75-5.653a8,8,0,0,1-5.971,0l-.751,5.653,3.221-1.933a1,1,0,0,1,1.03,0ZM2,8a6,6,0,0,0,9.187,5.082.994.994,0,0,1,.124-.08A6,6,0,1,0,2,8Z" transform="translate(4)" />',
  digitalArt: `
  <defs>
    <linearGradient id="digitalArtA" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffbf35" stop-opacity="0.6" />
      <stop offset="1" stop-color="#ffa900" />
    </linearGradient>
  </defs>
  <rect width="40" height="40" rx="20" opacity="0.1" fill="url(#digitalArtA)" />
  <path d="M10.87,4.32C4.526,4.32.72,9.409.72,11.953s1.269,4.453,3.806,4.453,3.807.636,3.807,2.544a4.342,4.342,0,0,0,4.44,4.453c8.247,0,12.053-4.453,12.053-8.9C24.825,6.864,18.481,4.32,10.87,4.32ZM9.284,6.864A1.59,1.59,0,1,1,7.7,8.455a1.588,1.588,0,0,1,1.586-1.59Zm5.074,0a1.59,1.59,0,1,1-1.585,1.59,1.588,1.588,0,0,1,1.585-1.59Zm5.075,2.544A1.592,1.592,0,1,1,17.847,11a1.588,1.588,0,0,1,1.585-1.59ZM4.844,10.681a1.59,1.59,0,1,1-1.586,1.59,1.587,1.587,0,0,1,1.586-1.59Zm7.929,6.361a1.908,1.908,0,1,1-1.9,1.909,1.905,1.905,0,0,1,1.9-1.909Z" transform="translate(7.28 6.08)" fill="url(#a)" />`,
  camera: `
  <defs>
    <linearGradient id="a" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#23d2c3" stop-opacity="0.6" />
      <stop offset="1" stop-color="#01bcad" />
    </linearGradient>
  </defs>
  <g transform="translate(10 12)">
    <path d="M17.5,16H2.5A2.454,2.454,0,0,1,0,13.6V4.8A2.454,2.454,0,0,1,2.5,2.4H5.387L6.806.356A.845.845,0,0,1,7.5,0h5a.842.842,0,0,1,.693.356L14.613,2.4H17.5A2.455,2.455,0,0,1,20,4.8v8.8A2.454,2.454,0,0,1,17.5,16ZM2.5,4a.818.818,0,0,0-.833.8v8.8a.818.818,0,0,0,.833.8h15a.818.818,0,0,0,.833-.8V4.8A.818.818,0,0,0,17.5,4H14.167a.843.843,0,0,1-.694-.356L12.054,1.6H7.946L6.527,3.644A.842.842,0,0,1,5.834,4ZM10,12.8a4.09,4.09,0,0,1-4.166-4A4.09,4.09,0,0,1,10,4.8a4.091,4.091,0,0,1,4.167,4A4.091,4.091,0,0,1,10,12.8Zm0-6.4A2.454,2.454,0,0,0,7.5,8.8a2.5,2.5,0,0,0,5,0A2.454,2.454,0,0,0,10,6.4Z" transform="translate(0 0)" fill="url(#a)" />
  </g>
  <g opacity="0.1">
    <rect width="40" height="40" rx="20" fill="#15caf2" />
  </g>`,
  tradingCard: `
  <defs>
    <linearGradient id="tradingCardA" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#466fff" stop-opacity="0.6" />
      <stop offset="1" stop-color="#3462ff" />
    </linearGradient>
  </defs>
  <g transform="translate(0 -0.5)">
    <rect width="40" height="40" rx="20" transform="translate(0 0.5)" opacity="0.1" fill="url(#tradingCardA)" />
    <g transform="translate(11.2 13.7)">
      <path d="M1.5,16.181a2.113,2.113,0,0,0,2.126,2.1H16.381a2.113,2.113,0,0,0,2.126-2.1V10.406H1.5Zm2.506-2.85a1.132,1.132,0,0,1,1.139-1.125H6.967a1.132,1.132,0,0,1,1.139,1.125v.75a1.132,1.132,0,0,1-1.139,1.125H5.144a1.132,1.132,0,0,1-1.139-1.125Z" transform="translate(-1.5 -4.919)" fill="url(#tradingCardA)" />
      <path d="M16.381,3.75H3.626A2.113,2.113,0,0,0,1.5,5.85v.975H18.507V5.85a2.113,2.113,0,0,0-2.126-2.1Z" transform="translate(-1.5 -3.75)" fill="url(#tradingCardA)" />
    </g>
  </g>`,
  handLimit: `
  <defs>
    <linearGradient id="handLimitA" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffb4b4" stop-opacity="0.6" />
      <stop offset="1" stop-color="red" />
      <stop offset="1" stop-color="#ff227f" />
    </linearGradient>
  </defs>
  <path d="M20,0A20,20,0,1,1,0,20,20,20,0,0,1,20,0Z" opacity="0.1" fill="url(#handLimitA)" />
  <path d="M15.124,3.733V9.52a.56.56,0,0,1-.56.56h0A.56.56,0,0,1,14,9.52V1.12A1.12,1.12,0,0,0,12.884,0h0a1.12,1.12,0,0,0-1.12,1.12v8.4a.56.56,0,0,1-.56.56h0a.56.56,0,0,1-.56-.56V2.8a.933.933,0,0,0-.933-.933h0a.933.933,0,0,0-.933.933v7.467a.56.56,0,0,1-.56.56h0a.56.56,0,0,1-.56-.56V5.6a.933.933,0,0,0-.933-.933h0a.933.933,0,0,0-.933.933V15.728c.312,4.51,2.785,5.577,6.276,5.577a6.435,6.435,0,0,0,5.666-3.118c.433-.7,3.059-4.788,3.285-5.215a4.954,4.954,0,0,1,1.133-1.494,2.521,2.521,0,0,0,.59-.786c.041-.075.159-.988-.519-.985a3.364,3.364,0,0,0-1.747.579,6.117,6.117,0,0,0-1.861,2.065,1.137,1.137,0,0,1-.945.593c-.374.009-.277-.175-.3-.947h0V3.733a1.12,1.12,0,0,0-1.12-1.12h0A1.12,1.12,0,0,0,15.124,3.733Z" transform="translate(7.813 9.347)" fill="url(#a)" />`,
  sport: `
  <defs>
    <linearGradient id="a" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#00bce5" stop-opacity="0.6" />
      <stop offset="1" stop-color="#02aacf" />
    </linearGradient>
  </defs>
  <path d="M20,0A20,20,0,1,1,0,20,20,20,0,0,1,20,0Z" opacity="0.1" fill="url(#a)" />
  <path d="M14.087,13.525a.725.725,0,0,0-.375-.638A10.706,10.706,0,0,0,8.125,11.45a16.7,16.7,0,0,0-3.587.425.6.6,0,0,0-.525.65.61.61,0,0,0,.169.431.581.581,0,0,0,.443.182,3.662,3.662,0,0,0,.462-.1A15.242,15.242,0,0,1,8.125,12.7a9.431,9.431,0,0,1,4.962,1.287.857.857,0,0,0,.413.138.587.587,0,0,0,.588-.6Zm1.2-2.687a.838.838,0,0,0-.438-.762A13.142,13.142,0,0,0,8,8.313a13.985,13.985,0,0,0-3.787.525.749.749,0,0,0-.6.8.746.746,0,0,0,.75.75,2.731,2.731,0,0,0,.462-.1,11.954,11.954,0,0,1,3.138-.413,11.728,11.728,0,0,1,6.1,1.55,1.1,1.1,0,0,0,.475.162.746.746,0,0,0,.75-.75Zm1.35-3.1a.945.945,0,0,0-.5-.875,12.9,12.9,0,0,0-3.662-1.382,19.474,19.474,0,0,0-4.288-.468,16.163,16.163,0,0,0-4.55.587.994.994,0,0,0-.482.318.9.9,0,0,0-.193.606.9.9,0,0,0,.256.65.858.858,0,0,0,.644.262,2.283,2.283,0,0,0,.5-.1A14.371,14.371,0,0,1,8.2,6.875a17.513,17.513,0,0,1,3.869.425,10.933,10.933,0,0,1,3.169,1.187,1.009,1.009,0,0,0,.5.15.886.886,0,0,0,.631-.256.852.852,0,0,0,.269-.644ZM19.2,9.6a9.39,9.39,0,0,1-1.287,4.818,9.564,9.564,0,0,1-3.494,3.494A9.375,9.375,0,0,1,9.6,19.2a9.416,9.416,0,0,1-4.818-1.287,9.532,9.532,0,0,1-3.494-3.494A9.4,9.4,0,0,1,0,9.6,9.4,9.4,0,0,1,1.287,4.782,9.532,9.532,0,0,1,4.782,1.287,9.416,9.416,0,0,1,9.6,0a9.375,9.375,0,0,1,4.818,1.287,9.564,9.564,0,0,1,3.494,3.494A9.39,9.39,0,0,1,19.2,9.6Z" transform="translate(10.4 10.4)" fill="url(#a)" />`,
  reload:
    '<path d="M12.359,19.776a8.839,8.839,0,0,0,6.279-5.835.922.922,0,0,0-.53-1.16.86.86,0,0,0-1.11.554A7.072,7.072,0,0,1,11.975,18,6.77,6.77,0,0,1,5.5,16.03L3.065,13.638H6.087a.91.91,0,0,0,0-1.819H.87a.872.872,0,0,0-.807.569l0,.011A.945.945,0,0,0,0,12.731v5.453a.89.89,0,0,0,.87.909.89.89,0,0,0,.869-.909V14.83l2.55,2.5A8.508,8.508,0,0,0,10.44,20,8.328,8.328,0,0,0,12.359,19.776ZM19.131,8.181a.871.871,0,0,0,.806-.569l0-.011A.963.963,0,0,0,20,7.269V1.817a.87.87,0,1,0-1.739,0V5.17l-2.55-2.5A8.446,8.446,0,0,0,7.641.224,8.838,8.838,0,0,0,1.363,6.059a.92.92,0,0,0,.53,1.16A.86.86,0,0,0,3,6.666,7.069,7.069,0,0,1,8.026,2,6.769,6.769,0,0,1,14.5,3.97l2.435,2.392H13.913a.91.91,0,0,0,0,1.819Z"/>',
  reload2: `<g id="Group_reload2" data-name="Group reload2" transform="translate(-1 -2)">
    <path
      id="reload2_Shape"
      data-name="reload2 Shape"
      d="M2.567,13.324a1,1,0,1,1,1.886-.665,8,8,0,1,0,1.872-8.3l-2.8,2.632H7a1,1,0,0,1,0,2H1.014a1.006,1.006,0,0,1-.14-.008H.865l-.016,0-.016,0H.826l-.022,0h0q-.048-.01-.1-.024h0L.686,8.941l-.006,0-.011,0A1,1,0,0,1,0,7.977V1.991a1,1,0,0,1,2,0V5.679L4.934,2.923a10,10,0,1,1,7.639,17.06Q12.283,20,12,20A10,10,0,0,1,2.567,13.324Z"
      transform="translate(1 2)"
    />
  </g>`,
  coin: `<g transform="translate(-0.5)">
    <path d="M12.325,0a8.424,8.424,0,0,0-1.132.079A8.4,8.4,0,0,0,10.073,0,9.063,9.063,0,0,0,3.068,3.478a.358.358,0,0,0-.045.053A11.053,11.053,0,0,0,1.6,5.831a.423.423,0,0,0-.026.056A11.716,11.716,0,0,0,.913,7.725a.376.376,0,0,0-.029.116A12.242,12.242,0,0,0,.5,10.887a12.072,12.072,0,0,0,.91,4.625.362.362,0,0,0,.017.044,11.184,11.184,0,0,0,1.3,2.307.367.367,0,0,0,.04.053A10.233,10.233,0,0,0,4.55,19.77a.364.364,0,0,0,.065.052,8.746,8.746,0,0,0,5.458,1.951,8.4,8.4,0,0,0,1.121-.079,8.425,8.425,0,0,0,1.132.079c5.279,0,9.573-4.884,9.573-10.887S17.6,0,12.325,0ZM8.361.981q-.186.1-.368.2c-.068.039-.136.077-.2.119-.1.064-.206.131-.307.2H6.76A7.9,7.9,0,0,1,8.479.917C8.439.937,8.4.961,8.361.981ZM4.577,17.269H3.231a10.376,10.376,0,0,1-.88-1.5H3.772c.059.133.119.265.183.395.027.056.056.111.084.167q.115.226.24.445c.031.054.06.11.092.164C4.438,17.049,4.507,17.159,4.577,17.269ZM2.177,6.382H3.614q-.1.246-.187.5c-.016.047-.033.093-.048.139-.054.161-.1.324-.152.489H1.76A10.85,10.85,0,0,1,2.177,6.382Zm-.926,4.5c0-.253.014-.5.029-.751h1.5c0,.006,0,.012,0,.018-.014.243-.024.486-.024.733,0,.126,0,.25.008.375h-1.5C1.261,11.137,1.251,11.013,1.251,10.887ZM2.8,12.013c.014.158.033.315.053.471,0,.011,0,.023,0,.035.028.209.062.416.1.621H1.479c-.073-.369-.131-.744-.168-1.126Zm.233-3.754c-.01.044-.018.089-.027.134s-.021.1-.03.148c-.046.235-.086.473-.118.714,0,.011,0,.023,0,.035,0,.032-.007.064-.011.1H1.356a11.338,11.338,0,0,1,.2-1.126Zm.09,5.631c.012.046.023.092.035.138,0,.017.008.034.013.05.062.229.131.453.206.676.016.047.032.093.048.139s.028.083.042.123H2.023a11,11,0,0,1-.369-1.126ZM4.371,4.837c-.032.054-.062.109-.092.164q-.124.219-.24.445c-.028.056-.057.11-.084.167l-.009.019H2.538a10.216,10.216,0,0,1,.975-1.5H4.828c-.046.066-.092.131-.137.2Q4.525,4.577,4.371,4.837Zm1.1-2.584H6.5c-.093.081-.186.163-.277.249-.075.071-.149.145-.222.219s-.148.151-.22.227-.139.148-.206.224-.118.138-.176.207H4.164A9.08,9.08,0,0,1,5.467,2.252ZM3.813,18.019H5.1c.042.056.084.111.128.166q.17.214.35.417c.067.076.137.15.206.224s.146.153.221.228c.03.03.059.062.089.092H4.966A9.3,9.3,0,0,1,3.813,18.019Zm4.666,2.837A8.017,8.017,0,0,1,6.05,19.9h.908c.048.037.1.072.146.108.078.058.155.116.235.171q.221.154.451.3c.067.041.135.079.2.119q.182.105.368.2C8.4,20.813,8.439,20.837,8.478,20.857Zm3.847.166a7.731,7.731,0,0,1-.884-.051l-.145-.02-.06-.009q-.195-.028-.386-.065l-.032-.007q-.186-.037-.369-.082l-.055-.015c-.114-.029-.227-.06-.339-.094l-.1-.033c-.1-.03-.191-.062-.285-.1-.077-.028-.152-.059-.227-.089-.051-.02-.1-.039-.154-.061a9.362,9.362,0,0,1-4.711-4.68c-.009-.02-.018-.04-.027-.06q-.082-.175-.158-.354c-.02-.048-.039-.1-.059-.144-.038-.093-.075-.187-.111-.281-.023-.061-.044-.123-.066-.184-.03-.083-.059-.167-.086-.252-.023-.069-.044-.139-.065-.208S3.954,14.08,3.931,14s-.041-.148-.06-.221-.04-.158-.059-.237-.035-.152-.052-.228-.033-.16-.048-.241-.029-.154-.043-.232-.027-.166-.039-.25-.023-.151-.033-.228-.021-.181-.03-.272c-.008-.071-.016-.142-.022-.213-.009-.1-.015-.209-.021-.315,0-.059-.008-.118-.011-.177-.007-.165-.011-.331-.011-.5s0-.334.011-.5c0-.059.008-.118.011-.177.006-.105.012-.211.021-.315.006-.071.014-.142.022-.213.009-.091.019-.182.03-.272s.021-.152.033-.228.025-.167.039-.25.028-.155.043-.232.032-.161.048-.241.034-.152.052-.228.038-.158.059-.237.039-.148.06-.222.046-.16.071-.239.043-.139.065-.208c.028-.084.057-.168.087-.252.022-.062.043-.123.066-.184.036-.095.073-.188.111-.281.02-.048.038-.1.059-.144q.076-.179.158-.354c.009-.02.018-.04.027-.06a9.364,9.364,0,0,1,4.711-4.68c.051-.021.1-.041.154-.061.075-.03.151-.061.227-.089.094-.034.19-.065.285-.1l.1-.033c.112-.034.225-.065.339-.094l.055-.014c.122-.03.245-.057.369-.082l.032-.007q.191-.037.386-.065L11.3.822,11.441.8a7.829,7.829,0,0,1,.884-.051c4.864,0,8.822,4.547,8.822,10.136S17.19,21.023,12.325,21.023Z"/>
    <path d="M18.571,4C14.121,4,10.5,8.21,10.5,13.385a9.871,9.871,0,0,0,3.564,7.782l.155-.285a2.852,2.852,0,0,1,.929-1.014l2.331-1.409a.143.143,0,0,0,.063-.119v-.457a4.088,4.088,0,0,1-2.061-2.706,1.7,1.7,0,0,1-.954-1.517v-.649a1.582,1.582,0,0,1,.7-1.329A6.638,6.638,0,0,1,15.66,9.99c.089-.215.164-.375.225-.5.186-.4.2-.419.064-1.136-.05-.267-.089-.538-.118-.806a.486.486,0,0,1,.119-.383.513.513,0,0,1,.781.074,3.41,3.41,0,0,0,.613.655,3.162,3.162,0,0,0,1.937.759q.162.007.328.029c2.234.295,3.444,1.443,3.7,3.51a1.611,1.611,0,0,1,.5,1.177v.54a1.477,1.477,0,0,1-.688,1.285,3.97,3.97,0,0,1-1.651,2.5v.444a.083.083,0,0,0,.011.047l1.761.907a1.768,1.768,0,0,1,.8.856l.1.226a10.134,10.134,0,0,0,2.5-6.78C26.642,8.21,23.022,4,18.571,4Z" transform="translate(-6.246 -2.498)"/>
  </g>
  `,
  grid: `<defs>
    <clipPath id="gridA">
      <path d="M19,20H12a1,1,0,0,1-1-1V12a1,1,0,0,1,1-1h7a1,1,0,0,1,1,1v7A1,1,0,0,1,19,20Zm-6-7v5h5V13ZM8,20H1a1,1,0,0,1-1-1V12a1,1,0,0,1,1-1H8a1,1,0,0,1,1,1v7A1,1,0,0,1,8,20ZM2,13v5H7V13ZM19,9H12a1,1,0,0,1-1-1V1a1,1,0,0,1,1-1h7a1,1,0,0,1,1,1V8A1,1,0,0,1,19,9ZM13,2V7h5V2ZM8,9H1A1,1,0,0,1,0,8V1A1,1,0,0,1,1,0H8A1,1,0,0,1,9,1V8A1,1,0,0,1,8,9ZM2,2V7H7V2Z" transform="translate(2 2)"/>
    </clipPath>
  </defs>
  <g transform="translate(0 0)">
    <path d="M12,20a1,1,0,0,1-1-1V12a1,1,0,0,1,1-1h7a1,1,0,0,1,1,1v7a1,1,0,0,1-1,1Zm1-2h5V13H13ZM1,20a1,1,0,0,1-1-1V12a1,1,0,0,1,1-1H8a1,1,0,0,1,1,1v7a1,1,0,0,1-1,1Zm1-2H7V13H2ZM12,9a1,1,0,0,1-1-1V1a1,1,0,0,1,1-1h7a1,1,0,0,1,1,1V8a1,1,0,0,1-1,1Zm1-2h5V2H13ZM1,9A1,1,0,0,1,0,8V1A1,1,0,0,1,1,0H8A1,1,0,0,1,9,1V8A1,1,0,0,1,8,9Z" transform="translate(2 2)" fill="none"/>
    <g clip-path="url(#gridA)">
      <g transform="matrix(1, 0, 0, 1, 2, 2)">
        <rect width="22" height="22" transform="translate(0 0)"/>
      </g>
    </g>
  </g>`,
  grid1: `<defs>
    <clipPath id="gridA1">
      <path d="M19,20H12a1,1,0,0,1-1-1V12a1,1,0,0,1,1-1h7a1,1,0,0,1,1,1v7A1,1,0,0,1,19,20Zm-6-7v5h5V13ZM8,20H1a1,1,0,0,1-1-1V12a1,1,0,0,1,1-1H8a1,1,0,0,1,1,1v7A1,1,0,0,1,8,20ZM2,13v5H7V13ZM19,9H12a1,1,0,0,1-1-1V1a1,1,0,0,1,1-1h7a1,1,0,0,1,1,1V8A1,1,0,0,1,19,9ZM13,2V7h5V2ZM8,9H1A1,1,0,0,1,0,8V1A1,1,0,0,1,1,0H8A1,1,0,0,1,9,1V8A1,1,0,0,1,8,9ZM2,2V7H7V2Z" transform="translate(2 2)"/>
    </clipPath>
  </defs>
  <g transform="translate(0 0)">
    <path d="M12,20a1,1,0,0,1-1-1V12a1,1,0,0,1,1-1h7a1,1,0,0,1,1,1v7a1,1,0,0,1-1,1Zm1-2h5V13H13ZM1,20a1,1,0,0,1-1-1V12a1,1,0,0,1,1-1H8a1,1,0,0,1,1,1v7a1,1,0,0,1-1,1Zm1-2H7V13H2ZM12,9a1,1,0,0,1-1-1V1a1,1,0,0,1,1-1h7a1,1,0,0,1,1,1V8a1,1,0,0,1-1,1Zm1-2h5V2H13ZM1,9A1,1,0,0,1,0,8V1A1,1,0,0,1,1,0H8A1,1,0,0,1,9,1V8A1,1,0,0,1,8,9Z" transform="translate(2 2)" fill="none"/>
    <g clip-path="url(#gridA1)">
      <g transform="matrix(1, 0, 0, 1, 2, 2)">
        <rect width="24" height="24" transform="translate(0 0)"/>
      </g>
    </g>
  </g>`,
  medium:
    '<path d="M8.5,7A8.5,8.5,0,1,0,17,15.5,8.5,8.5,0,0,0,8.5,7ZM22,8c-2.209,0-4,3.358-4,7.5S19.791,23,22,23s4-3.358,4-7.5S24.209,8,22,8Zm6.5,1c-.828,0-1.5,2.91-1.5,6.5s.672,6.5,1.5,6.5S30,19.09,30,15.5,29.328,9,28.5,9Z" transform="translate(-4 -3)"/>',
  instagram:
    '<path id="iconmonstr-instagram-11" d="M11,1.983c2.937,0,3.285.011,4.446.064,2.981.136,4.373,1.55,4.509,4.509.053,1.16.063,1.508.063,4.445s-.011,3.285-.063,4.445c-.137,2.956-1.525,4.373-4.509,4.509-1.16.053-1.507.064-4.446.064s-3.285-.011-4.445-.064c-2.988-.137-4.373-1.557-4.509-4.51-.053-1.16-.064-1.507-.064-4.445s.012-3.284.064-4.445C2.183,3.6,3.571,2.182,6.555,2.046c1.16-.052,1.508-.063,4.445-.063ZM11,0C8.013,0,7.639.013,6.465.066,***********,2.466.067,6.464.013,7.639,0,8.013,0,11s.013,3.362.066,4.536c.183,3.995,2.4,6.215,6.4,6.4C7.639,21.987,8.013,22,11,22s3.362-.013,4.536-.066c3.991-.183,6.217-2.4,6.4-6.4C21.987,14.362,22,13.987,22,11s-.013-3.361-.066-4.535c-.18-3.991-2.4-6.215-6.4-6.4C14.362.013,13.987,0,11,0Zm0,5.352A5.649,5.649,0,1,0,16.649,11,5.649,5.649,0,0,0,11,5.352Zm0,9.315A3.667,3.667,0,1,1,14.667,11,3.666,3.666,0,0,1,11,14.667ZM16.872,3.809a1.32,1.32,0,1,0,1.319,1.32A1.321,1.321,0,0,0,16.872,3.809Z" />',
  copy: `<defs>
    <clipPath id="copyA">
      <path d="M19,22H10a3,3,0,0,1-3-3V10a3,3,0,0,1,3-3h9a3,3,0,0,1,3,3v9A3,3,0,0,1,19,22ZM10,9a1,1,0,0,0-1,1v9a1,1,0,0,0,1,1h9a1,1,0,0,0,1-1V10a1,1,0,0,0-1-1ZM4,15H3a3,3,0,0,1-3-3V3A3,3,0,0,1,3,0h9a3,3,0,0,1,3,3V4a1,1,0,1,1-2,0V3a1,1,0,0,0-1-1H3A1,1,0,0,0,2,3v9a1,1,0,0,0,1,1H4a1,1,0,1,1,0,2Z" transform="translate(1 1)"/>
    </clipPath>
    <filter id="copyB" x="-61" y="-43" width="144" height="144" filterUnits="userSpaceOnUse">
      <feOffset dy="18" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="20" result="c"/>
      <feFlood flood-color="#6b67d2" flood-opacity="0.349"/>
      <feComposite operator="in" in2="c"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g transform="translate(0 0)">
    <path d="M4,13H3a1,1,0,0,1-1-1V3A1,1,0,0,1,3,2h9a1,1,0,0,1,1,1V4a1,1,0,0,0,2,0V3a3,3,0,0,0-3-3H3A3,3,0,0,0,0,3v9a3,3,0,0,0,3,3H4a1,1,0,0,0,0-2Z" transform="translate(1 1)"/>
    <g clip-path="url(#copyA)">
      <g transform="matrix(1, 0, 0, 1, 1, 1)" filter="url(#copyB)">
        <rect width="24" height="24" transform="translate(0 0)"/>
      </g>
    </g>
  </g>`,
  externalLink: `<g transform="translate(0 0)">
    <path d="M3,20a3,3,0,0,1-3-3V6A3,3,0,0,1,3,3H9A1,1,0,0,1,9,5H3A1,1,0,0,0,2,6V17a1,1,0,0,0,1,1H14a1,1,0,0,0,1-1V11a1,1,0,1,1,2,0v6a3,3,0,0,1-3,3Zm4.293-7.293a1,1,0,0,1,0-1.414L16.585,2H13a1,1,0,0,1,0-2h6a1,1,0,0,1,.4.082h0l.018.008.008,0L19.438.1l.014.007.006,0,.019.01h0A1,1,0,0,1,20,1.028V7a1,1,0,1,1-2,0V3.415L8.708,12.707a1,1,0,0,1-1.415,0Z" transform="translate(2 2)"/>
  </g>`,
  // 0 0 13.333 13.333
  yellowCoin: `<defs>
    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffbf35"/>
      <stop offset="1" stop-color="#ffa900"/>
    </linearGradient>
  </defs>
  <g id="icons_Coins_Filled" data-name="icons/Coins/Filled" transform="translate(-1.333 -1.333)">
    <path id="Shape" d="M5.992,8.338a.328.328,0,0,0,.487.285A4.667,4.667,0,1,0,.047,2.19a.328.328,0,0,0,.285.487A6.131,6.131,0,0,1,5.992,8.338Z" transform="translate(5.997 1.333)" fill="url(#linear-gradient)"/>
    <path id="Shape-2" data-name="Shape" d="M9.333,4.667A4.667,4.667,0,1,1,4.667,0,4.667,4.667,0,0,1,9.333,4.667Z" transform="translate(1.333 5.333)" fill="url(#linear-gradient)"/>
  </g>`,
  // 0 0 13.333 13.333
  greenCoin: `<defs>
    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#23d2c3"/>
      <stop offset="1" stop-color="#01bcad"/>
    </linearGradient>
  </defs>
  <g id="icons_Coins_Filled" data-name="icons/Coins/Filled" transform="translate(-1.333 -1.333)">
    <path id="Shape" d="M5.992,8.338a.328.328,0,0,0,.487.285A4.667,4.667,0,1,0,.047,2.19a.328.328,0,0,0,.285.487A6.131,6.131,0,0,1,5.992,8.338Z" transform="translate(5.997 1.333)" fill="url(#linear-gradient)"/>
    <path id="Shape-2" data-name="Shape" d="M9.333,4.667A4.667,4.667,0,1,1,4.667,0,4.667,4.667,0,0,1,9.333,4.667Z" transform="translate(1.333 5.333)" fill="url(#linear-gradient)"/>
  </g>`,
  columnChart: `<defs>
    <clipPath id="columnChart-clip-path">
      <path id="Combined_Shape" data-name="Combined Shape" d="M15.273,14.546H12.364a.728.728,0,0,1-.727-.727V4.364a.728.728,0,0,1,.727-.727h2.909A.728.728,0,0,1,16,4.364v9.454A.728.728,0,0,1,15.273,14.546ZM13.091,5.091v8h1.454v-8ZM9.454,14.546H6.546a.728.728,0,0,1-.727-.727V.727A.728.728,0,0,1,6.546,0H9.454a.728.728,0,0,1,.728.727V13.818A.728.728,0,0,1,9.454,14.546ZM7.273,1.454V13.091H8.727V1.454ZM3.637,14.546H.727A.728.728,0,0,1,0,13.818V8a.728.728,0,0,1,.727-.727h2.91A.728.728,0,0,1,4.364,8v5.818A.728.728,0,0,1,3.637,14.546ZM1.454,8.727v4.364H2.909V8.727Z" transform="translate(0 0)"/>
    </clipPath>
    <filter id="columnChart_Icon_Color" x="-60.727" y="-43.455" width="137.455" height="137.455" filterUnits="userSpaceOnUse">
      <feOffset dy="18" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="20" result="blur"/>
      <feFlood flood-color="#6b67d2" flood-opacity="0.349"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="Group_187" data-name="Group 187" transform="translate(0 0)">
    <g id="Group_187-2" data-name="Group 187" clip-path="url(#columnChart-clip-path)">
      <g transform="matrix(1, 0, 0, 1, 0, 0)" filter="url(#columnChart_Icon_Color)">
        <rect id="_Icon_Color-2" data-name="↳ Icon Color" width="17.455" height="17.455" transform="translate(-0.73 -1.45)" fill="#02aacf"/>
      </g>
    </g>
  </g>`,
  arrowUp2: '<path d="M7.5,0,15,10.5H0Z" />',
  arrowDown2:
    '<path d="M7.5,0,15,10.5H0Z" transform="translate(15 10.5) rotate(180)" />',
  clock: `<g id="clock_7" data-name="clock 7" transform="translate(-1 -1)">
    <path id="Combined_Shape" data-name="Combined Shape" d="M3.222,18.778A11,11,0,1,1,18.778,3.222,11,11,0,1,1,3.222,18.778ZM2,11a9,9,0,1,0,9-9A9.01,9.01,0,0,0,2,11Zm11.293,3.707-3-3A1,1,0,0,1,10,11V5a1,1,0,1,1,2,0v5.585l2.707,2.707a1,1,0,1,1-1.414,1.414Z" transform="translate(1 1)" />
  </g>`,
  //cup's viewbox: 0 0 14.667 12
  cup: `<g id="Shape" transform="translate(2.667)" fill="none">
    <path d="M 1.999997138977051 -3.814697265625e-06 L 7.33333683013916 -3.814697265625e-06 C 8.437907218933105 -3.814697265625e-06 9.33333683013916 0.8954267501831055 9.33333683013916 1.999996662139893 L 9.33333683013916 4.666666507720947 C 9.33333683013916 7.243996620178223 7.243997097015381 9.33333683013916 4.666666984558105 9.33333683013916 C 2.08933687210083 9.33333683013916 -2.86102294921875e-06 7.243996620178223 -2.86102294921875e-06 4.666666507720947 L -2.86102294921875e-06 1.999996662139893 C -2.86102294921875e-06 0.8954267501831055 0.8954267501831055 -3.814697265625e-06 1.999997138977051 -3.814697265625e-06 Z M 4.666666984558105 7.999996662139893 C 6.507616996765137 7.999996662139893 7.999997138977051 6.507616519927979 7.999997138977051 4.666666507720947 L 7.999997138977051 1.999996662139893 C 7.999997138977051 1.631806373596191 7.701527118682861 1.333336353302002 7.33333683013916 1.333336353302002 L 1.999997138977051 1.333336353302002 C 1.63180685043335 1.333336353302002 1.33333683013916 1.631806373596191 1.33333683013916 1.999996662139893 L 1.33333683013916 4.666666507720947 C 1.33333683013916 6.507616519927979 2.825716972351074 7.999996662139893 4.666666984558105 7.999996662139893 Z" stroke="none" fill="#fff"/>
  </g>
  <g id="Shape-2" data-name="Shape" transform="translate(0 1.333)" fill="none">
    <path d="M 1.99999988079071 -2.86102294921875e-06 L 4 -2.86102294921875e-06 L 4 5.33333683013916 L 2.666669845581055 5.33333683013916 C 1.193909883499146 5.33333683013916 -2.384185791015625e-07 4.139427185058594 -2.384185791015625e-07 2.666666984558105 L -2.384185791015625e-07 1.999996900558472 C -2.384185791015625e-07 0.8954267501831055 0.8954298496246338 -2.86102294921875e-06 1.99999988079071 -2.86102294921875e-06 Z M 2.666669845581055 3.999997138977051 L 2.666669845581055 1.333337068557739 L 1.99999988079071 1.333337068557739 C 1.631809949874878 1.333337068557739 1.333329916000366 1.631807088851929 1.333329916000366 1.999996900558472 L 1.333329916000366 2.666666984558105 C 1.333329916000366 3.40304708480835 1.93028998374939 3.999997138977051 2.666669845581055 3.999997138977051 Z" stroke="none" fill="#fff"/>
  </g>
  <g id="Shape-3" data-name="Shape" transform="translate(0 1.333)" fill="none">
    <path d="M 10.66666603088379 -2.86102294921875e-06 L 12.66666603088379 -2.86102294921875e-06 C 13.77123641967773 -2.86102294921875e-06 14.66666603088379 0.8954267501831055 14.66666603088379 1.999996900558472 L 14.66666603088379 2.666666984558105 C 14.66666603088379 4.139427185058594 13.47275638580322 5.33333683013916 11.99999618530273 5.33333683013916 L 10.66666603088379 5.33333683013916 L 10.66666603088379 -2.86102294921875e-06 Z M 11.99999618530273 3.999997138977051 C 12.73637580871582 3.999997138977051 13.33333587646484 3.40304708480835 13.33333587646484 2.666666984558105 L 13.33333587646484 1.999996900558472 C 13.33333587646484 1.631807088851929 13.03485584259033 1.333337068557739 12.66666603088379 1.333337068557739 L 11.99999618530273 1.333337068557739 L 11.99999618530273 3.999997138977051 Z" stroke="none" fill="#fff"/>
  </g>
  <g id="Shape-4" data-name="Shape" transform="translate(4.667 8)" fill="none">
    <path d="M 2.666666746139526 0 C 3.034856796264648 0 3.33333683013916 0.2984800338745117 3.33333683013916 0.6666700839996338 L 3.33333683013916 2.666669845581055 L 4.666666984558105 2.666669845581055 C 5.034856796264648 2.666669845581055 5.33333683013916 2.965139865875244 5.33333683013916 3.333329916000366 C 5.33333683013916 3.701519966125488 5.034856796264648 4 4.666666984558105 4 L 0.6666665077209473 4 C 0.2984766960144043 4 -3.337860107421875e-06 3.701519966125488 -3.337860107421875e-06 3.333329916000366 C -3.337860107421875e-06 2.965139865875244 0.2984766960144043 2.666669845581055 0.6666665077209473 2.666669845581055 L 1.999996662139893 2.666669845581055 L 1.999996662139893 0.6666700839996338 C 1.999996662139893 0.2984800338745117 2.298476696014404 0 2.666666746139526 0 Z" stroke="none" fill="#fff"/>
  </g>`,
  payment: `
  <path d="M6 9.75V12.75M12 8.25V12.75M9 5.25V12.75M5.85 15.75H12.15C13.4101 15.75 14.0402 15.75 14.5215 15.5048C14.9448 15.289 15.289 14.9448 15.5048 14.5215C15.75 14.0402 15.75 13.4101 15.75 12.15V5.85C15.75 4.58988 15.75 3.95982 15.5048 3.47852C15.289 3.05516 14.9448 2.71095 14.5215 2.49524C14.0402 2.25 13.4101 2.25 12.15 2.25H5.85C4.58988 2.25 3.95982 2.25 3.47852 2.49524C3.05516 2.71095 2.71095 3.05516 2.49524 3.47852C2.25 3.95982 2.25 4.58988 2.25 5.85V12.15C2.25 13.4101 2.25 14.0402 2.49524 14.5215C2.71095 14.9448 3.05516 15.289 3.47852 15.5048C3.95982 15.75 4.58988 15.75 5.85 15.75Z" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>`,
  trash: `
  <path d="M4 6H20L18.4199 20.2209C18.3074 21.2337 17.4512 22 16.4321 22H7.56786C6.54876 22 5.69264 21.2337 5.5801 20.2209L4 6Z" stroke="#ea3942" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M7.34491 3.14716C7.67506 2.44685 8.37973 2 9.15396 2H14.846C15.6203 2 16.3249 2.44685 16.6551 3.14716L18 6H6L7.34491 3.14716Z" stroke="#ea3942" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M2 6H22" stroke="#ea3942" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M10 11V16" stroke="#ea3942" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M14 11V16" stroke="#ea3942" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>`,
  edit: `
  <svg xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape" xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd" xmlns="http://www.w3.org/2000/svg" xmlns:svg="http://www.w3.org/2000/svg" viewBox="0 0 8.4666595 10.58333275" version="1.1" x="0px" y="0px"><g transform="translate(-461.61038,48.943169)"><path style="paint-order:stroke fill markers;" d="m 467.15319,-48.412969 a 0.26460996,0.26460996 0 0 0 -0.18655,0.077 l -4.11706,4.115511 a 0.26460996,0.26460996 0 0 0 -0.076,0.187069 v 1.496033 a 0.26460996,0.26460996 0 0 0 0.26355,0.265616 h 1.49603 a 0.26460996,0.26460996 0 0 0 0.18758,-0.07803 l 4.11707,-4.114994 a 0.26460996,0.26460996 0 0 0 0,-0.375171 l -1.4981,-1.496033 a 0.26460996,0.26460996 0 0 0 -0.18656,-0.077 z m -10e-4,0.637687 1.12345,1.122929 -3.85197,3.851445 h -1.12086 v -1.122929 z m -3.9548,6.23993 a 0.2645835,0.2645835 0 0 0 -0.26355,0.265616 0.2645835,0.2645835 0 0 0 0.26355,0.264067 h 5.2927 a 0.2645835,0.2645835 0 0 0 0.26407,-0.264067 0.2645835,0.2645835 0 0 0 -0.26407,-0.265616 z" fill="currentColor" stroke="none"/></g></svg>`,
  arrowRight: `
  <svg aria-hidden="true" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd"></path></svg>`,
  facebook:`
  <path d="M22.675 0h-21.35C.595 0 0 .594 0 1.326v21.348C0 23.405.595 24 1.326 24H12.82v-9.294H9.692v-3.622h3.128V8.41c0-3.1 1.894-4.788 4.659-4.788 1.325 0 2.463.099 2.796.144v3.24l-1.918.001c-1.504 0-1.796.715-1.796 1.763v2.31h3.59l-.467 3.622h-3.123V24h6.117C23.405 24 24 23.405 24 22.674V1.326C24 .594 23.405 0 22.675 0z"/>
  `,
  admin:`
  <svg class="svg-icon" style="width: 1em; height: 1em;vertical-align: middle;overflow: hidden;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"><path d="M537.216 1003.776h-427.52c-27.072 0-52.8-11.456-70.592-31.296a76.352 76.352 0 0 1-19.904-58.88l-0.192-60.16a32 32 0 0 1 64 0v63.872c-0.384 5.44 0.576 8.832 3.968 12.608a30.656 30.656 0 0 0 22.72 9.856h427.52a32 32 0 0 1 0 64z"  /><path d="M50.752 934.144a32 32 0 0 1-32-32v-60.032a457.536 457.536 0 0 1 277.952-421.12 32.192 32.192 0 0 1 42.048 16.832 32.128 32.128 0 0 1-16.896 42.112 393.408 393.408 0 0 0-239.104 362.24v60.032a32 32 0 0 1-32 31.936z"  /><path d="M460.736 514.496a247.36 247.36 0 0 1-247.104-247.104A247.424 247.424 0 0 1 460.736 20.224a247.424 247.424 0 0 1 247.168 247.168 247.36 247.36 0 0 1-247.168 247.104z m0-430.272a183.36 183.36 0 0 0-183.104 183.168 183.296 183.296 0 0 0 183.104 183.104 183.36 183.36 0 0 0 183.168-183.104 183.36 183.36 0 0 0-183.168-183.168zM778.56 829.376c-42.944 0-77.888-34.944-77.888-77.888s34.944-77.888 77.888-77.888 77.888 34.944 77.888 77.888-34.944 77.888-77.888 77.888z m0-110.976a33.216 33.216 0 1 0 0.064 66.368 33.216 33.216 0 0 0-0.064-66.368z"  /><path d="M834.56 960.384a22.4 22.4 0 0 1-22.144-19.2 35.2 35.2 0 0 0-34.56-30.208c-17.152 0-32 12.864-34.624 30.016a22.272 22.272 0 0 1-28.736 17.984 217.536 217.536 0 0 1-84.352-49.344 22.4 22.4 0 0 1 1.28-33.728 35.136 35.136 0 0 0 8.32-44.864 35.84 35.84 0 0 0-42.88-15.04 22.4 22.4 0 0 1-29.76-15.936c-3.712-16.448-5.632-32.768-5.632-48.64s1.92-32.192 5.632-48.64a22.144 22.144 0 0 1 11.2-14.72 22.528 22.528 0 0 1 18.496-1.216 34.944 34.944 0 1 0 34.56-59.968 22.336 22.336 0 0 1-8.192-16.64 21.248 21.248 0 0 1 6.976-16.96 217.472 217.472 0 0 1 84.352-49.28 22.4 22.4 0 0 1 28.672 18.048 35.328 35.328 0 0 0 34.624 29.952 35.2 35.2 0 0 0 34.56-30.208 22.4 22.4 0 0 1 28.608-18.304 217.6 217.6 0 0 1 85.248 49.088 22.336 22.336 0 0 1-1.472 34.048 34.88 34.88 0 0 0-8.896 45.056 35.84 35.84 0 0 0 43.776 14.784 22.592 22.592 0 0 1 30.208 15.552 214.336 214.336 0 0 1 0 98.816 22.592 22.592 0 0 1-30.336 15.552 35.456 35.456 0 0 0-43.648 14.72 35.008 35.008 0 0 0 8.96 45.184 22.4 22.4 0 0 1 1.408 34.048 218.368 218.368 0 0 1-85.312 49.024 19.584 19.584 0 0 1-6.336 1.024z m-56.768-94.144c30.144 0 57.024 17.216 70.592 42.752 10.624-4.736 20.864-10.624 30.528-17.536a79.232 79.232 0 0 1-1.728-82.56 81.024 81.024 0 0 1 72.832-39.744 164.16 164.16 0 0 0 0-35.264 81.28 81.28 0 0 1-72.768-39.68 80.128 80.128 0 0 1 1.792-82.496 166.336 166.336 0 0 0-30.656-17.664c-13.504 25.6-40.448 42.816-70.592 42.816-29.824 0-56.64-17.024-70.336-42.304a179.072 179.072 0 0 0-30.336 17.792c15.104 24.384 16.256 55.68 1.28 81.792-14.528 25.216-43.008 42.432-71.296 39.872a147.776 147.776 0 0 0 0 35.008 83.84 83.84 0 0 1 71.296 39.744c15.04 26.24 13.888 57.536-1.216 81.92 9.472 6.976 19.648 12.928 30.336 17.792 13.696-25.216 40.512-42.24 70.272-42.24z"/></svg>`,
  sort:`<svg xmlns="http://www.w3.org/2000/svg" xmlns:v="https://svgstorm.com"
viewBox = "0 0 158 158"
width="158"
height="158">
<g fill="#000000" fill-opacity="0.03" stroke="None">
<path d="
M 0.00 38.00 
C 0.50 38.00 1.00 38.00 1.00 38.00
C 3.01 20.53 19.72 3.10 38.00 1.00
C 38.16 0.94 38.00 0.00 38.00 0.00
L 0.00 0.00
L 0.00 38.00 Z"/>
</g>
<g fill="#000000" fill-opacity="0.58" stroke="None">
<path d="
M 38.00 1.00 
C 39.57 0.61 42.22 1.94 43.00 0.00
L 38.00 0.00
C 38.00 0.00 38.16 0.94 38.00 1.00 Z"/>
</g>
<g fill="#000000" fill-opacity="0.99" stroke="None">
<path d="
M 38.00 1.00 
C 19.72 3.10 3.01 20.53 1.00 38.00
C 0.61 39.57 1.94 42.22 0.00 43.00
L 0.00 117.00
C 2.48 136.20 18.87 155.05 38.00 157.00
C 39.57 157.39 42.22 156.06 43.00 158.00
L 117.00 158.00
C 136.74 155.89 156.54 138.17 157.00 118.00
C 157.11 117.00 156.53 115.27 158.00 115.00
L 158.00 43.00
C 156.25 42.17 157.34 39.56 157.00 38.00
C 154.95 18.04 135.57 2.34 117.00 0.00
L 43.00 0.00
C 42.22 1.94 39.57 0.61 38.00 1.00
M 87.00 51.00 
C 85.34 56.58 77.36 65.02 71.00 66.00
C 70.00 62.66 66.37 58.94 63.00 58.00
C 63.65 72.58 61.69 87.86 64.00 102.00
C 56.96 105.57 37.65 107.37 42.00 93.00 C 42.00 81.33 42.00 69.67 42.00 58.00
C 37.91 60.38 35.57 66.03 31.00 66.00
C 28.63 60.30 21.95 56.16 18.00 52.00
C 28.12 39.74 42.08 29.72 51.00 17.00
C 63.58 24.40 75.54 39.81 87.00 51.00
M 116.00 100.00 
C 120.09 97.62 122.43 91.97 127.00 92.00
C 128.03 99.21 143.26 102.61 137.75 109.75 C 126.34 119.62 116.31 134.36 104.00 141.00
C 94.79 128.55 76.93 118.03 71.00 104.00
C 76.66 100.39 80.37 92.23 87.00 92.00
C 88.00 95.34 91.63 99.06 95.00 100.00
C 94.35 85.42 96.31 70.14 94.00 56.00
C 100.43 54.70 120.65 50.55 116.00 65.00 C 116.00 76.67 116.00 88.33 116.00 100.00 Z"/>
</g>
<g fill="#000000" fill-opacity="0.03" stroke="None">
<path d="
M 157.00 38.00 
C 157.02 38.10 158.00 38.00 158.00 38.00
L 158.00 0.00
L 117.00 0.00
C 135.57 2.34 154.95 18.04 157.00 38.00 Z"/>
</g>
<g fill="#000000" fill-opacity="0.09" stroke="None">
<path d="
M 87.00 51.00 
C 75.54 39.81 63.58 24.40 51.00 17.00
C 42.08 29.72 28.12 39.74 18.00 52.00
C 21.95 56.16 28.63 60.30 31.00 66.00
C 35.57 66.03 37.91 60.38 42.00 58.00
C 42.00 69.67 42.00 81.33 42.00 93.00 C 37.65 107.37 56.96 105.57 64.00 102.00
C 61.69 87.86 63.65 72.58 63.00 58.00
C 66.37 58.94 70.00 62.66 71.00 66.00
C 77.36 65.02 85.34 56.58 87.00 51.00
M 54.00 21.00 
C 63.51 31.33 73.71 41.55 84.00 51.00
C 80.97 55.47 75.98 58.57 73.00 63.00
C 68.19 59.64 64.77 54.31 60.00 51.00
C 60.62 67.60 58.73 84.82 61.00 101.00
C 55.67 101.00 50.33 101.00 45.00 101.00
C 45.66 84.09 43.68 66.47 46.00 50.00
C 40.98 52.59 35.54 57.98 33.00 63.00
C 28.86 60.30 26.09 55.65 22.00 53.00
C 30.87 41.64 42.66 29.84 54.00 21.00 Z"/>
</g>
<g fill="#000000" fill-opacity="0.98" stroke="None">
<path d="
M 54.00 21.00 
C 42.66 29.84 30.87 41.64 22.00 53.00
C 26.09 55.65 28.86 60.30 33.00 63.00
C 35.54 57.98 40.98 52.59 46.00 50.00
C 43.68 66.47 45.66 84.09 45.00 101.00
C 50.33 101.00 55.67 101.00 61.00 101.00
C 58.73 84.82 60.62 67.60 60.00 51.00
C 64.77 54.31 68.19 59.64 73.00 63.00
C 75.98 58.57 80.97 55.47 84.00 51.00
C 73.71 41.55 63.51 31.33 54.00 21.00 Z"/>
</g>
<g fill="#000000" fill-opacity="0.59" stroke="None">
<path d="
M 0.00 43.00 
C 1.94 42.22 0.61 39.57 1.00 38.00
C 1.00 38.00 0.50 38.00 0.00 38.00
L 0.00 43.00 Z"/>
</g>
<g fill="#000000" fill-opacity="0.58" stroke="None">
<path d="
M 158.00 43.00 
L 158.00 38.00
C 158.00 38.00 157.02 38.10 157.00 38.00
C 157.34 39.56 156.25 42.17 158.00 43.00 Z"/>
</g>
<g fill="#000000" fill-opacity="0.11" stroke="None">
<path d="
M 116.00 100.00 
C 116.00 88.33 116.00 76.67 116.00 65.00 C 120.65 50.55 100.43 54.70 94.00 56.00
C 96.31 70.14 94.35 85.42 95.00 100.00
C 91.63 99.06 88.00 95.34 87.00 92.00
C 80.37 92.23 76.66 100.39 71.00 104.00
C 76.93 118.03 94.79 128.55 104.00 141.00
C 116.31 134.36 126.34 119.62 137.75 109.75 C 143.26 102.61 128.03 99.21 127.00 92.00
C 122.43 91.97 120.09 97.62 116.00 100.00
M 113.00 57.00 
C 113.00 74.00 113.00 91.00 113.00 108.00
C 117.16 104.08 122.84 100.19 125.00 95.00
C 129.14 97.70 131.91 102.35 136.00 105.00
C 128.06 116.74 115.07 125.80 106.00 137.00
C 94.43 128.56 84.21 116.40 74.00 106.00
C 78.35 104.06 83.11 99.36 85.00 95.00
C 89.81 98.36 93.23 103.69 98.00 107.00
C 97.38 90.40 99.27 73.18 97.00 57.00
C 102.33 57.00 107.67 57.00 113.00 57.00 Z"/>
</g>
<g fill="#000000" fill-opacity="0.98" stroke="None">
<path d="
M 113.00 57.00 
C 107.67 57.00 102.33 57.00 97.00 57.00
C 99.27 73.18 97.38 90.40 98.00 107.00
C 93.23 103.69 89.81 98.36 85.00 95.00
C 83.11 99.36 78.35 104.06 74.00 106.00
C 84.21 116.40 94.43 128.56 106.00 137.00
C 115.07 125.80 128.06 116.74 136.00 105.00
C 131.91 102.35 129.14 97.70 125.00 95.00
C 122.84 100.19 117.16 104.08 113.00 108.00
C 113.00 91.00 113.00 74.00 113.00 57.00 Z"/>
</g>
<g fill="#000000" fill-opacity="0.73" stroke="None">
<path d="
M 157.00 118.00 
C 157.02 117.89 158.00 118.00 158.00 118.00
L 158.00 115.00
C 156.53 115.27 157.11 117.00 157.00 118.00 Z"/>
</g>
<g fill="#000000" fill-opacity="0.03" stroke="None">
<path d="
M 0.00 158.00 
L 38.00 158.00
C 38.00 157.50 38.00 157.00 38.00 157.00
C 18.87 155.05 2.48 136.20 0.00 117.00
L 0.00 158.00 Z"/>
</g>
<g fill="#000000" fill-opacity="0.02" stroke="None">
<path d="
M 117.00 158.00 
L 158.00 158.00
L 158.00 118.00
C 158.00 118.00 157.02 117.89 157.00 118.00
C 156.54 138.17 136.74 155.89 117.00 158.00 Z"/>
</g>
<g fill="#000000" fill-opacity="0.58" stroke="None">
<path d="
M 38.00 158.00 
L 43.00 158.00
C 42.22 156.06 39.57 157.39 38.00 157.00
C 38.00 157.00 38.00 157.50 38.00 158.00 Z"/>
</g>
</svg>
`,
  dollarSign: '<path d="M12 2v1m0 18v1m1-20H9a5 5 0 0 0-5 5v0a5 5 0 0 0 5 5h2a1 1 0 0 1 1 1v0a1 1 0 0 1-1 1H8m4-15H9a5 5 0 0 0-5 5v0a5 5 0 0 0 5 5h2a1 1 0 0 1 1 1v0a1 1 0 0 1-1 1H8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" fill="none"/>',
  repeat: '<path d="M4 12a8 8 0 0 1 8-8V2l3 3-3 3V6a6 6 0 0 0-6 6H4zm16 0a8 8 0 0 1-8 8v2l-3-3 3-3v2a6 6 0 0 0 6-6h2z" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" fill="currentColor"/>',
}

export default Icons
