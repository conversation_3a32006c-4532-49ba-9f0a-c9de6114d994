import { create } from 'zustand'
import StorageRepository from 'domain/repositories/storage_repository'
import Chain from 'domain/value_objects/chain'
import { toast } from 'react-toastify'
import { getContractAddress } from 'lib/utils'

const storageRepository = new StorageRepository()

type State = {
  activeChain: Chain | null;
};

type Action = {
  setChain: (chain: Chain) => void;
  getChain: () => void;
};

export const ChainStateProvider = create<State & Action>((set, getState) => ({
  activeChain: null,
  setChain: (chain: Chain) => {
    try {
      set({ activeChain: chain })
      storageRepository.setValue('activeChain', JSON.stringify(chain))
    } catch (error) {
      toast.error('An error occurred while adding the asset to the cart')
    }
  },
  getChain: () => {
    const state = getState()
    if (state.activeChain === null) {
      const activeChain = storageRepository.getValue('activeChain')
      if (activeChain !== null) {
        set({ activeChain: JSON.parse(activeChain) })
      } else {
        set({ activeChain: Chain.ethereum() })
        storageRepository.setValue(
          'activeChain',
          JSON.stringify(Chain.ethereum())
        )
      }
    }
    return
  },
  getContractAddresses: () => {
    const state = getState()
    if (state.activeChain !== null) {
      return getContractAddress(state.activeChain.getId)
    }
  },
}))
