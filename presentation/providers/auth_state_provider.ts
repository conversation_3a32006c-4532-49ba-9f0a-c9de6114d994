import { create } from 'zustand'

import User from 'domain/models/user'
import MeUseCase from 'presentation/use_cases/me_use_case'

type State = {
  me: User | null | undefined
}

type Action = {
  init: () => void
  reload: () => void
  logout: () => void
}

const meUseCase = new MeUseCase()

export const AuthStateProvider = create<State & Action>()((set, getState) => ({
  me: null,
  init: async () => {
    const state = getState()
    if (state.me !== undefined) {
      set({ me: state.me })
    }
    try {
      const me = await meUseCase.me()
      set({ me: me })
    } catch(e) {
      set({ me: null })
    }
  },
  reload: async () => {
    try {
      const me = await meUseCase.me()
      set({ me: me })
    } catch(e) {
      set({ me: null })
    }
  },
  logout: async () => {
    try {
      set({ me: null })
    } catch(e) {
      set({ me: null })
    }
  },
}))
