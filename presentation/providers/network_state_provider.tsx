import { createContext, useContext, useState, ReactNode, useEffect } from 'react'
import Chain from 'domain/value_objects/chain'
import { create } from 'zustand' // Di chuyển import lên đầu file

type NetworkState = {
  selectedNetwork: any
  setSelectedNetwork: (network: any) => void
}

// Create a zustand store for global state management
type NetworkStateStore = {
  network: any
  setNetwork: (network: any) => void
  init: () => void
}

export const NetworkStateProvider = create<NetworkStateStore>((set) => ({
  network: null,
  setNetwork: (network: any) => {
    set({ network })
    // Lưu vào localStorage khi network thay đổi
    if (typeof window !== 'undefined') {
      localStorage.setItem('selectedNetwork', JSON.stringify(network))
    }
  },
  init: () => {
    // Khởi tạo từ localStorage nếu có
    if (typeof window !== 'undefined') {
      const savedNetwork = localStorage.getItem('selectedNetwork')
      if (savedNetwork) {
        try {
          set({ network: JSON.parse(savedNetwork) })
        } catch (error) {
          console.error('Failed to parse saved network', error)
        }
      }
    }
  },
}))

const NetworkContext = createContext<NetworkState | undefined>(undefined)

export const NetworkProvider = ({ children }: { children: ReactNode }) => {
  const [selectedNetwork, setSelectedNetwork] = useState<any>(null)

  // Initialize with default network or from localStorage if available
  useEffect(() => {
    const savedNetwork = localStorage.getItem('selectedNetwork')
    if (savedNetwork) {
      try {
        setSelectedNetwork(JSON.parse(savedNetwork))
      } catch (error) {
        console.error('Failed to parse saved network', error)
      }
    }
  }, [])

  // Save to localStorage when network changes
  useEffect(() => {
    if (selectedNetwork) {
      localStorage.setItem('selectedNetwork', JSON.stringify(selectedNetwork))
    }
  }, [selectedNetwork])

  return (
    <NetworkContext.Provider value={{ selectedNetwork, setSelectedNetwork }}>
      {children}
    </NetworkContext.Provider>
  )
}

export const useNetworkState = () => {
  const context = useContext(NetworkContext)
  if (context === undefined) {
    throw new Error('useNetworkState must be used within a NetworkProvider')
  }
  return context
}