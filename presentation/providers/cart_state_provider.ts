import { create } from 'zustand'
import StorageRepository from 'domain/repositories/storage_repository'
import Asset from 'domain/models/asset'
import { toast } from 'react-toastify'

const storageRepository = new StorageRepository()

type State = {
  cart: Asset[];
};

type Action = {
  setCart: (asset: Asset) => void;
  getCart: () => void;
  remove: (asset: Asset) => void;
  removeAll: () => void;
};

export const CartStateProvider = create<State & Action>((set, getState) => ({
  cart: [],
  setCart: (asset: Asset) => {
    try {
      const state = getState()
      const newCart = state.cart.filter((item) => item.id !== asset.id)
      newCart.push(asset)
      set({ cart: newCart })
      storageRepository.setValue('cart', JSON.stringify(newCart))
      toast.success('Asset added to cart')
    } catch (error) {
      toast.error('An error occurred while adding the asset to the cart')
    }
  },
  getCart: () => {
    const state = getState()
    if (state.cart.length === 0) {
      const cart = storageRepository.getValue('cart')
      if (cart !== null) {
        set({ cart: JSON.parse(cart) })
      }
    }
    return
  },
  remove: (asset: Asset) => {
    try {
      const state = getState()
      const newCart = state.cart.filter((item) => item.id !== asset.id)
      set({ cart: newCart })
      storageRepository.setValue('cart', JSON.stringify(newCart))
      toast.success('Asset removed from cart')
    } catch (error) {
      toast.error('An error occurred while removing the asset from the cart')
    }
  },
  removeAll: () => {
    try {
      set({ cart: [] })
      storageRepository.clearValue('cart')
      toast.success('Cart cleared')
    } catch (error) {
      toast.error('An error occurred while clearing the cart')
    }
  },
}))
