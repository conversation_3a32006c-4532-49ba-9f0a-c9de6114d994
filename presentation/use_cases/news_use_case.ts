import NewsRepository from 'domain/repositories/news_repository'
import NewsModel from 'domain/models/news'

export default class NewsUseCase {
  private repository: NewsRepository

  constructor () {
    this.repository = new NewsRepository()
  }

  getNews (): Promise<NewsModel[]> {
    return this.repository.getNews()
  }
  editNews (news: any): Promise<NewsModel[]> {
    return this.repository.editNews(news)
  }
}