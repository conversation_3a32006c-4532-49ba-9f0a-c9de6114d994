import 'reflect-metadata'

import LoginSuccess from 'domain/models/login_success'
import UserRepository from 'domain/repositories/user_repository'
import User from 'domain/models/user'

export default class AuthUseCase {
  private userRepository: UserRepository

  constructor () {
    this.userRepository = new UserRepository()
  }

  login (publicAddress: string, signature: string): Promise<LoginSuccess> {
    return new Promise((resolve, reject) => {
      this.userRepository
        .login(publicAddress, signature)
        .then((loginSuccess) => {
          resolve(loginSuccess)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  logout (): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.userRepository
        .logout()
        .then((isLoggedOut) => {
          resolve(isLoggedOut)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  create (publicAddress: string, signature: string): Promise<User> {
    return new Promise((resolve, reject) => {
      this.userRepository
        .findByPublicAddress(publicAddress)
        .then((user) => {
          this.login(publicAddress, signature).then(() => {
            resolve(user)
          })
        })
        .catch(() => {
          this.userRepository
            .create(publicAddress)
            .then((user) => {
              this.login(publicAddress, signature).then(() => {
                resolve(user)
              })
            })
            .catch((e) => {
              reject(e)
            })
        })
    })
  }

  linkWallet (publicAddress: string, signature:string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.userRepository.linkWallet(publicAddress, signature)
        .then((data) => {
          resolve(data)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
}
