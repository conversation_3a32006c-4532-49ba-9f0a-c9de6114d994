import ContractRepository from '../../domain/repositories/contract_repository'
import {Query} from '../../domain/repositories/contract_repository/query'
import Contract from '../../domain/models/contract'
import Chain from '../../domain/value_objects/chain'

export default class ContractUseCase {

  private repository: ContractRepository

  constructor () {
    this.repository = new ContractRepository()
  }

  findById (id: string): Promise<Contract> {
    return this.repository.findById(id)
  }

  findByChainAndAddress (chain: Chain, address: string): Promise<Contract> {
    return this.repository.findByChainAndAddress(chain, address)
  }

  search (query: Query): Promise<Contract[]> {
    return this.repository.search(query)
  }

  totalCount (query: Query): Promise<number> {
    return this.repository.totalCount(query)
  }

}
