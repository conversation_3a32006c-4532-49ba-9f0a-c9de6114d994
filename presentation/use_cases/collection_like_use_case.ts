import CollectionLikeRepository from '../../domain/repositories/collection_like_repository'
import {Query} from '../../domain/repositories/collection_like_repository/query'
import CollectionLike from '../../domain/models/collection_like'

export default class CollectionLikeUseCase {

  private repository: CollectionLikeRepository

  constructor () {
    this.repository = new CollectionLikeRepository()
  }

  search (query: Query): Promise<CollectionLike[]> {
    return this.repository.search(query)
  }

  totalCount (query: Query): Promise<number> {
    return this.repository.totalCount(query)
  }

  collectionIds (query: Query): Promise<string[]> {
    return this.repository.collectionIds(query)
  }

}
