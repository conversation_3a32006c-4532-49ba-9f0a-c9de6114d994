import CollectionLike from '../../domain/models/collection_like'
import CollectionWatchRepository from '../../domain/repositories/collection_watch_repository'
import {Query} from '../../domain/repositories/collection_watch_repository/query'

export default class CollectionWatchUseCase {

  private repository: CollectionWatchRepository

  constructor () {
    this.repository = new CollectionWatchRepository()
  }

  search (query: Query): Promise<CollectionLike[]> {
    return this.repository.search(query)
  }

  totalCount (query: Query): Promise<number> {
    return this.repository.totalCount(query)
  }

  collectionIds (query: Query): Promise<string[]> {
    return this.repository.collectionIds(query)
  }

}
