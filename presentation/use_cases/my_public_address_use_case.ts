import UserPublicAddressRepository from '../../domain/repositories/user_public_address_repository'

export default class MyPublicAddressUseCase {

  private repository: UserPublicAddressRepository

  constructor () {
    this.repository = new UserPublicAddressRepository()
  }

  mine (): Promise<string[]> {
    return this.repository.search()
  }

  create (publicAddress: string, signature: string): Promise<string> {
    return this.repository.create(publicAddress, signature)
  }

}
