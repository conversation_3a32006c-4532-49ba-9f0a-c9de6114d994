import UserRepository from '../../domain/repositories/user_repository'
import User from '../../domain/models/user'
import LoginStatus from '../../domain/models/login_status'

export default class MeUseCase {

  private repository: UserRepository

  constructor () {
    this.repository = new UserRepository()
  }

  me () : Promise<User> {
    return this.repository.me()
  }

  loginStatus (): Promise<LoginStatus> {
    return this.repository.loginStatus()
  }

  logout (): Promise<boolean> {
    return this.repository.logout()
  }

  update (user: User): Promise<User> {
    return this.repository.update(user)
  }

  updateEmail (email: string): Promise<User> {
    return this.repository.updateEmail(email)
  }

  updateLanguage (languageName: string): Promise<User> {
    return this.repository.updateLanguage(languageName)
  }

  loginWithGoogle (): Promise<void> {
    return this.repository.loginWithGoogle().then((url) => {
      window.open(url, '_self', 'noopener,noreferrer')
    })
  }
  
  setTokenUser (token: string): void {
    return this.repository.setTokenUser(token)
  }
}
