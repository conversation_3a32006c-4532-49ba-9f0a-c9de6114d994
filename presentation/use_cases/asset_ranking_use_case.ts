import Asset from '../../domain/models/asset'
import { Query } from '../../domain/repositories/asset_ranking/query'
import AssetRankingRepository from '../../domain/repositories/asset_ranking_repository'

export default class AssetRankingUseCase {
  private repository: AssetRankingRepository

  constructor () {
    this.repository = new AssetRankingRepository()
  }

  search (query: Query): Promise<Asset[]> {
    return this.repository.search(query)
  }

  totalCount (query: Query): Promise<number> {
    return this.repository.totalCount(query)
  }
} 