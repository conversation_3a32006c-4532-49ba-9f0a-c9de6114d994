import ReportRepository from '../../domain/repositories/report_repository'
import Asset from '../../domain/models/asset'
import AssetReason from '../../domain/models/report/asset_reason'
import Report from '../../domain/models/report'
import Collection from '../../domain/models/collection'
import CollectionReason from '../../domain/models/report/collection_reason'

export default class ReportUseCase {

  private repository: ReportRepository

  constructor () {
    this.repository = new ReportRepository()
  }

  createAssetReport (asset: Asset, reason: AssetReason): Promise<Report> {
    return this.repository.createAssetReport(asset, reason)
  }

  createCollectionReport (collection: Collection, reason: CollectionReason): Promise<Report> {
    return this.repository.createCollectionReport(collection, reason)
  }

}
