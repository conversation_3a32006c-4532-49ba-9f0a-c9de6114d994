import UserRepository from '../../domain/repositories/user_repository'
import LoginSuccess from '../../domain/models/login_success'

export default class LoginUseCase {

  private repository: UserRepository

  constructor () {
    this.repository = new UserRepository()
  }

  login (publicAddress: string, signature: string): Promise<LoginSuccess> {
    return this.repository.login(publicAddress, signature)
  }

}
