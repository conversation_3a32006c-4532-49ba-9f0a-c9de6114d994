import OfferRepository from '../../domain/repositories/offer_repository'
import Offer from '../../domain/models/offer'
import { Query } from '../../domain/repositories/offer_repository/query'
import NftLandContractOfferService from 'domain/services/nft_land_contract/nft_land_contract_offer_service'
import { Status } from 'domain/models/offer/status'
import { ethers } from 'ethers'
import { formatNumber } from 'lib/number'
import { TipInputItem } from '@t2-web/nft_land_js/lib/types'
import ConvertConfig from 'domain/services/utils/convert_config'

export default class OfferUseCase {
  private repository: OfferRepository

  constructor () {
    this.repository = new OfferRepository()
  }

  findById (id: string): Promise<Offer> {
    return this.repository.findById(id)
  }

  search (query: Query): Promise<Offer[]> {
    return this.repository.search(query)
  }

  totalCount (query: Query): Promise<number> {
    return this.repository.totalCount(query)
  }

  save (
    offer: Offer,
    provider: ethers.providers.JsonRpcProvider,
    publicAddress: string,
    contractsConfig: any,
  ): Promise<Offer> {
    return new Promise((resolve, reject) => {
      const nftLandContractOfferService = new NftLandContractOfferService({
        providerOrSigner: provider,
        ...ConvertConfig.convertContractConfig(contractsConfig),
      })
      const createOrUpdateFunction =
        offer.id === undefined
          ? nftLandContractOfferService.create(offer, publicAddress)
          : nftLandContractOfferService.update(offer, publicAddress)
      createOrUpdateFunction
        .then((response: any) => {
          offer.signature = response.signature
          offer.orderParams = JSON.stringify(response.parameters)
          offer.orderHash = response.orderHash
          this.repository
            .save(offer)
            .then((offer: Offer) => resolve(offer))
            .catch((e) => reject(e))
        })
        .catch((e) => reject(e))
    })
  }

  cancel (
    offer: Offer,
    // provider: ethers.providers.JsonRpcProvider,
    // publicAddress: string,
    // domainRegistryAddress: string,
    // marketplaceAddress: string
  ): Promise<Offer> {
    return new Promise((resolve, reject) => {
      // const nftLandContractOfferService = new NftLandContractOfferService(
      //   provider,
      //   domainRegistryAddress,
      //   marketplaceAddress
      // )
      // nftLandContractOfferService
      //   .cancel(offer, publicAddress)
      //   .then(() => {
      offer.status = Status.canceled()
      offer.signature = ''
      offer.orderParams = ''
      this.repository
        .cancel(offer)
        .then((offer: Offer) => resolve(offer))
        .catch((e) => reject(e))
    //     })
    //     .catch((e) => reject(e))
    })
  }

  fulfill (
    offer: Offer,
    provider: ethers.providers.JsonRpcProvider,
    publicAddress: string,
    contractsConfig: any,
    tips?: TipInputItem[]
  ): Promise<Offer> {
    return new Promise((resolve, reject) => {
      const nftLandContractOfferService = new NftLandContractOfferService({
        providerOrSigner: provider,
        ...ConvertConfig.convertContractConfig(contractsConfig),
      })
      nftLandContractOfferService
        .fulfill(offer, publicAddress, undefined, tips)
        .then(() => {
          offer.status = Status.finished()
          this.repository
            .accept(offer)
            .then((offer: Offer) => resolve(offer))
            .catch((e) => reject(e))
        })
        .catch((e) => reject(e))
    })
  }

  mapOffersTable (offers: Offer[], cancelOffer: Function, acceptOffer: Function, userLogin: any)  {
    try {
      let rs = []
      for (let index = 0; index < offers?.length; index++) {
        let offer = offers[index]
        const priceUsd = Number( offer?.price || 0) * (offer?.paymentToken?.priceUsd || 0)
        const priceFloor = Number( offer?.asset?.currentPrice || 0) * (offer?.asset?.currentPaymentToken?.priceUsd || 0)
        let action:any = null
        const isMakeOffer = offer?.user?.id == userLogin?.id || false
        const isOwnerAsset = userLogin?.isOwnerAsset || false
        if (isMakeOffer) {
          action = {
            offer, 
            text: 'Cancel', 
            onClick: cancelOffer,
          }
        } else if(isOwnerAsset && (offer.expiresAt?.diffNow()?.valueOf() || 0) > 0 && offer?.status?.isActive()) {
          action = {
            offer, 
            isAccept: true,
            text: 'Accept', 
            onClick: acceptOffer,
          }
        }

        rs.push({
          unitPrice: {
            price: Number( offer?.price || 0),
            paymentToken: offer?.paymentToken,
          },
          usdPrice: `$${formatNumber(priceUsd)}`,
          quantity: Number(offer?.quantity || 0),
          floorDifference: priceFloor == 0 ? null : {
            percent: formatNumber(Math.abs(priceFloor - priceUsd)/priceFloor * 100),
            text: priceUsd >= priceFloor ? 'above' : 'below',
          },
          userFrom: offer.user,
          expiration: offer.expiresAt?.toRelative() || '',
          action: action,
        })
      }
      console.log('mapOffersTable=======rs', rs)
      return rs
    }catch (err) {
      return []
    }
  }
  mapOffersTableV2 (offers: Offer[], cancelOffer: Function, acceptOffer: Function, type: string, isMe: boolean)  {
    try {
      let rs = []
      for (let index = 0; index < offers?.length; index++) {
        let offer = offers[index]
        const priceUsd = Number( offer?.price || 0) * (offer?.paymentToken?.priceUsd || 0)
        const priceFloor = Number( offer?.asset?.currentPrice || 0) * (offer?.asset?.currentPaymentToken?.priceUsd || 0)
        let action: any = {
          isHide: true,
          offer, 
          text: 'Cancel', 
          onClick: cancelOffer,
        }
        console.log('🚀 ~ OfferUseCase ~ mapOffersTableV2 ~ isMe:', isMe)
        if (type == 'makeOffer' && isMe) {
        
          action = {
            isHide: false,
            offer, 
            text: 'Cancel', 
            onClick: cancelOffer,
          }
        }
        if (type == 'receiveOffer' && isMe  && (offer.expiresAt?.diffNow()?.valueOf() || 0) > 0 && offer?.status?.isActive()) {
          action = {
            isHide: false,
            offer, 
            isAccept: true,
            text: 'Accept', 
            onClick: acceptOffer,
          }
        }
        
        rs.push({
          asset: offer?.asset,
          unitPrice: {
            price: Number( offer?.price || 0),
            paymentToken: offer?.paymentToken,
          },
          usdPrice: `$${formatNumber(priceUsd)}`,
          quantity: Number(offer?.quantity || 0),
          floorDifference: priceFloor == 0 ? null : {
            percent: formatNumber(Math.abs(priceFloor - priceUsd)/priceFloor * 100),
            text: priceUsd >= priceFloor ? 'above' : 'below',
          },
          userFrom: offer.user,
          expiration: offer.expiresAt?.toRelative() || '',
          createdAt: offer.createdAt?.toRelative() || '',
          status: offer?.status ? {
            id: offer?.status?.getId(),
            name: offer?.status?.getName(),
            label: offer?.status?.getLabel(),
          } : null,
          action: action,
        })
      }
      console.log('mapOffersTableV2=======rs', rs)
      return rs
    }catch (err) {
      return []
    }
  }
}
