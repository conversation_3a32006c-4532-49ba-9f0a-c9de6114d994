import ListingRepository from '../../domain/repositories/listing_repository'
import Listing from '../../domain/models/listing'
import ListingOrder from '../../domain/models/listing_order'
import { Query } from '../../domain/repositories/listing_repository/query'
import { QueryOrder } from 'data/datasources/interfaces/listing_datasource/query_order'
import NftLandContractListingService from 'domain/services/nft_land_contract/nft_land_contract_listing_service'
import ConvertConfig from 'domain/services/utils/convert_config'
import { Status } from 'domain/models/listing/status'
import { Status as ListingOrderStatus } from 'domain/models/listing/order_status'
import { formatNumber } from 'lib/number'
import { ethers, BigNumberish } from 'ethers'
import BigNumber from 'bignumber.js'
import { envConfig } from 'presentation/constant/env'
import CreateInputItemConverter from 'domain/services/nft_land_contract/converters/create_input_item'
import NftLandContractAdaptor  from 'domain/external_services/nft_land_contract_adaptor'
import NftLandContractUtils  from 'domain/external_services/nft_land_contract_utils'

export default class ListingUseCase {
  private repository: ListingRepository

  constructor () {
    this.repository = new ListingRepository()
  }

  findById (id: string): Promise<Listing> {
    return this.repository.findById(id)
  }

  search (query: Query): Promise<Listing[]> {
    return this.repository.search(query)
  }

  totalCount (query: Query): Promise<number> {
    return this.repository.totalCount(query)
  }

  save (
    listing: Listing,
    provider: ethers.providers.JsonRpcProvider,
    publicAddress: string,
    contractsConfig: any,
  ): Promise<Listing> {
    return new Promise((resolve, reject) => {
      const nftLandContractListingService = new NftLandContractListingService({
        providerOrSigner: provider,
        ...ConvertConfig.convertContractConfig(contractsConfig),
      })
      const createOrUpdateFunction =
        listing.id === undefined
          ? nftLandContractListingService.create(listing, publicAddress)
          : nftLandContractListingService.update(listing, publicAddress)
      createOrUpdateFunction
        .then((response) => {
          console.log('--------response contract', response)
          listing.signature = response.signature
          listing.orderParams = JSON.stringify(response.parameters)
          listing.orderHash = response.orderHash
          this.repository
            .save(listing)
            .then((listing: Listing) => resolve(listing))
            .catch((e) => reject(e))
        })
        .catch((e) => reject(e))
    })
  }

  cancel (
    listing: Listing,
    provider: ethers.providers.JsonRpcProvider,
    publicAddress: string,
    contractsConfig: any
  ): Promise<Listing> {
    return new Promise((resolve, reject) => {
      const nftLandContractListingService = new NftLandContractListingService({
        providerOrSigner: provider,
        ...ConvertConfig.convertContractConfig(contractsConfig),
      })
      nftLandContractListingService.cancel(listing, publicAddress).then(() => {
        listing.status = Status.canceled()
        this.repository
          .save(listing)
          .then((listing: Listing) => resolve(listing))
          .catch((e) => reject(e))
      }).catch((e) => reject(e))
    })
  }

  fulfill (
    listing: Listing,
    unitsToFill: BigNumberish,
    provider: ethers.providers.JsonRpcProvider,
    publicAddress: string,
    contractsConfig: any
  ): Promise<Listing> {
    return new Promise((resolve, reject) => {
      const nftLandContractListingService = new NftLandContractListingService({
        providerOrSigner: provider,
        ...ConvertConfig.convertContractConfig(contractsConfig),
      })
      nftLandContractListingService.fulfill(listing, unitsToFill, publicAddress,).then(() => {
        // listing.status = Status.finished()
        // this.repository
        //   .save(listing)
        //   .then((listing: Listing) => resolve(listing))
        //   .catch((e) => reject(e))
        resolve(listing)
      }).catch((e) => reject(e))
    })
  }

  claimNft (
    order: ListingOrder,
    provider: ethers.providers.JsonRpcProvider,
    accountAddress: string,
    contractsConfig: any
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      this.repository
        .getSignatureClaimNft(order?.id || '')
        .then((data) => {
          console.log('getSignatureClaimNft--------data', data)
          const nftLandContractListingService = new NftLandContractListingService({
            providerOrSigner: provider,
            ...ConvertConfig.convertContractConfig(contractsConfig),
          })
          nftLandContractListingService.claimNFT(data, accountAddress).then((resTx) => {
            console.log('claimNFT--------resTx', resTx)
            this.repository
              .updateOrder({
                id: order?.id || '',
                statusId: ListingOrderStatus.finished()?.getId(),
                transactionHash: resTx.hash,
              })
              .then((resData: ListingOrder) => resolve(resData))
              .catch((e) => reject(e))
          }).catch((e) => reject(e))
        })
        .catch((e) => reject(e))
    })
  }

  refundToken (
    order: ListingOrder,
    provider: ethers.providers.JsonRpcProvider,
    accountAddress: string,
    contractsConfig: any
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      this.repository
        .getSignatureRefundToken(order?.id || '')
        .then((data) => {
          console.log('getSignatureRefundToken--------data', data)
          const nftLandContractListingService = new NftLandContractListingService({
            providerOrSigner: provider,
            ...ConvertConfig.convertContractConfig(contractsConfig),
          })
          nftLandContractListingService.refundForClaimNFT(data, accountAddress).then((resTx) => {
            console.log('refundToken--------resTx', resTx)
            this.repository
              .updateOrder({
                id: order?.id || '',
                statusId: ListingOrderStatus.refunded()?.getId(),
                refundTransactionHash: resTx.hash,
              })
              .then((resData: ListingOrder) => resolve(resData))
              .catch((e) => reject(e))
          }).catch((e) => reject(e))
        })
        .catch((e) => reject(e))
    })
  }

  buyViaSlash (
    listing: Listing,
    unitsToFill: BigNumberish,
    accountAddress: string,
    domain: string,
  ): Promise<any>  {
    return new Promise((resolve, reject) => {
      this.repository
        .createOrderSlash({
          listingId: listing?.id || '',
          userAddress: accountAddress,
          quantity: Number(unitsToFill),
          domain: domain,
        })
        .then((orderPaymentUrl: string) => {
          console.log('------orderPaymentUrl', orderPaymentUrl)
          window.location.href = orderPaymentUrl
          resolve(true)
        })
        .catch((e) => reject(e))
    })
  }

  findAssetIdByPaymentToken (id: string): Promise<ListingOrder> {
    return this.repository.findAssetIdByPaymentToken(id)
  }

  searchOrder (query: QueryOrder): Promise<ListingOrder[]> {
    return this.repository.searchOrder(query)
  }

  totalCountOrder (query: QueryOrder): Promise<number> {
    return this.repository.totalCountOrder(query)
  }

  async checkOrderGetQuantityToBuy (
    listing: Listing | undefined | null,
    provider: any,
  ): Promise<number>  {
    if(!listing || !listing?.orderHash) {
      throw new Error('Not found listing')
    }
    if(!provider) {
      throw new Error('Not found provider')
    }
    
    const asset = listing.listingAssets[0].asset
    const contracts: any = (envConfig as any).contracts[asset?.chainId || 0]
    
    if (!contracts?.domainRegistryAddress || !contracts?.marketplaceAddress) {
      throw new Error('Domain registry or marketplace address is not support in this network')
    }
    let error = ''
            
    // check status
    const nft = new NftLandContractAdaptor({ 
      providerOrSigner:provider, 
      contractAddress: contracts?.marketplaceAddress, 
      domainRegistryAddress: contracts?.domainRegistryAddress, 
      erc1155SeaDropConfigurerAddress: contracts?.erc1155SeaDropConfigurer,
      erc1155CloneFactoryAddress: contracts?.erc1155SeaDropCloneFactory,
    })
    const orderStatus = await nft.getOrderStatus(listing.orderHash)
    const totalFilled = orderStatus?.totalFilled?.toString()
    const totalSize = orderStatus?.totalSize?.toString()
    const quantityInit = listing?.orderParams?.offer[0].startAmount || 0
    let _quantity =  listing?.listingAssets[0].quantity || 0
    if(BigNumber(totalSize).gt(0) && BigNumber(quantityInit).gt(0) ) {
      const remaining = BigNumber(totalSize).minus(totalFilled).toNumber()
      _quantity = BigNumber(remaining).dividedBy(totalSize).times(quantityInit).toNumber()
    }
        
    if(orderStatus?.isCancelled == true) {
      error = 'This order has been cancelled.'
    }
    if(orderStatus?.isValidated == true && totalFilled == totalSize) {
      error = 'This order is sold out.'
    }
    if(error) {
      throw error
    }
      
    // check amount nft
    const userAddress = listing?.user?.publicAddresses[0]
    if(!userAddress) {
      return _quantity
    }
    
    const quantity = listing.listingAssets[0].quantity
    const contract = new NftLandContractUtils()
    const item = new CreateInputItemConverter()
    let itemToken = null
    if(asset?.contract?.schema.isErc1155()) {
      itemToken = item.tokenBalanceItem(item.ItemType.ERC1155, asset?.address, asset?.tokenId)
    } else if(asset?.contract?.schema.isErc721()) {
      itemToken = item.tokenBalanceItem(item.ItemType.ERC721, asset?.address, asset?.tokenId)
    }
    if(!itemToken) {
      return _quantity
    }
    const amount = await contract.getBalanceOf(userAddress, itemToken, provider)
    if(amount.lt(1)) {
      // error = 'The quantity not enough to buy'
      error = 'Insufficient quantity'
    }
    if(amount.lt(quantity)) {
      _quantity = amount.toNumber()
    }
    if(error) {
      throw error
    }
    return _quantity
  }

  mapListingsTable (listings: Listing[], cancelListing: Function, acceptListing: Function, userLogin: any)  {
    try {
      let rs = []
      for (let index = 0; index < listings?.length; index++) {
        let listing = listings[index]
        const asset = listing?.listingAssets[0]
        const priceUsd = Number( listing?.price || 0) * (listing?.paymentToken?.priceUsd || 0)
        let action:any = null
        const isMakeListing = listing?.user?.id == userLogin?.id || false
        if (isMakeListing) {
          action = {
            listing, 
            text: 'Cancel', 
            onClick: cancelListing,
          }
        } else if(
          // userLogin?.id && 
          listing?.isActiveFixedPrice()
        ) {
          const isPrivateListing = listing?.reservedBuyerAddress ? true : false
          const isPrivateListingForMe = 
          listing?.reservedBuyerAddress && 
          userLogin?.publicAddresses?.length && 
          userLogin.publicAddresses[0].toLowerCase() == listing?.reservedBuyerAddress.toLowerCase()

          action = {
            listing, 
            isAccept: true,
            text: 'Buy', 
            onClick: acceptListing,
            isPrivateListingForMe,
            isPrivateListing,
            buyerAddress: listing?.reservedBuyerAddress,
          }
        }

        rs.push({
          unitPrice: {
            price: Number( listing?.price || 0),
            paymentToken: listing?.paymentToken,
          },
          usdPrice: `$${formatNumber(priceUsd)}`,
          quantity: Number(asset?.quantity || 0),
          userFrom: listing.user,
          expiration: listing.endAt?.toRelative() || '',
          action: action,
        })
      }
      return rs
    }catch (err) {
      return []
    }
  }

  getListingBuyNow (listings: Listing[], userLogin: any, minListingId: any) {
    try {
      if(listings?.length < 1 || !minListingId) return null
      
      const _listings = listings?.filter((listing:Listing) => 
        listing?.isActiveFixedPrice()
      )
      if(userLogin) {
        const privateListing = _listings?.find((listing:Listing) => 
          listing?.reservedBuyerAddress && 
          userLogin?.publicAddresses?.length && 
          userLogin.publicAddresses[0].toLowerCase() == listing?.reservedBuyerAddress.toLowerCase())
        if(privateListing) {
          return privateListing
        } 
      }
      // return _listings?.find((listing:Listing) => !listing.isReservedForBuyer)
      return _listings?.find((listing:Listing) => listing?.id === minListingId)
    } catch (error) {
      return null
    }
  }

  getListingToBid (listings: Listing[]) {
    try {
      if(listings?.length < 1) return null
      return listings.find((listing:Listing) => 
        listing?.isActiveTimedAuction() 
      )
    } catch (error) {
      console.log('-----error', error)
      return null
    }
  }

  mapOrderTable (orders: ListingOrder[], claim: Function, refund: Function)  {
    try {
      let rs = []
      for (let index = 0; index < orders?.length; index++) {
        let order = orders[index],
          listing = order?.listing,
          action:any = null
        if(order?.status?.isActive() && listing?.isActiveFixedPrice()) {
          action = {
            order, 
            text: 'Claim NFT', 
            variantBtn: 'light',
            onClick: claim,
          }
        } else if(order?.status?.isActive()) {
          action = {
            order, 
            text: 'Refund', 
            variantBtn: 'red',
            onClick: claim,
          }
        } else if(order?.status?.isFinished()) {
          action = {
            text: 'Claimed NFT',
            variantBtn: 'gray4',
          }
        } else {
          action = {
            text: order?.status?.getLabel(),
            variantBtn: 'outlineBlack',
          }
          
        }

        rs.push({
          asset: order?.asset || null,
          unitPrice: {
            price: Number( listing?.price || 0),
            paymentToken: listing?.paymentToken,
          },
          quantity: Number(order?.quantity || 0),
          amount: {
            price: new BigNumber(listing?.price || 0).times(Number(order?.quantity || 0)).toNumber(),
            paymentToken: listing?.paymentToken,
          },
          network: order?.asset?.chain?.getLabel(),
          action: action,
        })
      }
      return rs
    }catch (err) {
      return []
    }
  }
}
