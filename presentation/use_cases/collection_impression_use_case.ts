import CollectionImpressionRepository from '../../domain/repositories/collection_impression_repository'
import {Query} from '../../domain/repositories/collection_impression_repository/query'
import CollectionImpression from '../../domain/models/collection_impression'

export default class CollectionImpressionUseCase {

  private repository: CollectionImpressionRepository

  constructor () {
    this.repository = new CollectionImpressionRepository()
  }

  search (query: Query): Promise<CollectionImpression[]> {
    return this.repository.search(query)
  }

  totalCount (query: Query): Promise<number> {
    return this.repository.totalCount(query)
  }

}
