import TargetEntityType from '../../domain/value_objects/target_entity_type'
import Impression from '../../domain/models/impression'
import LikeRepository from '../../domain/repositories/like_repository'

export default class LikeUseCase {

  private repository: LikeRepository

  constructor () {
    this.repository = new LikeRepository()
  }

  create (targetEntityType: TargetEntityType, targetEntityId: string): Promise<Impression> {
    return this.repository.create(targetEntityType, targetEntityId)
  }

  delete (targetEntityType: TargetEntityType, targetEntityId: string): Promise<boolean> {
    return this.repository.delete(targetEntityType, targetEntityId)
  }

}
