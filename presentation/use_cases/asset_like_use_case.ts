import AssetLikeRepository from '../../domain/repositories/asset_like_repository'
import {Query} from '../../domain/repositories/asset_like_repository/query'
import AssetLike from '../../domain/models/asset_like'

export default class AssetLikeUseCase {

  private repository: AssetLikeRepository

  constructor () {
    this.repository = new AssetLikeRepository()
  }

  search (query: Query): Promise<AssetLike[]> {
    return this.repository.search(query)
  }

  totalCount (query: Query): Promise<number> {
    return this.repository.totalCount(query)
  }

  assetIds (query: Query): Promise<string[]> {
    return this.repository.assetIds(query)
  }

}
