import SettingRepository from 'domain/repositories/setting_repository'
import {
  Setting<PERSON><PERSON>,
  SettingVMSale<PERSON>son,
  SettingVMBuyBackJson,
} from 'data/jsons/setting_json'

export const KEY_SETTING = {
  VM_SALES_ENABLE: 'VM_SALES_ENABLE',
  VM_BUY_BACK: 'VM_BUY_BACK',
}

/* eslint-disable space-before-function-paren */
export default class SettingUseCase {
  private repository: SettingRepository
  constructor() {
    this.repository = new SettingRepository()
  }
  /**
   * VM_SALES_ENABLE
   */
  getSettingVMSale(): Promise<SettingVMSaleJson> {
    const defaultValue = { enable: true }
    return new Promise((resolve, reject) => {
      this.repository
        .getSetting(KEY_SETTING.VM_SALES_ENABLE)
        .then((data: SettingJson) => {
          resolve(data.value || defaultValue)
        })
        .catch((e) => {
          resolve(defaultValue)
        })
    })
  }
  getSettingVMSaleStatus(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.getSettingVMSale()
        .then((data: SettingVMSaleJson) => {
          resolve(data.enable)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
  updateSettingVMSale(payload: SettingVMSaleJson): Promise<SettingVMSaleJson> {
    return new Promise((resolve, reject) => {
      this.repository
        .updateSetting({
          key: KEY_SETTING.VM_SALES_ENABLE,
          value: payload,
        })
        .then((data: SettingJson) => {
          resolve(data.value)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  /**
   * VM_BUY_BACK
   */
  getSettingVMBuyBack(): Promise<SettingVMBuyBackJson> {
    const defaultValue = { enable: false, rate: 0, timeByDay: 0 }
    return new Promise((resolve, reject) => {
      this.repository
        .getSetting(KEY_SETTING.VM_BUY_BACK)
        .then((data: SettingJson) => {
          resolve(data.value || defaultValue)
        })
        .catch((e) => {
          resolve(defaultValue)
        })
    })
  }
  updateSettingVMBuyBack(
    payload: SettingVMBuyBackJson
  ): Promise<SettingVMBuyBackJson> {
    return new Promise((resolve, reject) => {
      this.repository
        .updateSetting({
          key: KEY_SETTING.VM_BUY_BACK,
          value: {
            ...payload,
            rate: Number(payload.rate),
            timeByDay: Number(payload.timeByDay),
          },
        })
        .then((data: SettingJson) => {
          resolve(data.value)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
}
