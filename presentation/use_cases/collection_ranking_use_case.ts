import CollectionRanking from '../../domain/models/collection_ranking'
import { Query } from '../../domain/repositories/collection_ranking/query'
import CollectionRankingRepository from '../../domain/repositories/collection_ranking_repository'

// TODO: implement association with blockchain here
export default class CollectionRankingUseCase {

  private repository: CollectionRankingRepository

  constructor () {
    this.repository = new CollectionRankingRepository()
  }

  findById (id: string): Promise<CollectionRanking> {
    return this.repository.findById(id)
  }

  findBySlug (slug: string): Promise<CollectionRanking> {
    return this.repository.findBySlug(slug)
  }

  search (query: Query): Promise<CollectionRanking[]> {
    return this.repository.search(query)
  }

  totalCount (query: Query): Promise<number> {
    return this.repository.totalCount(query)
  }

}
