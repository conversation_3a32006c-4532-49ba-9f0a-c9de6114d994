import VendingMachinePack from 'domain/models/vending_machine_pack'
import VendingMachinePackRepository from 'domain/repositories/vending_machine_pack_repository/index'
import { Query } from 'domain/repositories/vending_machine_pack_repository/query'
import { VendingMachinePackListType } from 'domain/repositories/vending_machine_pack_repository/list_pack_type'
import { CreatePackParams, UpdatePackParams } from 'data/datasources/interfaces/vending_machine_pack_datasource'

export default class VendingMachinePackUseCase {

  private repository: VendingMachinePackRepository

  constructor () {
    this.repository = new VendingMachinePackRepository()
  }

  list (query: Query): Promise<VendingMachinePackListType> {
    return this.repository.list(query)
  }

  getById (id: string): Promise<VendingMachinePack> {
    return this.repository.getById(id)
  }

  create (params: CreatePackParams): Promise<VendingMachinePack> {
    return this.repository.create(params)
  }

  update (id: string, params: UpdatePackParams): Promise<VendingMachinePack> {
    return this.repository.update(id, params)
  }

  delete (id: string): Promise<void> {
    return this.repository.delete(id)
  }

  // Helper method to map packs for table display
  mapPacksForTable (packs: VendingMachinePack[]) {
    try {
      return packs.map((pack) => ({
        id: pack.id,
        name: pack.name,
        price: pack.getFormattedPrice(),
        category: pack.categoryId, // You might want to resolve this to category name
        createdAt: pack.getFormattedCreatedAt(),
        enabled: pack.isEnabled,
        action: pack, // Pass the entire pack object for actions
      }))
    } catch (err) {
      console.error('Error mapping packs for table:', err)
      return []
    }
  }

  // Helper method to validate pack data
  validatePackData (data: { 
    name: string; 
    description: string; 
    price: number | string; 
    categoryId: string; 
    imageUrl: string;
    odds: any[];
  }) {
    const errors: string[] = []

    if (!data.name || data.name.trim().length === 0) {
      errors.push('Pack name is required')
    }

    if (data.name && data.name.trim().length > 255) {
      errors.push('Pack name must be less than 255 characters')
    }

    if (!data.description || data.description.trim().length === 0) {
      errors.push('Pack description is required')
    }

    if (!data.price || Number(data.price) <= 0) {
      errors.push('Pack price must be greater than 0')
    }

    if (!data.categoryId || data.categoryId.trim().length === 0) {
      errors.push('Category is required')
    }

    if (!data.imageUrl || data.imageUrl.trim().length === 0) {
      errors.push('Image URL is required')
    }

    // Validate odds
    if (data.odds && data.odds.length > 0) {
      const totalRate = data.odds.reduce((sum, odd) => sum + (parseFloat(odd.rate) || 0), 0)
      if (totalRate !== 100) {
        errors.push('Total odds rate must equal 100%')
      }

      for (let i = 0; i < data.odds.length; i++) {
        const odd = data.odds[i]
        if (!odd.fromPrice || !odd.toPrice || !odd.rate) {
          errors.push(`Odds row ${i + 1}: All fields are required`)
        }
        if (parseFloat(odd.fromPrice) >= parseFloat(odd.toPrice)) {
          errors.push(`Odds row ${i + 1}: From price must be less than to price`)
        }
        if (parseFloat(odd.rate) <= 0 || parseFloat(odd.rate) > 100) {
          errors.push(`Odds row ${i + 1}: Rate must be between 0 and 100`)
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  }
}
