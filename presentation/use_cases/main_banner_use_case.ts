import MainBannerRepository from '../../domain/repositories/main_banner_repository'
import MainBanner from '../../domain/models/main_banner'
import {Query} from '../../domain/repositories/main_banner_repository/query'

export default class MainBannerUseCase {

  private repository: MainBannerRepository

  constructor () {
    this.repository = new MainBannerRepository()
  }

  findById (id: string): Promise<MainBanner> {
    return this.repository.findById(id)
  }

  search (query: Query): Promise<MainBanner[]> {
    return this.repository.search(query)
  }

  totalCount (query: Query): Promise<number> {
    return this.repository.totalCount(query)
  }

}
