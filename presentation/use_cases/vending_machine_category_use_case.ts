import VendingMachineCategory from 'domain/models/vending_machine_category'
import VendingMachineCategoryRepository from 'domain/repositories/vending_machine_category_repository/index'
import { Query } from 'domain/repositories/vending_machine_category_repository/query'
import { VendingMachineCategoryListType } from 'domain/repositories/vending_machine_category_repository/list_category_type'
import { CreateCategoryParams, UpdateCategoryParams } from 'data/datasources/interfaces/vending_machine_category_datasource'

export default class VendingMachineCategoryUseCase {

  private repository: VendingMachineCategoryRepository

  constructor () {
    this.repository = new VendingMachineCategoryRepository()
  }

  list (query: Query): Promise<VendingMachineCategoryListType> {
    return this.repository.list(query)
  }

  getById (id: string): Promise<VendingMachineCategory> {
    return this.repository.getById(id)
  }

  create (params: CreateCategoryParams): Promise<VendingMachineCategory> {
    return this.repository.create(params)
  }

  update (id: number, params: UpdateCategoryParams): Promise<VendingMachineCategory> {
    return this.repository.update(id, params)
  }

  delete (id: number): Promise<void> {
    return this.repository.delete(id)
  }

  // Helper method to map categories for table display
  mapCategoriesForTable (categories: VendingMachineCategory[]) {
    try {
      return categories.map((category) => ({
        id: category.id,
        name: category.name,
        createdAt: category.getFormattedCreatedAt(),
        enabled: category.enable,
        action: category, // Pass the entire category object for actions
      }))
    } catch (err) {
      console.error('Error mapping categories for table:', err)
      return []
    }
  }

  // Helper method to validate category data
  validateCategoryData (data: { name: string; enable?: boolean }) {
    const errors: string[] = []

    if (!data.name || data.name.trim().length === 0) {
      errors.push('Category name is required')
    }

    if (data.name && data.name.trim().length > 255) {
      errors.push('Category name must be less than 255 characters')
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  }
}
