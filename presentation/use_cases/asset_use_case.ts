import NftLandContractAssetService from 'domain/services/nft_land_contract/nft_land_contract_asset_service'
import AssetRepository from '../../domain/repositories/asset_repository'
import {Query} from '../../domain/repositories/asset_repository/query'
import Asset from '../../domain/models/asset'
import Chain from '../../domain/value_objects/chain'
import ConvertConfig from 'domain/services/utils/convert_config'
import { ethers } from 'ethers'
import { AssetsRefreshMetadata } from 'data/jsons/assets/assets_refresh_metadata_json'

export default class AssetUseCase {

  private repository: AssetRepository

  constructor () {
    this.repository = new AssetRepository()
  }

  findById (id: string): Promise<Asset> {
    return this.repository.findById(id)
  }

  findByChainAndAddressAndTokenId (chain: Chain, address: string, tokenId: string): Promise<Asset> {
    return this.repository.findByChainAndAddressAndTokenId(chain, address, tokenId)
  }

  search (query: Query): Promise<Asset[]> {
    return this.repository.search(query)
  }

  totalCount (query: Query): Promise<number> {
    return this.repository.totalCount(query)
  }
  
  createMetadata (asset: Asset): Promise<any> {
    return this.repository.createMetadata(asset)
  }
  
  create (asset: Asset): Promise<Asset> {
    return this.repository.create(asset)
  }
  
  checkOwnership (id: string, userAddress: string): Promise<boolean> {
    return this.repository.checkOwnership(id, userAddress)
  }
  
  refreshMetadata (params: AssetsRefreshMetadata): Promise<any> {
    return this.repository.refreshMetadata(params)
  }

  scCreateAsset (
    asset: Asset,
    accountAddress: string,
    provider: ethers.providers.JsonRpcProvider,
    contractsConfig: any,
  ) : Promise<ethers.ContractReceipt> {
    const nftLandContractAssetService = new NftLandContractAssetService({
      providerOrSigner: provider,
      ...ConvertConfig.convertContractConfig(contractsConfig),
    })
    return nftLandContractAssetService.create(
      asset,
      accountAddress
    )
  }

}
