import UserOfferSettingRepository from '../../domain/repositories/user_offer_setting_repository'
import UserOfferSetting from '../../domain/models/user_offer_setting'

export default class MyOfferSettingUseCase {

  private repository: UserOfferSettingRepository

  constructor () {
    this.repository = new UserOfferSettingRepository()
  }

  mine (): Promise<UserOfferSetting[]> {
    return this.repository.search()
  }

  update (collectionId: string, minimumPrice: number): Promise<UserOfferSetting> {
    return this.repository.update(collectionId, minimumPrice)
  }

}
