import React from 'react'
import VendingMachineImportCard from 'domain/models/vending_machine_import_card'
import VendingMachineCard from 'domain/models/vending_machine_card'
import VendingMachineCheckout from 'domain/models/vending_machine_checkout'
import VendingMachineCardRepository from 'domain/repositories/vending_machine_card_repository/index'
import { Query } from 'domain/repositories/vending_machine_card_repository/query'
import { QueryCheckout } from 'domain/repositories/vending_machine_card_repository/query_checkout'
import {
  VendingMachineListImportCardType,
  VendingMachineListCardType,
  VendingMachineListCheckoutType,
} from 'domain/repositories/vending_machine_card_repository/list_card_type'
import { PayloadUpdateVendingMachineCardJson } from 'data/jsons/vending-machine/vending_machine_card_json'
import { showFormatNumber } from 'lib/number'
import { saveAs } from 'file-saver'

const EMPTY = React.createElement('span', { className: 'text-gray-400' }, 'N/A')

/* eslint-disable space-before-function-paren */
export default class VendingMachineCardUseCase {
  private repository: VendingMachineCardRepository

  constructor() {
    this.repository = new VendingMachineCardRepository()
  }

  listImportCard(query: Query): Promise<VendingMachineListImportCardType> {
    return this.repository.listImportCard(query)
  }

  listCard(query: Query): Promise<VendingMachineListCardType> {
    return this.repository.listCard(query)
  }

  listCardIds(query: Query): Promise<VendingMachineListCardType> {
    return this.repository.listCardIds(query)
  }

  importCard(params: any): Promise<VendingMachineImportCard> {
    return this.repository.importCard(params)
  }

  batchImportCard(file: File): Promise<any> {
    return this.repository.batchImportCard(file)
  }

  importCardTemplate(): Promise<any> {
    return new Promise((resolve, reject) => {
      this.repository
        .importCardTemplate()
        .then((data) => {
          const contentType = 'text/csv;charset=utf-8'
          const file = new Blob([data], { type: contentType })
          saveAs(file, 'template_card.csv')
          resolve(true)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  updateCard(
    id: string,
    payload: PayloadUpdateVendingMachineCardJson
  ): Promise<any> {
    return this.repository.updateCard(id, payload)
  }

  mapCategory(cardId: string, categoryId: string): Promise<any> {
    return this.repository.mapCategory(cardId, categoryId)
  }

  currencyRate(
    baseCurrency: string = 'usd',
    currency: string = 'jpy'
  ): Promise<number> {
    return this.repository.currencyRate(baseCurrency, currency)
  }

  listCheckout(query: QueryCheckout): Promise<VendingMachineListCheckoutType> {
    return this.repository.listCheckout(query)
  }

  async exportCheckout(query: QueryCheckout) {
    const params = query.toJson()
    delete params.limit
    delete params.page
    const path = new QueryCheckout(params).toQueryString()
    const win = window.open(
      `${process.env.NEXT_PUBLIC_API_URL}/checkouts/export-csv${
        path ? '?' + path : ''
      }`,
      '_blank'
    )
    if (!win) {
      const data = await this.repository.exportCheckout(query)
      const contentType = 'text/csv;charset=utf-8'
      const file = new Blob([data], { type: contentType })
      saveAs(file, `checkouts_${new Date().valueOf()}.csv`)
      return
    }
    return true
  }

  mapListCardProcess(list: VendingMachineImportCard[]) {
    try {
      let rs = []
      for (let index = 0; index < list?.length; index++) {
        let card = list[index]
        rs.push({
          cardId: {
            value: card.cardId,
          },
          status: card.status,
          createdAt: {
            day: card.createdAt?.toFormat('yyyy-MM-dd') || '',
            time: card.createdAt?.toFormat('hh:mm:ss') || '',
          },
          scrapeAt: {
            day: card.scrapeAt?.toFormat('yyyy-MM-dd') || '',
            time: card.scrapeAt?.toFormat('hh:mm:ss') || '',
          },
          message: {
            value: card.message,
          },
        })
      }
      return rs
    } catch (err) {
      return []
    }
  }

  mapCurrency(currency: string): string {
    switch (currency) {
      case 'JPY':
        return '¥'
      case 'USD':
        return '$'
      default:
        return currency
    }
  }

  mapListCardInventory(list: VendingMachineCard[], rateYJP: number) {
    try {
      let rs = []
      for (let index = 0; index < list?.length; index++) {
        const card = list[index]
        let price = '',
          priceYJP = ''
        if (card.currency == 'JPY') {
          price = `$${showFormatNumber((Number(card.price) * 1) / rateYJP)}`
          priceYJP = `¥${showFormatNumber(card.price)}`
        } else if (card.currency == 'USD') {
          price = `$${showFormatNumber(card.price)}`
          priceYJP = `¥${showFormatNumber(Number(card.price) * rateYJP)}`
        } else {
          price = `${showFormatNumber(card.price)} ${card.currency}`
        }

        rs.push({
          cardId: { value: card.cardId },
          status: { value: card.status },
          shop: { value: card.shop },
          category: { value: card?.category?.name || EMPTY },
          price: { value: price, valueCurrency: priceYJP },
          stock: { value: showFormatNumber(card.stock) },
          mintedQty: { value: showFormatNumber(card.mintedQty) },
          buyBackQty: { value: showFormatNumber(card.buyBackQty) },
          convertedQty: { value: showFormatNumber(card.convertedQty) },
          enable: { value: card.isEnabled, id: card.id },
        })
      }
      return rs
    } catch (err) {
      return []
    }
  }

  mapSales(list: VendingMachineCheckout[]) {
    try {
      let rs = []
      for (let index = 0; index < list?.length; index++) {
        let el = list[index]
        const explorerUrl = el.chain.getExplorerUrl()
        rs.push({
          date: {
            day: el.date?.toFormat('yyyy-MM-dd') || '',
            time: el.date?.toFormat('hh:mm:ss') || '',
          },
          status: el.status,
          category: { value: el.category },
          price: { value: showFormatNumber(el.price || 0, 0, 6, '$') },
          cardId: { value: el.cardId },
          quantity: { value: showFormatNumber(el.quantity) },
          buyTx: {
            value: el.checkoutTxHash,
            link: `${explorerUrl}/tx/${el.checkoutTxHash}`,
          },
          mintTx: {
            value: el.mintTxHash,
            link: `${explorerUrl}/tx/${el.mintTxHash}`,
          },
          recipient: {
            value: el.toAddress,
            link: `${explorerUrl}/address/${el.toAddress}`,
          },
        })
      }
      return rs
    } catch (err) {
      return []
    }
  }
}
