import UserRepository from '../../domain/repositories/user_repository'
import User from '../../domain/models/user'

export default class UserUseCase {

  private repository: UserRepository

  constructor () {
    this.repository = new UserRepository()
  }

  findById (id: string): Promise<User> {
    return this.repository.findById(id)
  }

  findByPublicAddress (publicAddress: string): Promise<User> {
    return this.repository.findByPublicAddress(publicAddress)
  }

  findByUsername (username: string, isExact: boolean = true): Promise<any> {
    return this.repository.findByUsername(username, isExact)
  }

  nonce (publicAddress: string): Promise<number> {
    return this.repository.nonce(publicAddress)
  }

  create (publicAddress: string): Promise<User> {
    return this.repository.create(publicAddress)
  }

  isUsernameAvailable (username: string): Promise<boolean> {
    return this.repository.isUsernameAvailable(username)
  }

  isEmailAvailable (email: string): Promise<boolean> {
    return this.repository.isEmailAvailable(email)
  }

}
