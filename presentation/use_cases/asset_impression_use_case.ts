import AssetImpressionRepository from '../../domain/repositories/asset_impression_repository'
import AssetImpression from '../../domain/models/asset_impression'
import {Query} from '../../domain/repositories/asset_impression_repository/query'

export default class AssetImpressionUseCase {

  private repository: AssetImpressionRepository

  constructor () {
    this.repository = new AssetImpressionRepository()
  }

  search (query: Query): Promise<AssetImpression[]> {
    return this.repository.search(query)
  }

  totalCount (query: Query): Promise<number> {
    return this.repository.totalCount(query)
  }

  assetIds (query: Query): Promise<string[]> {
    return this.repository.assetIds(query)
  }

}
