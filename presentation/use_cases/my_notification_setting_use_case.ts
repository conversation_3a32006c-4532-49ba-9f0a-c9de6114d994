import UserNotificationSettingRepository from '../../domain/repositories/user_notification_setting_repository'
import UserNotificationSetting from '../../domain/models/user_notification_setting'

export default class MyNotificationSettingUseCase {

  private repository: UserNotificationSettingRepository

  constructor () {
    this.repository = new UserNotificationSettingRepository()
  }

  mine (): Promise<UserNotificationSetting> {
    return this.repository.mine()
  }

  update (setting: UserNotificationSetting): Promise<UserNotificationSetting> {
    return this.repository.update(setting)
  }

}
