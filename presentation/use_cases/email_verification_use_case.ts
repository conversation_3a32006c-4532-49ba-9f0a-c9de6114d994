import EmailVerificationRepository from '../../domain/repositories/email_verification_repository'
import EmailVerification from '../../domain/models/email_verification'

export default class EmailVerificationUseCase {

  private repository: EmailVerificationRepository

  constructor () {
    this.repository = new EmailVerificationRepository()
  }

  issue (email: string): Promise<EmailVerification> {
    return this.repository.issue(email)
  }

  verify (token: string): Promise<boolean> {
    return this.repository.verify(token)
  }

  hasActiveRequest (): Promise<boolean> {
    return this.repository.hasActiveRequest()
  }

  resendEmail (): Promise<boolean> {
    return this.repository.resendEmail()
  }

}
