import BoostedCollectionRepository from '../../domain/repositories/boosted_collection_repository'
import BoostedCollection from '../../domain/models/boosted_collection'
import {Query} from '../../domain/repositories/boosted_collection_repository/query'

export default class BoostedCollectionUseCase {

  private repository: BoostedCollectionRepository

  constructor () {
    this.repository = new BoostedCollectionRepository()
  }

  findById (id: string): Promise<BoostedCollection> {
    return this.repository.findById(id)
  }

  search (query: Query): Promise<BoostedCollection[]> {
    return this.repository.search(query)
  }

  totalCount (query: Query): Promise<number> {
    return this.repository.totalCount(query)
  }

}
