import AnnouncementRepository from '../../domain/repositories/announcement_repository'
import Announcement from '../../domain/models/announcement'
import {Query} from '../../domain/repositories/announcement_repository/query'

export default class AnnouncementUseCase {

  private repository: AnnouncementRepository

  constructor () {
    this.repository = new AnnouncementRepository()
  }

  findById (id: string): Promise<Announcement> {
    return this.repository.findById(id)
  }

  search (query: Query): Promise<Announcement[]> {
    return this.repository.search(query)
  }

  totalCount (query: Query): Promise<number> {
    return this.repository.totalCount(query)
  }
}
