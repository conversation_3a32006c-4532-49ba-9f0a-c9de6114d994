import AssetDailyStatRepository from '../../domain/repositories/asset_daily_stat_repository'
import AssetDailyStat from '../../domain/models/asset_daily_stat'
import {Query} from '../../domain/repositories/asset_daily_stat_repository/query'

export default class AssetDailyStatUseCase {

  private repository: AssetDailyStatRepository

  constructor () {
    this.repository = new AssetDailyStatRepository()
  }

  findById (id: string): Promise<AssetDailyStat> {
    return this.repository.findById(id)
  }

  search (query: Query): Promise<AssetDailyStat[]> {
    return this.repository.search(query)
  }

  totalCount (query: Query): Promise<number> {
    return this.repository.totalCount(query)
  }
}
