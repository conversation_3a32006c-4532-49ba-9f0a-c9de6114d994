import PaymentTokens from 'domain/models/payment_tokens'
import PaymentTokensRepository from 'domain/repositories/payment_tokens_repository'

export default class PaymentTokensUseCase {

  private repository: PaymentTokensRepository

  constructor () {
    this.repository = new PaymentTokensRepository()
  }

  getPaymentTokens (chainId?: number): Promise<PaymentTokens[]> {
    return this.repository.getPaymentTokens(chainId)
  }
}
