import WatchRepository from '../../domain/repositories/watch_repository'
import TargetEntityType from '../../domain/value_objects/target_entity_type'
import Watch from '../../domain/models/watch'

export default class WatchUseCase {

  private repository: WatchRepository

  constructor () {
    this.repository = new WatchRepository()
  }

  create (targetEntityType: TargetEntityType, targetEntityId: string): Promise<Watch> {
    return this.repository.create(targetEntityType, targetEntityId)
  }

  delete (targetEntityType: TargetEntityType, targetEntityId: string): Promise<boolean> {
    return this.repository.delete(targetEntityType, targetEntityId)
  }

}
