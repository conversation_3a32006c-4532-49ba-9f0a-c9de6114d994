import CollectionDailyStatRepository from '../../domain/repositories/collection_daily_stat_repository'
import CollectionDailyStat from '../../domain/models/collection_daily_stat'
import {Query} from '../../domain/repositories/collection_daily_stat_repository/query'

export default class CollectionDailyStatUseCase {

  private repository: CollectionDailyStatRepository

  constructor () {
    this.repository = new CollectionDailyStatRepository()
  }

  findById (id: string): Promise<CollectionDailyStat> {
    return this.repository.findById(id)
  }

  search (query: Query): Promise<any> {
    return this.repository.search(query)
  }

  totalCount (query: Query): Promise<number> {
    return this.repository.totalCount(query)
  }

}
