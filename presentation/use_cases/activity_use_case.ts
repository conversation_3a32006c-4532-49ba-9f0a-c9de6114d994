import { Query } from 'domain/repositories/activity_repository/query'
import Activity from 'domain/models/activity'
import ActivityRepository from 'domain/repositories/activity_repository'
import { showFormatNumber } from 'lib/number'

const mapIcon: any = {
  listings: 'tag',
  sales: 'cart1',
  sale: 'cart1',
  bids: 'tag',
  transfers: 'reload',
  transfer: 'reload',
  minted: 'coin',
  offer: 'hand',
  listed: 'tag',
}

const mapLabel: any = {
  listings: 'Listing',
  sales: 'Sales',
  sale: 'Sales',
  bids: 'Bids',
  transfer: 'Transfer',
  transfers: 'Transfers',
  minted: 'Minted',
  offer: 'Offer',
  listed: 'Listed',
}


export default class ActivityUseCase {

  private repository: ActivityRepository

  constructor () {
    this.repository = new ActivityRepository()
  }

  findById (id: string): Promise<Activity> {
    return this.repository.findById(id)
  }

  search (query: Query): Promise<Activity[]> {
    console.log('🚀 ~ ActivityUseCase ~ search ~ query:', query)
    return this.repository.search(query)
  }

  totalCount (query: Query): Promise<number> {
    console.log('🚀 ~ ActivityUseCase ~ totalCount ~ query:', query)
    return this.repository.totalCount(query)
  }
  
  mapActivitiesForAsset (activities: Activity[])  {
    try {
      let rs = []
      for (let index = 0; index < activities?.length; index++) {
        let activity = activities[index]
        const minedAt = activity?.minedAt
        const time = minedAt?.toFormat('yyyy-MM-dd') || ''
        const event = activity?.activityEvent
        const noToUser = event.isListed() || event.isOffer()
        const noPrice = event.isMinted() || event.isTransfer()
        rs.push({
          event:  {
            label: mapLabel[event.getName()],
            icon: mapIcon[event.getName()],
          },
          unitPrice: noPrice ? null : {
            price: showFormatNumber( activity?.price || 0, 0, 3),
            paymentToken: activity?.paymentToken,
          },
          quantity: Number(activity?.quantity || 0),
          fromUser: activity?.fromUser || { recipient: activity?.offerer  },
          toUser: noToUser ? null : (activity?.toUser || { offerer: activity?.recipient }),
          time: time,

        })
      }
      return rs
    }catch (err) {
      return []
    }
  }
  mapActivitiesForAssetV2 (activities: Activity[])  {
    try {
      let rs = []
      for (let index = 0; index < activities?.length; index++) {
        let activity = activities[index]
        const minedAt = activity?.minedAt
        const time = minedAt?.toFormat('yyyy-MM-dd') || ''
        const event = activity?.activityEvent
        const noToUser = event.isListed() || event.isOffer()
        const noPrice = event.isMinted() || event.isTransfer()
        rs.push({
          event:  {
            label: mapLabel[event.getName()],
            icon: mapIcon[event.getName()],
          },
          asset: activity?.asset || null,
          unitPrice: noPrice ? null : {
            price: showFormatNumber( activity?.price || 0, 0, 3),
            paymentToken: activity?.paymentToken,
          },
          quantity: activity?.quantity || 0,
          fromUser: activity?.fromUser || { recipient: activity?.offerer  },
          toUser: noToUser ? null : (activity?.toUser || { offerer: activity?.recipient }),
          time: time,

        })
      }
      return rs
    }catch (err) {
      return []
    }
  }
}
