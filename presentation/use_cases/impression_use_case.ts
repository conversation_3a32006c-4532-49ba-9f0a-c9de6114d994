import ImpressionRepository from '../../domain/repositories/impression_repository'
import TargetEntityType from '../../domain/value_objects/target_entity_type'
import Impression from '../../domain/models/impression'

export default class ImpressionUseCase {

  private repository: ImpressionRepository

  constructor () {
    this.repository = new ImpressionRepository()
  }

  create (targetEntityType: TargetEntityType, targetEntityId: string): Promise<Impression> {
    return this.repository.create(targetEntityType, targetEntityId)
  }

}
