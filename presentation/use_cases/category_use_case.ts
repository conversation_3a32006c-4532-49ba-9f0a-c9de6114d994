import {Query} from '../../domain/repositories/boosted_collection_repository/query'
import CategoryRepository from '../../domain/repositories/category_repository'
import Category from '../../domain/models/category'

export default class CategoryUseCase {

  private repository: CategoryRepository

  constructor () {
    this.repository = new CategoryRepository()
  }

  findById (id: string): Promise<Category> {
    return this.repository.findById(id)
  }

  findBySlug (slug: string): Promise<Category> {
    return this.repository.findBySlug(slug)
  }

  search (query: Query): Promise<Category[]> {
    return this.repository.search(query)
  }

  totalCount (query: Query): Promise<number> {
    return this.repository.totalCount(query)
  }

}
