import NftLandContractCollectionService from 'domain/services/nft_land_contract/nft_land_contract_collection_service'
import { ethers } from 'ethers'
import Collection from '../../domain/models/collection'
import CollectionRepository from '../../domain/repositories/collection_repository'
import { Query } from '../../domain/repositories/collection_repository/query'
import ConvertConfig from 'domain/services/utils/convert_config'
// TODO: implement association with blockchain here
export default class CollectionUseCase {

  private repository: CollectionRepository

  constructor () {
    this.repository = new CollectionRepository()
  }

  findById (id: string): Promise<Collection> {
    return this.repository.findById(id)
  }

  findBySlug (slug: string, isExact = true, chainId?: number): Promise<any> {
    return this.repository.findBySlug(slug, isExact, chainId)
  }

  search (query: Query): Promise<Collection[]> {
    return this.repository.search(query)
  }

  totalCount (query: Query): Promise<number> {
    return this.repository.totalCount(query)
  }

  save (collection: Collection): Promise<Collection> {
    return this.repository.save(collection)
  }

  import (collection: Collection & { txHash: string } & { contractAddress: string }): Promise<Collection> {
    return this.repository.import(collection)
  }
  importFromContractAddress (collection: Collection & { contractAddress: string } & { logoImageUrl?: string, featuredImageUrl?: string, bannerImageUrl?: string }): Promise<Collection> {
    return this.repository.importFromContractAddress(collection)
  }
  scCreateCollection (
    collection: Collection,
    accountAddress: string,
    tokenContractType: number,
    provider: ethers.providers.JsonRpcProvider,
    contractsConfig: any
  ) : Promise<ethers.ContractReceipt> {
    const nftLandContractCollectionService = new NftLandContractCollectionService({
      providerOrSigner: provider,
      ...ConvertConfig.convertContractConfig(contractsConfig),
    })
    return nftLandContractCollectionService.create(
      collection,
      accountAddress,
      tokenContractType
    )
  }
}
