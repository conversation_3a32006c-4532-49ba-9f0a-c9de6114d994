import { mainnet, polygonAmoy, holesky, polygon, sepolia, bscTestnet, 
  oasys, bsc, arbitrumSepolia, avalancheFuji, optimismSepolia, 
  polygonZkEvmCardona, arbitrum, avalanche, optimism, metis, polygonZkEvm } from 'wagmi/chains'
import { type Chain } from 'viem'

let polygonTestnet : any= { ...polygonAmoy }
polygonTestnet = {
  ...polygonTestnet,
  nativeCurrency: {
    decimals: 18,
    symbol: 'POL',
    name: 'P<PERSON>',
  },
  rpcUrls: {
    default: {
      http: ['https://api.zan.top/polygon-amoy'], 
    },
  },
}
let polygonMainnet:any  = { ...polygon }
polygonMainnet = {
  ...polygonMainnet,
  nativeCurrency: {
    decimals: 18,
    symbol: 'POL',
    name: 'POL',
  },
}
let oasysMainnet:any = { ...oasys }
oasysMainnet = {
  ...oasysMainnet,
  nativeCurrency: {
    decimals: 18,
    symbol: 'OAS',
    name: 'O<PERSON>',
  },
}
let bscTestnetShow:any = { ...bscTestnet }
bscTestnetShow = {
  ...bscTestnetShow,
  nativeCurrency: {
    decimals: 18,
    symbol: 'BNB',
    name: 'BNB',
  },
}
const mchverseTestnet: Chain = {
  id: 420, 
  name: 'MCH Testnet',
  nativeCurrency: {
    name: 'OAS',
    symbol: 'OAS',
    decimals: 18,
  },
  rpcUrls: {
    default: {
      http: ['https://rpc.oasys.sand.mchdfgh.xyz'], 
    },
  },
  blockExplorers: {
    default: { name: 'MCH Testnet Explorer', url: 'https://explorer.oasys.sand.mchdfgh.xyz' },
  },
  testnet: true,
}

const mchverse: Chain = {
  id: 29548, 
  name: 'MCH Verse',
  nativeCurrency: {
    name: 'OAS',
    symbol: 'OAS',
    decimals: 18,
  },
  rpcUrls: {
    default: {
      http: ['https://rpc.oasys.mycryptoheroes.net'], 
    },
  },
  blockExplorers: {
    default: { name: 'MCH Verse Explorer', url: 'https://explorer.oasys.mycryptoheroes.net' },
  },
  testnet: false,
}
const sgverse: Chain = {
  id: 812397,
  name: 'SG Verse',
  nativeCurrency: {
    name: 'OAS',
    symbol: 'OAS',
    decimals: 18,
  },
  rpcUrls: {
    default: {
      http: ['https://rpc.sgverse.net/'],
    },
  },
  blockExplorers: {
    default: { name: 'SG Verse Explorer', url: 'https://explorer.sgverse.net' },
  },
  testnet: false,
}
const sgverseTestnet: Chain = {
  id: 247101234,
  name: 'SG Verse Testnet',
  nativeCurrency: {
    name: 'OAS',
    symbol: 'OAS',
    decimals: 18,
  },
  rpcUrls: {
    default: {
      http: ['https://sgv-testnet.alt.technology/'],
    },
  },
  blockExplorers: {
    default: { name: 'SG Verse Testnet Explorer', url: 'https://sgv-testnet-explorer.alt.technology/' },
  },
  testnet: false,
}
const homeverse: Chain = {
  id: 19011,
  name: 'Home Verse',
  nativeCurrency: {
    name: 'OAS',
    symbol: 'OAS',
    decimals: 18,
  },
  rpcUrls: {
    default: {
      http: ['https://rpc.mainnet.oasys.homeverse.games'],
    },
  },
  blockExplorers: {
    default: { name: 'Home Verse Explorer', url: 'https://explorer.oasys.homeverse.games' },
  },
  testnet: false,
}

const homeverseTestnet: Chain = {
  id: 40875,
  name: 'Home Verse',
  nativeCurrency: {
    name: 'OAS',
    symbol: 'OAS',
    decimals: 18,
  },
  rpcUrls: {
    default: {
      http: ['https://rpc.testnet.oasys.homeverse.games'],
    },
  },
  blockExplorers: {
    default: { name: 'Home Verse Explorer', url: 'https://explorer.testnet.oasys.homeverse.games/' },
  },
  testnet: false,
}
const tcgverse: Chain = {
  id: 2400,
  name: 'TCG Verse',
  nativeCurrency: {
    name: 'OAS',
    symbol: 'OAS',
    decimals: 18,
  },
  rpcUrls: {
    default: {
      http: ['https://rpc.tcgverse.xyz/'],
    },
  },
  blockExplorers: {
    default: { name: 'TCG Verse Explorer', url: 'https://explorer.tcgverse.xyz/' },
  },
  testnet: false,
}
const tcgverseTestnet: Chain = {
  id: 12401,
  name: 'TCG Verse Testnet',
  nativeCurrency: {
    name: 'OAS',
    symbol: 'OAS',
    decimals: 18,
  },
  rpcUrls: {
    default: {
      http: ['https://rpc.testnet.tcgverse.xyz/'],
    },
  },
  blockExplorers: {
    default: { name: 'TCG Verse Testnet Explorer', url: 'https://explorer.testnet.tcgverse.xyz/' },
  },
  testnet: true,
}
const monadTestnet: Chain = {
  id: 10143,
  name: 'Monad Testnet',
  nativeCurrency: {
    name: 'MON',
    symbol: 'MON',
    decimals: 18,
  },
  rpcUrls: {
    default: {
      http: ['https://testnet-rpc.monad.xyz'],
    },
  },
  blockExplorers: {
    default: { name: 'Monad Testnet Explorer', url: 'https://testnet.monadexplorer.com' },
  },
  testnet: true,
}
const oasysTestnet: Chain = {
  id: 9372,
  name: 'OASYS Testnet',
  nativeCurrency: {
    name: 'OAS',
    symbol: 'OAS',
    decimals: 18,
  },
  rpcUrls: {
    default: {
      http: ['https://rpc.testnet.oasys.games'],
    },
  },
  blockExplorers: {
    default: { name: 'OASYS Testnet Explorer', url: 'https://explorer.testnet.oasys.games' },
  },
  testnet: true,
}
const metisSepolia: Chain = {
  id: 59902,
  name: 'Metis Sepolia',
  nativeCurrency: {
    name: 'sMETIS',
    symbol: 'sMETIS',
    decimals: 18,
  },
  rpcUrls: {
    default: {
      http: ['https://sepolia.metisdevops.link'],
    },
  },
  blockExplorers: {
    default: { name: 'Metis Sepolia Explorer', url: 'https://sepolia-explorer.metisdevops.link/' },
  },
  testnet: true,
}
const hyperliquid: Chain = {
  id: 999,
  name: 'Hyperliquid',
  nativeCurrency: {
    name: 'HYPE',
    symbol: 'HYPE',
    decimals: 18,
  },
  rpcUrls: {
    default: {
      http: ['https://rpc-proxy.hyper7rush.online/main/evm/999'],
    },
  },
  blockExplorers: {
    default: { name: 'Hyperliquid Explorer', url: 'https://hyperevmscan.io/' },
  },
  testnet: false,
}
const hyperliquidTestnet: Chain = {
  id: 998,
  name: 'Hyperliquid Testnet',
  nativeCurrency: {
    name: 'HYPE',
    symbol: 'HYPE',
    decimals: 18,
  },
  rpcUrls: {
    default: {
      http: ['https://rpc.hyperliquid-testnet.xyz/evm'],
    },
  },
  blockExplorers: {
    default: { name: 'Hyperliquid Testnet Explorer', url: 'https://testnet.purrsec.com/' },
  },
  testnet: true,
}



const chainsTestnet = [holesky, mchverseTestnet, polygonTestnet, 
  homeverseTestnet,sgverseTestnet, monadTestnet, 
  sepolia,  bscTestnetShow, oasysTestnet, arbitrumSepolia, avalancheFuji,
  optimismSepolia, polygonZkEvmCardona, metisSepolia, tcgverseTestnet,
  hyperliquid] as const
const chainsMainnet = [mchverse, mainnet, polygonMainnet, monadTestnet, 
  sgverse, homeverse, bsc, oasysMainnet, arbitrum, avalanche, optimism, 
  metis, polygonZkEvm] as const

export const chains = process.env.NEXT_PUBLIC_NODE_ENV === 'production' ? chainsMainnet : chainsTestnet
