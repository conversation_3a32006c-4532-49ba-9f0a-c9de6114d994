import dynamic from 'next/dynamic'
import { useState, useRef, memo } from 'react'
import FilterTime from 'presentation/components/filter-time'
import Tooltip from 'presentation/components/tooltip'
import PeriodRangesSelect from 'presentation/components/select/period-ranges-select'
const Column = dynamic(() => import('@ant-design/plots').then(({ Column }) => Column), { ssr: false })

const MemoPlot = memo(({ config, getChartRef}: any) => {
  return <Column {...config} onReady={(chartRef) => getChartRef(chartRef)} />
}, (pre, next) => {
  return JSON.stringify(pre.config) === JSON.stringify(next.config)
})

MemoPlot.displayName = 'MemoPlot'

const HighConcentrationChart = (props:any) => {
  const {
    periodRanges = [],
    chartData = [],
    defaultSelected,
    onChangeFilterParam = () => {},
    filterTypes = [],
  } = props

  const ref = useRef<any>(null)
  const [legendData, setLegendData] = useState([])

  const config: any = {
    height: 320,
    data: chartData,
    isStack: true,
    isPercent: true,
    xField: 'year',
    yField: 'value',
    xAxis: {
      title: {
        text: 'UTC                     ',
        position: 'start',
        offset: 14,
        style: {
          fontSize: 12,
        },
      },
    },
    yAxis: {
      title: {
        text: 'Value($)',
        style: {
          fontSize: 14,
        },
      },
    },
    seriesField: 'type',
    legend: false,
    color: ({ type }: any) => {
      switch (type) {
        case 'All Holders':
          return '#81C1EB'
        case 'Top 100':
          return '#5A69F0'
        default:
          return 'transparent'
      }
    },
  }

  const getChartRef = (chartRef: any) => {
    if (!chartRef) return
    ref.current = chartRef

    const chart = ref.current.chart
    const legendData = chart.geometries[0]?.dataArray?.map((item: any) => {
      const origin = item[0]._origin
      origin.color = item[0].color
      origin.checked = true
      return origin
    })
    setLegendData(legendData)
  }

  const handleLegendClick = (item: any, i: number) => {
    const newItem = item
    newItem.checked = !newItem.checked
    const currentLegend: any = [...legendData]
    currentLegend[i] = newItem

    const filteredLegendData = currentLegend
      .filter((l: any) => l.checked)
      .map((l: any) => l.type)

    const chart = ref.current.chart
    if (chart) {
      chart.filter('type', (val:any) => {
        return filteredLegendData.indexOf(val) > -1
      })
      chart.render()
    }
    setLegendData(currentLegend)
  }

  return (
    <div className="rounded-10 p-5 border border-gray-3 flex flex-col justify-between">
      <div className="flex flex-wrap justify-between">
        <div className="flex items-center mb-4">
          <p className="text-lg font-bold">Holding Concentration</p>
          <Tooltip className="-mt-0.5">Whales</Tooltip>
        </div>
        <FilterTime
          className="mb-4" options={filterTypes}
          defaultSelected={filterTypes[0]}
          onSelect={(option) => {
            //TODO onChangeFilterParam('type', option)
          }}
        />
        {periodRanges.length > 0 && (
          <div className="shrink-0 mb-4">
            <PeriodRangesSelect
              periodRanges={periodRanges}
              defaultSelected={defaultSelected}
              onSelect={(option) => {
                onChangeFilterParam('periodRange', option.value)
              }}
              shortenTitle
            />
          </div>
        )}
      </div>

      <div className="md:px-5 px-3">
        <ul className="flex flex-wrap items-start lg:justify-center justify-start w-full">
          {legendData?.map((item: any, i: number) => (
            <li
              key={i} onClick={() => handleLegendClick(item, i)}
              className={`flex flex-col mb-5 cursor-pointer ${i === 0 ? 'xl:ml-0' : 'xl:ml-12'} ml-4`}
            >
              <div className="inline-flex items-center">
                <span
                  className="rounded-full w-2 h-2 mr-2"
                  style={{ backgroundColor: item.checked ? item.color : '#aaa' }}
                ></span>
                <span className="font-medium text-sm mr-0.5">{item.type}</span>
                {i > 0 && (
                  <Tooltip className="flex items-center -mt-0.5">Description</Tooltip>
                )}
              </div>
            </li>
          ))}
        </ul>
        <MemoPlot config={config} getChartRef={getChartRef} />
      </div>
    </div>
  )
}

export default HighConcentrationChart
