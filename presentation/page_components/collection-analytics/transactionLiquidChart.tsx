import dynamic from 'next/dynamic'
import { useState, useRef, memo } from 'react'
import PeriodRangesSelect from 'presentation/components/select/period-ranges-select'
const DualAxes = dynamic(() => import('@ant-design/plots').then(({ DualAxes }) => DualAxes), { ssr: false })

const MemoPlot = memo(({ config, getChartRef}: any) => {
  return <DualAxes {...config} onReady={(chartRef) => getChartRef(chartRef)} />
}, (pre, next) => {
  return JSON.stringify(pre.config) === JSON.stringify(next.config)
})

MemoPlot.displayName = 'MemoPlot'

const TransactionLiquidChart = (props:any) => {
  const {
    periodRanges = [],
    chartData = [],
    defaultSelected,
    onChangePeriodRanges = () => {},
  } = props

  const ref = useRef<any>(null)
  const [legendTransfersData, setLegendTransfersData] = useState([])
  const [legendLiquidityData, setLegendLiquidityData] = useState([])

  const [selectedPeriodRangeText, setSelectedPeriodRangeText] = useState(defaultSelected.value || 7)

  const config: any = {
    data: [chartData, chartData],
    height: 320,
    xField: 'year',
    yField: ['value', 'count'],
    legend: false,
    geometryOptions: [
      {
        geometry: 'line',
        color: '#5A66F9',
      },
      {
        geometry: 'line',
        color: '#BB6BD9',
      },
    ],
  }

  const getChartRef = (chartRef: any) => {
    if (!chartRef) return
    ref.current = chartRef
    const chart = chartRef.chart

    const legendTransfersData: any = [{
      color: '#5A66F9',
      checked: true,
    }]
    setLegendTransfersData(legendTransfersData)

    const legendLiquidityData: any = [{
      color: '#BB6BD9',
      checked: true,
    }]
    setLegendLiquidityData(legendLiquidityData)
  }

  const handleLegendTransfersClick = (item: any, i: number) => {
    const newItem = item
    newItem.checked = !newItem.checked
    const currentLegend: any = [...legendTransfersData]
    currentLegend[i] = newItem

    const filteredLegendData = currentLegend
      .filter((l: any) => l.checked)

    const chart = ref.current.chart
    if (chart) {
      chart.views[0]?.filter('value', () => {
        return filteredLegendData.length > 0
      })
      chart.render()
    }
    setLegendTransfersData(currentLegend)
  }

  const handleLegendLiquidityClick = (item: any, i: number) => {
    const newItem = item
    newItem.checked = !newItem.checked
    const currentLegend: any = [...legendLiquidityData]
    currentLegend[i] = newItem

    const filteredLegendData = currentLegend
      .filter((l: any) => l.checked)

    const chart = ref.current.chart
    if (chart) {
      chart.views[1]?.filter('value', () => {
        return filteredLegendData.length > 0
      })
      chart.render()
    }
    setLegendLiquidityData(currentLegend)
  }

  return <div className="rounded-10 p-5 border border-gray-3">
    <div className="flex flex-wrap justify-between">
      <p className="text-lg font-bold mb-4">Transactions & Liquidity</p>
      <div className="shrink-0 mb-4">
        <PeriodRangesSelect
          periodRanges={periodRanges}
          defaultSelected={defaultSelected}
          onSelect={(option) => {
            setSelectedPeriodRangeText(option.subValue)
            onChangePeriodRanges('periodRange', option.value)
          }}
        />
      </div>
    </div>

    <ul className="flex flex-wrap items-start xl:justify-center justify-start">
      <li className="flex flex-col mb-5 xl:ml-0 ml-4">
        <div className="inline-flex items-center">
          <span
            className="rounded-full w-2 h-2 mr-2"
            style={{ backgroundColor: '#72D8DA' }}
          ></span>
          <span className="font-medium text-sm mr-1">
            Sales({selectedPeriodRangeText}{selectedPeriodRangeText !== 'All time' && 'D'})
          </span>
        </div>
        <span className="w-full text-right font-medium pr-1">123</span>
        <span className="w-full text-right text-sm text-red-1 font-medium pr-1">-24.54%</span>
      </li>
      {legendTransfersData?.map((item: any, i: number) => (
        <li key={i} onClick={() => handleLegendTransfersClick(item, i)} className="flex flex-col mb-5 ml-4 cursor-pointer">
          <div className="inline-flex items-center">
            <span
              className="rounded-full w-2 h-2 mr-2"
              style={{ backgroundColor: item.checked ? item.color : '#aaa' }}
            ></span>
            <span className="font-medium text-sm mr-1">
              Transfers({selectedPeriodRangeText}{selectedPeriodRangeText !== 'All time' && 'D'})
            </span>
          </div>
          <span className="w-full text-right font-medium pr-1">152</span>
          <span className="w-full text-right text-sm text-primary-1 font-medium pr-1">+10.95%</span>
        </li>
      ))}
      {legendLiquidityData?.map((item: any, i: number) => (
        <li key={i} onClick={() => handleLegendLiquidityClick(item, i)} className="flex flex-col mb-5 ml-4">
          <div className="inline-flex items-center">
            <span
              className="rounded-full w-2 h-2 mr-2"
              style={{ backgroundColor: item.checked ? item.color : '#aaa' }}
            ></span>
            <span className="font-medium text-sm mr-1">
              Liquidity({selectedPeriodRangeText}{selectedPeriodRangeText !== 'All time' && 'D'})
            </span>
          </div>
          <span className="w-full text-right font-medium pr-1">1.23%</span>
          <span className="w-full text-right text-sm text-red-1 font-medium pr-1">-24.54%</span>
        </li>
      ))}
    </ul>

    <MemoPlot config={config} getChartRef={getChartRef} />
  </div>
}

export default TransactionLiquidChart
