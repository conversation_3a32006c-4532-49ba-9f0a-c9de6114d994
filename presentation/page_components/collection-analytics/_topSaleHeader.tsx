import Image from 'next/image'

export const topSaleHeader = [
  {
    title: '#',
    key: 'id',
  },
  {
    title: 'NFT',
    key: 'nft',
    render: ({ imageSrc, name }: any) => (
      <div className="flex items-center w-max">
        <Image alt="" src={imageSrc} width={32} height={32} className="rounded-10 mr-1" />
        <p className="font-medium">{name}</p>
      </div>
    ),
  },
  {
    title: 'Owner',
    key: 'owner',
  },
  {
    title: 'Last Price',
    key: 'lastPrice',
    sortable: true,
    render: ({ coin, usd }: any) => (
      <div className="text-right">
        <p>{coin}</p>
        <p className="text-sm">{usd}</p>
      </div>
    ),
  },
  {
    title: 'Highest Price(24H)',
    key: 'highestPrice24h',
    render: ({ coin, usd }: any) => (
      <div className="text-right">
        <p>{coin}</p>
        <p className="text-sm">{usd}</p>
      </div>
    ),
  },
  {
    title: 'Sales(24H)',
    key: 'sales24h',
    render: ({ amount }: any) => (
      <div className="text-right">
        <p>{amount}</p>
      </div>
    ),
  },
  {
    title: 'Last Deal',
    key: 'lastDeal',
    render: ({ datetime }: any) => (
      <div className="text-right" style={{ paddingRight: '9px' }}>
        <p>{datetime}</p>
      </div>
    ),
  },
]
