import { useState, useEffect } from 'react'
import HolderTrendsChart from '../collection-analytics/holderTrendsChart'
import AmountDistributionPie from '../collection-analytics/amountDistributionPie'
import HoldingPeriodDistributionPie from '../collection-analytics/holdingPeriodDistributionPie'
import HighConcentrationChart from '../collection-analytics/highConcentrationChart'
import WhaleHoldersTable from '../collection-analytics/whaleHoldersTable'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import { FilterParams } from 'types/type'
import PeriodRange from 'domain/repositories/collection_daily_stat_repository/period_range'

//Todo remove mock data
import {
  filterWhaleHolders,
  whaleHoldersTableData,
  amountDistributionPieTempData,
  holdingPeriodDistributionTempData,
  holderTrendsTempData,
  highConcentrationTempData,
  filterConcentration,
} from 'presentation/controllers/mockData/collection_analytics_mock_data'
import { pageSizes } from 'lib/hook/customHook'
//End todo remove mock data

const periodRanges = PeriodRange.getResourceArray()
const PageSize = pageSizes.pageSize10

const AnalyticsHolder = (props: any) => {
  const {
    collectionId = '',
  } = props

  const initHolderTrendsParams: FilterParams = {
    periodRange: 'lastSevenDays',
    page: 1,
    limit: 60,
  }
  const {
    filterParams: filterHolderTrendsParams,
    changeFilterParam: changeHolderTrendsFilterParam,
  } = useChangeFilterParams(initHolderTrendsParams)
  const [holderTrendsData, setHolderTrendsData] = useState<any>([])
  const [isFetchingHolderTrends, setIsFetchingHolderTrends] = useState(true)
  const fetchHolderTrends = () => {
    setIsFetchingHolderTrends(true)
    //TODO
    if (process.env.NEXT_PUBLIC_NODE_ENV === 'development') {
      let data = [holderTrendsTempData, holderTrendsTempData]
      setHolderTrendsData(data)
    }
    //END TODO
  }

  const initHighConcentrationParams: FilterParams = {
    periodRange: 'lastSevenDays',
    page: 1,
    limit: 60,
  }
  const {
    filterParams: filterHighConcentrationParams,
    changeFilterParam: changeHighConcentrationFilterParam,
  } = useChangeFilterParams(initHighConcentrationParams)
  const [highConcentrationData, setHighConcentrationata] = useState<any>([])
  const [isFetchingHighConcentration, setIsFetchingHighConcentration] = useState(true)
  const fetchHighConcentration = () => {
    setIsFetchingHighConcentration(true)
    //TODO
    if (process.env.NEXT_PUBLIC_NODE_ENV === 'development') {
      let data = [...highConcentrationTempData]
      setHighConcentrationata(data)
    }
    //END TODO
  }

  const [amountDistributionData, setAmountDistributionData] = useState<any>([])
  const [isFetchingAmountDistribution, setIsFetchingAmountDistribution] = useState(true)
  const fetchAmountDistribution = () => {
    setIsFetchingAmountDistribution(true)
    //TODO
    if (process.env.NEXT_PUBLIC_NODE_ENV === 'development') {
      let data = [...amountDistributionPieTempData]
      setAmountDistributionData(data)
    }
    //END TODO
    setIsFetchingAmountDistribution(false)
  }

  const [holdingPeriodDistributionData, setHoldingPeriodDistributionData] = useState<any>([])
  const [isFetchingHoldingPeriodDistribution, setIsFetchingHoldingPeriodDistribution] = useState(true)
  const fetchHoldingPeriodDistribution = () => {
    setIsFetchingHoldingPeriodDistribution(true)
    //TODO
    if (process.env.NEXT_PUBLIC_NODE_ENV === 'development') {
      let data = [...holdingPeriodDistributionTempData]
      setHoldingPeriodDistributionData(data)
    }
    //END TODO
    setIsFetchingHoldingPeriodDistribution(false)
  }

  const initTopWhaleHoldersParams: FilterParams = {
    collectionIds: collectionId,
    page: 1,
    limit: 60,
  }
  const {
    filterParams: filterTopWhaleHoldersParams,
    changeFilterParam: changeTopWhaleHoldersFilterParam,
    setFilterParams: changeTopWhaleHoldersFilterParams,
  } = useChangeFilterParams(initTopWhaleHoldersParams)
  const [topWhaleHoldersCurrentPage, setTopWhaleHoldersCurrentPage] = useState(1)
  const [topWhaleHoldersCurrentLimit, setTopWhaleHoldersCurrentLimit] = useState(PageSize)
  const [topWhaleHoldersData, setTopWhaleHoldersData] = useState<any>([])
  const [topWhaleHoldersTotal, setTopWhaleHoldersTotal] = useState(0)
  const [isFetchingTopWhaleHolders, setIsFetchingTopWhaleHolders] = useState(true)
  const fetchTopWhaleHolders = () => {
    setIsFetchingTopWhaleHolders(true)
    //TODO
    if (process.env.NEXT_PUBLIC_NODE_ENV === 'development') {
      let data = [...whaleHoldersTableData]
      setTopWhaleHoldersTotal(data.length)
      data = data.slice((topWhaleHoldersCurrentPage - 1) * topWhaleHoldersCurrentLimit,
        topWhaleHoldersCurrentPage * topWhaleHoldersCurrentLimit)
      setTopWhaleHoldersData(data)
    }
    //END TODO
    setIsFetchingTopWhaleHolders(false)
  }

  useEffect(() => {
    fetchTopWhaleHolders()
  }, [filterTopWhaleHoldersParams])

  useEffect(() => {
    setIsFetchingHolderTrends(false)
  }, [holderTrendsData])

  useEffect(() => {
    setIsFetchingHighConcentration(false)
  }, [highConcentrationData])

  useEffect(() => {
    fetchHolderTrends()
    fetchHighConcentration()
    fetchAmountDistribution()
    fetchHoldingPeriodDistribution()
  }, [])

  return (
    <div className="container-lg">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
        {!isFetchingHolderTrends && (
          <HolderTrendsChart
            periodRanges={periodRanges}
            defaultSelected={periodRanges[0]}
            chartData={holderTrendsData}
            onChangePeriodRanges={changeHolderTrendsFilterParam}
          />
        )}
        {!isFetchingHighConcentration && (
          <HighConcentrationChart
            periodRanges={periodRanges}
            defaultSelected={periodRanges[0]}
            chartData={highConcentrationData}
            onChangeFilterParam={changeHighConcentrationFilterParam}
            filterTypes={filterConcentration}
          />
        )}
        {!isFetchingAmountDistribution && (
          <AmountDistributionPie chartData={amountDistributionData} />
        )}
        {!isFetchingHoldingPeriodDistribution && (
          <HoldingPeriodDistributionPie chartData={holdingPeriodDistributionData} />
        )}
      </div>

      <WhaleHoldersTable
        tableData={topWhaleHoldersData}
        currentPage={topWhaleHoldersCurrentPage}
        currentLimit={topWhaleHoldersCurrentLimit}
        total={topWhaleHoldersTotal}
        filterParams={filterTopWhaleHoldersParams}
        onChangePage={setTopWhaleHoldersCurrentPage}
        onChangeCurrentLimit={setTopWhaleHoldersCurrentLimit}
        onChangeFilterParam={changeTopWhaleHoldersFilterParam}
        onChangeFilterParams={changeTopWhaleHoldersFilterParams}
        isFetching={isFetchingTopWhaleHolders}
        filterHolderTypes={filterWhaleHolders}
      />
    </div>
  )
}

export default AnalyticsHolder
