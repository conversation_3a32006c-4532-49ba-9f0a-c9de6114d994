import dynamic from 'next/dynamic'
import { useState, useRef, memo } from 'react'
import Tooltip from 'presentation/components/tooltip'
const Pie = dynamic(() => import('@ant-design/plots').then(({ Pie }) => Pie), { ssr: false })

const MemoPlot = memo(({ config, getChartRef}: any) => {
  return <Pie {...config} onReady={(chartRef) => getChartRef(chartRef)} />
}, (pre, next) => {
  return JSON.stringify(pre.config) === JSON.stringify(next.config)
})

MemoPlot.displayName = 'MemoPlot'

const HoldingPeriodDistributionPie = (props:any) => {
  const {
    chartData = [],
  } = props
  const ref = useRef<any>(null)
  const [legendData, setLegendData] = useState([])

  const config: any = {
    width: 277,
    height: 277,
    appendPadding: 10,
    data: chartData,
    angleField: 'value',
    colorField: 'type',
    color: (...args:any) => {
      const type = args[0].type
      switch (type) {
        case '-24H':
          return '#3946EE'
        case '1-7D':
          return '#FBAD38'
        case '7-30D':
          return '#BB6BD9'
        case '30D-3M':
          return '#AB1DF2'
        case '3M-1Y':
          return '#6DC2EF'
        case '1Y-':
          return '#72D8DA'
        default:
          return 'transparent'
      }
    },
    radius: 1,
    innerRadius: 0.64,
    label: false,
    statistic: false,
    interactions: [
      {
        type: 'element-selected',
      },
      {
        type: 'element-active',
      },
    ],
    pieStyle: {
      lineWidth: 0,
    },
    legend: false,
  }

  const getChartRef = (chartRef: any) => {
    if (!chartRef) return
    ref.current = chartRef

    const chart = ref.current.chart
    const legendData = chart.geometries[0]?.dataArray?.map((item: any) => {
      const origin = item[0]._origin
      origin.color = item[0].color
      origin.checked = true
      return origin
    })
    setLegendData(legendData)
  }

  const handleLegendClick = (item: any, i: number) => {
    const newItem = item
    newItem.checked = !newItem.checked
    const currentLegend: any = [...legendData]
    currentLegend[i] = newItem

    const filteredLegendData = currentLegend
      .filter((l: any) => l.checked)
      .map((l: any) => l.type)

    const chart = ref.current.chart
    if (chart) {
      chart.filter('type', (val:any) => {
        return filteredLegendData.indexOf(val) > -1
      })
      chart.render()
    }
    setLegendData(currentLegend)
  }

  return (
    <div className="rounded-10 p-5 border border-gray-3">
      <div className="flex items-center mb-8">
        <p className="text-lg font-bold text-dark-primary dark:text-white">
          Holding Period Distribution
        </p>
        <Tooltip className="-mt-0.5">Holding Period Distribution</Tooltip>
      </div>
      <div className="flex flex-wrap">
        <div className="xl:w-3/5 md:w-full sm:w-3/5 w-full">
          <MemoPlot config={config} getChartRef={getChartRef} />
        </div>
        <div className="flex items-center xl:justify-end md:justify-center sm:justify-end justify-center xl:w-2/5 md:w-full sm:w-2/5 w-full pr-5">
          <ul className="flex flex-col xl:space-y-5 md:space-y-2 sm:space-y-5 space-y-2 w-32 flex-none">
            {legendData?.map((item: any, i: number) => (
              <li
                key={i} onClick={() => handleLegendClick(item, i)}
                className={'inline-flex justify-between cursor-pointer'}
              >
                <div className="inline-flex items-center">
                  <span
                    className="rounded-full w-2 h-2 mr-2"
                    style={{ backgroundColor: item.checked ? item.color : '#aaa' }}
                  ></span>
                  <span className="font-medium text-sm text-dark-3 dark:text-white">
                    {item.type}
                  </span>
                </div>
                <span className="font-medium text-dark-primary dark:text-white">
                  {item.value}%
                </span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  )
}

export default HoldingPeriodDistributionPie
