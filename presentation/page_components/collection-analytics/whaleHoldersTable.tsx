import FilterTime from 'presentation/components/filter-time'
import Table from 'presentation/components/table'
import Pagination from 'presentation/components/pagination'
import Tooltip from 'presentation/components/tooltip'
import LoadingData from 'presentation/components/skeleton/loading'
import { whaleholdersHeader } from './_whaleholdersHeader'

const WhalesHolderTable = (props:any) => {
  const {
    tableData = [],
    currentPage = 1,
    currentLimit = 10,
    total = 0,
    filterParams,
    onChangePage = () => {},
    onChangeCurrentLimit = () => {},
    onChangeFilterParam = () => {},
    onChangeFilterParams = () => {},
    isFetching,
    filterHolderTypes = [],
  } = props

  return (
    <div className="mt-10">
      <div className="flex flex-wrap justify-between">
        <div className="flex flex-wrap md:mb-0 mb-3">
          <div className="flex items-center">
            <p className="font-bold text-lg">Whales</p>
            <Tooltip>Whales</Tooltip>
          </div>
          <p className="text-sm text-gray-1 dark:text-gray-3 self-end md:ml-20 pl-2 w-full">
            474 whales hold NFTs of the collection.
          </p>
        </div>

        {/* TODO */}
        <FilterTime
          options={filterHolderTypes}
          defaultSelected={filterHolderTypes[0]}
          onSelect={(option) => {
            //TODO onChangeFilterParam('holderType', option)
          }}
        />
      </div>

      <div className="table-wrap border rounded-20 mt-4">
        {isFetching ? (
          <LoadingData/>
        ) : (
          <>
            <div className="w-full overflow-x-auto pb-3">
              <Table
                head={whaleholdersHeader}
                data={tableData}
                className="common-table holder min-w-[1200px]"
              />
            </div>
            <div className="mt-2 px-5 mb-9">
              <Pagination
                currentPage={currentPage}
                totalCount={total}
                pageSize={currentLimit}
                onPageChange={(page: number) => {
                  onChangePage(page)
                  onChangeFilterParams({...filterParams, page})
                }}
                onChangePageSize={(limit: number) => {
                  onChangePage(1)
                  onChangeCurrentLimit(limit)
                  onChangeFilterParams({...filterParams, limit, page: 1})
                }}
              />
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default WhalesHolderTable
