import { useState, useEffect } from 'react'
import { rowOptions } from 'presentation/controllers/mockData/options'
import ButtonIcon from 'presentation/components/button/button-icon'
import Select from 'presentation/components/select'
import Switch from 'presentation/components/switch'
import Tooltip from 'presentation/components/tooltip'
import Table from 'presentation/components/table'
import Pagination from 'presentation/components/pagination'
import DatetimeRange from 'presentation/components/datetimeRange'
import LoadingData from 'presentation/components/skeleton/loading'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import { activityAnalyticsHeader } from './_activityAnalyticsHeader'
import ActivityUseCase from 'presentation/use_cases/activity_use_case'
import CategoryUseCase from 'presentation/use_cases/category_use_case'
import { Query as CategoryQuery } from 'domain/repositories/category_repository/query'
import { Query as ActivityQuery } from 'domain/repositories/activity_repository/query'

const activityUseCase = new ActivityUseCase()
const categoryUseCase = new CategoryUseCase()

//Todo remove mock data
import { pageSizes } from 'lib/hook/customHook'
import Category from 'domain/models/category'
import { toast } from 'react-toastify'
//End todo remove mock data

let PageSize = pageSizes.pageSize20

const ActivityTab = (props: any) => {
  const { collectionId = '' } = props
  const [showDateRange, setShowDateRange] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [currentLimit, setCurrentLimit] = useState(PageSize)
  const [tableData, setTableData] = useState<any>([])
  const [total, setTotal] = useState(0)
  const [categories, setCategories] = useState<Category[]>([])
  const { filterParams, changeFilterParam, setFilterParams } =
    useChangeFilterParams({
      collectionIds: collectionId,
      categoryId: '',
      page: 1,
      limit: PageSize,
    })
  const [isFetching, setIsFetching] = useState(true)
  const [filterCategories, setFilterCategories] = useState<any>([])

  const fetchCategories = async () => {
    const query = new CategoryQuery()
    await categoryUseCase
      .search(query)
      .then((res) => {
        setCategories(res)
      })
      .catch((_error) => {
        toast.error('Failed to fetch categories')
      })
  }

  const fetchTableData = async () => {
    setIsFetching(true)
    const query = new ActivityQuery({
      collectionIds: filterParams.collectionIds,
      categoryId: filterParams.categoryId,
      page: filterParams.page,
    })
    await activityUseCase
      .totalCount(query)
      .then((res) => {
        setTotal(res)
      })
      .catch((_error) => {
        toast.error('Failed to fetch activity data')
      })
    await activityUseCase
      .search(query)
      .then((res) => {
        setTableData(res)
      })
      .catch((_error) => {
        toast.error('Failed to fetch activity data')
      })
  }

  const formatCategories = () => {
    const formattedCategories = categories.map((category) => {
      return {
        id: category.id,
        name: category.name,
        value: category.slug,
      }
    })
    setFilterCategories(formattedCategories)
  }

  useEffect(() => {
    fetchTableData()
  }, [filterParams])

  useEffect(() => {
    if (categories.length > 0) {
      formatCategories()
    }
  }, [categories])

  useEffect(() => {
    fetchCategories()
  }, [])

  return (
    <div>
      <div className="flex items-center justify-between flex-wrap">
        <p className="font-bold text-23 mb-2">Activity</p>
        <div className="flex flex-wrap items-center">
          <div className="inline-flex items-center mb-2">
            <Switch onClick={() => { }} />
            <div className="ml-1 mr-7 flex items-center">
              <p className="text-sm font-bold">Whale Activity</p>
              <Tooltip>Whale Activity</Tooltip>
            </div>
          </div>

          {filterCategories.length > 0 && (
            <Select
              options={filterCategories}
              onSelect={(option) =>
                changeFilterParam('categoryId', option.value)
              }
              className="shrink-0"
            />
          )}

          <div className="inline-flex items-center mb-2">
            <div className="flex relative w-10 h-10 ml-5 z-50">
              <ButtonIcon
                svgIcon="calendar"
                iconClassName="w-5 h-5 stroke-none fill-primary"
                onClick={() => setShowDateRange(!showDateRange)}
              />
              {showDateRange && (
                <div className="absolute top-10 -right-[500%]">
                  <DatetimeRange
                    showTimeInput={false}
                    showDuration={false}
                    onCloseDateRange={() => {
                      setShowDateRange(false)
                    }}
                  />
                </div>
              )}
            </div>
            <ButtonIcon
              className="mx-5"
              svgIcon="refresh"
              iconClassName="w-5 h-5 stroke-none fill-primary"
              onClick={() => {
                setCurrentPage(1),
                setFilterParams({ ...filterParams, page: 1 })
              }}
            />
            <Select
              defaultSelected={rowOptions[0]}
              options={rowOptions}
              onSelect={(option) => {
                setCurrentPage(1)
                setCurrentLimit(option.value)
                setFilterParams({
                  ...filterParams,
                  limit: option.value,
                  page: 1,
                })
              }}
            />
          </div>
        </div>
      </div>
      <div className="mt-2 table-wrap border rounded-20">
        {isFetching ? (
          <LoadingData />
        ) : (
          <>
            <div className="w-full overflow-x-auto pb-3 text-dark-primary dark:text-white">
              <Table
                head={activityAnalyticsHeader}
                data={tableData}
                className="common-table activity min-w-[1200px]"
              />
            </div>
            <div className="mt-2 px-5 mb-9">
              <Pagination
                currentPage={currentPage}
                totalCount={total}
                pageSize={currentLimit}
                onPageChange={(page: number) => {
                  setCurrentPage(page)
                  setFilterParams({ ...filterParams, page })
                }}
                onChangePageSize={(limit: number) => {
                  setCurrentPage(1)
                  setCurrentLimit(limit)
                  setFilterParams({ ...filterParams, limit, page: 1 })
                }}
              />
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default ActivityTab
