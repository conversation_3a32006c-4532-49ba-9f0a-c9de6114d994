import { useState, useEffect } from 'react'
import LeaderBoardGUI from '../analytics/leaderBoardGUI'
import { filterTimeOptions } from 'presentation/controllers/mockData/options'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'

//Todo remove mock data
import {
  leaderBoardTableData,
  topBuyerData as topBuyerTableData,
  topSellerData as topSellerTableData,
} from 'presentation/controllers/mockData/analytics_leaderboard_mock_data'
import { pageSizes } from 'lib/hook/customHook'
//End todo remove mock data

const PageSize = pageSizes.pageSize10

const LeaderboardTab = (props: any) => {
  const {
    analyticTabs,
    showHeadline = true,
    collectionId = '',
  } = props

  const [leaderBoardCurrentPage, setLeaderBoardCurrentPage] = useState(1)
  const [leaderBoardCurrentLimit, setLeaderBoardCurrentLimit] = useState(PageSize)
  const [leaderBoardData, setLeaderBoardData] = useState<any>([])
  const [leaderBoardTotal, setLeaderBoardTotal] = useState(0)
  const {
    filterParams: leaderBoardFilterParams,
    changeFilterParam: onChangeLeaderBoardFilterParam,
    setFilterParams: onChangeLeaderBoardFilterParams,
  } = useChangeFilterParams({
    collectionIds: collectionId,
    page: 1,
    limit: PageSize,
  })
  const [isFetchingLeaderBoard, setIsFetchingLeaderBoard] = useState(true)

  const [topBuyerCurrentPage, setTopBuyerCurrentPage] = useState(1)
  const [topBuyerData, setTopBuyerData] = useState<any>([])
  const [topBuyerTotal, setTopBuyerTotal] = useState(0)
  const {
    filterParams: topBuyerFilterParams,
    changeFilterParam: onChangeTopBuyerFilterParams,
  } = useChangeFilterParams({
    collectionIds: collectionId,
    listed: false,
    sortBy: filterTimeOptions[0]?.value,
    page: 1,
    limit: 20,
  })
  const [isFetchingTopBuyer, setIsFetchingTopBuyer] = useState(true)

  const [topSellerCurrentPage, setTopSellerCurrentPage] = useState(1)
  const [topSellerData, setTopSellerData] = useState<any>([])
  const [topSellerTotal, setTopSellerTotal] = useState(0)
  const {
    filterParams: topSellerFilterParams,
    changeFilterParam: onChangeTopSellerFilterParams,
  } = useChangeFilterParams({
    collectionIds: collectionId,
    listed: false,
    sortBy: filterTimeOptions[0]?.value,
    page: 1,
    limit: 20,
  })
  const [isFetchingTopSeller, setIsFetchingTopSeller] = useState(true)

  const fetchLeaderBoardTableData = () => {
    setIsFetchingLeaderBoard(true)
    //TODO
    setLeaderBoardTotal(leaderBoardTableData.length)
    let data = leaderBoardTableData.slice((leaderBoardCurrentPage - 1) * leaderBoardCurrentLimit, leaderBoardCurrentPage * leaderBoardCurrentLimit)
    setLeaderBoardData(data)
    setTimeout(() => {
      setIsFetchingLeaderBoard(false)
    }, 300)
  }

  const fetchTopBuyerTableData = () => {
    setIsFetchingTopBuyer(true)
    //TODO
    setTopBuyerTotal(topBuyerTableData.length)
    let data = topBuyerTableData.slice((topBuyerCurrentPage - 1) * PageSize, topBuyerCurrentPage * PageSize)
    setTopBuyerData(data)
    setTimeout(() => {
      setIsFetchingTopBuyer(false)
    }, 300)
  }

  const fetchTopSellerTableData = () => {
    setIsFetchingTopSeller(true)
    //TODO
    setTopSellerTotal(topSellerTableData.length)
    let data = topSellerTableData.slice((topBuyerCurrentPage - 1) * PageSize, topBuyerCurrentPage * PageSize)
    setTopSellerData(data)
    setTimeout(() => {
      setIsFetchingTopSeller(false)
    }, 300)
  }

  useEffect(() => {
    fetchLeaderBoardTableData()
  }, [leaderBoardFilterParams])

  useEffect(() => {
    fetchTopBuyerTableData()
  }, [topBuyerFilterParams])

  useEffect(() => {
    fetchTopSellerTableData()
  }, [topSellerFilterParams])

  return (
    <LeaderBoardGUI
      showHeadline={showHeadline}
      analyticTabs={analyticTabs}
      filterTimeOptions={filterTimeOptions}

      leaderBoardFilterParams={leaderBoardFilterParams}
      leaderBoardCurrentPage={leaderBoardCurrentPage}
      setLeaderBoardCurrentPage={setLeaderBoardCurrentPage}
      leaderBoardCurrentLimit={leaderBoardCurrentLimit}
      setLeaderBoardCurrentLimit={setLeaderBoardCurrentLimit}
      leaderBoardData={leaderBoardData}
      leaderBoardTotal={leaderBoardTotal}
      onChangeLeaderBoardFilterParam={onChangeLeaderBoardFilterParam}
      onChangeLeaderBoardFilterParams={onChangeLeaderBoardFilterParams}
      isFetchingLeaderBoard={isFetchingLeaderBoard}

      topBuyerCurrentPage={topBuyerCurrentPage}
      setTopBuyerCurrentPage={setTopBuyerCurrentPage}
      topBuyerData={topBuyerData}
      topBuyerTotal={topBuyerTotal}
      onChangeTopBuyerFilterParams={onChangeTopBuyerFilterParams}
      isFetchingTopBuyer={isFetchingTopBuyer}

      topSellerCurrentPage={topSellerCurrentPage}
      setTopSellerCurrentPage={setTopSellerCurrentPage}
      topSellerData={topSellerData}
      topSellerTotal={topSellerTotal}
      onChangeTopSellerFilterParams={onChangeTopSellerFilterParams}
      isFetchingTopSeller={isFetchingTopSeller}
    />
  )
}

export default LeaderboardTab
