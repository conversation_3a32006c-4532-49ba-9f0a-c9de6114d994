import Link from 'next/link'
import Image from 'next/image'
import SvgIcon from 'presentation/components/svg-icon'
import ShowMore from 'presentation/components/show-more'
import ButtonIcon from 'presentation/components/button/button-icon'
import Tooltip from 'presentation/components/tooltip'
import Button from 'presentation/components/button'
import SocialNetworks from '../socialNetworks'
import Collection from 'domain/models/collection'

const CollectionAnalyticsCard = ({ collection, analyticCards }: any) => {
  const activeCollection: Collection = collection
  return (
    <div className="collection-analytics-card">
      <div className="info-left">
        <div className="flex">
          <div className="relative shrink-0 w-16 h-16 lg:w-[100px] lg:h-[100px] rounded-full mr-5">
            <Image
              className="rounded-full"
              src={activeCollection.logoImage?.url!}
              alt="Character"
              fill
            />
          </div>
          <div>
            <p className="text-xl font-medium dark:text-slate-300">
              {activeCollection.name}
              <SvgIcon
                name="verify"
                className="w-5 h-5 ml-1 stroke-none fill-primary-2"
              />
            </p>
            <p className="mb-10p text-stroke-thin">
              <span className="text-black dark:text-white">Created by </span>
              <Link
                href={`${activeCollection.owner?.username}`}
                className="text-green-1 leading-none align-bottom"
              >
                <span className="text-primary-2 max-w-[100px] truncate inline-block">
                  {activeCollection.owner?.username}
                </span>
              </Link>
            </p>
            <ShowMore
              height="h-4"
              showMoreClass="flex items-center justify-center"
            >
              <p className="text-gray-1 dark:text-gray-3 text-stroke-thin">
                {activeCollection.description}
              </p>
            </ShowMore>
          </div>
        </div>
        <div className="mt-30p flex flex-wrap">
          {/* //Todo add functionality */}
          <ButtonIcon
            className="mr-3 shrink-0 px-4 py-2 mb-3 bg-white"
            svgIcon="plus"
            iconClassName="w-5 h-5 stroke-none fill-primary-2"
          >
            <span className="align-middle font-medium text-primary-2">
              Watchlist
            </span>
          </ButtonIcon>
          <div className="flex flex-wrap">
            <SocialNetworks {...activeCollection} />
          </div>
        </div>
      </div>

      <div className="info-right">
        <div className="flex xl:flex-nowrap flex-wrap gap-5">
          {/* Todo */}
          {analyticCards?.map((card: any, index: number) => (
            <div
              key={index}
              className="min-w-[130px] text-center px-2 py-15p rounded-10 bg-blue-2 dark:bg-blue-1"
            >
              <p className="font-bold sm:text-xl">{card.totalValue}</p>
              <p className={`font-medium text-${card.type}`}>
                {card.percentage}
              </p>
              <div className="flex items-center justify-center">
                <p className="text-sm text-gray-1 dark:text-gray-3 text-stroke-thin">
                  {card.field}
                </p>
                <Tooltip className="-mt-0.5">{card.description}</Tooltip>
              </div>
            </div>
          ))}
        </div>
        <Button variant="dark" size="sm" className="mt-25p">
          Buy Now
        </Button>
      </div>
    </div>
  )
}

export default CollectionAnalyticsCard
