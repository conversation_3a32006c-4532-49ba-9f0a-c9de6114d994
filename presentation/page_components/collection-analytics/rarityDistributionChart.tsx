import dynamic from 'next/dynamic'
import { useState, useRef, memo } from 'react'
import Tooltip from 'presentation/components/tooltip'
const Bar = dynamic(() => import('@ant-design/plots').then(({ Bar }) => Bar), { ssr: false })

const legendChartSubData: any = [
  {
    amount: 87,
  },
  {
    amount: 34,
  },
  {
    amount: 5731,
  },
  {
    amount: 4148,
  },
]

const MemoPlot = memo(({ config, getChartRef}: any) => {
  return <Bar {...config} onReady={(chartRef) => getChartRef(chartRef)} />
}, (pre, next) => {
  return JSON.stringify(pre.config) === JSON.stringify(next.config)
})

MemoPlot.displayName = 'MemoPlot'

const RarityDistributionChart = (props:any) => {
  const {
    chartData = [],
    maxValue = 0,
  } = props

  const ref = useRef<any>(null)
  const [legendData, setLegendData] = useState([])

  const config: any = {
    height: 280,
    data: chartData,
    xField: 'value',
    yField: 'year',
    seriesField: 'type',
    color: ({ type }: any) => {
      switch (type) {
        case 'Legendary':
          return '#F0AF51'
        case 'Rare':
          return '#B071D3'
        case 'Classic':
          return '#5A69F0'
        default:
          return '#C3C3CB'
      }
    },
    label: {
      position: 'right',
      offset: 4,
    },
    yAxis: {
      title: {
        text: 'Rarity Scorce',
        style: {
          fontSize: 14,
        },
      },
    },
    xAxis: {
      label: {
        formatter: (value:any) => {
          return value >= maxValue ? 'NFTs' : value
        },
      },
    },
    appendPadding: 10,
    legend: false,
  }

  const getChartRef = (chartRef: any) => {
    if (!chartRef) return
    ref.current = chartRef

    const legendData: any = [
      {
        color: '#FBAD38',
        checked: true,
        type: 'Legendary',
      },
      {
        color: '#B071D3',
        checked: true,
        type: 'Rare',
      },
      {
        color: '#5A69F0',
        checked: true,
        type: 'Classic',
      },
      {
        color: '#C3C3CB',
        checked: true,
        type: 'Normal',
      },
    ]
    setLegendData(legendData)
  }

  const handleLegendClick = (item: any, i: number) => {
    const newItem = item
    newItem.checked = !newItem.checked
    const currentLegend: any = [...legendData]
    currentLegend[i] = newItem

    const filteredLegendData = currentLegend
      .filter((l: any) => l.checked)
      .map((l: any) => l.type)

    const chart = ref.current.chart
    if (chart) {
      chart.filter('type', (val:any) => {
        return filteredLegendData.indexOf(val) > -1
      })
      chart.render()
    }
    setLegendData(currentLegend)
  }

  return (
    <div className="pt-5 pb-2.5 rounded-10 border border-gray-3">
      <div className="mb-22p flex items-center px-5">
        <p className="text-lg font-bold">Rarity Distribution</p>
        <Tooltip>Rarity Distribution</Tooltip>
      </div>

      <ul className="flex flex-wrap items-start xl:justify-center justify-start">
        {legendData?.map((item: any, i: number) => (
          <li key={i} onClick={() => handleLegendClick(item, i)} className="flex flex-col mb-5 ml-4 cursor-pointer">
            <div className="inline-flex items-center">
              <span
                className="rounded-full w-2 h-2 mr-2"
                style={{ backgroundColor: item.checked ? item.color : '#aaa' }}
              ></span>
              <span className="font-medium text-sm mr-1">{item.type}</span>
            </div>
            <span className="w-full text-right font-medium pr-1">
              {legendChartSubData[i].amount?.toLocaleString()}
            </span>
          </li>
        ))}
      </ul>

      <div className="w-full pl-2 pr-2.5">
        <MemoPlot config={config} getChartRef={getChartRef} />
      </div>
    </div>
  )
}

export default RarityDistributionChart
