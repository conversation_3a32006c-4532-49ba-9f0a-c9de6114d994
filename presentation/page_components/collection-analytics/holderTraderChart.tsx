import dynamic from 'next/dynamic'
import { useState, useRef, memo } from 'react'
import PeriodRangesSelect from 'presentation/components/select/period-ranges-select'
const DualAxes = dynamic(() => import('@ant-design/plots').then(({ DualAxes }) => DualAxes), { ssr: false })

const MemoPlot = memo(({ config, getChartRef}: any) => {
  return <DualAxes {...config} onReady={(chartRef) => getChartRef(chartRef)} />
}, (pre, next) => {
  return JSON.stringify(pre.config) === JSON.stringify(next.config)
})

MemoPlot.displayName = 'MemoPlot'

const legendColumnChartSubData: any = [
  {
    label: 'Buyers',
    amount: 65,
    percent: -44.44,
  },
  {
    label: 'Sellers',
    amount: 76,
    percent: -43.28,
  },
]

const HolderTraderChart = (props:any) => {
  const {
    periodRanges = [],
    chartData = [],
    defaultSelected,
    onChangePeriodRanges = () => {},
  } = props

  const ref = useRef<any>(null)
  const [legendHoldersData, setLegendHoldersData] = useState([])
  const [legendColumnsData, setLegendColumnsData] = useState([])

  const [selectedPeriodRangeText, setSelectedPeriodRangeText] = useState(defaultSelected.value || 7)

  const config: any = {
    data: chartData,
    height: 320,
    xField: 'time',
    yField: ['value', 'count'],
    geometryOptions: [
      {
        geometry: 'column',
        isGroup: true,
        seriesField: 'type',
        color: ['#5A66F9', '#BB6BD9'],
      },
      {
        geometry: 'line',
        lineStyle: {
          lineWidth: 2,
        },
        color: '#02AACF',
      },
    ],
    annotations: {},
    legend: false,
  }

  const getChartRef = (chartRef: any) => {
    if (!chartRef) return
    ref.current = chartRef
    const chart = chartRef.chart

    const legendColumnsData: any = [
      {
        color: '#5A66F9',
        checked: true,
        type: 'uv',
      },
      {
        color: '#BB6BD9',
        checked: true,
        type: 'bill',
      },
    ]
    setLegendColumnsData(legendColumnsData)

    const legendHolderssData: any = [{
      color: '#02AACF',
      checked: true,
    }]
    setLegendHoldersData(legendHolderssData)
  }

  const handleLegendColumnClick = (item: any, i: number) => {
    const newItem = item
    newItem.checked = !newItem.checked
    const currentLegend: any = [...legendColumnsData]
    currentLegend[i] = newItem

    const filteredLegendData = currentLegend
      .filter((l: any) => l.checked)
      .map((l: any) => l.type)

    const chart = ref.current.chart
    if (chart) {
      chart.views[0]?.filter('type', (val:any) => {
        return filteredLegendData.indexOf(val) > -1
      })
      chart.render()
    }
    setLegendColumnsData(currentLegend)
  }

  const handleLegendHoldersClick = (item: any, i: number) => {
    const newItem = item
    newItem.checked = !newItem.checked
    const currentLegend: any = [...legendHoldersData]
    currentLegend[i] = newItem

    const filteredLegendData = currentLegend
      .filter((l: any) => l.checked)

    const chart = ref.current.chart
    if (chart) {
      chart.views[1]?.filter('value', () => {
        return filteredLegendData.length > 0
      })
      chart.render()
    }
    setLegendHoldersData(currentLegend)
  }

  return <div className="rounded-10 p-5 border border-gray-3">
    <div className="flex flex-wrap justify-between">
      <p className="text-lg font-bold mb-4">Holders & Traders</p>
      <div className="shrink-0 mb-4">
        <PeriodRangesSelect
          periodRanges={periodRanges}
          defaultSelected={defaultSelected}
          onSelect={(option) => {
            setSelectedPeriodRangeText(option.subValue)
            onChangePeriodRanges('periodRange', option.value)
          }}
        />
      </div>
    </div>

    <ul className="flex flex-wrap items-start xl:justify-center justify-start">
      {legendHoldersData?.map((item: any, i: number) => (
        <li
          key={i} onClick={() => handleLegendHoldersClick(item, i)}
          className="flex flex-col mb-5 xl:ml-0 ml-4 cursor-pointer"
        >
          <div className="inline-flex items-center">
            <span
              className="rounded-full w-2 h-2 mr-2"
              style={{ backgroundColor: item.checked ? item.color : '#aaa' }}
            ></span>
            <span className="font-medium text-sm mr-1">Holders</span>
          </div>
          <span className="w-full text-right font-medium pr-1">3.38K</span>
          <span className="w-full text-right text-sm text-red-1 font-medium pr-1">-0.03%</span>
        </li>
      ))}
      {legendColumnsData?.map((item: any, i: number) => (
        <li key={i} onClick={() => handleLegendColumnClick(item, i)} className="flex flex-col mb-5 ml-4">
          <div className="inline-flex items-center">
            <span
              className="rounded-full w-2 h-2 mr-2"
              style={{ backgroundColor: item.checked ? item.color : '#aaa' }}
            ></span>
            <span className="font-medium text-sm mr-1">
              {legendColumnChartSubData[i].label}({selectedPeriodRangeText}{selectedPeriodRangeText !== 'All time' && 'D'})
            </span>
          </div>
          <span className="w-full text-right font-medium pr-1">
            {legendColumnChartSubData[i].amount}
          </span>
          <span className={`w-full text-right text-sm font-medium pr-1 ${legendColumnChartSubData[i].percent > 0 ? 'text-primary-1' : 'text-red-1'}`}>
            {legendColumnChartSubData[i].percent > 0 ? '+' : ''}{legendColumnChartSubData[i].percent}%
          </span>
        </li>
      ))}
    </ul>

    <MemoPlot config={config} getChartRef={getChartRef} />
  </div>
}

export default HolderTraderChart
