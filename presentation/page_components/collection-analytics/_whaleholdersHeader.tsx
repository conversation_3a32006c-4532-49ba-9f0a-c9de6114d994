export const whaleholdersHeader = [
  {
    title: '#',
    key: 'id',
  },
  {
    title: 'Address',
    key:'address',
    render: ({imageSrc, address}: any) => (
      <div className="flex items-center" style={{ maxWidth: '80px' }}>
        {/* <Image alt='' src={imageSrc} width={21} height={21}/> */}
        <p className="font-medium truncate">{address}</p>
      </div>
    ),
  },
  {
    title: 'NFTs',
    key: 'nfts',
    description: 'NFTs',
    sortable: true,
    options: {
      tooltipPosition: 'right',
    },
    render: ({ qty, percentage }: any) => (
      <div className="text-right" style={{ paddingRight: '14px' }}>
        <p className="font-medium">{qty}</p>
        <p className="text-sm">{percentage}</p>
      </div>
    ),
  },
  {
    title: 'Holding Value',
    key: 'holdingValue',
    description: 'Holding Value',
    sortable: true,
    options: {
      tooltipPosition: 'right',
    },
    render: ({ price, percentage }: any) => (
      <div className="text-right" style={{ paddingRight: '14px' }}>
        <p className="font-medium">{price}</p>
        <p className="text-sm">{percentage}</p>
      </div>
    ),
  },
  {
    title: 'Esl Value',
    key: 'estValue',
    description: 'Esl Value',
    sortable: true,
    options: {
      tooltipPosition: 'right',
    },
    render: ({price, percentage}: any) => (
      <div className="flex flex-wrap justify-end" style={{ paddingRight: '14px' }}>
        <p>{price}</p>
        <div className="percentage-bar">
          <div className="percentage-track" style={{width: percentage}}></div>
        </div>
      </div>
    ),
  },
  {
    title: 'Est URP',
    key: 'estUrp',
    description: 'Est Urp',
    sortable: true,
    options: {
      tooltipPosition: 'right',
    },
    render: ({price, percentage}: any) => (
      <div className="flex flex-wrap justify-end" style={{ paddingRight: '14px' }}>
        <p>{price}</p>
        <div className="percentage-bar">
          <div className="percentage-track" style={{width: percentage}}></div>
        </div>
      </div>
    ),
  },
  {
    title: 'Buy Volume',
    key: 'buyVolume',
    description: 'Buy Volume description',
    sortable: true,
    options: {
      tooltipPosition: 'right',
    },
    render: ({price, percentage}: any) => (
      <div className="flex flex-wrap justify-end" style={{ paddingRight: '14px' }}>
        <p>{price}</p>
        <div className="percentage-bar">
          <div className="percentage-track" style={{width: percentage}}></div>
        </div>
      </div>
    ),
  },
  {
    title: 'Sell Volume',
    key: 'sellVolume',
    description: 'Sell Volume description',
    sortable: true,
    options: {
      tooltipPosition: 'bottomRight',
    },
    render: ({price, percentage}: any) => (
      <div className="flex flex-wrap justify-end" style={{ paddingRight: '14px' }}>
        <p>{price}</p>
        <div className="percentage-bar">
          <div className="percentage-track" style={{width: percentage}}></div>
        </div>
      </div>
    ),
  },
  {
    title: 'Last Trade',
    key: 'lastTrade',
    render: ({ datetime }: any) => (
      <div className="text-right" style={{ paddingRight: '9px' }}>
        <p className="text-primary-2 whitespace-nowrap">{datetime}</p>
      </div>
    ),
  },
]
