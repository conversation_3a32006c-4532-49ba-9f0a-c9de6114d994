import dynamic from 'next/dynamic'
import { useState, useRef, memo } from 'react'
import Tooltip from 'presentation/components/tooltip'
import PeriodRangesSelect from 'presentation/components/select/period-ranges-select'
const Mix = dynamic(() => import('@ant-design/plots').then(({ Mix }) => Mix), { ssr: false })

const MemoPlot = memo(({ config, getChartRef}: any) => {
  return <Mix {...config} onReady={(chartRef) => getChartRef(chartRef)} />
}, (pre, next) => {
  return JSON.stringify(pre.config) === JSON.stringify(next.config)
})

MemoPlot.displayName = 'MemoPlot'

const PriceChart = (props:any) => {
  const {
    periodRanges = [],
    chartData = [],
    defaultSelected,
    onChangePeriodRanges = () => {},
  } = props

  const ref = useRef<any>(null)
  const [legendFloorPriceData, setLegendFloorPriceData] = useState([])
  const [legendAvgPriceData, setLegendAvgPriceData] = useState([])
  const [legendMaxPriceData, setLegendMaxPriceData] = useState([])
  const [legendMinPriceData, setLegendMinPriceData] = useState([])

  const config: any = {
    appendPadding: 8,
    tooltip: {
      shared: false,
    },
    syncViewPadding: true,
    height: 267,
    padding: 10,
    plots: [
      {
        type: 'line',
        options: {
          data: [
            {
              date: '02-01',
              value: 160,
            },
            {
              date: '02-02',
              value: 245,
            },
            {
              date: '02-03',
              value: 207,
            },
            {
              date: '02-04',
              value: 120,
            },
            {
              date: '02-05',
              value: 140,
            },
            {
              date: '02-06',
              value: 180,
            },
          ],
          xField: 'date',
          yField: 'value',
          xAxis: {
            tickCount: 6,
          },
          yAxis: {
            max: 800,
            tickCount: 6,
          },
          color: '#BB6BDA',
          meta: false,
          label: false,
        },
      },
      {
        type: 'line',
        options: {
          data: [
            {
              date: '02-01',
              value: 280,
            },
            {
              date: '02-02',
              value: 370,
            },
            {
              date: '02-03',
              value: 149,
            },
            {
              date: '02-04',
              value: 160,
            },
            {
              date: '02-05',
              value: 260,
            },
            {
              date: '02-06',
              value: 320,
            },
          ],
          xField: 'date',
          yField: 'value',
          xAxis: false,
          yAxis: {
            max: 800,
            tickCount: 6,
          },
          color: '#5A66F9',
          meta: false,
          smooth: false,
          label: false,
        },
      },
      {
        type: 'scatter',
        options: {
          data: chartData,
          xField: 'date',
          yField: 'value',
          xAxis: false,
          yAxis: false,
          shape: 'circle',
          sizeField: 'value',
          size: 7,
          pointStyle: {
            fill: '#FABC62',
            // stroke: '#B9BEFA',
            opacity: 0.8,
          },
          label: false,
          meta: false,
          color: '#FABC62',
        },
      },
      {
        type: 'scatter',
        options: {
          data: [
            {
              date: '02-01',
              value: 426,
            },
            {
              date: '02-01 1',
              value: 496,
            },
            {
              date: '02-01 2',
              value: 400,
            },
            {
              date: '02-01 3',
              value: 410,
            },
            {
              date: '02-01 4',
              value: 390,
            },
            {
              date: '02-01 5',
              value: 426,
            },
            {
              date: '02-01 6',
              value: 380,
            },
            {
              date: '02-01 7',
              value: 410,
            },
            {
              date: '02-01 8',
              value: 390,
            },
            {
              date: '02-02',
              value: 334,
            },
            {
              date: '02-03',
              value: 330,
            },
            {
              date: '02-03 1',
              value: 496,
            },
            {
              date: '02-03 2',
              value: 400,
            },
            {
              date: '02-03 3',
              value: 410,
            },
            {
              date: '02-03 4',
              value: 390,
            },
            {
              date: '02-03 5',
              value: 426,
            },
            {
              date: '02-03 6',
              value: 380,
            },
            {
              date: '02-03 7',
              value: 410,
            },
            {
              date: '02-03 8',
              value: 390,
            },
            {
              date: '02-04',
              value: 420,
            },
            {
              date: '02-05',
              value: 390,
            },
            {
              date: '02-06',
              value: 380,
            },
          ],
          xField: 'date',
          yField: 'value',
          xAxis: false,
          yAxis: false,
          shape: 'circle',
          pointStyle: {
            fill: '#E7E8F8',
            stroke: '#B9BEFA',
            opacity: 0.8,
          },
          sizeField: 'value',
          size: 7,
          label: false,
          meta: false,
          color: '#E7E8F8',
        },
      },
      {
        type: 'scatter',
        options: {
          data: [
            {
              date: '02-01',
              value: 126,
            },
            {
              date: '02-01 1',
              value: 326,
            },
            {
              date: '02-01 2',
              value: 333,
            },
            {
              date: '02-01 3',
              value: 217,
            },
            {
              date: '02-01 4',
              value: 300,
            },
            {
              date: '02-01 5',
              value: 126,
            },
            {
              date: '02-01 6',
              value: 280,
            },
            {
              date: '02-01 7',
              value: 310,
            },
            {
              date: '02-01 8',
              value: 225,
            },
            {
              date: '02-02',
              value: 124,
            },
            {
              date: '02-03',
              value: 250,
            },
            {
              date: '02-03 1',
              value: 296,
            },
            {
              date: '02-03 2',
              value: 310,
            },
            {
              date: '02-03 3',
              value: 200,
            },
            {
              date: '02-03 4',
              value: 290,
            },
            {
              date: '02-03 5',
              value: 326,
            },
            {
              date: '02-03 6',
              value: 285,
            },
            {
              date: '02-03 7',
              value: 198,
            },
            {
              date: '02-03 8',
              value: 190,
            },
            {
              date: '02-04',
              value: 320,
            },
            {
              date: '02-05',
              value: 367,
            },
            {
              date: '02-06',
              value: 280,
            },
          ],
          xField: 'date',
          yField: 'value',
          xAxis: false,
          yAxis: false,
          shape: 'circle',
          pointStyle: {
            fill: '#B9BEFA',
            // stroke: '#B9BEFA',
            opacity: 0.8,
          },
          sizeField: 'value',
          size: [5, 20],
          label: false,
          meta: false,
          color: '#E7E8F8',
        },
      },
    ],
  }

  const getChartRef = (chartRef: any) => {
    if (!chartRef) return
    ref.current = chartRef
    const chart = chartRef.chart

    const legendFloorPriceData = chart.views[0]?.geometries[0]?.dataArray?.map((item: any) => {
      const origin = item[0]._origin
      origin.color = item[0].color
      origin.checked = true
      return origin
    })
    setLegendFloorPriceData(legendFloorPriceData)

    const legendAvgPricData = chart.views[1]?.geometries[0]?.dataArray?.map((item: any) => {
      const origin = item[0]._origin
      origin.color = item[0].color
      origin.checked = true
      return origin
    })
    setLegendAvgPriceData(legendAvgPricData)

    const legendMaxPriceData: any = [{
      color: '#1EA2A6',
      checked: true,
    }]
    setLegendMaxPriceData(legendMaxPriceData)

    const legendMinPriceData: any = [{
      color: '#FBAD37',
      checked: true,
    }]
    setLegendMinPriceData(legendMinPriceData)
  }

  const handleLegendFloorPriceClick = (item: any, i: number) => {
    const newItem = item
    newItem.checked = !newItem.checked
    const currentLegend: any = [...legendFloorPriceData]
    currentLegend[i] = newItem

    const filteredLegendData = currentLegend
      .filter((l: any) => l.checked)

    const chart = ref.current.chart
    if (chart) {
      chart.views[0]?.filter('value', () => {
        return filteredLegendData.length > 0
      })
      chart.render()
    }
    setLegendFloorPriceData(currentLegend)
  }

  const handleLegendAvgPriceClick = (item: any, i: number) => {
    const newItem = item
    newItem.checked = !newItem.checked
    const currentLegend: any = [...legendAvgPriceData]
    currentLegend[i] = newItem

    const filteredLegendData = currentLegend
      .filter((l: any) => l.checked)

    const chart = ref.current.chart
    if (chart) {
      chart.views[1]?.filter('value', () => {
        return filteredLegendData.length > 0
      })
      chart.render()
    }
    setLegendAvgPriceData(currentLegend)
  }

  const handleLegendMaxPriceClick = (item: any, i: number) => {
    const newItem = item
    newItem.checked = !newItem.checked
    const currentLegend: any = [...legendMinPriceData]
    currentLegend[i] = newItem

    const filteredLegendData = currentLegend
      .filter((l: any) => l.checked)

    const chart = ref.current.chart
    if (chart) {
      chart.views[3]?.filter('value', () => {
        return filteredLegendData.length > 0
      })
      chart.views[4]?.filter('value', () => {
        return filteredLegendData.length > 0
      })
      chart.render()
    }
    setLegendMaxPriceData(currentLegend)
  }

  const handleLegendMinPriceClick = (item: any, i: number) => {
    const newItem = item
    newItem.checked = !newItem.checked
    const currentLegend: any = [...legendMinPriceData]
    currentLegend[i] = newItem

    const filteredLegendData = currentLegend
      .filter((l: any) => l.checked)

    const chart = ref.current.chart
    if (chart) {
      chart.views[2]?.filter('value', () => {
        return filteredLegendData.length > 0
      })
      chart.render()
    }
    setLegendMinPriceData(currentLegend)
  }

  return (
    <div className="pt-4 px-5 pb-7 mt-10 rounded-10 border border-gray-3">
      <div className="flex items-center">
        <p className="text-lg font-bold">Price (ETH)</p>
        <Tooltip>Description</Tooltip>
      </div>

      <div className="xl:px-20">
        <div className="mt-3 md:mt-7 flex gap-4 flex-wrap justify-between">
          <PeriodRangesSelect
            periodRanges={periodRanges}
            defaultSelected={defaultSelected}
            onSelect={(option) => {
              onChangePeriodRanges('periodRange', option.value)
            }}
          />

          <ul className="flex flex-wrap items-start">
            {legendFloorPriceData?.map((item: any, i: number) => (
              <li key={i} onClick={() => handleLegendFloorPriceClick(item, i)} className="flex flex-col mb-5 ml-12 cursor-pointer">
                <div className="inline-flex items-center">
                  <span
                    className="rounded-full w-2 h-2 mr-2"
                    style={{ backgroundColor: item.checked ? item.color : '#aaa' }}
                  ></span>
                  <span className="font-medium text-sm mr-1">Floor Price</span>
                </div>
                <span className="w-full text-right text-primary-2 font-medium pr-1">69.5</span>
              </li>
            ))}
            {legendAvgPriceData?.map((item: any, i: number) => (
              <li key={i} onClick={() => handleLegendAvgPriceClick(item, i)} className="flex flex-col mb-5 ml-12 cursor-pointer">
                <div className="inline-flex items-center">
                  <span
                    className="rounded-full w-2 h-2 mr-2"
                    style={{ backgroundColor: item.checked ? item.color : '#aaa' }}
                  ></span>
                  <span className="font-medium text-sm mr-1">Avg Price(7D)</span>
                </div>
                <span className="w-full text-right text-primary-2 font-medium pr-1">79.7171</span>
                <span className="w-full text-right text-sm text-green-1 font-medium pr-1">+26.41%</span>
              </li>
            ))}
            {legendMaxPriceData?.map((item: any, i: number) => (
              <li key={i} onClick={() => handleLegendMaxPriceClick(item, i)} className="flex flex-col mb-5 ml-12 cursor-pointer">
                <div className="inline-flex items-center">
                  <span
                    className="rounded-full w-2 h-2 mr-2"
                    style={{ backgroundColor: item.checked ? item.color : '#aaa' }}
                  ></span>
                  <span className="font-medium text-sm mr-1">Max Price(7D)</span>
                </div>
                <span className="w-full text-right text-primary-2 font-medium pr-1">246.49</span>
              </li>
            ))}
            {legendMinPriceData?.map((item: any, i: number) => (
              <li key={i} onClick={() => handleLegendMinPriceClick(item, i)} className="flex flex-col mb-5 ml-12 cursor-pointer">
                <div className="inline-flex items-center">
                  <span
                    className="rounded-full w-2 h-2 mr-2"
                    style={{ backgroundColor: item.checked ? item.color : '#aaa' }}
                  ></span>
                  <span className="font-medium text-sm mr-1">Min Price(7D)</span>
                </div>
                <span className="w-full text-right text-primary-2 font-medium pr-1">0</span>
              </li>
            ))}
          </ul>
        </div>

        <div className="mt-3">
          <MemoPlot config={config} getChartRef={getChartRef} />
        </div>
      </div>
    </div>
  )
}

export default PriceChart
