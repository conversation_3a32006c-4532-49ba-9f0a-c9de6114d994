import { useState, useEffect } from 'react'
import { FilterParams } from 'types/type'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import RarityDistributionChart from './rarityDistributionChart'
import Ra<PERSON><PERSON><PERSON><PERSON><PERSON> from './rarityPriceChart'
import NFTTable from './nftTable'
import AssetUseCase from 'presentation/use_cases/asset_use_case'
import { Query as AssetQuery } from 'domain/repositories/asset_repository/query'
import SortBy from 'domain/repositories/asset_repository/sort_by'
import Asset from 'domain/models/asset'

const assetUseCase = new AssetUseCase()

//Todo remove mock data
import {
  rarityDistributionChartTempData,
  rarityPriceChartTempData,
} from 'presentation/controllers/mockData/collection_analytics_mock_data'
//End todo remove mock data

const RarityTab = (props:any) => {
  const {
    collectionId = '',
  } = props

  const initParams: FilterParams = {
    collectionIds: collectionId,
    rarityScore: '',
    minPrice: '',
    maxPrice: '',
    page: 1,
    limit: 25,
    sortBy: 'recentlyListed',
  }

  const [rarityDistributionChartData, setRarityDistributionChartData] = useState<any>([])
  const [rarityDistributionMaxValue, setRarityDistributionMaxValue] = useState<any>([])
  const [isFetchingRarityDistributionChart, setIsFetchingRarityDistributionChart] = useState(true)

  const [rarityPriceMaxValue, setRarityPriceMaxValue] = useState<any>([])
  const [rarityPriceChartData, setRarityPriceChartData] = useState<any>([])
  const [isFetchingRarityPriceChart, setIsFetchingRarityPriceChart] = useState(true)

  const [currentPage, setCurrentPage] = useState(1)
  const [assets, setAssets] = useState<Asset[]>([])
  const [total, setTotal] = useState<number>()
  const [isFetchingAssets, setIsFetchingAssets] = useState<boolean>(true)
  const [sortList, setSortList] = useState<any>([])
  const {filterParams, changeFilterParam, setFilterParams} = useChangeFilterParams(initParams)


  const fetchRarityDistributionChartData = async () => {
    setIsFetchingRarityDistributionChart(true)
    //TODO
    const maxValue = Math.max(...rarityDistributionChartTempData.map((record:any) => record.value))
    setRarityDistributionMaxValue(maxValue)
    setRarityDistributionChartData(rarityDistributionChartTempData)
  }

  const fetchRarityPriceChartData = async () => {
    setIsFetchingRarityPriceChart(true)
    //TODO
    const maxValue = Math.max(...rarityPriceChartTempData.map((record:any) => record['Rating']))
    setRarityPriceMaxValue(maxValue)
    setRarityPriceChartData(rarityPriceChartTempData)
  }

  const formatSortList = () => {
    const sortList = SortBy.getResourceArray().map((sort:any) => {
      return {
        id: sort.id,
        name: sort.label,
        value: sort.name,
      }
    })
    setSortList(sortList)
  }

  const fetchAssets = async () => {
    setIsFetchingAssets(true)
    const query = new AssetQuery({
      collectionIds: filterParams.collectionIds,
      rarityScore: filterParams.rarityScore,
      minPrice: filterParams.minPrice,
      maxPrice: filterParams.maxPrice,
      page: filterParams.page,
      limit: filterParams.limit,
      sortBy: SortBy.fromName(filterParams.sortBy!),
    })
    await assetUseCase.totalCount(query).then((res) => {
      setTotal(res)
    }).catch((_error) => {
      setTotal(0)
    })
    await assetUseCase.search(query).then((res) => {
      setAssets(res)
    }).catch((_error) => {
      setAssets([])
    })
    setIsFetchingAssets(false)
  }

  useEffect(() => {
    setIsFetchingRarityDistributionChart(false)
  }, [rarityDistributionChartData])

  useEffect(() => {
    setIsFetchingRarityPriceChart(false)
  }, [rarityPriceChartData])

  useEffect(() => {
    fetchAssets()
  }, [filterParams])

  useEffect(() => {
    fetchRarityDistributionChartData()
    fetchRarityPriceChartData()
    formatSortList()
  }, [])

  return (
    <>
      <div className="container-lg">
        <div className="grid lg:grid-cols-2 grid-cols-1 lg:gap-x-10 lg:gap-y-0 gap-y-5">
          {!isFetchingRarityDistributionChart && (
            <RarityDistributionChart
              chartData={rarityDistributionChartData}
              maxValue={rarityDistributionMaxValue}
            />
          )}
          {!isFetchingRarityPriceChart && (
            <RarityPriceChart
              chartData={rarityPriceChartData}
              maxValue={rarityPriceMaxValue}
            />
          )}
        </div>
      </div>

      <NFTTable
        tableData={assets}
        currentPage={currentPage}
        total={total}
        filterParams={filterParams}
        onChangePage={setCurrentPage}
        onChangeFilterParam={changeFilterParam}
        onChangeFilterParams={setFilterParams}
        isFetching={isFetchingAssets}
        sortList={sortList}
      />
    </>
  )
}

export default RarityTab
