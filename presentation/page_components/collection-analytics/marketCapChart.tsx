import React, { useState } from 'react'
import PeriodRangesSelect from 'presentation/components/select/period-ranges-select'
import ActivityChart from '../collection/activityChart'

const MarketCapChart = (props:any) => {
  const {
    periodRanges = [],
    chartData = [],
    defaultSelected,
    onChangePeriodRanges = () => {},
  } = props

  const [selectedPeriodRangeText, setSelectedPeriodRangeText] = useState(defaultSelected.value || 90)

  return (
    <div className="pt-4 px-5 pb-7 rounded-10 border border-gray-3">
      <p className="text-lg font-bold">Market Cap & Volume</p>
      <div className="xl:px-20">
        <div className="mt-3 md:mt-7 flex gap-4 flex-wrap justify-between">
          <PeriodRangesSelect
            periodRanges={periodRanges}
            defaultSelected={defaultSelected}
            onSelect={(option) => {
              setSelectedPeriodRangeText(option.subValue)
              onChangePeriodRanges('periodRange', option.value)
            }}
          />
          <div className="flex gap-4">
            <div>
              <p className="font-medium text-sm">{selectedPeriodRangeText} Day Avg. Price</p>
              <p className="font-bold text-primary-2">2.1691</p>
            </div>
            <div>
              <p className="font-medium text-sm">{selectedPeriodRangeText} Day Volume</p>
              <p className="font-bold text-primary-2">51,217.8958</p>
            </div>
          </div>
        </div>
        <div className="h-[200px] mt-3">
          <ActivityChart data={chartData} />
        </div>
      </div>
    </div>
  )
}

export default MarketCapChart
