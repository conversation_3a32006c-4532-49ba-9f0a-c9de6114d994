import dynamic from 'next/dynamic'
import { useState, useRef, memo } from 'react'
import Tooltip from 'presentation/components/tooltip'
import PeriodRangesSelect from 'presentation/components/select/period-ranges-select'
const DualAxes = dynamic(() => import('@ant-design/plots').then(({ DualAxes }) => DualAxes), { ssr: false })

const MemoPlot = memo(({ config, getChartRef}: any) => {
  return <DualAxes {...config} onReady={(chartRef) => getChartRef(chartRef)} />
}, (pre, next) => {
  return JSON.stringify(pre.config) === JSON.stringify(next.config)
})

MemoPlot.displayName = 'MemoPlot'

const HolderTrendsChart = (props:any) => {
  const {
    periodRanges = [],
    chartData = [],
    defaultSelected,
    onChangePeriodRanges = () => {},
  } = props

  const ref = useRef<any>(null)
  const [legendHoldersData, setLegendHoldersData] = useState([])
  const [legendWhalesData, setLegendWhalesData] = useState([])

  const config: any = {
    data: chartData,
    height: 320,
    xField: 'year',
    yField: ['value', 'count'],
    xAxis: {
      title: {
        text: 'UTC                     ',
        position: 'start',
        offset: 14,
        style: {
          fontSize: 12,
        },
      },
    },
    yAxis: {
      value: {
        title: {
          text: 'Holders',
          style: {
            fontSize: 14,
          },
        },
      },
      count: {
        title: {
          text: 'Whales',
          style: {
            fontSize: 14,
          },
        },
      },
    },
    legend: false,
    geometryOptions: [
      {
        geometry: 'line',
        color: '#72D8DA',
      },
      {
        geometry: 'line',
        color: '#BB6BD9',
      },
    ],
  }

  const getChartRef = (chartRef: any) => {
    if (!chartRef) return
    ref.current = chartRef
    const chart = chartRef.chart

    const legendHoldersData: any = [{
      color: '#5A66F9',
      checked: true,
    }]
    setLegendHoldersData(legendHoldersData)

    const legendWhalesData: any = [{
      color: '#BB6BD9',
      checked: true,
    }]
    setLegendWhalesData(legendWhalesData)
  }

  const handleLegendHoldersClick = (item: any, i: number) => {
    const newItem = item
    newItem.checked = !newItem.checked
    const currentLegend: any = [...legendHoldersData]
    currentLegend[i] = newItem

    const filteredLegendData = currentLegend
      .filter((l: any) => l.checked)

    const chart = ref.current.chart
    if (chart) {
      chart.views[0]?.filter('value', () => {
        return filteredLegendData.length > 0
      })
      chart.render()
    }
    setLegendHoldersData(currentLegend)
  }

  const handleLegendWhalesClick = (item: any, i: number) => {
    const newItem = item
    newItem.checked = !newItem.checked
    const currentLegend: any = [...legendWhalesData]
    currentLegend[i] = newItem

    const filteredLegendData = currentLegend
      .filter((l: any) => l.checked)

    const chart = ref.current.chart
    if (chart) {
      chart.views[1]?.filter('value', () => {
        return filteredLegendData.length > 0
      })
      chart.render()
    }
    setLegendWhalesData(currentLegend)
  }

  return <div className="rounded-10 p-5 border border-gray-3">
    <div className="flex flex-wrap justify-between">
      <div className="flex items-start justify-start mb-4">
        <p className="text-lg font-bold">Holder Trends</p>
        <Tooltip className="-mt-0.5">Description</Tooltip>
      </div>
      {periodRanges.length > 0 && (
        <div className="shrink-0 mb-4">
          <PeriodRangesSelect
            periodRanges={periodRanges}
            defaultSelected={defaultSelected}
            onSelect={(option) => {
              onChangePeriodRanges('periodRange', option.value)
            }}
            shortenTitle
          />
        </div>
      )}
    </div>

    <ul className="flex flex-wrap items-start xl:justify-center justify-start">
      {legendHoldersData?.map((item: any, i: number) => (
        <li key={i} onClick={() => handleLegendHoldersClick(item, i)} className="flex flex-col mb-5 mr-4 cursor-pointer">
          <div className="inline-flex items-center">
            <span
              className="rounded-full w-2 h-2 mr-2"
              style={{ backgroundColor: item.checked ? item.color : '#aaa' }}
            ></span>
            <span className="font-medium text-sm mr-1">Holders</span>
          </div>
          <span className="w-full text-right font-medium pr-1">{(3409).toLocaleString()}</span>
          <span className="w-full text-right text-sm text-red-1 font-medium pr-1">-24.54%</span>
        </li>
      ))}
      {legendWhalesData?.map((item: any, i: number) => (
        <li key={i} onClick={() => handleLegendWhalesClick(item, i)} className="flex flex-col mb-5">
          <div className="inline-flex items-center">
            <span
              className="rounded-full w-2 h-2 mr-2"
              style={{ backgroundColor: item.checked ? item.color : '#aaa' }}
            ></span>
            <span className="font-medium text-sm mr-0.5">Whales</span>
            <Tooltip className="flex items-center -mt-0.5">Description</Tooltip>
          </div>
          <span className="w-full text-right font-medium pr-5">{(474).toLocaleString()}</span>
          <span className="w-full text-right text-sm text-primary-1 font-medium pr-5">+10.95%</span>
        </li>
      ))}
    </ul>

    <MemoPlot config={config} getChartRef={getChartRef} />
  </div>
}

export default HolderTrendsChart
