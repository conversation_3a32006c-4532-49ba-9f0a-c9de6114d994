import { useState } from 'react'
import { rowOptions } from 'lib/valueObjects'
import Pagination from 'presentation/components/pagination'
import FilterTime from 'presentation/components/filter-time'
import Table from 'presentation/components/table'
import Select from 'presentation/components/select'
import LoadingData from 'presentation/components/skeleton/loading'
import { topSaleHeader } from './_topSaleHeader'

const TopSaleBoard = (props:any) => {
  const {
    filterTimeOptions = [],
    defaultTimeFilter,
    filterParams,
    currentPage = 1,
    onChangePage = () => {},
    currentLimit = 20,
    onChangeCurrentLimit = () => {},
    tableData = [],
    total = 0,
    onChangeFilterParam = () => {},
    onChangeFilterParams = () => {},
    isFetching = true,
  } = props

  const [filterTime, setFilterTime] = useState(defaultTimeFilter ? defaultTimeFilter : filterTimeOptions[3])

  return (
    <div className="mt-10">
      <div className="flex flex-wrap gap-y-3 justify-between">
        <p className="pl-5 font-bold text-lg">Top Sales({filterTime.name})</p>
        <div className="flex flex-wrap gap-5">
          <FilterTime
            options={filterTimeOptions}
            defaultSelected={filterTime}
            onSelect={(option) => {setFilterTime(option)}}
          />
          <Select
            defaultSelected={rowOptions[0]}
            options={rowOptions}
            onSelect={(option) => {
              onChangePage(1)
              onChangeCurrentLimit(option.value)
              onChangeFilterParams({...filterParams, limit: option.value, page: 1})
            }}
          />
        </div>
      </div>
      <div className="table-wrap mt-5 border rounded-20">
        {isFetching ? (
          <LoadingData/>
        ) : (
          <>
            <div className="w-full overflow-x-auto pb-3">
              <Table
                head={topSaleHeader}
                data={tableData}
                className="common-table top-sale min-w-[1000px]"
              />
            </div>
            <div className="mt-2 px-5 mb-9">
              <Pagination
                enableSelectRow={true}
                currentPage={currentPage}
                totalCount={total}
                pageSize={currentLimit}
                onPageChange={(page: number) => {
                  onChangePage(page)
                  onChangeFilterParams({...filterParams, page})
                }}
                onChangePageSize={(limit: number) => {
                  onChangePage(1)
                  onChangeCurrentLimit(limit)
                  onChangeFilterParams({...filterParams, limit, page: 1})
                }}
              />
            </div>
          </>
        )
        }
      </div>
    </div>
  )
}

export default TopSaleBoard
