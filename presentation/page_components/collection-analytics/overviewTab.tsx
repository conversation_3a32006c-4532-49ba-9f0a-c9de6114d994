import Link from 'next/link'
import { useEffect, useState } from 'react'
import SkeletonAsset from 'presentation/components/skeleton/assets'
import MarketCapChart from '../collection-analytics/marketCapChart'
import PriceChart from '../collection-analytics/priceChart'
import TransactionLiquid<PERSON>hart from '../collection-analytics/transactionLiquid<PERSON>hart'
import HolderTrader<PERSON>hart from '../collection-analytics/holderTrader<PERSON>hart'
import TopSaleBoard from '../collection-analytics/topSaleBoard'
import CollectionCommon from 'presentation/components/collection/common'
import NoData from 'presentation/components/no-data'
import Asset from 'presentation/components/asset'
import { filterMinHoursDay } from 'presentation/controllers/mockData/options'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import { FilterParams } from 'types/type'
import AssetUseCase from 'presentation/use_cases/asset_use_case'
import CollectionUseCase from 'presentation/use_cases/collection_use_case'
import CollectionDailyStatUseCase from 'presentation/use_cases/collection_daily_stat_use_case'
import { Query as AssetQuery } from 'domain/repositories/asset_repository/query'
import { Query as CollectionQuery } from 'domain/repositories/collection_repository/query'
import { Query as CollectionDailyStatQuery } from 'domain/repositories/collection_daily_stat_repository/query'
import { pageSizes } from 'lib/hook/customHook'
import AssetModel from 'domain/models/asset'
import PeriodRange from 'domain/repositories/collection_daily_stat_repository/period_range'

const assetUseCase = new AssetUseCase()
const collectionUseCase = new CollectionUseCase()
const collectionDailyStatsUseCase = new CollectionDailyStatUseCase()

//Todo remove mock data
import { topSaleTableData } from 'presentation/controllers/mockData/collection_analytics_mock_data'
import {
  holderTraderChartData1,
  holderTraderChartData2,
} from 'presentation/controllers/mockData/collection_daily_stats_mock_data'
//End todo remove mock data

const PageSize = pageSizes.pageSize20

const OverviewTab = (props: any) => {
  const { collectionId = '' } = props

  const initCollectionDailyStatParams: FilterParams = {
    periodRange: 'lastNinetyDays',
    page: 1,
    limit: 60,
  }

  const {
    filterParams: filterCollectionDailyStatParams,
    changeFilterParam: changeCollectionDailyStatFilterParam,
  } = useChangeFilterParams(initCollectionDailyStatParams)
  const [collectionDailyStats, setCollectionDailyStats] = useState<any>([])
  const [isCollectionDailyStatsReady, setIsCollectionDailyStatsReady] =
    useState(false)

  const {
    filterParams: filterPriceChartParams,
    changeFilterParam: changePriceChartFilterParam,
  } = useChangeFilterParams({ ...initCollectionDailyStatParams })
  const [priceChartData, setPriceChartData] = useState<any>([])
  const [isPriceChartDataReady, setIsPriceChartDataReady] = useState(false)

  const {
    filterParams: filterTransactionLiquidParams,
    changeFilterParam: changeTransactionLiquidFilterParam,
  } = useChangeFilterParams({ ...initCollectionDailyStatParams })
  const [transactionLiquidData, setTransactionLiquidData] = useState<any>([])
  const [isTransactionLiquidDataReady, setIsTransactionLiquidDataReady] =
    useState(false)

  const {
    filterParams: filterHolderTraderParams,
    changeFilterParam: changeHolderTraderFilterParam,
  } = useChangeFilterParams({ ...initCollectionDailyStatParams })
  const [holderTraderData, setHolderTraderData] = useState<any>([])
  const [isHolderTraderDataReady, setIsHolderTraderDataReady] = useState(false)

  const [currentPage, setCurrentPage] = useState(1)
  const [currentLimit, setCurrentLimit] = useState(PageSize)
  const [tableData, setTableData] = useState<any>([])
  const [total, setTotal] = useState(0)
  const { filterParams, changeFilterParam, setFilterParams } =
    useChangeFilterParams({
      page: 1,
      limit: PageSize,
    })
  const [isFetching, setIsFetching] = useState(true)

  const [soldAssets, setSoldAssets] = useState<AssetModel[]>([])
  const [isFetchingSoldAssets, setIsFetchingSoldAssets] = useState(false)

  const [createdAssets, setCreatedAssets] = useState<any>([])
  const [isFetchingCreatedAssets, setIsFetchingCreatedAssets] = useState(false)

  const [collections, setCollections] = useState<any>([])
  const [isFetchingCollections, setIsFetchingCollections] = useState(false)

  const [periodRanges, setPeriodRanges] = useState<any>([])

  const fetchCollectionDailyStats = async () => {
    const query = new CollectionDailyStatQuery({
      collectionId: collectionId,
      periodRange: PeriodRange.fromName(
        filterCollectionDailyStatParams.periodRange!
      ),
      page: filterCollectionDailyStatParams.page,
      limit: filterCollectionDailyStatParams.limit,
    })
    await collectionDailyStatsUseCase
      .search(query)
      .then((res) => {
        setCollectionDailyStats(res)
      })
      .catch((_error) => {
        setCollectionDailyStats([])
      })
  }

  const fetchPriceChartData = async () => {
    const query = new CollectionDailyStatQuery({
      collectionId: collectionId,
      periodRange: PeriodRange.fromName(filterPriceChartParams.periodRange!),
      page: filterPriceChartParams.page,
      limit: filterPriceChartParams.limit,
    })
    await collectionDailyStatsUseCase
      .search(query)
      .then((res) => {
        setPriceChartData(res)
      })
      .catch((_error) => {
        setPriceChartData([])
      })
  }

  const fetchTransactionLiquidData = async () => {
    const query = new CollectionDailyStatQuery({
      collectionId: collectionId,
      periodRange: PeriodRange.fromName(
        filterTransactionLiquidParams.periodRange!
      ),
      page: filterTransactionLiquidParams.page,
      limit: filterTransactionLiquidParams.limit,
    })
    await collectionDailyStatsUseCase
      .search(query)
      .then((res) => {
        setTransactionLiquidData(res)
      })
      .catch((_error) => {
        setTransactionLiquidData([])
      })
  }

  const fetchHolderTraderData = async () => {
    //TODO
    setHolderTraderData([holderTraderChartData1, holderTraderChartData2])
  }

  const fetchTableData = () => {
    setIsFetching(true)
    //TODO
    setTotal(topSaleTableData.length)
    let data = topSaleTableData.slice(
      (currentPage - 1) * currentLimit,
      currentPage * currentLimit
    )
    setTableData(data)
    setTimeout(() => {
      setIsFetching(false)
    }, 300)
  }

  const fetchSoldAssets = async () => {
    setIsFetchingSoldAssets(true)
    const query = new AssetQuery({
      collectionIds: [collectionId],
      sortBy: 'recentlySold',
      limit: 6,
    })
    await assetUseCase
      .search(query)
      .then((res) => {
        setSoldAssets(res)
      })
      .catch((_error) => {
        setSoldAssets([])
      })
    setIsFetchingSoldAssets(false)
  }

  const fetchCreatedAssets = async () => {
    setIsFetchingCreatedAssets(true)
    const query = new AssetQuery({
      collectionIds: [collectionId],
      sortBy: 'recentlyCreated',
      limit: 6,
    })
    await assetUseCase
      .search(query)
      .then((res) => {
        setCreatedAssets(res)
      })
      .catch((_error) => {
        setCreatedAssets([])
      })
    setIsFetchingCreatedAssets(false)
  }

  const fetchCollections = async () => {
    setIsFetchingCollections(true)
    const query = new CollectionQuery({
      limit: 5,
      sortBy: 'recentlyListed',
    })
    await collectionUseCase
      .search(query)
      .then((res) => {
        setCollections(res)
      })
      .catch((_error) => {
        setCollections([])
      })
    setIsFetchingCollections(false)
  }

  const getPeriodRanges = () => {
    const data = PeriodRange.getResourceArray()
    setPeriodRanges(data)
  }

  useEffect(() => {
    fetchSoldAssets()
    fetchCreatedAssets()
    fetchCollections()
  }, [collectionId])

  useEffect(() => {
    fetchTableData()
  }, [filterParams])

  useEffect(() => {
    fetchCollectionDailyStats()
  }, [filterCollectionDailyStatParams])
  useEffect(() => {
    setIsCollectionDailyStatsReady(true)
  }, [collectionDailyStats])

  useEffect(() => {
    fetchPriceChartData()
  }, [filterPriceChartParams])
  useEffect(() => {
    setIsPriceChartDataReady(true)
  }, [priceChartData])

  useEffect(() => {
    fetchTransactionLiquidData()
  }, [filterTransactionLiquidParams])
  useEffect(() => {
    setIsTransactionLiquidDataReady(true)
  }, [transactionLiquidData])

  useEffect(() => {
    fetchHolderTraderData()
  }, [filterHolderTraderParams])
  useEffect(() => {
    setIsHolderTraderDataReady(true)
  }, [holderTraderData])

  useEffect(() => {
    getPeriodRanges()
  }, [])

  return (
    <div className="container-lg">
      {isCollectionDailyStatsReady && (
        <MarketCapChart
          periodRanges={periodRanges}
          defaultSelected={periodRanges[4]}
          onChangePeriodRanges={changeCollectionDailyStatFilterParam}
          chartData={collectionDailyStats}
        />
      )}

      {isPriceChartDataReady && (
        <PriceChart
          periodRanges={periodRanges}
          defaultSelected={periodRanges[0]}
          onChangePeriodRanges={changePriceChartFilterParam}
          chartData={priceChartData}
        />
      )}

      <div className="mt-10 grid md:grid-cols-2 gap-10">
        {isTransactionLiquidDataReady && (
          <TransactionLiquidChart
            periodRanges={periodRanges}
            defaultSelected={periodRanges[0]}
            onChangePeriodRanges={changeTransactionLiquidFilterParam}
            chartData={transactionLiquidData}
          />
        )}

        {isHolderTraderDataReady && (
          <HolderTraderChart
            periodRanges={periodRanges}
            defaultSelected={periodRanges[0]}
            onChangePeriodRanges={changeHolderTraderFilterParam}
            chartData={holderTraderData}
          />
        )}
      </div>

      <TopSaleBoard
        filterTimeOptions={filterMinHoursDay}
        defaultTimeFilter={filterMinHoursDay[0]}
        filterParams={filterParams}
        currentPage={currentPage}
        onChangePage={setCurrentPage}
        currentLimit={currentLimit}
        onChangeCurrentLimit={setCurrentLimit}
        tableData={tableData}
        total={total}
        onChangeFilterParam={changeFilterParam}
        onChangeFilterParams={setFilterParams}
        isFetching={isFetching}
      />

      <div className="mt-43p">
        <p className="text-23 pl-5 mb-21p font-bold">Recently Sold</p>
        <div
          className={`product-wrap collection-main full-page no-gap
            ${!isFetchingSoldAssets && soldAssets.length === 0 ? 'no-data' : ''}
            justify-center md:justify-start`}
        >
          {isFetchingSoldAssets ? (
            [...Array(6)].map((_, i) => <SkeletonAsset key={i} />)
          ) : (
            <>
              {soldAssets.length > 0 ? (
                soldAssets.map((asset: any, index: number) => (
                  <Asset
                    key={asset.id + '-' + index}
                    record={asset}
                    showDaysLeft={false}
                  />
                ))
              ) : (
                <div className="w-full rounded-lg border flex justify-center items-center py-24">
                  <NoData />
                </div>
              )}
            </>
          )}
        </div>
      </div>

      <div className="mt-43p">
        <p className="text-23 pl-5 mb-21p font-bold">Recently Created</p>
        <div
          className={`product-wrap collection-main full-page no-gap
            ${
    !isFetchingCreatedAssets && createdAssets.length === 0
      ? 'no-data'
      : ''
    }
            justify-center md:justify-start`}
        >
          {isFetchingCreatedAssets ? (
            [...Array(6)].map((_, i) => <SkeletonAsset key={i} />)
          ) : (
            <>
              {createdAssets.length > 0 ? (
                createdAssets.map((asset: any, index: number) => (
                  <Asset
                    key={asset.id + '-' + index}
                    record={asset}
                    showDaysLeft={false}
                  />
                ))
              ) : (
                <div className="w-full rounded-lg border flex justify-center items-center py-24">
                  <NoData />
                </div>
              )}
            </>
          )}
        </div>
      </div>

      <div className="mt-43p">
        <p className="text-23 pl-5 mb-21p font-bold">
          You Might Be Interested At
        </p>
        <div className="container-lg">
          {isFetchingCollections ? (
            [...Array(5)].map((_, i) => <SkeletonAsset key={i} />)
          ) : (
            <div className="collection-cont">
              {collections.length > 0 ? (
                collections.map((collection: any, index: number) => (
                  <div key={index} className="collection">
                    <Link href={`/collection/${collection.slug}`}>
                      <CollectionCommon {...collection} record={collection} />
                    </Link>
                  </div>
                ))
              ) : (
                <div className="w-full rounded-lg border flex justify-center items-center py-24">
                  <NoData />
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default OverviewTab
