import dynamic from 'next/dynamic'
import Tooltip from 'presentation/components/tooltip'
const Scatter = dynamic(() => import('@ant-design/plots').then(({ Scatter }) => Scatter), { ssr: false })

const RarityPriceChart = (props:any) => {
  const {
    chartData = [],
    maxValue = 0,
  } = props

  const config: any = {
    height: 300,
    appendPadding: 10,
    data: chartData,
    xField: 'Rating',
    yField: 'Revenue (Millions)',
    shape: 'circle',
    colorField: 'Genre',
    size: 4,
    legend: false,
    yAxis: {
      nice: true,
      line: {
        style: {
          stroke: '#aaa',
        },
      },
      title: {
        text: 'Price(ETH)',
        style: {
          fontSize: 14,
        },
      },
    },
    xAxis: {
      min: 1,
      grid: {
        line: {
          style: {
            stroke: '#eee',
          },
        },
      },
      line: {
        style: {
          stroke: '#aaa',
        },
      },
      label: {
        formatter: (value:any) => {
          return value >= maxValue ? value+'(Score)' : value
        },
      },
    },
  }

  return (
    <div className="pt-5 pb-2.5 rounded-10 border border-gray-3 flex flex-col justify-between">
      <div className="mb-22p flex items-center px-5">
        <p className="text-lg font-bold">Rarity & Price</p>
        <Tooltip>Rarity & Price</Tooltip>
      </div>
      <div className="w-full pl-2 pr-2.5">
        <Scatter {...config} />
      </div>
    </div>
  )
}

export default RarityPriceChart
