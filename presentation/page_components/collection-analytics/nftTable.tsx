import Asset from 'presentation/components/asset'
import Select from 'presentation/components/select'
import NoData from 'presentation/components/no-data'
import SelectSearch from 'presentation/components/select/select-search'
import ToggleWrap from 'presentation/components/toggle-wrap'
import SkeletonAsset from 'presentation/components/skeleton/assets'
import ToggleVerticalFilterPrice from 'presentation/components/toggle-wrap/toggle-vertical-filter-price'
import ToggleVerticalFilterRarityScore from 'presentation/components/toggle-wrap/toggle-vertical-filter-rarity-score'
import PaginationDot from 'presentation/components/pagination/pagination-dot'

//Todo remove mock data
import { selectSearchOptions } from 'presentation/controllers/mockData/common_mock_data'
//End todo remove mock data

const RarityNFTTable = (props:any) => {
  const {
    tableData = [],
    currentPage = 1,
    currentLimit = 20,
    total = 0,
    filterParams,
    onChangePage = () => {},
    onChangeFilterParam = () => {},
    onChangeFilterParams = () => {},
    isFetching,
    sortList = [],
  } = props

  return (
    <>
      <div className="mt-10 product-layout">
        <div className="product-layout-filter">
          <ToggleWrap
            title="Filter"
            contentClass="border-t-0 overflow-hidden"
            backgroundTitleClassName="bg-primary-2"
          >
            <ToggleVerticalFilterRarityScore
              defaultShow
              changeFilterParam={onChangeFilterParam}
            />
            <ToggleVerticalFilterPrice
              defaultShow
              minPrice={filterParams.minPrice || ''}
              maxPrice={filterParams.maxPrice || ''}
              changeFilterParam={onChangeFilterParam}
            />
          </ToggleWrap>
        </div>

        <div className="w-full md:w-9/12 nft-rarity-wrap">
          <div className="mb-5 flex flex-wrap justify-between">
            <div>
              <p className="font-bold text-23">NFT</p>
              {isFetching ? (
                <p className='w-36 h-4 bg-gray-3 mt-5 rounded-lg animate-pulse'></p>
              ) : (
                <p className="text-gray-1 dark:text-gray-3 text-stroke-thin mt-5">Results：{total}</p>
              )}
            </div>
            <div className="mt-2 lg:mt-0 flex flex-wrap lg:flex-nowrap gap-5">
              <SelectSearch options={selectSearchOptions} className="shrink-0"/>
              {sortList.length > 0 && (
                <Select
                  className="h-fit"
                  options={sortList}
                  onSelect={(option) => {onChangeFilterParam('sortBy', option.value)}}
                />
              )}
            </div>
          </div>
          <div
            className={`product-wrap no-gap ${!isFetching && tableData.length === 0 ? 'no-data' : ''}
              justify-center md:justify-start`}
          >
            {isFetching ? (
              [...Array(5)].map((_, id) => (<SkeletonAsset key={id}/>))
            ) : (
              <>
                {tableData.length > 0 ? tableData.map((asset: any, index:number) => (
                  <Asset key={asset.id+'-'+index} record={asset} showDaysLeft={false} />
                )) : (
                  <div className="w-full rounded-lg border flex justify-center items-center py-24">
                    <NoData/>
                  </div>
                )}
              </>
            )}
          </div>
          {tableData.length > 20 && !isFetching && (
            <div className="flex justify-center mt-5">
              <PaginationDot
                currentPage={currentPage}
                totalCount={total}
                pageSize={currentLimit}
                onPageChange={(page: number) => {
                  onChangePage(page),
                  onChangeFilterParams({...filterParams, page})
                }}
              />
            </div>
          )}
        </div>
      </div>
    </>
  )
}

export default RarityNFTTable
