import AssetThumbnail from 'presentation/components/asset/thumbnail'
import Button from 'presentation/components/button'
import Link from 'next/link'

export const orderTableHeader = [
  {
    title: 'Item',
    key: 'asset',
    render: (asset: any) => (
      <Link
        href={`/assets/${asset?.id}`}
        className="py-3 flex items-center relative w-max"
      >
        {asset && <AssetThumbnail asset={asset}
          className="h-[60px] !w-[60px] !rounded-none"
          classNameImage="!rounded-none !border-none" />}
        <div className="max-w-[12rem] py-1 ml-2">
          <p className="font-medium truncate text-dark-primary dark:text-white">{asset?.name}</p>
          <span className="text-sm font-medium text-primary-2 break-words">
            {asset?.collection?.name}
          </span>
        </div>
      </Link> 
    ),
  },
  {
    title: 'Unit Price',
    key: 'unitPrice',
    tdClass: 'whitespace-nowrap',
    render: ({ price, paymentToken }: any) => (
      <div className="px-2 uppercase min-w-[130px]">
        {price} {paymentToken?.symbol || paymentToken?.label}
      </div>
    ),
  },
  {
    title: 'Quantity',
    key: 'quantity',
  },
  {
    title: 'Amount',
    key: 'amount',
    tdClass: 'whitespace-nowrap',
    render: ({ price, paymentToken }: any) => (
      <div className="px-2 uppercase">
        {price} {paymentToken?.symbol || paymentToken?.label}
      </div>
    ),
  },
  {
    title: 'Network',
    key: 'network',
  },
  {
    title: '',
    key: 'action',
    tdClass: 'text-right',
    render: (action: any) => (
      <>
        {action && (
          <Button 
            variant={action.variantBtn} 
            size="xs" 
            className={`!py-1.5 !px-[16px] w-[130px] ${!action?.onClick ? 'cursor-text' : ''}`}
            onClick={() => {action.onClick ? action.onClick(action.order) : null}}
          >
            {action.text}
          </Button>
        )}
      </>
    ),
  },
]


