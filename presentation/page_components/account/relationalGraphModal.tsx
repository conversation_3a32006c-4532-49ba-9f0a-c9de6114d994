import dynamic from 'next/dynamic'
import Tooltip from 'presentation/components/tooltip'
import Modal from 'presentation/components/modal'
import FilterTime from 'presentation/components/filter-time'
import Image from 'next/image'

const RadialTreeGraph = dynamic(() => import('@ant-design/graphs').then((_package) => {
  return _package.RadialTreeGraph
}), { ssr: false })


const RelationalGraphModal = (props:any) => {
  const {
    chartData = [],
    legends = [],
    filterTypes = [],
    defaultType = {},
    onChangeFilterParam = () => {},
    isFetching = true,
    showModal = false,
    onClose = () => {},
  } = props

  const config: any = {
    data: chartData,
    height: 400,
    nodeCfg: {
      type: 'image',
      style: (arg: any) => {
        return {
          fill: arg.id === 'Classification' ? '#7E87FF' : '#D8EADF',
          stroke: arg.id === 'Classification' ? '#3946EE' : '#77C18E',
          lineWidth: 2,
          strokeOpacity: 0.45,
          backgroundColor: arg.id === 'Classification' ? '#7E87FF' : '#D8EADF',
        }
      },
    },
    edgeCfg: {
      style: {
        stroke: '#77C18E',
      },
      endArrow: {
        type: 'triangle',
        fill: '#77C18E',
        d: 15,
        size: 8,
      },
      edgeStateStyles: {
        hover: {
          stroke: '#77C18E',
          lineWidth: 2,
        },
      },
    },
    behaviors: ['drag-canvas', 'drag-node'],
  }

  return (
    <Modal
      title="Relational Graph"
      isShow={showModal}
      onClose={() => onClose(false)}
      contentWrapClass="rounded-b-10 dark:bg-black"
      modalWidthClass="min-w-3/4 md:min-w-2/4 xl:min-w-1/4 lg:w-fit w-11/12"
    >
      <div className="pt-10p px-4 lg:pl-14 md:pl-8 lg:pr-7 md:pr-6 lg:max-w-[716px] w-full">
        <div className="mb-5 flex flex-nowrap justify-between items-center gap-2">
          <p className="flex-grow text-sm text-gray-1 dark:text-gray-3 text-stroke-thin md:w-auto w-full">
            The relationship between the address and its most related counterparties.
            Only Send or Receive included.
          </p>
          <FilterTime
            options={filterTypes}
            defaultSelected={defaultType}
            onSelect={(option) => onChangeFilterParam('type', option.value)}
          />
        </div>
        {!isFetching && (
          <>
            <div className="w-full h-[360px] overflow-hidden bg-transparent">
              <RadialTreeGraph {...config} />
            </div>
            <div className="flex flex-col items-end w-full space-y-1.5 mb-4">
              {legends.map((legend:any, index:number) => (
                <div key={index} className="inline-flex items-center justify-start space-x-2 w-48">
                  <div className="flex items-center flex-none">
                    <Image alt={legend.label} src={legend.img} width={30} height={30} />
                  </div>
                  <span className="font-medium text-dark-primary dark:text-white">{legend.label}</span>
                  {legend.tooltip && (
                    <Tooltip className="-mt-0.5">{legend.tooltip}</Tooltip>
                  )}
                </div>
              ))}
            </div>
          </>
        )}
      </div>
    </Modal>
  )
}

export default RelationalGraphModal
