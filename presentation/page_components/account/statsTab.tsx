import { useState, useEffect } from 'react'
import HoldingPie from './holdingPie'
import CumulativeChart from './cumulativeChart'
import RelatedAddressBoard from './relatedAddressBoard'
import TraderChart from './traderChart'
import CollectionsBoard from './collectionsBoard'
import RelationalGraphModal from './relationalGraphModal'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'

//Todo remove mock data
import { filterHoldingPie, dayOptions, filterGraph } from 'presentation/controllers/mockData/options'

import {
  relatedAddressTempData,
  accountCollectionTempData,
  holdingTempData,
  cumulativeTempData,
  traderTempData,
  traderLegendTempData,
  relationalGraphTempData,
  relationalGraphLegendTempData,
} from 'presentation/controllers/mockData/account_mock_data'
//End todo remove mock data

const AccountStatsTab = () => {
  const [holdingData, setHoldingData] = useState<any>([])
  const {
    filterParams: holdingFilterParams,
    changeFilterParam: setHoldingFilterParam,
  } = useChangeFilterParams({})
  const [isFetchingHolding, setIsFetchingHolding] = useState(true)

  const [cumulativeData, setCumulativeData] = useState<any>([])
  const {
    filterParams: cumulativeFilterParams,
    changeFilterParam: setCumulativeFilterParam,
  } = useChangeFilterParams({})
  const [isFetchingCumulative, setIsFetchingCumulative] = useState(true)

  const [traderData, setTraderData] = useState<any>([])
  const [traderLegendsData, setTraderLegendsData] = useState<any>([])
  const {
    filterParams: traderFilterParams,
    changeFilterParam: setTraderFilterParam,
  } = useChangeFilterParams({})
  const [isFetchingTrader, setIsFetchingTrader] = useState(true)

  const [addressesCurrentPage, setAddressesCurrentPage] = useState(1)
  const [addressesCurrentLimit, setAddressesCurrentLimit] = useState(5)
  const [addressesTableData, setAddressesTableData] = useState<any>([])
  const [addressesTotal, setAddressesTotal]  = useState(0)
  const {
    filterParams: addressesFilterParams,
    setFilterParams: setAddressesFilterParams,
  } = useChangeFilterParams({
    page: 1,
    limit: 5,
  })
  const [isFetchingAddresses, setIsFetchingAddresses] = useState(true)

  const [relationalGraphData, setRelationalGraphData] = useState<any>([])
  const [relationalGraphLegendsData, setRelationalGraphLegendsData] = useState<any>([])
  const {
    filterParams: relationalGraphFilterParams,
    changeFilterParam: setRelationalGraphFilterParam,
  } = useChangeFilterParams({})
  const [isFetchingRelationalGraph, setIsFetchingRelationalGraph] = useState(true)

  const [collectionsCurrentPage, setCollectionsCurrentPage] = useState(1)
  const [collectionsCurrentLimit, setCollectionsCurrentLimit] = useState(20)
  const [collectionsTableData, setCollectionsTableData] = useState<any>([])
  const [collectionsTotal, setCollectionsTotal]  = useState(0)
  const {
    filterParams: collectionsFilterParams,
    setFilterParams: setCollectionsFilterParams,
  } = useChangeFilterParams({
    page: 1,
    limit: 20,
  })
  const [isFetchingCollections, setIsFetchingCollections] = useState(true)

  const [showModal, setShowModal] = useState(false)

  const fetchHoldingData = () => {
    setIsFetchingHolding(true)
    // TODO
    setHoldingData(holdingTempData)
    setIsFetchingHolding(false)
  }

  const fetchCumulativeData = () => {
    setIsFetchingCumulative(true)
    // TODO
    setCumulativeData(cumulativeTempData)
    setIsFetchingCumulative(false)
  }

  const fetchTraderData = () => {
    setIsFetchingTrader(true)
    // TODO
    setTraderData(traderTempData)
    setTraderLegendsData(traderLegendTempData)
    setIsFetchingTrader(false)
  }

  const fetchAddressesTableData = () => {
    setIsFetchingAddresses(true)
    // TODO
    setAddressesTotal(relatedAddressTempData.length)
    const data = relatedAddressTempData.slice((addressesCurrentPage - 1) * addressesCurrentLimit,
      addressesCurrentPage * addressesCurrentLimit)
    setAddressesTableData(data)
    setIsFetchingAddresses(false)
  }

  const fetchRelationalGraphData = () => {
    setIsFetchingRelationalGraph(true)
    //TODO
    setRelationalGraphData(relationalGraphTempData)
    setRelationalGraphLegendsData(relationalGraphLegendTempData)
    setIsFetchingRelationalGraph(false)
  }

  const fetchCollectionsTableData = () => {
    setIsFetchingCollections(true)
    // TODO
    setCollectionsTotal(accountCollectionTempData.length)
    const data = accountCollectionTempData.slice((collectionsCurrentPage - 1) * collectionsCurrentLimit,
      collectionsCurrentPage * collectionsCurrentLimit)
    setCollectionsTableData(data)
    setIsFetchingCollections(false)
  }

  const openModal = () => {
    fetchRelationalGraphData()
    setShowModal(true)
  }

  useEffect(() => {
    fetchHoldingData()
  }, [holdingFilterParams])

  useEffect(() => {
    fetchCumulativeData()
  }, [cumulativeFilterParams])

  useEffect(() => {
    fetchTraderData()
  }, [traderFilterParams])

  useEffect(() => {
    fetchAddressesTableData()
  }, [addressesFilterParams])

  useEffect(() => {
    fetchRelationalGraphData()
  }, [relationalGraphFilterParams])

  useEffect(() => {
    fetchCollectionsTableData()
  }, [collectionsFilterParams])

  return (
    <div>
      <div className="container-lg">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-5 lg:gap-10">
          <HoldingPie
            chartData={holdingData}
            filterTypes={filterHoldingPie}
            defaultType={filterHoldingPie[0]}
            onChangeFilterParam={setHoldingFilterParam}
            isFetching={isFetchingHolding}
          />
          <CumulativeChart
            chartData={cumulativeData}
            periodRanges={dayOptions}
            defaultPeriodRange={dayOptions[0]}
            onChangePeriodRanges={setCumulativeFilterParam}
            isFetching={isFetchingCumulative}
          />
          <TraderChart
            chartData={traderData}
            legends={traderLegendsData}
            periodRanges={dayOptions}
            defaultPeriodRange={dayOptions[0]}
            onChangePeriodRanges={setTraderFilterParam}
            isFetching={isFetchingTrader}
          />
          <RelatedAddressBoard
            currentPage={addressesCurrentPage}
            currentLimit={addressesCurrentLimit}
            tableData={addressesTableData}
            total={addressesTotal}
            filterParams={addressesFilterParams}
            onChangePage={setAddressesCurrentPage}
            onChangeCurrentLimit={setAddressesCurrentLimit}
            onChangeFilterParams={setAddressesFilterParams}
            isFetching={isFetchingAddresses}
            showGraphModal={() => openModal()}
          />
        </div>
      </div>

      <CollectionsBoard
        currentPage={collectionsCurrentPage}
        currentLimit={collectionsCurrentLimit}
        tableData={collectionsTableData}
        total={collectionsTotal}
        filterParams={collectionsFilterParams}
        onChangePage={setCollectionsCurrentPage}
        onChangeCurrentLimit={setCollectionsCurrentLimit}
        onChangeFilterParams={setCollectionsFilterParams}
        isFetching={isFetchingCollections}
      />

      {showModal && (
        <RelationalGraphModal
          chartData={relationalGraphData}
          legends={relationalGraphLegendsData}
          filterTypes={filterGraph}
          defaultType={filterGraph[0]}
          onChangeFilterParam={setRelationalGraphFilterParam}
          isFetching={isFetchingRelationalGraph}
          showModal={showModal}
          onClose={setShowModal}
        />
      )}
    </div>
  )
}

export default AccountStatsTab
