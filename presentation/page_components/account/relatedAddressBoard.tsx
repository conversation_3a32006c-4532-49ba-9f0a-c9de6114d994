import Table from 'presentation/components/table'
import Pagination from 'presentation/components/pagination'
import Tooltip from 'presentation/components/tooltip'
import SvgIcon from 'presentation/components/svg-icon'
import LoadingData from 'presentation/components/skeleton/loading'
import { relatedAddressHeader } from './_tableHeaders'

const RelatedAddressBoard = (props: any) => {
  const {
    currentPage = 1,
    currentLimit = 20,
    tableData = [],
    total = 0,
    filterParams,
    onChangePage = () => {},
    onChangeCurrentLimit = () => {},
    onChangeFilterParams = () => {},
    isFetching = true,
    showGraphModal,
  } = props

  return (
    <div className="table-wrap border border-gray-3 rounded-10">
      <div className="flex flex-col justify-between h-full">
        <div className="px-5 pt-5 flex justify-between items-center flex-wrap gap-4 md:gap-5">
          <div className="flex items-center shri">
            <p className="text-lg font-bold text-dark-primary dark:text-white">
              Related Addresses
            </p>
            <Tooltip iconSize="w-4 h-4 -mt-0.5">Related Addresses</Tooltip>
          </div>
          <a className="text-primary-2 cursor-pointer shrink-0" onClick={showGraphModal}>
            <SvgIcon name="share" className="w-5 h-5 mr-2 stroke-none fill-primary-2"/>
            Relational Graph
          </a>
        </div>

        <div className="flex flex-col">
          {isFetching ? (
            <LoadingData/>
          ) : (
            <>
              <div className="w-full overflow-x-auto pb-3 mt-4">
                <Table
                  head={relatedAddressHeader}
                  data={tableData}
                  className="table-no-border related-address min-w-[500px]"
                />
              </div>
              <div className="mt-2 px-5 mb-6">
                <Pagination
                  enableSelectRow={false}
                  currentPage={currentPage}
                  totalCount={total}
                  pageSize={currentLimit}
                  onPageChange={(page: number) => {
                    onChangePage(page)
                    onChangeFilterParams({...filterParams, page})
                  }}
                  onChangePageSize={(limit: number) => {
                    onChangePage(1)
                    onChangeCurrentLimit(limit)
                    onChangeFilterParams({...filterParams, limit, page: 1})
                  }}
                  isShortened={true}
                />
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default RelatedAddressBoard
