import { use, useRef, useState, useEffect } from 'react'
import Image from 'next/image'
import Tooltip from 'presentation/components/tooltip'
import Copy from 'presentation/components/copy'
import SvgIcon from 'presentation/components/svg-icon'
import CollectionTableItem from 'presentation/components/collection/table-item'
import ButtonIcon from 'presentation/components/button/button-icon'
import BtnSocialShare from 'presentation/page_components/account-details/btnSocialShare'
const StatisticTop = () => {
  const textRef = useRef(null)
  const [urlShare, setUrlShare] = useState<string>('')
   const getUrlShare = () => {
    setUrlShare(
      window.location.href || ''
    )
  }
  useEffect(() => {
    getUrlShare()
  })
  return (
    <div className="flex flex-col md:flex-row items-center md:items-start flex-wrap gap-5">
      <div className="px-3 pb-4 pt-3 w-full sm:w-fit md:w-1/2 lg:w-1/3 min-w-[365px] h-fit rounded-10 border border-gray-3 relative">
        <BtnSocialShare urlShare={urlShare} />
        {/* <ButtonIcon
          svgIcon="share"
          iconClassName="w-5 h-5 stroke-none fill-primary-2"
          className="absolute top-3 right-3 bg-white dark:bg-blue-1"
        /> */}
        <div className="mt-3 flex sm:flex-nowrap flex-wrap items-center">
          <Image alt="character"
            width={80} height={80} className="rounded-full"
            style={{ objectFit: 'cover' }}
            src="/asset/images/owner-avatar.png"
          />
          <div className="sm:ml-18p sm:mt-0 mt-3 sm:w-auto w-full">
            <p className="text-xl font-bold">barthazian.eth</p>
            <div className="flex mt-2 leading-4">
              <Copy ref={textRef}>
                <p ref={textRef} className="text-sm font-medium max-w-[4rem] truncate">
                  0xc4c16a645b21a
                </p>
              </Copy>
              <SvgIcon name="yellowCoin" viewBox="0 0 13.333 13.333" className="w-[13px] h-[13px] ml-2 stroke-none" />
              <p className="text-sm ml-5">🐋 Whale</p>
            </div>
          </div>
        </div>
        <div className="pt-10p pb-3 px-2 sm:px-4 mt-8 flex sm:flex-nowrap flex-wrap items-baseline gap-3 sm:gap-5 rounded-10 bg-blue-2 dark:bg-blue-1">
          <div>
            <p className="text-sm text-gray-1 dark:text-gray-3 text-stroke-thin">NFTs</p>
            <p className="font-bold mt-1 text-dark-primary dark:text-white">3,975</p>
          </div>
          <div>
            <p className="text-sm text-gray-1 dark:text-gray-3 text-stroke-thin">Collections</p>
            <p className="font-bold mt-1 text-dark-primary dark:text-white">222</p>
          </div>
          <div className="overflow-hidden">
            <div className="flex items-baseline">
              <p className="text-sm text-gray-1 dark:text-gray-3 text-stroke-thin">Most Holding</p>
              <Tooltip iconSize="w-4 h-4">Some Description</Tooltip>
            </div>
            <CollectionTableItem
              imagesrc="/asset/images/character-bird.png"
              name="By the Digital Art"
              nickname="@endilo"
            />
          </div>
        </div>
      </div>

      <div className="w-full md:w-1/2 flex-1">
        <div className="grid grid-cols-12 gap-3 md:gap-5">
          <div className="col-span-12 sm:col-span-6 lg:col-span-4 rounded-10 bg-blue-2 dark:bg-blue-1 pt-10p pb-3 px-5">
            <p className="flex justify-between items-center flex-wrap">
              <span className="font-bold sm:text-xl md:text-lg xl:text-xl text-dark-primary dark:text-white truncate">
                $9,003,025.61
              </span>
              <span className="text-blue-4 text-sm text-stroke-thin">#97</span>
            </p>
            <div className="mt-1 text-sm font-medium flex flex-wrap">
              <p>
                <span className="mr-1 text-dark-3 dark:text-white">7D</span>
                <span className="text-green-1 mr-15p">+0.15%</span>
              </p>
              <p>
                <span className="mr-1 text-dark-3 dark:text-white">30D</span>
                <span className="text-red-1">-0.15%</span>
              </p>
            </div>
            <div className="mt-1 flex items-center">
              <p className="text-sm text-gray-1 dark:text-gray-3 text-stroke-thin truncate">
                Est Holding Value
              </p>
              <Tooltip iconSize="w-4 h-4 -mt-1">Est Holding Value</Tooltip>
            </div>
          </div>

          <div className="col-span-12 sm:col-span-6 lg:col-span-4 rounded-10 bg-blue-2 dark:bg-blue-1 pt-10p pb-3 px-5">
            <p className="flex justify-between items-center flex-wrap">
              <span className="font-bold sm:text-xl md:text-lg xl:text-xl text-dark-primary dark:text-white truncate">
                $1,265,335.09
              </span>
              <span className="text-blue-4 text-sm text-stroke-thin">#681</span>
            </p>
            <div className="mt-1 text-sm font-medium flex flex-wrap">
              <p>
                <span className="mr-1 text-dark-3 dark:text-white">7D</span>
                <span className="text-green-1 mr-15p">+0.15%</span>
              </p>
              <p>
                <span className="mr-1 text-dark-3 dark:text-white">30D</span>
                <span className="text-red-1">-0.15%</span>
              </p>
            </div>
            <div className="mt-1 flex items-center">
              <p className="text-sm text-gray-1 dark:text-gray-3 text-stroke-thin truncate">
                Holding Value
              </p>
              <Tooltip iconSize="w-4 h-4 -mt-1">Holding Value</Tooltip>
            </div>
          </div>

          <div className="col-span-12 sm:col-span-6 lg:col-span-4 rounded-10 bg-blue-2 dark:bg-blue-1 pt-10p pb-3 px-5">
            <p className="flex justify-between items-center flex-wrap">
              <span className="font-bold sm:text-xl md:text-lg xl:text-xl text-dark-primary dark:text-white truncate">
                $10,712,110.11
              </span>
              <span className="text-blue-4 text-sm text-stroke-thin">#96</span>
            </p>
            <div className="mt-1 text-sm font-medium flex flex-wrap">
              <p>
                <span className="mr-1 text-dark-3 dark:text-white">7D</span>
                <span className="text-green-1 mr-15p">+0.15%</span>
              </p>
              <p>
                <span className="mr-1 text-dark-3 dark:text-white">30D</span>
                <span className="text-gray-6 dark:text-white font-normal text-stroke-thin">—</span>
              </p>
            </div>
            <div className="mt-1 flex items-center">
              <p className="text-sm text-gray-1 dark:text-gray-3 text-stroke-thin truncate">PnL</p>
              <Tooltip iconSize="w-4 h-4 -mt-1">PnL</Tooltip>
            </div>
          </div>

          <div className="col-span-12 md:col-span-6 lg:col-span-4 xl:col-span-3 rounded-10 bg-blue-2 dark:bg-blue-1 pt-10p pb-2 px-5">
            <p className="font-bold text-lg text-dark-primary dark:text-white">
              $1,716,160.60
            </p>
            <p className="mt-1 text-sm font-medium">
              <span className="mr-1 text-dark-3 dark:text-white">7D</span>
              <span className="text-dark-primary dark:text-white">$65.73K</span>
            </p>
            <p className="mt-1 text-sm font-medium">
              <span className="mr-1 text-dark-3 dark:text-white">7D</span>
              <span className="text-dark-primary dark:text-white">$65.73K</span>
            </p>
            <div className="mt-1 flex items-center">
              <p className="text-sm text-gray-1 dark:text-gray-3 text-stroke-thin">Buy Volume</p>
              <Tooltip iconSize="w-4 h-4 -mt-1">Buy Volume</Tooltip>
            </div>
          </div>

          <div className="col-span-12 md:col-span-6 lg:col-span-4 xl:col-span-3 rounded-10 bg-blue-2 dark:bg-blue-1 pt-10p pb-2 px-5">
            <p className="font-bold text-lg text-dark-primary dark:text-white">$3,368,665.50</p>
            <p className="mt-1 text-sm font-medium">
              <span className="mr-1 text-dark-3 dark:text-white">7D</span>
              <span className="text-dark-primary dark:text-white">$65.73K</span>
            </p>
            <p className="mt-1 text-sm font-medium">
              <span className="mr-1 text-dark-3 dark:text-white">7D</span>
              <span className="text-dark-primary dark:text-white">$65.73K</span>
            </p>
            <div className="mt-1 flex items-center">
              <p className="text-sm text-gray-1 dark:text-gray-3 text-stroke-thin">Sell Volume</p>
              <Tooltip iconSize="w-4 h-4 -mt-1">Sell Volume</Tooltip>
            </div>
          </div>

          <div className="col-span-12 md:col-span-6 lg:col-span-4 xl:col-span-3 rounded-10 bg-blue-2 dark:bg-blue-1 pt-10p pb-2 md:px-3 px-5">
            <p className="font-bold text-lg text-primary-2">6</p>
            <p className="mt-1 text-sm font-medium">
              <span className="mr-1">🐋</span>
              <span className="text-dark-3 dark:text-white">Whales 0</span>
            </p>
            <p className="mt-1 text-sm font-medium">
              <span className="mr-1">📜</span>
              <span className="text-dark-3 dark:text-white">Contracts 4</span>
            </p>
            <div className="mt-1 flex items-center">
              <p className="text-sm text-gray-1 dark:text-gray-3 text-stroke-thin">Related Addresses</p>
              <Tooltip iconSize="w-4 h-4 -mt-1">Related Addresses</Tooltip>
            </div>
          </div>

          <div className="col-span-12 md:col-span-6 lg:col-span-4 xl:col-span-3 rounded-10 bg-blue-2 dark:bg-blue-1 pt-10p pb-2 px-5">
            <p className="font-bold text-lg text-primary-2">5,398</p>
            <p className="mt-1 text-sm font-medium">
              <span className="mr-1 text-dark-3 dark:text-white">7D</span>
              <span className="text-dark-primary dark:text-white">77</span>
            </p>
            <p className="mt-1 text-sm font-medium">
              <span className="mr-1 text-dark-3 dark:text-white">7D</span>
              <span className="text-dark-primary dark:text-white">403</span>
            </p>
            <div className="mt-1 flex items-center">
              <p className="text-sm text-gray-1 dark:text-gray-3 text-stroke-thin">Activities</p>
              <Tooltip iconSize="w-4 h-4 -mt-1">Activities</Tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default StatisticTop
