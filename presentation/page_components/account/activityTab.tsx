import { useState, useEffect } from 'react'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import ActivityTrendChart from './activityTrendChart'
import WhaleActivityBoard from '../whale-tracking/whaleActivityBoard'
import { pageSizes } from 'lib/hook/customHook'
import CategoryUseCase from 'presentation/use_cases/category_use_case'
import CollectionUseCase from 'presentation/use_cases/collection_use_case'
import { Query as CollectionQuery } from 'domain/repositories/collection_repository/query'
import { Query as CategoryQuery } from 'domain/repositories/category_repository/query'

const collectionUseCase = new CollectionUseCase()
const categoryUseCase = new CategoryUseCase()

//Todo remove mock data
import { filterWeek } from 'presentation/controllers/mockData/options'
import { activityAnalyticsTempData } from 'presentation/controllers/mockData/whale_tracking_mock_data'
import {
  activityTrendTempData,
  activityTrendLegendTempData,
} from 'presentation/controllers/mockData/account_mock_data'
import Category from 'domain/models/category'
import Collection from 'domain/models/collection'
import { toast } from 'react-toastify'
//End todo remove mock data

const PageSize = pageSizes.pageSize20

const AccountActivityTab = () => {
  const [activityTrendData, setActivityTrendData] = useState<any>([])
  const [activityTrendLegendData, setActivityTrendLegendData] = useState<any>(
    []
  )
  const {
    filterParams: activityTrendFilterParams,
    changeFilterParam: onChangeActivityTrendFilterParam,
  } = useChangeFilterParams({ periodRange: '' })
  const [isFetchingActivityTrend, setIsFetchingActivityTrend] = useState(true)

  const [filterCollections, setFilterCollections] = useState<Collection[]>([])
  const [isFilterCollectionsReady, setIsFilterCollectionsReady] =
    useState(false)
  const [categories, setCategories] = useState<Category[]>([])
  const [filterCategories, setFilterCategories] = useState<any>([])
  const [isFilterCategoriesReady, setIsFilterCategoriesReady] = useState(false)

  const [whaleActivityCurrentPage, setWhaleActivityCurrentPage] = useState(1)
  const [whaleActivityCurrentLimit, setWhaleActivityCurrentLimit] =
    useState(PageSize)
  const [whaleActivityData, setWhaleActivityData] = useState<any>([])
  const [whaleActivityTotal, setWhaleActivityTotal] = useState(0)
  const {
    filterParams: whaleActivityFilterParams,
    changeFilterParam: onChangeWhaleActivityFilterParam,
    setFilterParams: onChangeWhaleActivityFilterParams,
  } = useChangeFilterParams({
    listed: false,
    collectionIds: [],
    categoryId: '',
    page: 1,
    limit: PageSize,
  })
  const [isFetchingWhaleActivity, setIsFetchingWhaleActivity] = useState(true)

  const fetchActivityTrendData = () => {
    setIsFetchingActivityTrend(true)
    // TODO
    setActivityTrendData(activityTrendTempData)
    setActivityTrendLegendData(activityTrendLegendTempData)
    setIsFetchingActivityTrend(false)
  }

  const fetchCollections = async () => {
    const query = new CollectionQuery()
    await collectionUseCase
      .search(query)
      .then((res) => {
        setFilterCollections(res)
      })
      .catch((_error) => {
        setFilterCollections([])
      })
    setIsFilterCollectionsReady(true)
  }

  const formatCategories = () => {
    const formattedCategories = categories.map((category: any) => {
      return {
        id: category.id,
        name: category.name,
        value: category.slug,
      }
    })
    setFilterCategories(formattedCategories)
    setIsFilterCategoriesReady(true)
  }

  const fetchWhaleActivityTableData = () => {
    setIsFetchingWhaleActivity(true)
    //TODO
    setWhaleActivityTotal(activityAnalyticsTempData.length)
    let data = activityAnalyticsTempData.slice(
      (whaleActivityCurrentPage - 1) * whaleActivityCurrentLimit,
      whaleActivityCurrentPage * whaleActivityCurrentLimit
    )
    setWhaleActivityData(data)
    setTimeout(() => {
      setIsFetchingWhaleActivity(false)
    }, 300)
  }

  const fetchCategories = async () => {
    const query = new CategoryQuery()
    await categoryUseCase
      .search(query)
      .then((res) => {
        setCategories(res)
      })
      .catch((_error) => {
        setCategories([])
        toast.error('Failed to fetch categories')
      })
  }

  useEffect(() => {
    if (categories.length > 0) {
      formatCategories()
    }
  }, [categories])

  useEffect(() => {
    fetchActivityTrendData()
  }, [activityTrendFilterParams])

  useEffect(() => {
    fetchWhaleActivityTableData()
  }, [whaleActivityFilterParams])

  useEffect(() => {
    fetchCollections()
    fetchCategories()
  }, [])

  return (
    <div>
      {!isFetchingActivityTrend && (
        <div className="container-lg">
          <ActivityTrendChart
            periodRanges={filterWeek}
            chartData={activityTrendData}
            defaultPeriodRanges={filterWeek[1]}
            onChangePeriodRanges={onChangeActivityTrendFilterParam}
            legends={activityTrendLegendData}
          />
        </div>
      )}
      <WhaleActivityBoard
        currentPage={whaleActivityCurrentPage}
        currentLimit={whaleActivityCurrentLimit}
        tableData={whaleActivityData}
        total={whaleActivityTotal}
        filterParams={whaleActivityFilterParams}
        onChangePage={setWhaleActivityCurrentPage}
        onChangeCurrentLimit={setWhaleActivityCurrentLimit}
        onChangeFilterParam={onChangeWhaleActivityFilterParam}
        onChangeFilterParams={onChangeWhaleActivityFilterParams}
        isFetching={isFetchingWhaleActivity}
        collections={filterCollections}
        isFilterCollectionsReady={isFilterCollectionsReady}
        categories={filterCategories}
        isFilterCategoriesReady={isFilterCategoriesReady}
      />
    </div>
  )
}

export default AccountActivityTab
