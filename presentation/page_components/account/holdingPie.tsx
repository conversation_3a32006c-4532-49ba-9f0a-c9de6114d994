import dynamic from 'next/dynamic'
import { useState, useRef, memo } from 'react'
import { filterHoldingPie } from 'presentation/controllers/mockData/options'
import FilterTime from 'presentation/components/filter-time'
import Tooltip from 'presentation/components/tooltip'

const Pie = dynamic(() => import('@ant-design/plots').then(({ Pie }) => Pie), { ssr: false })

const MemoPlot = memo(({ config, getChartRef}: any) => {
  return <Pie {...config} onReady={(chartRef) => getChartRef(chartRef)} />
}, (pre, next) => {
  return JSON.stringify(pre.config) === JSON.stringify(next.config)
})

MemoPlot.displayName = 'MemoPlot'

const HoldingPie = (props:any) => {
  const {
    chartData = [],
    filterTypes = [],
    defaultType = {},
    onChangeFilterParam = () => {},
    isFetching = true,
  } = props

  const ref = useRef<any>(null)
  const [legendData, setLegendData] = useState([])

  const getChartRef = (chartRef: any) => {
    if (!chartRef) return
    ref.current = chartRef

    const chart = ref.current.chart
    const legendData = chart.geometries[0]?.dataArray?.map((item: any) => {
      const origin = item[0]._origin
      origin.color = item[0].color
      origin.checked = true
      return origin
    })
    setLegendData(legendData)
  }

  const handleLegendClick = (item: any, i: number) => {
    const newItem = item
    newItem.checked = !newItem.checked
    const currentLegend: any = [...legendData]
    currentLegend[i] = newItem

    const filteredLegendData = currentLegend
      .filter((l: any) => l.checked)
      .map((l: any) => l.type)

    const chart = ref.current.chart
    if (chart) {
      chart.filter('type', (val:any) => {
        return filteredLegendData.indexOf(val) > -1
      })
      chart.render()
    }
    setLegendData(currentLegend)
  }

  const config: any = {
    width: 277,
    height: 272,
    appendPadding: 10,
    data: chartData,
    angleField: 'value',
    colorField: 'type',
    color: (...args:any) => {
      const type = args[0].type
      switch (type) {
        case 'dotdotdots':
          return '#5A66F9'
        case 'Art Blocks':
          return '#72D8DA'
        case 'JOYWORLD':
          return '#6DC2EF'
        case 'Fluf World':
          return '#BB6BD9'
        case 'BEEPLE':
          return '#D690F0'
        case 'RaidParty RaidParty':
          return '#5CC489'
        case 'More Loot':
          return '#269958'
        case 'others':
          return '#F3AA3B'
        default:
          return 'transparent'
      }
    },
    radius: 1,
    innerRadius: 0.64,
    meta: false,
    label: false,
    statistic:false,
    pieStyle: {
      lineWidth: 0,
    },
    legend: false,
  }

  return <div className="rounded-10 p-5 border border-gray-3">
    <div className="flex flex-wrap justify-between gap-4 md:gap-5 mb-4">
      <div className="flex items-center mb-4">
        <p className="text-lg font-bold text-dark-primary dark:text-white">
          Holding Distribution
        </p>
        <Tooltip iconSize="w-4 h-4 -mt-0.5">Holding Distribution</Tooltip>
      </div>
      <FilterTime
        options={filterTypes}
        defaultSelected={defaultType}
        onSelect={(option) => onChangeFilterParam(option)}
        className="shrink-0 mb-4"
      />
    </div>

    {!isFetching && (
      <div className="flex flex-wrap">
        <div className="flex items-center xl:justify-start md:justify-center sm:justify-start justify-center xl:w-3/5 md:w-full sm:w-3/5 w-full">
          <MemoPlot config={config} getChartRef={getChartRef} />
        </div>
        <div className="flex items-center xl:justify-end md:justify-center sm:justify-end justify-center xl:w-2/5 md:w-full sm:w-2/5 w-full pr-4">
          <ul className="flex flex-col w-full xl:space-y-5 md:space-y-2 sm:space-y-5 space-y-2 flex-none">
            {legendData?.map((item: any, i: number) => (
              <li
                key={i} onClick={() => handleLegendClick(item, i)}
                className={'inline-flex justify-between cursor-pointer'}
              >
                <div className="inline-flex items-center truncate">
                  <span
                    className="rounded-full w-2 h-2 mr-2 flex-none"
                    style={{ backgroundColor: item.checked ? item.color : '#aaa' }}
                  ></span>
                  <span className="font-medium text-sm text-dark-3 dark:text-white truncate">
                    {item.type}
                  </span>
                </div>
                <span className="font-medium w-16 flex-none text-dark-primary dark:text-white text-right">
                  {item.value}%
                </span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    )}
  </div>
}

export default HoldingPie
