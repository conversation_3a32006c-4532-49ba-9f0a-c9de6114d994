import Tooltip from 'presentation/components/tooltip'
import LoadingData from 'presentation/components/skeleton/loading'
import Table from 'presentation/components/table'
import Pagination from 'presentation/components/pagination'
import { collectionTableHeader } from './_tableHeaders'

const CollectionsBoard = (props:any) => {
  const {
    currentPage = 1,
    currentLimit = 20,
    tableData = [],
    total = 0,
    filterParams,
    onChangePage = () => {},
    onChangeCurrentLimit = () => {},
    onChangeFilterParams = () => {},
    isFetching = true,
  } = props

  return (
    <div className="mt-10">
      <div className="flex items-center">
        <p className="font-bold text-lg text-dark-primary dark:text-white">Collections</p>
        <Tooltip className="-mt-0.5">Collections</Tooltip>
      </div>

      <div className="table-wrap border rounded-20 mt-4">
        {isFetching ? (
          <LoadingData/>
        ) : (
          <>
            <div className="w-full overflow-x-auto pb-3">
              <Table
                head={collectionTableHeader}
                data={tableData}
                className="table-no-border account-collections min-w-[1200px]"
              />
            </div>
            <div className="mt-2 px-5 mb-9">
              <Pagination
                currentPage={currentPage}
                totalCount={total}
                pageSize={currentLimit}
                onPageChange={(page: number) => {
                  onChangePage(page)
                  onChangeFilterParams({...filterParams, page})
                }}
                onChangePageSize={(limit: number) => {
                  onChangePage(1)
                  onChangeCurrentLimit(limit)
                  onChangeFilterParams({...filterParams, limit, page: 1})
                }}
              />
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default CollectionsBoard
