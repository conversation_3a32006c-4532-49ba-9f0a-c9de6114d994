import dynamic from 'next/dynamic'
import { useState, useRef, memo } from 'react'
import Select from 'presentation/components/select'
import Tooltip from 'presentation/components/tooltip'

const Line = dynamic(() => import('@ant-design/plots').then(({ Line }) => Line), { ssr: false })

const MemoPlot = memo(({ config, getChartRef}: any) => {
  return <Line {...config} onReady={(chartRef) => getChartRef(chartRef)} />
}, (pre, next) => {
  return JSON.stringify(pre.config) === JSON.stringify(next.config)
})

MemoPlot.displayName = 'MemoPlot'

const CumulativeChart = (props:any) => {
  const {
    chartData = [],
    periodRanges = [],
    defaultPeriodRange,
    onChangePeriodRanges = () => {},
    isFetching = true,
  } = props

  const ref = useRef<any>(null)
  const [legendData, setLegendData] = useState([])

  const config: any = {
    data: chartData,
    height: 272,
    xField: 'year',
    yField: 'value',
    xAxis: {
      title: {
        text: 'UTC              ',
        position: 'start',
        offset: 14,
        style: {
          fontSize: 12,
        },
      },
    },
    seriesField: 'type',
    legend: false,
    color: (...args:any) => {
      const type = args[0].type
      switch (type) {
        case 'PnL':
          return '#BB6BD9'
        case 'Est Holding Value':
          return '#72D8DA'
        case 'Holding Value':
          return '#5A66F9'
        default:
          return 'transparent'
      }
    },
  }

  const getChartRef = (chartRef: any) => {
    if (!chartRef) return
    ref.current = chartRef

    const chart = ref.current.chart
    const legendData = chart.geometries[0]?.dataArray?.map((item: any) => {
      const origin = item[0]._origin
      origin.color = item[0].color
      origin.checked = true
      return origin
    })
    setLegendData(legendData)
  }

  const handleLegendClick = (item: any, i: number) => {
    const newItem = item
    newItem.checked = !newItem.checked
    const currentLegend: any = [...legendData]
    currentLegend[i] = newItem

    const filteredLegendData = currentLegend
      .filter((l: any) => l.checked)
      .map((l: any) => l.type)

    const chart = ref.current.chart
    if (chart) {
      chart.filter('type', (val:any) => {
        return filteredLegendData.indexOf(val) > -1
      })
      chart.render()
    }
    setLegendData(currentLegend)
  }

  return (
    <div className="rounded-10 p-5 border border-gray-3">
      <div className="flex flex-col xl:justify-between md:justify-start sm:justify-between justify-start h-full">
        <div className="flex flex-wrap justify-between gap-4 md:gap-5">
          <div className="flex items-center mb-4">
            <p className="text-lg font-bold text-dark-primary dark:text-white">
              Cumulative Gains & Losses
            </p>
            <Tooltip iconSize="w-4 h-4 -mt-0.5">
              Cumulative Gains & Losses
            </Tooltip>
          </div>
          <Select
            options={periodRanges}
            defaultSelected={defaultPeriodRange}
            onSelect={(option) => onChangePeriodRanges(option)}
            className="shrink-0 mb-4"
          />
        </div>

        {!isFetching && (
          <div className="flex flex-col mt-8">
            <ul className="flex flex-wrap items-start lg:justify-center justify-start w-full gap-4 mb-5">
              {legendData?.map((item: any, i: number) => (
                <li
                  key={i} onClick={() => handleLegendClick(item, i)}
                  className="flex flex-col cursor-pointer"
                >
                  <div className="inline-flex items-center">
                    <span
                      className="rounded-full w-2 h-2 mr-2"
                      style={{ backgroundColor: item.checked ? item.color : '#aaa' }}
                    ></span>
                    <span className="font-medium text-sm text-dark-3 dark:text-white">
                      {item.type}
                    </span>
                    <Tooltip className="flex items-center -mt-1">Description</Tooltip>
                  </div>
                </li>
              ))}
            </ul>
            <MemoPlot config={config} getChartRef={getChartRef} />
          </div>
        )}
      </div>
    </div>
  )
}

export default CumulativeChart
