import { useState, useRef, memo } from 'react'
import FilterTime from 'presentation/components/filter-time'
import Tooltip from 'presentation/components/tooltip'
import dynamic from 'next/dynamic'

const Column = dynamic(() => import('@ant-design/plots').then(({ Column }) => Column), { ssr: false })

const MemoPlot = memo(({ config, getChartRef}: any) => {
  return <Column {...config} onReady={(chartRef) => getChartRef(chartRef)} />
}, (pre, next) => {
  return JSON.stringify(pre.config) === JSON.stringify(next.config)
})

MemoPlot.displayName = 'MemoPlot'

const ActivityTrendChart = (props:any) => {
  const {
    periodRanges = [],
    chartData = [],
    defaultPeriodRanges,
    onChangePeriodRanges = () => {},
    legends = [],
  } = props

  const ref = useRef<any>(null)
  const [legendData, setLegendData] = useState([])
  const [filterTime, setFilterTime] = useState(defaultPeriodRanges ? defaultPeriodRanges : periodRanges[0])

  const config: any = {
    height: 255,
    data: chartData,
    xField: 'time',
    yField: 'value',
    xAxis: {
      title: {
        text: 'UTC                     ',
        position: 'start',
        offset: 14,
        style: {
          fontSize: 12,
        },
      },
    },
    yAxis: {
      title: {
        text: 'Activities',
        style: {
          fontSize: 14,
        },
      },
    },
    legend: false,
    isStack: true,
    seriesField: 'type',
    columnWidthRatio: 0.4,
    color: ({ type }: any) => {
      switch (type) {
        case 'Mint':
          return '#269958'
        case 'Buy':
          return '#5A66F9'
        case 'Sell':
          return '#BB6BD9'
        case 'Burn':
          return '#EB5757'
        case 'Send':
          return '#1CA2A5'
        case 'Receive':
          return '#E98F0B'
        default:
          return 'transparent'
      }
    },
  }

  const getChartRef = (chartRef: any) => {
    if (!chartRef) return
    ref.current = chartRef

    const chart = ref.current.chart
    const legendData = chart.geometries[0]?.dataArray?.map((item: any) => {
      const origin = item[0]._origin
      origin.color = item[0].color
      origin.checked = true
      return origin
    })
    setLegendData(legendData)
  }

  const handleLegendClick = (item: any, i: number) => {
    const newItem = item
    newItem.checked = !newItem.checked
    const currentLegend: any = [...legendData]
    currentLegend[i] = newItem

    const filteredLegendData = currentLegend
      .filter((l: any) => l.checked)
      .map((l: any) => l.type)

    const chart = ref.current.chart
    if (chart) {
      chart.filter('type', (val:any) => {
        return filteredLegendData.indexOf(val) > -1
      })
      chart.render()
    }
    setLegendData(currentLegend)
  }

  return (
    <div className="rounded-10 py-4 mt-10 border border-gray-3">
      <div className="px-5 flex flex-wrap justify-between items-center">
        <div className="flex items-center mb-4">
          <p className="text-lg font-bold text-dark-primary dark:text-white">
            Activity Trends
          </p>
          <Tooltip iconSize="w-4 h-4 -mt-0.5">Activity Trends</Tooltip>
        </div>
        <FilterTime
          className="mb-4" options={periodRanges}
          defaultSelected={defaultPeriodRanges}
          onSelect={(option) => {
            setFilterTime(option)
            onChangePeriodRanges(option)
          }}
        />
      </div>
      <div className="mt-4 md:px-5 px-3">
        <ul className="flex flex-wrap items-start lg:justify-center justify-start w-full">
          {legendData?.map((item: any, i: number) => (
            <li
              key={i} onClick={() => handleLegendClick(item, i)}
              className={`flex flex-col mb-5 cursor-pointer ${i === 0 ? 'xl:ml-0' : 'xl:ml-12'} ml-4`}
            >
              <div className="inline-flex items-center">
                <span
                  className="rounded-full w-2 h-2 mr-2"
                  style={{ backgroundColor: item.checked ? item.color : '#aaa' }}
                ></span>
                <span className="font-medium text-sm mr-0.5 text-dark-3 dark:text-white">
                  {item.type}({filterTime?.name})
                </span>
                <Tooltip className="flex items-center -mt-0.5">Description</Tooltip>
              </div>
              <div className="inline-flex items-end justify-center space-x-2 font-medium mt-1">
                <span className="text-sm leading-none text-dark-3 dark:text-white">
                  {legends[i].amount}
                </span>
                <span className={`text-xs leading-none ${legends[i].percent > 0 ? 'text-primary-2' : 'text-red-1'}`}>
                  {legends[i].percent > 0 ? '+' : ''}{(legends[i].percent.toFixed(2)).toLocaleString()}%
                </span>
              </div>
            </li>
          ))}
        </ul>

        <MemoPlot config={config} getChartRef={getChartRef} />
      </div>
    </div>
  )
}

export default ActivityTrendChart
