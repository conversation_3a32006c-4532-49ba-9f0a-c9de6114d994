import dynamic from 'next/dynamic'
import Image from 'next/image'
import Link from 'next/link'
import BadgeText from 'presentation/components/badge/badgeText'
import Modal from 'presentation/components/modal'
import clsx from 'clsx'

const Bar = dynamic(() => import('@ant-design/plots').then(({ Bar }) => Bar), {
  ssr: false,
})

const NftTabModal = (props: any) => {
  const { showModal, onClose, asset, chartData } = props

  const config: any = {
    height: 230,
    data: chartData,
    xField: 'value',
    yField: 'year',
    seriesField: 'year',
    color: [
      '#F0AF51',
      '#B071D3',
      '#B071D3',
      '#5A69F0',
      '#5A69F0',
      '#5A69F0',
      '#C3C3CB',
      '#C3C3CB',
      '#C3C3CB',
    ],
    legend: false,
  }

  return (
    <Modal
      isShow={showModal}
      onClose={onClose}
      title="Rarity Rank"
      contentWrapClass="dark:bg-black"
      modalWidthClass="min-w-3/4 md:min-w-2/4 xl:min-w-[740px] sm:w-fit w-11/12"
    >
      <div className="xl:px-22p pt-5 pb-4 xl:pb-11 flex flex-col md:flex-row gap-4 lg:gap-9">
        <div className="w-full md:w-[360px]">
          <div className="flex sm:flex-nowrap flex-wrap sm:justify-start justify-center gap-5">
            <div className="relative shrink-0 w-[150px] h-[150px]">
              <Image
                alt={asset.name}
                fill
                src={asset.thumbnail?.url}
                className="rounded-[20px]"
              />
            </div>
            <div className="sm:flex-1 sm:w-fit w-full truncate">
              <p className="text-xl font-medium italic truncate max-w-full text-dark-primary dark:text-white">
                {asset.name}
              </p>
              <div className="py-10p px-4 mt-18p rounded-10 bg-blue-2">
                <div className="flex justify-between items-center">
                  <p className="font-medium text-dark-primary dark:text-white">
                    Rarity Score
                  </p>
                  <p className="font-medium text-sm text-blue-4">
                    {asset.rarityScore}
                  </p>
                </div>
                <div className="mt-3 flex justify-between items-center">
                  <BadgeText size="xs">Classic</BadgeText>
                  <p className="text-sm text-gray-1 dark:text-gray-3 text-stroke-thin">
                    Rank #{asset.rank}
                  </p>
                </div>
              </div>
              <div className="px-2 mt-2 flex justify-between">
                <p className="text-dark-primary dark:text-white text-stroke-thin">
                  Last Price
                </p>
                <p className="text-dark-primary dark:text-white text-stroke-thin">
                  {asset.lastPrice} {asset.symbol}
                </p>
              </div>
            </div>
          </div>
          <div className="text-right mt-10p">
            <button className="px-15p py-2 rounded-30 bg-blue-6 dark:bg-blue-1 text-sm text-primary-2 text-stroke-thin">
              View More
            </button>
          </div>

          {/* Chart */}
          {chartData.length > 0 && (
            <>
              <div className="mt-5 mb-4 flex justify-between items-center">
                <p className="font-medium">Rarity Distribution</p>
                <Link
                  href=""
                  className="text-xs text-primary-2 text-stroke-thin"
                >
                  How to calculate?
                </Link>
              </div>
              <Bar {...config} />
            </>
          )}
        </div>

        <div className="w-full md:w-[265px] flex-grow flex flex-col">
          <p className="font-medium">Traits</p>
          <div className="mt-4 pb-15p h-full rounded-10 border border-gray-3">
            <div
              className={clsx(
                'pl-5 py-[5px] grid grid-cols-3 bg-blue-2 dark:bg-black rounded-t-10',
                'text-gray-1 dark:text-gray-3 text-stroke-thin text-sm'
              )}
            >
              <p>Trait type</p>
              <p>Value</p>
              <p className="pr-5 text-right">%</p>
            </div>
            {asset.attributeProperties.map((trait: any, index: number) => (
              <div key={index} className="pl-5 mt-15p grid grid-cols-3">
                <p className="text-sm font-medium">{trait.type}</p>
                <p className="text-sm font-medium">{trait.name}</p>
                <p className="pr-5 text-right text-sm font-medium text-blue-4">
                  {trait.percent}%
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </Modal>
  )
}

export default NftTabModal
