const PnlPositionsBoard = (props:any) => {
  const {
    closed = {},
    open = {},
  } = props

  return (
    <div className="w-full rounded-10 pb-5 border border-slate-300 overflow-hidden text-dark-primary dark:text-white">
      <div className="px-5 py-10p flex justify-between bg-blue-1">
        <p className="text-lg font-bold">Positions</p>
      </div>
      <div className="w-full">
        <p className="pl-5 mt-5 font-bold">Closed position</p>
        <p className="pl-30p py-[7px] pr-5 mt-6p flex justify-between text-stroke-thin">
          <span>Sent</span>
          <span>{closed.sent?.toLocaleString()} ETH</span>
        </p>
        <p className="pl-30p py-[7px] pr-5 mt-6p flex justify-between text-stroke-thin">
          <span>Received</span>
          <span>{closed.received?.toLocaleString()} ETH</span>
        </p>
        <p className="pl-30p py-[7px] pr-5 mt-6p flex justify-between text-stroke-thin">
          <span>Gas Paid</span>
          <span>{closed.gasFee?.toLocaleString()} ETH</span>
        </p>
        <p className="pl-30p py-[7px] pr-5 mt-6p flex justify-between bg-green-2 dark:bg-slate-600">
          <span className="text-stroke-thin">Realized Pnl</span>
          <span className="font-medium">{closed.realized?.toLocaleString()} ETH</span>
        </p>
      </div>
      <div className="w-full">
        <p className="pl-5 mt-5 font-bold">Open position</p>
        <p className="pl-30p py-[7px] pr-5 mt-6p flex justify-between text-stroke-thin">
          <span>Sent</span>
          <span>{open.sent?.toLocaleString()} ETH</span>
        </p>
        <p className="pl-30p py-[7px] pr-5 mt-6p flex justify-between text-stroke-thin">
          <span>Value @ Floor</span>
          <span>{open.valueFloor?.toLocaleString()} ETH</span>
        </p>
        <p className="pl-30p py-[7px] pr-5 mt-6p flex justify-between text-stroke-thin">
          <span>Gas Paid</span>
          <span>{open.gasFee?.toLocaleString()} ETH</span>
        </p>
        <p className="pl-30p py-[7px] pr-5 mt-6p flex justify-between bg-green-2 dark:bg-slate-600">
          <span className="text-stroke-thin">Unrealized P&L</span>
          <span className="font-medium">{open.unrealized?.toLocaleString()} ETH</span>
        </p>
      </div>
    </div>
  )
}

export default PnlPositionsBoard
