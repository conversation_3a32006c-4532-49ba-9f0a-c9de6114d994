import { useState, useEffect } from 'react'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import Select from 'presentation/components/select'
import Table from 'presentation/components/table'
import Pagination from 'presentation/components/pagination'
import LoadingData from 'presentation/components/skeleton/loading'
import PnlPositionsBoard from './pnlPositionsBoard'
import { accountPnlHeader } from './_tableHeaders'
import { pageSizes } from 'lib/hook/customHook'
import CategoryUseCase from 'presentation/use_cases/category_use_case'
import { Query } from 'domain/repositories/category_repository/query'

const categoryUseCase = new CategoryUseCase()

//Todo remove mock data
import { hourOptions, rowOptions } from 'presentation/controllers/mockData/options'
import {
  accountPnlTempData,
  accountPnlPositions,
} from 'presentation/controllers/mockData/account_mock_data'
import { toast } from 'react-toastify'
import Category from 'domain/models/category'
//End todo remove mock data

const PageSize = pageSizes.pageSize20

const AccountPnlTab = () => {
  const initParams = {
    categoryId: '',
    sortBy: '',
    limit: PageSize,
    page: 1,
  }
  const [currentPage, setCurrentPage] = useState(1)
  const [currentLimit, setCurrentLimit] = useState(PageSize)
  const [tableData, setTableData] = useState<any>([])
  const [total, setTotal] = useState(0)
  const { filterParams, changeFilterParam, setFilterParams } = useChangeFilterParams(initParams)
  const [isFetching, setIsFetching] = useState(true)
  const [filterCategories, setFilterCategories] = useState<any>([])
  const [positionsData, setPositionsData] = useState<any>({})
  const [isFetchingPositionsData, setIsFetchingPositionsData] = useState(true)
  const [categories, setCategories] = useState<Category[]>([])

  const fetchPositions = () => {
    setIsFetchingPositionsData(true)
    //TODO
    setPositionsData(accountPnlPositions)
    setIsFetchingPositionsData(false)
  }

  const fetchTableData = () => {
    setIsFetching(true)
    //TODO
    setTotal(accountPnlTempData.length)
    let data = accountPnlTempData.slice((currentPage - 1) * currentLimit, currentPage * currentLimit)
    setTableData(data)
    setIsFetching(false)
  }

  const formatCategories = () => {
    const formattedCategories = categories.map((category: any) => {
      return {
        id: category.id,
        name: category.name,
        value: category.slug,
      }
    })
    setFilterCategories([{ id: '', name: 'All', value: '' }, ...formattedCategories])
  }

  const fetchCategories = async () => {
    const query = new Query()
    await categoryUseCase.search(query).then((res) => {
      setCategories(res)
    }).catch((_error) => {
      setCategories([])
      toast.error('Failed to fetch categories')
    })
  }

  useEffect(() => {
    fetchTableData()
  }, [filterParams])

  useEffect(() => {
    if (categories.length > 0) {
      formatCategories()
    }
  }, [categories])

  useEffect(() => {
    fetchCategories()
    fetchPositions()
  }, [])

  return (
    <div className="mt-5 filter-table">

      <div className="filter-left">
        {!isFetchingPositionsData && (
          <PnlPositionsBoard
            closed={positionsData.closed}
            open={positionsData.open}
          />
        )}
      </div>

      <div className="table-container account-pnl">
        <div className="flex flex-wrap justify-between items-center gap-5 mb-5">
          <p className="shrink-0 text-gray-1 dark:text-gray-3 text-stroke-thin">Showing 1-20 out of 1,414</p>

          <div className="flex flex-wrap gap-3 md:gap-5">
            {filterCategories.length > 0 && (
              <Select
                options={filterCategories}
                onSelect={(option) => changeFilterParam('categoryId', option.value)}
                className="shrink-0"
              />
            )}
            {/* TODO */}
            <Select
              options={hourOptions}
              onSelect={(option) => { changeFilterParam('sortBy', option.value) }}
            />
            {/* End TODO */}
            <Select
              options={rowOptions}
              onSelect={(option) => {
                setCurrentPage(1),
                setCurrentLimit(option.value),
                setFilterParams({ ...filterParams, limit: option.value, page: 1 })
              }}
            />
          </div>
        </div>

        <div className="w-full border rounded-20">
          {isFetching ? (
            <LoadingData />
          ) : (
            <>
              <div className="w-full overflow-x-auto pb-3">
                <Table
                  head={accountPnlHeader}
                  data={tableData}
                  className="common-table account-pnl min-w-[1020px]"
                />
              </div>
              <div className="mt-2 px-5 mb-9">
                <Pagination
                  currentPage={currentPage}
                  totalCount={total}
                  pageSize={currentLimit}
                  onPageChange={(page: number) => {
                    setCurrentPage(page)
                    setFilterParams({ ...filterParams, page })
                  }}
                  onChangePageSize={(limit: number) => {
                    setCurrentPage(1)
                    setCurrentLimit(limit)
                    setFilterParams({ ...filterParams, limit, page: 1 })
                  }}
                />
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default AccountPnlTab
