import CollectionTableItem from 'presentation/components/table/collection-item'
import PriceBarTableItem from 'presentation/components/table/price-bar'
import AssetTableItem from 'presentation/components/table/asset-item'

export const accountPnlHeader = [
  {
    title: 'Last Date',
    key: 'lastDate',
    sortable: true,
    render: ({datetime}: any) => (
      <p className="w-full text-right pr-3.5">{datetime}</p>
    ),
  },
  {
    title: 'NFT',
    key: 'nft',
    sortable: true,
    render: (data: any) => (
      <div style={{ paddingLeft: '5px' }}>
        <AssetTableItem
          asset={data}
          imageSize={32}
          imageRounded="rounded-[10px]"
          nameOnly
        />
      </div>
    ),
  },
  {
    title: 'Token ID',
    key: 'tokenId',
    sortable: true,
    render: ({tokenId}: any) => (
      <div style={{ paddingLeft: '5px' }}>
        <p>{tokenId}</p>
      </div>
    ),
  },
  {
    title: 'Buy',
    key: 'buy',
    sortable: true,
    options: {
      sortKey: 'price',
    },
    render: ({price, usdPrice}: any) => (
      <div className="text-right">
        <p>{price}</p>
        <p>{usdPrice}</p>
      </div>
    ),
  },
  {
    title: 'Sell',
    key: 'sell',
    sortable: true,
    options: {
      sortKey: 'price',
    },
    render: ({price, usdPrice}: any) => (
      <div className="text-right">
        <p>{price}</p>
        <p>{usdPrice}</p>
      </div>
    ),
  },
  {
    title: 'Difference',
    key: 'difference',
    sortable: true,
    options: {
      sortKey: 'price',
    },
    render: ({price, usdPrice}: any) => (
      <div className="text-right">
        <p>{price}</p>
        <p>{usdPrice}</p>
      </div>
    ),
  },
  {
    title: 'Floor Price',
    key: 'floorPrice',
    sortable: true,
    options: {
      sortKey: 'price',
    },
    render: ({price, usdPrice}: any) => (
      <div className="text-right text-primary-2">
        <p>{price}</p>
        <p>{usdPrice}</p>
      </div>
    ),
  },
  {
    title: 'Symbol',
    key: 'symbol',
    render: ({symbol}: any) => (
      <div className="text-right" style={{ paddingRight: '12px' }}>
        <p>{symbol}</p>
      </div>
    ),
  },
]

export const collectionTableHeader = [
  {
    title: 'Collection',
    key: 'collection',
    description: 'Description',
    options: {
      tooltipPosition: 'right',
    },
    render: (data: any) => (<CollectionTableItem record={data} />),
  },
  {
    title: 'NFTs',
    key: 'nfts',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: ({amount}: any) => (
      <div className="text-right" style={{ paddingRight: '26px' }}>
        <p>{amount}</p>
      </div>
    ),
  },
  {
    title: 'Holding Value',
    key: 'holdingValue',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: (data: any) => (<PriceBarTableItem {...data}/>),
  },
  {
    title: 'Est HV',
    key: 'estHv',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: (data: any) => (<PriceBarTableItem {...data}/>),
  },
  {
    title: 'Pnl',
    key: 'pnl',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: (data: any) => (<PriceBarTableItem {...data}/>),
  },
  {
    title: 'Buy Volume',
    key: 'buyVolume',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: (data: any) => (<PriceBarTableItem {...data}/>),
  },
  {
    title: 'Sell Volume',
    key: 'sellVolume',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: (data: any) => (<PriceBarTableItem {...data}/>),
  },
  {
    title: 'Last Deal',
    key: 'lastDeal',
    sortable: true,
    render: ({datetime}: any) => (
      <div className="text-right" style={{ paddingRight: '18px' }}>
        <p className="text-green-1">{datetime}</p>
      </div>
    ),
  },
]

export const relatedAddressHeader = [
  {
    title: 'Address',
    key: 'address',
  },
  {
    title: 'Send',
    key: 'send',
    description: 'Send',
    sortable: true,
    render: ({count}: any) => (
      <div className="text-right" style={{ paddingRight: '26px' }}>
        <p>{count}</p>
      </div>
    ),
  },
  {
    title: 'Receive',
    key: 'receive',
    description: 'Send',
    sortable: true,
    render: ({count}: any) => (
      <div className="text-right" style={{ paddingRight: '26px' }}>
        <p>{count}</p>
      </div>
    ),
  },
  {
    title: 'Last Transfer',
    key: 'lastTransfer',
    render: ({datetime}: any) => (
      <div className="text-right" style={{ paddingRight: '18px' }}>
        <p>{datetime}</p>
      </div>
    ),
  },
]
