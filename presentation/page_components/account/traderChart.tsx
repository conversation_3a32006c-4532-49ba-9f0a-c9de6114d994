import dynamic from 'next/dynamic'
import { useState, useRef, memo } from 'react'
import Tooltip from 'presentation/components/tooltip'
import Select from 'presentation/components/select'

const Scatter = dynamic(() => import('@ant-design/plots').then(({ Scatter }) => Scatter), { ssr: false })

const MemoPlot = memo(({ config, getChartRef}: any) => {
  return <Scatter {...config} onReady={(chartRef) => getChartRef(chartRef)} />
}, (pre, next) => {
  return JSON.stringify(pre.config) === JSON.stringify(next.config)
})

MemoPlot.displayName = 'MemoPlot'

const TraderChart = (props:any) => {
  const {
    chartData = [],
    legends = [],
    periodRanges = [],
    defaultPeriodRange,
    onChangePeriodRanges = () => {},
    isFetching = true,
  } = props

  const ref = useRef<any>(null)
  const [selectedDay, setSelectedDay] = useState(defaultPeriodRange)
  const [legendList, setLegendList] = useState([])

  const config: any = {
    height: 330,
    data: chartData,
    xField: 'date',
    yField: 'value',
    xAxis: {
      title: {
        text: 'UTC                     ',
        position: 'start',
        offset: 14,
        style: {
          fontSize: 12,
        },
      },
    },
    yAxis: {
      title: {
        text: 'Price(ETH)',
        style: {
          fontSize: 14,
        },
      },
    },
    colorField: 'type',
    color: (...args:any) => {
      const type = args[0].type
      switch (type) {
        case 'Buy':
          return '#7F8AF2'
        case 'Sell':
          return '#E9B15C'
        default:
          return 'transparent'
      }
    },
    meta: {
      value: {
        min: 0,
        max: 6,
      },
    },
    legend: false,
  }

  const getChartRef = (chartRef: any) => {
    if (!chartRef) return
    ref.current = chartRef

    const chart = ref.current.chart
    const legendList = chart.geometries[0]?.dataArray?.map((item: any) => {
      const origin = item[0]._origin
      origin.color = item[0].color
      origin.checked = true
      return origin
    })
    setLegendList(legendList)
  }

  const handleLegendClick = (item: any, i: number) => {
    const newItem = item
    newItem.checked = !newItem.checked
    const currentLegend: any = [...legendList]
    currentLegend[i] = newItem

    const filteredLegendList = currentLegend
      .filter((l: any) => l.checked)
      .map((l: any) => l.type)

    const chart = ref.current.chart
    if (chart) {
      chart.filter('type', (val:any) => {
        return filteredLegendList.indexOf(val) > -1
      })
      chart.render()
    }
    setLegendList(currentLegend)
  }

  return (
    <div className="rounded-10 p-5 border border-gray-3 flex flex-col justify-between">
      <div className="flex flex-wrap justify-between">
        <div className="flex items-center">
          <p className="text-lg font-bold text-dark-primary dark:text-white">
            Traders
          </p>
          <Tooltip iconSize="w-4 h-4 -mt-0.5">Traders</Tooltip>
        </div>
        <Select
          options={periodRanges}
          defaultSelected={defaultPeriodRange}
          onSelect={(option) => {
            setSelectedDay(option)
            onChangePeriodRanges(option)
          }}
          className="shrink-0"
        />
      </div>

      {!isFetching && (
        <div className="flex flex-col sm:mt-0 mt-4">
          <ul className="flex flex-wrap justify-center w-full gap-5 mb-5">
            {legendList?.map((item: any, i: number) => (
              <li
                key={i} onClick={() => handleLegendClick(item, i)}
                className="flex flex-col cursor-pointer"
              >
                <div className="inline-flex items-center">
                  <span
                    className="rounded-full w-2 h-2 mr-2"
                    style={{ backgroundColor: item.checked ? item.color : '#aaa' }}
                  ></span>
                  <span className="font-medium text-sm text-dark-3 dark:text-white">
                    {item.type}({selectedDay.value?.replace('days', '')}D)
                  </span>
                </div>
                <span className="w-full text-right font-medium pr-1 text-dark-primary dark:text-white">
                  {legends[i].amount?.toLocaleString()}
                </span>
                <span className={`w-full text-right text-sm font-medium pr-1
                  ${legends[i].percent > 0 ? 'text-primary-1' : 'text-red-1'}`}
                >
                  {legends[i].percent > 0 ? '+' : ''}{(legends[i].percent.toFixed(2)).toLocaleString()}%
                </span>
              </li>
            ))}
          </ul>
          <MemoPlot config={config} getChartRef={getChartRef} />
        </div>
      )}
    </div>
  )
}

export default TraderChart
