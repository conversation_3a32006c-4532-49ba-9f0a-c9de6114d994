import { useEffect, useState } from 'react'
import { FilterParams } from 'types/type'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import Select from 'presentation/components/select'
import NoData from 'presentation/components/no-data'
import SelectSearch from 'presentation/components/select/select-search'
import ToggleWrap from 'presentation/components/toggle-wrap'
import SkeletonAsset from 'presentation/components/skeleton/assets'
import PaginationDot from 'presentation/components/pagination/pagination-dot'
import NftTabModal from './nftTabModal'
import ToggleVerticalSearchCollection from 'presentation/components/toggle-wrap/toggle-vertical-search-collections'
import ToggleVerticalFilterRarityScore from 'presentation/components/toggle-wrap/toggle-vertical-filter-rarity-score'
import ToggleVerticalFilterPrice from 'presentation/components/toggle-wrap/toggle-vertical-filter-price'
import Asset from 'presentation/components/asset'
import { pageSizes } from 'lib/hook/customHook'
import AssetUseCase from 'presentation/use_cases/asset_use_case'
import SortBy from 'domain/repositories/asset_repository/sort_by'
import FilterStatus from 'domain/repositories/asset_repository/filter_status'
import { Query as AssetQuery } from 'domain/repositories/asset_repository/query'
import AssetModel from 'domain/models/asset'

const assetUseCase = new AssetUseCase()

//Todo remove mock data
import { selectSearchOptions } from 'presentation/controllers/mockData/common_mock_data'
import { rarityDistributionTempData } from 'presentation/controllers/mockData/account_mock_data'
//End todo remove mock data

const PageSize = pageSizes.pageSize25

const AccountNftTab = () => {
  const initParams: FilterParams = {
    filterStatuses: [],
    minPrice: '',
    maxPrice: '',
    userId: '',
    page: 1,
    limit: PageSize,
    sortBy: 'recentlyListed',
  }

  const [showModal, setShowModal] = useState(false)
  const [selectedAsset, setSelectedAsset] = useState(null)

  const { filterParams, changeFilterParam } = useChangeFilterParams(initParams)
  const [currentPage, setCurrentPage] = useState<any>({})
  const [assets, setAssets] = useState<AssetModel[]>([])
  const [isFetchingAssets, setIsFetchingAssets] = useState(false)
  const [totalAssets, settotalAssets] = useState(0)
  const [sortList, setSortList] = useState<any>([])
  const [showSortList, setShowSortList] = useState(false)
  const [rarityDistributionData, setRarityDistributionData] = useState<any>([])

  const fetchAssets = async () => {
    setIsFetchingAssets(true)
    const query = new AssetQuery({
      filterStatuses: filterParams.filterStatuses?.map((status) => {
        return FilterStatus.fromName(status)
      }),
      minPrice: filterParams.minPrice,
      maxPrice: filterParams.maxPrice,
      userId: filterParams.userId,
      page: filterParams.page,
      limit: filterParams.limit,
      sortBy: SortBy.fromName(filterParams.sortBy!),
    })
    await assetUseCase
      .totalCount(query)
      .then((res) => {
        settotalAssets(res)
      })
      .catch((_error) => {
        settotalAssets(0)
      })
    await assetUseCase
      .search(query)
      .then((res) => {
        setAssets(res)
      })
      .catch((_error) => {
        setAssets([])
      })
    setIsFetchingAssets(false)
  }

  const fetchRarityDistributionData = () => {
    //TODO
    setRarityDistributionData(rarityDistributionTempData)
  }

  const formatSortList = () => {
    const sortList = SortBy.getResourceArray().map((sort: any) => {
      return {
        id: sort.id,
        name: sort.label,
        value: sort.name,
      }
    })
    setSortList(sortList)
    setShowSortList(true)
    fetchRarityDistributionData()
  }

  const handleRarityRank = (asset: any) => {
    setShowModal(true)
    setSelectedAsset({
      ...asset,
      attributeProperties: [
        { type: 'Artist', name: 'angelbaby', percent: 3.51 },
        { type: 'Drop', name: '2', percent: 31.58 },
        { type: 'Type', name: 'Music', percent: 61.4 },
      ],
    })
  }

  useEffect(() => {
    fetchAssets()
  }, [filterParams])

  useEffect(() => {
    formatSortList()
  }, [])

  return (
    <>
      <div className="mt-5 product-layout">
        <div className="product-layout-filter">
          <ToggleWrap title="Filter" contentClass="border-t-0 overflow-hidden">
            <ToggleVerticalSearchCollection
              defaultShow
              changeFilterParam={changeFilterParam}
            />
            <ToggleVerticalFilterRarityScore
              defaultShow
              changeFilterParam={changeFilterParam}
            />
            <ToggleVerticalFilterPrice
              defaultShow
              minPrice={filterParams.minPrice || ''}
              maxPrice={filterParams.maxPrice || ''}
              changeFilterParam={changeFilterParam}
            />
          </ToggleWrap>
        </div>

        <div className="w-full md:w-9/12 nft-rarity-wrap">
          <div className="mb-5 flex flex-wrap justify-between">
            <div>
              <p className="font-bold text-23">NFT</p>
              {isFetchingAssets ? (
                <p className="w-36 h-4 bg-gray-3 mt-5 rounded-lg animate-pulse"></p>
              ) : (
                <p className="text-gray-1 dark:text-gray-3 text-stroke-thin mt-5">
                  Results: {totalAssets.toLocaleString()}
                </p>
              )}
            </div>
            <div className="mt-2 lg:mt-0 flex flex-wrap lg:flex-nowrap gap-5">
              <SelectSearch
                options={selectSearchOptions}
                className="shrink-0"
              />
              {showSortList && (
                <Select
                  options={sortList}
                  onSelect={(option) => {
                    changeFilterParam('sortBy', option.value)
                  }}
                  className="h-fit"
                />
              )}
            </div>
          </div>

          <div
            className={`product-wrap no-gap ${!isFetchingAssets && assets.length === 0 ? 'no-data' : ''
            } mt-0`}
          >
            {isFetchingAssets ? (
              [...Array(5)].map((_, id) => <SkeletonAsset key={id} />)
            ) : (
              <>
                {assets.length > 0 ? (
                  assets.map((asset: any, index: number) => (
                    <Asset
                      key={asset.id + '-' + index}
                      record={asset}
                      showDaysLeft={false}
                      showOfferText={false}
                      showAddToCart={true}
                    />
                  ))
                ) : (
                  <div className="w-full rounded-lg border flex justify-center items-center py-24">
                    <NoData />
                  </div>
                )}
              </>
            )}
          </div>
          {assets.length > 20 && !isFetchingAssets && (
            <div className="mt-6 flex justify-center">
              <PaginationDot
                currentPage={currentPage}
                totalCount={totalAssets}
                pageSize={PageSize}
                onPageChange={(page: number) => {
                  setCurrentPage(page), changeFilterParam('page', page)
                }}
              />
            </div>
          )}
        </div>
      </div>

      {showModal && (
        <NftTabModal
          isShow={showModal}
          asset={selectedAsset}
          chartData={rarityDistributionData}
          onClose={() => setShowModal(false)}
        />
      )}
    </>
  )
}

export default AccountNftTab
