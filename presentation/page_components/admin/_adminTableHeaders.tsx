import PriceBarTableItem from 'presentation/components/table/price-bar'
import UserTableItem from 'presentation/components/table/user-item'
import Image from 'next/image'
import SvgIcon from 'presentation/components/svg-icon'

export const adminTableHeader = [
  {
    title: 'Total amount',
    key: 'totalAmount',
    render: ({ amount }: any) => (
      <div className="text-right">
        <p>${amount?.toLocaleString()}</p>
      </div>
    ),
  },
  {
    title: '2.5% Total',
    key: 'total',
    render: ({ amount }: any) => (
      <div className="text-right">
        <p>${amount?.toLocaleString()}</p>
      </div>
    ),
  },
  {
    title: 'NFTs',
    key: 'nfts',
    render: ({ amount }: any) => (
      <div className="text-right">
        <p>{amount?.toLocaleString()}</p>
      </div>
    ),
  },
  {
    title: 'NFTs traded',
    key: 'nftstraded',
    render: ({ amount }: any) => (
      <div className="text-right">
        <p>{amount?.toLocaleString()}</p>
      </div>
    ),
  },
  {
    title: 'Accounts',
    key: 'accounts',
    render: ({ amount }: any) => (
      <div className="text-right">
        <p>{amount?.toLocaleString()}</p>
      </div>
    ),
  },
  {
    title: 'New Accounts',
    key: 'newAccounts',
    render: ({ amount }: any) => (
      <div className="text-right">
        <p>{amount?.toLocaleString()}</p>
      </div>
    ),
  },
  {
    title: 'Total NFTs',
    key: 'totalNfts',
    render: ({ amount }: any) => (
      <div className="text-right">
        <p>{amount?.toLocaleString()}</p>
      </div>
    ),
  },
]

export const adminAccountHeader = [
  {
    title: 'Owner',
    key: 'owner',
    render: (data: any) => (<UserTableItem record={data} />),
  },
  {
    title: 'Pnl',
    key: 'pnl',
    sortable: true,
    render: (data: any) => (<PriceBarTableItem {...data} paddingRight="0" />),
  },
  {
    title: 'Buy Volume',
    key: 'buyVolume',
    sortable: true,
    render: (data: any) => (<PriceBarTableItem {...data} paddingRight="0" />),
  },
  {
    title: 'Sell Volume',
    key: 'sellVolume',
    sortable: true,
    render: (data: any) => (<PriceBarTableItem {...data} paddingRight="0" />),
  },
  {
    title: 'Collections',
    key: 'collections',
    sortable: true,
    render: ({ amount }: any) => (
      <div className="text-right">
        <p>{amount?.toLocaleString()}</p>
      </div>
    ),
  },
  {
    title: 'Minted',
    key: 'minted',
    sortable: true,
    render: ({ count }: any) => (
      <div className="text-right">
        <p>{count?.toLocaleString()}</p>
      </div>
    ),
  },
  {
    title: 'Start date',
    key: 'startDate',
    sortable: true,
    render: ({ datetime }: any) => (
      <div className="text-right">
        <p>{datetime}</p>
      </div>
    ),
  },
]

export const adminCategoryHeader = [
  {
    title: 'Name',
    key: 'name',
    render: ({ name, thumbnail }: any) => (
      <div className="flex items-center min-w-max">
        <p className="font-medium mr-10p shrink-0">{name}</p>
        <Image alt={name} width={25} height={25} src={thumbnail?.url} className="rounded-full" />
      </div>
    ),
  },
  {
    title: 'Slug',
    key: 'slug',
  },
  {
    title: 'Description',
    key: 'description',
    render: ({ text }: any) => (
      <p className="font-medium max-w-[100px] truncate">{text}</p>
    ),
    tdClass: 'truncate max-w-[100px]',
  },
  {
    title: 'Count',
    key: 'count',
    render: ({ data }: any) => (
      <>
        {(data || 0).toLocaleString()}
      </>
    ),
  },
  {
    title: '',
    key: 'more',
    render: ({ onClick }: any) => (
      <div onClick={() => onClick()}>
        <SvgIcon name="moreVertical" className="w-5 h-5 stroke-none fill-primary-2 cursor-pointer" />
      </div>
    ),
  },
  {
    title: '',
    key: 'delete',
    render: ({ onClick }: any) => (
      <div className='px-2' onClick={() => onClick()}>
        <SvgIcon name="xCircle" className="w-5 h-5 stroke-none fill-primary-2 cursor-pointer" />
      </div>
    ),
  },
]

export const adminDetailHeader = [
  {
    title: 'Time',
    key: 'time',
  },
  {
    title: 'Section',
    key: 'section',
  },
  {
    title: 'Action',
    key: 'action',
  },
]
