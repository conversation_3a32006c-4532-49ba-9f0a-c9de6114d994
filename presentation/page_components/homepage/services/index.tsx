import Image from 'next/image'
import Heading from 'presentation/components/heading'

type Props = {
  isMobile: boolean
}

const Services = (props: Props) => {
  return props.isMobile ? (
    <div className="mt-10 py-9 container-df bg-gray-4 dark:bg-gray-8">
      <h1 className="font-heading text-center text-[27px] text-dark-primary dark:text-white">
        Welcome!<br />NFT Marketplace Mall
      </h1>
      <p className="mt-5 text-gray-1 dark:text-gray-3">
        VIEW YOUR COLLECTION<br />
        In the Mythical Marketplace you can now view all your items and manage your inventory via My Collection.
      </p>
      <button
        className="py-3.25 mt-5 w-full text-lg rounded-full text-white bg-gradient-primary-2 shadow-normal poppins-medium tracking-[1px]"
      >
        Connect wallet
      </button>
      <button
        className={`py-3.25 mt-5 w-full text-base rounded-full text-primary-2 dark:text-white font-medium
          bg-gradient-primary-1-opacity-10 dark-bg-gradient-primary-1-opacity-50 shadow-normal`}
      >
        Register
      </button>
      <div className="mt-10">
        <Image
          src="/asset/images/marketplace-mall-preview-img.svg"
          width={343} height={184} sizes="100vw"
          style={{ width: '100%', height: 'auto' }} alt=""
        />
      </div>
    </div>
  ) : (
    <div className="service-section bg-gray-4 dark:bg-gray-8 text-dark-primary dark:text-white">
      <div className="container-df pt-12 pb-20 flex flex-col items-center">
        <div className="px-second block">
          <Heading className="service-heading-1 leading-10">Assets available for sale one Quill</Heading>
          <div className="service-file-types mt-12 xl:px-14 grid md:grid-cols-4 xs:grid-cols-2 xs:gap-4">
            <div className="text-center">
              <Image className="inline-block" src="/asset/images/image-placeholder.png" width={100} height={131} alt="" />
              <p className="mt-2 font-medium">IMAGE</p>
              <p className="text-sm text-dark-primary dark:text-white text-stroke-thin">
                JPG, PNG, GIF, SVG
              </p>
            </div>
            <div className="text-center">
              <Image className="inline-block" src="/asset/images/video-placeholder.png" width={100} height={131} alt="" />
              <p className="mt-2 font-medium">VIDEO</p>
              <p className="text-sm text-dark-primary dark:text-white text-stroke-thin">
                MP4, WEBM
              </p>
            </div>
            <div className="text-center">
              <Image className="inline-block" src="/asset/images/audio-placeholder.png" width={100} height={131} alt="" />
              <p className="mt-2 font-medium">AUDIO</p>
              <p className="text-sm text-dark-primary dark:text-white text-stroke-thin">
                MP3, WAV, OGG
              </p>
            </div>
            <div className="text-center">
              <Image className="inline-block" src="/asset/images/3d-placeholder.png" width={100} height={131} alt="" />
              <p className="mt-2 font-medium">3D MODEL</p>
              <p className="text-sm text-dark-primary dark:text-white text-stroke-thin">
                GLB, GLTF
              </p>
            </div>
          </div>
        </div>
        {/* <div className="service-content pr-second mt-20">
          <div className="relative service-image h-80 mr-16">
            <Image src="/asset/images/marketplace-mall-preview-img.svg" fill style={{ objectFit: 'contain' }} alt="" />
          </div>
          <div className="service-text">
            <h2 className="font-medium italic">
              Analytics function
            </h2>
            <p className="mt-4">
              Analyze the NFT market width multifaceted date
            </p>

            <h2 className="mt-7 font-medium italic">
              Board trading of the same NFT
            </h2>
            <p className="mt-4">
              Normally, you need to go to the individual page of each NFT to make a purchase. But NFT-LAND enables board trading.
            </p>

            <h2 className="mt-7 font-medium italic">
              Random Generate function
            </h2>
            <p className="mt-4">
              Generates a single NFT by randomly assembling parts materials by the user.
            </p>
          </div>
        </div> */}
        <Heading className="service-heading-2 mt-24 xs:mt-16 xs:leading-[56px]">Wellcome <span className="not-italic">!</span> NFT Marketplace Mall</Heading>
        <p className="service-heading-2-description text-gray-1 dark:text-gray-3 text-center mt-4">
          VIEW YOUR COLLECTION<br />
          In the Mythical Marketplace you can now view all your items and manage your inventory via My Collection.
        </p>
      </div>
    </div>
  )
}

export default Services
