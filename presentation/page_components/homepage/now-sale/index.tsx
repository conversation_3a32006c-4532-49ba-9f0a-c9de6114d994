import { useState, useEffect } from 'react'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import Button from 'presentation/components/button'
import Collection from 'presentation/components/collection'
import Heading from 'presentation/components/heading'
import Select from 'presentation/components/select'
import SkeletonAsset from 'presentation/components/skeleton/assets'
import NoData from 'presentation/components/no-data'
import CollectionUseCase from 'presentation/use_cases/collection_use_case'
import { Query } from 'domain/repositories/collection_repository/query'
import SortBy from 'domain/repositories/collection_repository/sort_by'
import CollectionModel from 'domain/models/collection'

const collectionUseCase = new CollectionUseCase()

const NowSale = () => {
  const initParams: any = {
    page: 1,
    limit: 4,
    sortBy: 'recentlyListed',
  }

  const {filterParams, changeFilterParam} = useChangeFilterParams(initParams)
  const [collections, setCollections] = useState<CollectionModel[]>([])
  const [isFetchingCollections, setIsFetchingCollections] = useState(false)
  const [sortList, setSortList] = useState<any>([])
  const [showSortList, setShowSortList] = useState(false)

  const fetchCollections = async () => {
    setIsFetchingCollections(true)
    const query = new Query({
      page: filterParams.page,
      limit: filterParams.limit,
      sortBy: SortBy.fromName(filterParams.sortBy!),
    })
    await collectionUseCase.search(query).then((res) => {
      setCollections(res)
    }).catch((_error) => {
      setCollections([])
    })
    setIsFetchingCollections(false)
  }

  const formatSortList = () => {
    const sortList = SortBy.getResourceArray().map((sort:any) => {
      return {
        id: sort.id,
        name: sort.label,
        value: sort.name,
      }
    })
    setSortList(sortList)
    setShowSortList(true)
  }

  useEffect(() => {
    formatSortList()
  }, [])

  useEffect(() => {
    fetchCollections()
  }, [filterParams])

  return (
    <section className="nowsale-section pt-4 md:pt-10">
      <Heading className="hidden md:block">Now Sale</Heading>
      <div className="md:hidden flex justify-between">
        <Heading>Top NFTs</Heading>
        {showSortList && (
          <Select
            options={sortList}
            onSelect={(option) => {changeFilterParam('sortBy', option.value)}}
          />
        )}
      </div>
      {isFetchingCollections ? (
        <div className="w-full grid grid-cols-2 md:grid-cols-4 grid-rows-1 gap-3 overflow-x-auto">
          {[...Array(4)].map((_, i) => (<SkeletonAsset key={i}/>))}
        </div>
      ) : (
        <div className="flex collection-wrap mt-6 pb-4 md:pb-0 overflow-x-auto md:overflow-x-hidden">
          {collections.length > 0 ? collections.map((collection: any, index: number) => (
            <div key={index} className="collection">
              <Collection {...collection} record={collection} coinSymbolSize={22} />
            </div>
          )) : (
            <div className="w-full pr-5">
              <div className="w-full rounded-lg border flex justify-center items-center py-24">
                <NoData/>
              </div>
            </div>
          )}
        </div>
      )}

      <div className="hidden md:block text-center mt-7">
        <Button href="/assets" variant="light">See More</Button>
      </div>
    </section>
  )
}

export default NowSale
