import Image from 'next/image'
import Link from 'next/link'
import NoData from 'presentation/components/no-data'
import SkeletonHeroBanner from 'presentation/components/skeleton/hero-banner'
import SvgIcon from 'presentation/components/svg-icon'
import { useEffect, useState } from 'react'
import { Autoplay, Navigation, Pagination } from 'swiper'
import 'swiper/css'
import { Swiper, SwiperSlide } from 'swiper/react'
import MainBanner from '../../../../domain/models/main_banner'
import { Query } from '../../../../domain/repositories/main_banner_repository/query'
import MainBannerUseCase from '../../../use_cases/main_banner_use_case'
import { getChainName } from 'lib/utils'
const useCase = new MainBannerUseCase()

const Hero = () => {
  const [isFetching, setIsFetching] = useState<boolean>(true)
  const [mainBanners, setMainBanners] = useState<MainBanner[]>([])

  const fetchMainBanners = () => {
    setIsFetching(true)
    const query = new Query()
    useCase.search(query)
      .then((banners: MainBanner[]) => {
        setMainBanners(banners)
        setIsFetching(false)
      }).catch((error) => {
        setMainBanners([])
        setIsFetching(false)
      })
  }
  const renderLink = (mainBanner: MainBanner) => {
    if(mainBanner?.url?.startsWith('https://') || mainBanner?.url?.startsWith('http://')) {
      return (  <Link href={`/collections/${getChainName(mainBanner?.collection?.chainId)}/${mainBanner?.collection?.slug}`} target='_blank'>
        <Image
          src={mainBanner?.collection?.bannerImage?.url || ''}
          alt={mainBanner?.collection?.name || ''}
          layout="fill"
          objectFit='cover'
          objectPosition="center"
        />
      </Link>)
    }
    if (mainBanner?.url) {
      return (
        <Link href={`/collections/${getChainName(mainBanner?.collection?.chainId)}/${mainBanner?.collection?.slug}`}>
          <Image
            src={mainBanner?.banner?.url || ''}
            alt={mainBanner?.title || ''}
            layout="fill"
            objectFit='cover'
            objectPosition="center"
          />
        </Link>
      )
    }else{
      return (
        <Image
          src={mainBanner?.banner?.url || ''}
          alt={mainBanner?.title || ''}
          layout="fill"
          objectFit='cover'
          objectPosition="center"
        />
      )
    }
  }
  useEffect(() => {
    fetchMainBanners()
  }, [])

  return (
    <div className='container m-auto'>
      <div className="hero-wrapper xml:max-h-full bg-[url('/asset/icons/banner-main-bg.png')] bg-cover ">
        <div className='lg:pl-20 lg:pr-20 md:pl-10 md:pr-10'>
          {isFetching ? (
            <SkeletonHeroBanner />
          ) : mainBanners.length > 0 ? (
            <Swiper
              slidesPerView={'auto'}
              spaceBetween={17}
              freeMode={true}
              breakpoints={{
                768: {
                  slidesPerView: 'auto',
                  spaceBetween: 25,
                },
                1920: {
                  spaceBetween: 25,
                  slidesPerView: 3,
                },
              }}
              className="mySwiper"
              loop={true}
              autoplay={{
                delay: 2500,
                disableOnInteraction: false,
              }}
              modules={[Autoplay, Pagination, Navigation]}
            >
              { mainBanners.map((mainBanner: MainBanner, index: number) => (
                <SwiperSlide key={index}>
                  <div className="relative w-full h-full">
                    {renderLink(mainBanner)}
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>
          ) :(
            <div className="w-full rounded-lg border flex justify-center items-center py-24">
              <NoData />
            </div>
          )}
        </div>

        {/* Need to remove later */}
        {/* <div className="news-tag mt-4 rounded-tl-[10px] py-1 w-full
          text-sm md:text-base
          flex md:hidden justify-between bg-gradient-to-b from-gradient-sky to-gradient-ocean text-white">
          <p className="max-w-[70%] truncate">Best marketplace all over the world</p>
          <Link href="/">
            <>
              News
              <SvgIcon name="chevronRight" className="w-5 h-5 stroke-none fill-white"/>
            </>
          </Link>
        </div> */}
      </div>
    </div>
  )
}

export default Hero

