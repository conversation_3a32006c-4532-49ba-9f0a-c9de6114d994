
import Collection from 'domain/models/collection'
import SortBy from 'domain/repositories/activity_repository/sort_by'
import { Query as CollectionQuery } from 'domain/repositories/collection_repository/query'
import { screen, useScreenWidth } from 'lib/hook/customHook'
import { DateTime } from 'luxon'
import Link from 'next/link'
import Button from 'presentation/components/button'
import CollectionComponent from 'presentation/components/collection'
import Heading from 'presentation/components/heading'
import NoData from 'presentation/components/no-data'
import Select from 'presentation/components/select'
import SkeletonAsset from 'presentation/components/skeleton/assets'
import AssetUseCase from 'presentation/use_cases/asset_use_case'
import CollectionUseCase from 'presentation/use_cases/collection_use_case'
import { useEffect, useState } from 'react'
import { getChainName } from 'lib/utils'
import { NetworkStateProvider } from 'presentation/providers/network_state_provider'

const collectionUseCase = new CollectionUseCase()

const sortList = SortBy.getResourceArray().map((sort: any) => {
  return {
    id: sort.id,
    name: sort.label,
    value: sort.name,
  }
})

const NewCollections = () => {
  // Add NetworkStateProvider
  const networkStateProvider = NetworkStateProvider((state) => ({
    network: state.network,
  }))
  
  // Add state to track if network is ready
  const [isNetworkReady, setIsNetworkReady] = useState<boolean>(false)
  
  // Add state to track previous chainId to detect changes
  const [prevChainId, setPrevChainId] = useState<string>('')

  const initParams: any = {
    page: 1,
    limit: 5,
    sortBy: SortBy.recentlyListed(),
  }

  const [productType, setProductType] = useState('collections')
  const [filterParams, setFilterParams] = useState(initParams)
  const [isFetchingCollections, setIsFetchingCollections] =
    useState<boolean>(false)
  const [collections, setCollections] = useState<Collection[]>([])
  const [isPC, setIsPC] = useState<boolean>(false)
  const screenWidth = useScreenWidth()

  // Monitor network state changes and reset page when chainId changes
  useEffect(() => {
    if (networkStateProvider.network !== undefined) {
      setIsNetworkReady(true)
      
      const currentChainId = networkStateProvider.network?.chainId || ''
      
      // Only reset if chainId actually changed and is not the initial load
      if (prevChainId !== currentChainId) {
        // Reset page to 1 when chainId changes
        setFilterParams({
          ...filterParams,
          page: 1,
        })
      }
      
      // Update previous chainId
      setPrevChainId(currentChainId)
    }
  }, [networkStateProvider.network])

  const fetchCollections = async () => {
    // Only proceed if network is ready
    if (!isNetworkReady) return
    
    setIsFetchingCollections(true)
    
    // Get chainId from network provider
    const chainId = networkStateProvider.network?.chainId || ''
    
    let QueryAssetsParams = { 
      ...filterParams,
      chainId: chainId, // Add chainId to query
    }
    
    const query = new CollectionQuery(QueryAssetsParams)
    
    collectionUseCase.search(query).then((collections: Collection[]) => {
      setCollections(collections)
    }).catch((error) => {})
    
    setIsFetchingCollections(false)
  }

  useEffect(() => {
    if (screenWidth && screenWidth > screen.md) {
      setIsPC(true)
    } else {
      setIsPC(true)
    }
  }, [screenWidth])

  useEffect(() => {
    if (isPC && isNetworkReady) {
      fetchCollections()
    }
  }, [filterParams, isPC, isNetworkReady, networkStateProvider.network?.chainId])

  return (
    <div className="new-collections-section topsale-section pt-12">
      <Heading>New Listings</Heading>
      <div className="md:flex mt-8">
        <div className="product-side mt-4 md:mt-0">
          <div
            className={`product-wrap collection-main !grid-cols-none ${
              !isFetchingCollections && collections.length === 0 && 'no-data'
            }`}
          >
            {isFetchingCollections ? (
              <div className="w-full grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3">
                {[...Array(5)].map((_, i) => (
                  <SkeletonAsset key={i} />
                ))}
              </div>
            ) : (
              <>
                {collections.length > 0 ? (
                  <div className="w-full grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 xl:grid-cols-5 gap-3">
                    {collections.map((collection: any, index) => (
                      <div key={index} className={`collection ${
                        collections.length % 2 === 1 && index === collections.length - 1 ? 'xs:col-span-2 xs:flex xs:justify-center md:col-span-1' : ''
                      }`}>
                        <div className={collections.length % 2 === 1 && index === collections.length - 1 ? 'xs:w-1/2 md:w-full' : 'w-full'}>
                          <Link href={`/collections/${getChainName(collection.chainId)}/${collection.slug}`}>
                            <CollectionComponent
                              {...collection}
                              record={collection}
                              coinSymbolSize={15}
                            />
                          </Link>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="w-full rounded-lg border flex justify-center items-center py-24">
                    <NoData />
                  </div>
                )}
              </>
            )}
          </div>
          
        </div>
      </div>
      <div className="block text-center mt-6">
        <Button href={`/${productType}`} variant="light">
          See More
        </Button>
      </div>
    </div>
  )
}

export default NewCollections
