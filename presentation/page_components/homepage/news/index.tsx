import { useEffect, useState } from 'react'
import Heading from 'presentation/components/heading'
import NewsUseCase from 'presentation/use_cases/news_use_case'
import NewsModel from 'domain/models/news'
import { url } from 'inspector'
const newsUseCase = new NewsUseCase()
const News = () => {
  const [news, setNews] = useState<NewsModel[]>([])
  const mapNewsData = (data: any) => {
    if (data?.length > 0) {
      let rs:any = data.map((item : any, index : number) => {
        const itemData = data[index]
        if (itemData) {
          return {
            id: itemData.id,
            date: itemData.title,
            description: itemData.body,
            url: itemData.url,
          }
        } else { 
          return item
        }
       
      })
      // filter news when have data
      rs = rs.filter((item : any) => item.date && item.description)
      setNews(rs)
    }
  }
  const fetchNews = async () => {
    try {
      const newsData = await newsUseCase.getNews()
      mapNewsData(newsData)
    } catch(err) {
      console.log('🚀 ~ file: index.tsx:fetchNews ~ err:', err)
    }
  }

  /***
   * USE EFFECT
   */
  useEffect(() => {
    fetchNews()
  }, [])

  return (
    <>
      {news?.length > 0 && (
        <div className="w-full bg-white dark:bg-dark-primary mb-16">
          <div className="w-full md:w-4/5 mx-auto px-4 sm:px-6 lg:px-8">
            <Heading className="dark:text-white">NEWS</Heading>
            <div className="space-y-6 mt-8">
              <div className="divide-y divide-gray-300 dark:divide-gray-700">
                {news.map((newsItem, index) => (
                  <div key={index} className="">
                    {newsItem?.url
                      ?
                      <a href={newsItem.url} target='_blank' rel="noreferrer" className=' hover:text-primary-2 dark:hover:text-primary-2 flex gap-4 items-start py-4 transition px-4'>
                        <div className="w-32 flex-shrink-0 text-md">
                          {newsItem.date}
                        </div>
                        <div className="flex-grow">
                          
                          <p className="break-words whitespace-pre-line">
                            {newsItem.description}
                          </p>
                        </div>
                      </a>
                      :
                      <div className="flex gap-4 items-start py-4 transition px-4">
                        <div className="w-32 flex-shrink-0 text-md">
                          {newsItem.date}
                        </div>
                        <div className="flex-grow">
                            
                          <p className="text-gray-800 dark:text-gray-300 break-all whitespace-pre-line">
                            {newsItem.description}
                          </p>
                        </div>
                      </div>
                    }
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default News
