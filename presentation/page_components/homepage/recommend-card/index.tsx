import clsx from 'clsx'
import Image from 'next/image'
import React,{ ReactNode, useEffect, useRef, useState, forwardRef} from 'react'


type Props = {
  background: string,
  title: string,
  description: string,
  imageSrc: string,
  children: ReactNode,
  maxHeight: number,
  ref: React.MutableRefObject<HTMLDivElement | null>
}

const RecommendCard = forwardRef<HTMLDivElement, Props>(
  ({ title, description, background, imageSrc, maxHeight, children }, ref) => {
    return (
      <div className="recommend-card w-full">
        <div className="card-image bg-white relative w-full h-50">
          <Image className="rounded-t-xl md:rounded-t-2xl" src={imageSrc} alt="" layout="fill" objectFit="cover"/>
        </div>
        <div className={clsx('card-content p-2 md:p-4 xl:px-10 xl:py-6 rounded-b-xl md:rounded-b-2xl bg-gray-1', background)}>
          <h2 ref={ref} style={{ minHeight: `${maxHeight }px` }} className="md:text-lg xl:text-xl text-white font-medium line-clamp-2">{title}</h2>
          <p className="md:mt-1 xl:mt-5 text-sm md:text-base text-white truncate">{description}</p>
          {children}
        </div>
      </div>
    )
  })
RecommendCard.displayName = 'RecommendCard'
export default RecommendCard

