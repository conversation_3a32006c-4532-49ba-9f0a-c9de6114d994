import { useState, useEffect } from 'react'
import { useScreenWidth, screen } from 'lib/hook/customHook'
import Select from 'presentation/components/select'
import Heading from 'presentation/components/heading'
import SkeletonAsset from 'presentation/components/skeleton/assets'
import NoData from 'presentation/components/no-data'
import AssetReservePrice from 'presentation/components/asset/reserve-price'
import Asset from 'domain/models/asset'
import AssetUseCase from 'presentation/use_cases/asset_use_case'
import { Query } from 'domain/repositories/asset_repository/query'
import Multimedia from 'domain/models/multimedia'
import SortBy from 'domain/repositories/asset_repository/sort_by'

const assetUseCase = new AssetUseCase()

const sortList = SortBy.getResourceArray().map((sort: any) => {
  return {
    id: sort.id,
    name: sort.label,
    value: sort.name,
  }
})

const TopSaleMobile = () => {
  const initParams: any = {
    page: 1,
    limit: 20,
    sortBy: SortBy.recentlyListed().getName(),
  }

  const [filterParams, setFilterParams] = useState(initParams)
  const [assets, setAssets] = useState<Asset[]>([])
  const [isFetchingAssets, setIsFetchingAssets] = useState(false)
  const screenWidth = useScreenWidth()
  const [isMobile, setIsMobile] = useState(false)

  const fetchAsset = async () => {
    setIsFetchingAssets(true)
    const query = new Query(filterParams)
    await assetUseCase
      .search(query)
      .then((res) => {
        setAssets(res)
      })
      .catch(() => { })
    setIsFetchingAssets(false)
  }

  useEffect(() => {
    if (screenWidth && screenWidth < screen.md) {
      setIsMobile(true)
    } else {
      setIsMobile(false)
    }
  }, [screenWidth])

  useEffect(() => {
    if (isMobile) {
      fetchAsset()
    }
  }, [filterParams, isMobile])

  return (
    <div className="pt-6 mb-8">
      <div className="mt-5 flex flex-row justify-between items-center">
        <Heading>Top Seller Item</Heading>
        <Select
          options={sortList}
          onSelect={(option) => {
            setFilterParams({ ...filterParams, sortBy: SortBy.fromName(option.value) })
          }}
        />
      </div>
      <div className="mt-5 -mr-df flex overflow-x-auto pb-4">
        {isFetchingAssets ? (
          [...Array(2)].map((_, i) => <SkeletonAsset key={i} />)
        ) : (
          <>
            {assets.length > 0 ? (
              assets.map((asset: any) => (
                <AssetReservePrice key={asset.id} record={asset} />
              ))
            ) : (
              <div className="w-full pr-5">
                <div className="w-full rounded-lg border flex justify-center items-center py-24">
                  <NoData />
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}

export default TopSaleMobile
