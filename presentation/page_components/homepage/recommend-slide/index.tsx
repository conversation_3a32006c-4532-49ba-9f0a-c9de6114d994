import { DateTime } from 'luxon'
import Button from 'presentation/components/button'
import Heading from 'presentation/components/heading'
import Tooltip from 'presentation/components/tooltip'
import { useEffect, useState, useRef } from 'react'
import { Pagination } from 'swiper'
import 'swiper/css'
import 'swiper/css/pagination'
import { Swiper, SwiperSlide } from 'swiper/react'
import BoostedCollection from '../../../../domain/models/boosted_collection'
import Multimedia from '../../../../domain/models/multimedia'
import { Query } from '../../../../domain/repositories/boosted_collection_repository/query'
import BoostedCollectionUseCase from '../../../use_cases/boosted_collection_use_case'
import RecommendCard from '../recommend-card'
import SkeletonRecommend from '../recommend-skeleton'
import { getChainName } from 'lib/utils'
const useCase = new BoostedCollectionUseCase()
// Extended type for boosted collections with UI properties
interface ExtendedBoostedCollection extends BoostedCollection {
  buttonText?: string;
  isExternal?: boolean;
}
const mockImagePaths =[
  'asset/images/Recommendation_1.png',
  'asset/images/Recommendation_2.png',
  'asset/images/Recommendation_3.png',
  'asset/images/Recommendation_4.png',
]

const Recommend = () => {

  const [isFetching, setIsFetching] = useState<boolean>(true)
  const [boostedCollections, setBoostedCollections] = useState<ExtendedBoostedCollection[]>([])
  const [maxHeight, setMaxHeight] = useState<number>(0)

  const titleRefs = useRef<(HTMLHeadingElement | null)[]>([])

  const bgColor = ['bg-black', 'bg-gradient-blue2', 'bg-gray-0']

  const fetchBoostedCollections = () => {
    setIsFetching(true)
    const query = new Query({ limit: 6, page: 1 })
    useCase.search(query)
      .then((boostedCollections: ExtendedBoostedCollection[]) => {
        
        boostedCollections.forEach((boostedCollection, index) => {
          if (boostedCollection.collection.logoImage !== null && boostedCollection.collection.featuredImage !== null && boostedCollection.collection.bannerImage !== null) {
            return
          }
          const multimedia = _createMockMultimedia(index)
          boostedCollection.collection.logoImage = multimedia
          boostedCollection.collection.featuredImage = multimedia
          boostedCollection.collection.bannerImage = multimedia
        })
        // add button text to array
        const boostedCollectionsShow: ExtendedBoostedCollection[] = boostedCollections.map((boostedCollection) => {
          return {
            ...boostedCollection,
            buttonText: 'Live',
            isExternal: false,
          }
        })
        // add recommendation-ad card to third item in array
        const adCard: any = {
          collection: {
            name: 'Promote your project here',
            description: 'Looking to advertise on Quill? Click here',
            slug: '',
            symbol: 'RDA',
            webUrl: 'https://forms.gle/C61MPGHqnwzDkdLo8',
            chainId: 1,
            logoImage: null,
            featuredImage: null,
            recommendationImage: {
              url: 'asset/icons/recommendation-ad.png',
            },
          },
          buttonText: 'Application',
          isExternal: true,
        }
        boostedCollectionsShow.splice(2, 0, adCard)
        setBoostedCollections(boostedCollectionsShow)
        setIsFetching(false)
      }).catch((error) => {
        setBoostedCollections([])
        setIsFetching(false)
      })
  }

  const _createMockMultimedia = (index: number): Multimedia => {
    const indexOfArray = index % 4
    const uniqueKey = `xxxxxxxxxxxxx-${index}${index}${index}${index}`
    return new Multimedia(
      `id:${uniqueKey}`,
      mockImagePaths[indexOfArray],
      'local',
      '',
      12345678,
      300,
      420,
      `hash:${uniqueKey}`,
      null,
      DateTime.now(),
      DateTime.now()
    )
  }

  useEffect(() => {
    fetchBoostedCollections()
  }, [])

  useEffect(() => {
    const heights = titleRefs.current.map((ref) => ref ? ref.offsetHeight : 0)
    console.log('🚀 ~ useEffect ~ heights:', heights)
    setMaxHeight(Math.max(...heights))
  }, [boostedCollections])

  return (
    <section className="recommend-section pb-10 px-3 block max-xl:mt-[10px] max-xml:mt-[4rem] relative overflow-hidden">
      <div className="absolute left-[-25%] top-0 w-1/2 h-full bg-[url('/asset/icons/recommend-bg.png')] bg-cover"></div>
      <div className="absolute right-[-25%] top-0 w-1/2 h-full bg-[url('/asset/icons/recommend-bg.png')] bg-cover"></div>
      <div className="flex justify-center">
        <Heading className="mr-1">Recommendation</Heading>
        <Tooltip contentWidth="xl:w-[295px]">If you want to apply, please apply from your own collection page</Tooltip>
      </div>
      <div className="mt-4 md:mt-9">
        {isFetching ? (
          <div className="grid grid-cols-3 gap-6 lg:gap-10">
            {[...Array(3)].map((_, i) => (
              <SkeletonRecommend key={i}/>
            ))}
          </div>
        ) : (
          <div className="md:w-9/12 lg:w-10/12 xl:w-11/12 mx-auto">
            <Swiper
              slidesPerView={1}
              spaceBetween={25}
              breakpoints={{
                768: {
                  slidesPerView: 3,
                  spaceBetween: 25,
                },
                1024: {
                  slidesPerView: 3,
                  spaceBetween: 40,
                },
                1536: {
                  slidesPerView: 3,
                  spaceBetween: 40,
                },
                1920:{
                  slidesPerView: 4,
                  spaceBetween: 40,
                },
              }}
              pagination={{
                clickable: true,
              }}
              modules={[Pagination]}
            >
              {boostedCollections.length > 0 ? boostedCollections.map((boostedCollection: ExtendedBoostedCollection, index: number) => (
                <SwiperSlide key={index} className="flex flex-col h-full">
                  <RecommendCard
                    title={boostedCollection.collection.name || ''}
                    description={boostedCollection.collection.description || ''}
                    background={bgColor[index]}
                    imageSrc={boostedCollection.collection?.recommendationImage?.url || ''}
                    maxHeight={maxHeight}
                    ref={(el: HTMLDivElement) => titleRefs.current[index] = el}
                  >
                    <Button
                      href={boostedCollection?.isExternal ? boostedCollection?.collection?.webUrl || '' : `/collections/${getChainName(boostedCollection.collection.chainId)}/${boostedCollection.collection.slug}` || ''}
                      variant="dark"
                      className="md:mt-3 xl:mt-4 card-button mt-2 max-sm:p-1 max-xs2:w-full"
                      target="_blank"
                    >
                      {boostedCollection.buttonText || 'Live'}
                    </Button>
                  </RecommendCard>
                </SwiperSlide>
              )) : null}
            </Swiper>
          </div>
        )}
      </div>
    </section>
  )
}

export default Recommend

