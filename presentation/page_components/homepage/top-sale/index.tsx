import Asset from 'domain/models/asset'
import Collection from 'domain/models/collection'
import SortBy from 'domain/repositories/activity_repository/sort_by'
import { Query as AssetQuery } from 'domain/repositories/asset_repository/query'
import { Query as CollectionQuery } from 'domain/repositories/collection_repository/query'
import { Query as AssetRankingQuery } from 'domain/repositories/asset_ranking/query'
import { screen, useScreenWidth } from 'lib/hook/customHook'
import { DateTime } from 'luxon'
import Link from 'next/link'
import AssetComponent from 'presentation/components/asset'
import Button from 'presentation/components/button'
import ButtonTag from 'presentation/components/button/button-tag'
import CollectionComponent from 'presentation/components/collection'
import Heading from 'presentation/components/heading'
import NoData from 'presentation/components/no-data'
import Select from 'presentation/components/select'
import SkeletonAsset from 'presentation/components/skeleton/assets'
import AssetUseCase from 'presentation/use_cases/asset_use_case'
import CollectionUseCase from 'presentation/use_cases/collection_use_case'
import AssetRankingUseCase from 'presentation/use_cases/asset_ranking_use_case'
import { useEffect, useState } from 'react'
import PeriodRangesSelect from 'presentation/components/select/period-ranges-select'
import PeriodRange from 'domain/repositories/asset_daily_stat_repository/period_range'
import { getChainName } from 'lib/utils'
import { NetworkStateProvider } from 'presentation/providers/network_state_provider'
import CollectionRankingUseCase from 'presentation/use_cases/collection_ranking_use_case'
import { Query as CollectionRankingQuery } from 'domain/repositories/collection_ranking/query'
const assetUseCase = new AssetUseCase()
const collectionUseCase = new CollectionUseCase()
const collectionRankingUseCase = new CollectionRankingUseCase()
const assetRankingUseCase = new AssetRankingUseCase()
const sortList = SortBy.getResourceArray().map((sort: any) => {
  return {
    id: sort.id,
    name: sort.label,
    value: sort.name,
  }
})

const TopSale = () => {
  const initParams: any = {
    page: 1,
    limit: 20,
    sortBy: SortBy.recentlyListed(),
    periodRange: 'allTime',
  }

  const periodRangesConstant = PeriodRange.getResourceArray().filter((item) => { 
    return item.name !== 'lastSixtyDays' && item.name!== 'lastFourteenDays'
  })
  
  const last24Hours =  {
    id: 10,
    name: 'last24Hours',
    label: 'Last 24 hours',
  }
  // add last24Hours first item
  let periodRanges = [last24Hours, ...periodRangesConstant]
  periodRanges = periodRanges
    .map((range) => {
      return {
        id: range.id,
        name: range.name,
        value: false,
        label: range.label,
      }
    })
  // Add NetworkStateProvider
  const networkStateProvider = NetworkStateProvider((state) => ({
    network: state.network,
  }))

  const [productType, setProductType] = useState('assets')
  const [filterParams, setFilterParams] = useState(initParams)
  const [isFetchingAssets, setIsFetchingAssets] = useState<boolean>(false)
  const [isFetchingCollections, setIsFetchingCollections] =
    useState<boolean>(false)
  const [assets, setAssets] = useState<Asset[]>([])
  const [collections, setCollections] = useState<Collection[]>([])
  const [isPC, setIsPC] = useState<boolean>(false)
  const screenWidth = useScreenWidth()

  const fetchAsset = async () => {
    setIsFetchingAssets(true)
    // Get chainId from network provider
    const chainId = networkStateProvider.network?.chainId || ''
    
    const query = new AssetRankingQuery({
      ...filterParams,
      chainId: chainId,
    })

    try {
      const assets = await assetRankingUseCase.search(query)
      console.log('🚀 ~ fetchAsset ~ assets:', assets)
      setAssets(assets)
    } catch (error) {
      console.error('Error fetching assets:', error)
      setAssets([])
    }
    setIsFetchingAssets(false)
  }

  const fetchCollections = async () => {
    setIsFetchingCollections(true)
    // Get chainId from network provider
    const chainId = networkStateProvider.network?.chainId || ''
    
    let QueryAssetsParams = { ...filterParams }
    QueryAssetsParams.sortBy = SortBy.totalVolume()
    QueryAssetsParams.chainId = chainId // Add chainId to query params

    const query = new CollectionRankingQuery(QueryAssetsParams)
    collectionRankingUseCase.search(query).then((res: any) => {
      console.log('🚀 ~ collectionRankingUseCase.search ~ res:', res)
      let collections = []
      if (res.length > 0) { 
        collections = res.map((item: any) => {
          return {
            ...item,
          }
        })
      }
      setCollections(collections)
    }).catch((error) => {
      console.log('🚀 ~ collectionRankingUseCase.search ~ error:', error)
      setCollections([])
    })
    
    setIsFetchingCollections(false)
  }
  const getDefaultRanges = () => {
    let rs = periodRanges.filter((range) => range.name === filterParams.periodRange)[0] || null
    return rs
  }
  useEffect(() => {
    if (screenWidth && screenWidth > screen.md) {
      setIsPC(true)
    } else {
      setIsPC(true)
    }
  }, [screenWidth])

  useEffect(() => {
    console.log('🚀 ~ TopSale ~ filterParams:', filterParams)
    if (isPC) {
      fetchCollections()
      fetchAsset()
    }
  }, [filterParams, isPC, networkStateProvider.network]) // Add network dependency


  return (
    <div className="topsale-section pt-12 pb-12">
      <Heading>Top Sale</Heading>
      <div className="md:flex mt-8">
        <div className="product-side mt-4 md:mt-0">
          <div className="sort-bar flex flex-wrap justify-between">
            <div className="flex justify-start md2:w-3/6 w-3/6">
              <ButtonTag
                active={productType === 'collections'}
                onClick={() => setProductType('collections')}
                className="mr-5"
              >
                Collection
              </ButtonTag>
              <ButtonTag
                active={productType === 'assets'}
                onClick={() => setProductType('assets')}
              >
                Assets
              </ButtonTag>
            </div>
            <div className="flex justify-end md2:w-3/6 w-3/6 md2:mt-0">
              {/* <Select
                options={sortList}
                onSelect={(option) => {
                  setFilterParams({
                    ...filterParams,
                    sortBy: SortBy.fromName(option.value),
                  })
                }}
              /> */}
              <PeriodRangesSelect
                periodRanges={periodRanges}
                defaultSelected={getDefaultRanges()}
                onSelect={(option) => {
                  console.log('🚀 ~ ActivityTag ~ option:', option)
                  console.log('🚀 ~ ActivityTag ~ filterParams:', filterParams)
                  setFilterParams({
                    ...filterParams,
                    periodRange: option.value,
                  })
                
                }}
              />
            </div>
          </div>
          {productType === 'assets' ? (
            <div
              className={`product-wrap ${
                !isFetchingAssets && assets.length === 0 && 'no-data'
              }`}
            >
              {isFetchingAssets ? (
                [...Array(5)].map((_, i) => <SkeletonAsset key={i} />)
              ) : (
                <>
                  {assets.length > 0 ? (
                    assets.map((asset: any, index: number) => (
                      <AssetComponent
                        key={`${asset.id}-${index}`}
                        record={asset}
                      />
                    ))
                  ) : (
                    <div className="w-full rounded-lg border flex justify-center items-center py-24">
                      <NoData />
                    </div>
                  )}
                </>
              )}
            </div>
          ) : (
            <div
              className={`product-wrap collection-main ${
                !isFetchingCollections && collections.length === 0 && 'no-data'
              }`}
            >
              {isFetchingCollections ? (
                <div className="w-full grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3">
                  {[...Array(5)].map((_, i) => (
                    <SkeletonAsset key={i} />
                  ))}
                </div>
              ) : (
                <>
                  {collections.length > 0 ? (
                    collections.map((collection: any, index) => (
                      <div key={index} className="collection">
                        <Link href={`/collections/${getChainName(collection.chainId)}/${collection.slug}`}>
                          <CollectionComponent
                            {...collection}
                            record={collection}
                            coinSymbolSize={15}
                          />
                        </Link>
                      </div>
                    ))
                  ) : (
                    <div className="w-full rounded-lg border flex justify-center items-center py-24">
                      <NoData />
                    </div>
                  )}
                </>
              )}
            </div>
          )}
        </div>
      </div>
      <div className="block text-center mt-6">
        <Button href={`/${productType}`} variant="light">
          See More
        </Button>
      </div>
    </div>
  )
}

export default TopSale
