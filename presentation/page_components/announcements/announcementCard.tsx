import Image from 'next/image'
import But<PERSON> from 'presentation/components/button'
import Link from 'next/link'
import Announcement from 'domain/models/announcement'

type Props = {
  record: any,
  className?: string,
}

const AnnouncementsCard = (props: Props) => {
  const {
    record,
    className = '',
  } = props

  const announcement:Announcement = {...record}

  return (
    <>
      <div className="max-w-sm bg-white border border-gray-200 rounded-3xl shadow dark:bg-gray-800 dark:border-gray-700">
        <Image className="rounded-t-lg" src={'/asset/images/<EMAIL>'}
          height={400}
          width={500}
          objectFit="cover"
          alt="annoucement Image"
        />
        <div className="p-5">
          <h5 className="mb-2 text-2xl font-bold tracking-tight text-gray-900 dark:text-white">{announcement.title}</h5>
          {announcement.body && (
            <p className="mb-3 font-normal text-gray-700 dark:text-gray-400 text-ellipsis">{announcement.body.substring(0,60)}...</p>
          )}
          <Button href={`/announcements/${announcement.id}`} variant="light" className="inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg">
                Read more
            <svg aria-hidden="true" className="w-4 h-4 ml-2 -mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd"></path></svg>
          </Button>
        </div>
      </div>

    </>
  )
}

export default AnnouncementsCard
