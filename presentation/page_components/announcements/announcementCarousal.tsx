/* eslint-disable @next/next/no-img-element */
import Image from 'next/image'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Autoplay, Pagination } from 'swiper'
import 'swiper/css'
import 'swiper/css/pagination'
import Button from 'presentation/components/button'

{/* <Image src={'/asset/images/<EMAIL>'} className="absolute block w-full -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2" alt="..." layout='fill'></Image> */}
const AnnouncementsCarousal = () => {

  return (
    <>
      <Swiper
        spaceBetween={30}
        pagination={{
          clickable: true,
        }}
        modules={[Autoplay, Pagination]}
        className="mySwiper"
      >
        {/* //Todo: make mock data and replace fixed data using controllers */}
        <SwiperSlide >
          <div className='grid md:grid-cols-2 grid-rows-1 place-content-center'>
            <img
              src={'/asset/images/<EMAIL>'}
              alt={'slider'}
              className="lg:h-[350px] 2xl:h-[500px] md:h-[100]"
            />
            <div className="p-5 md:text-start 2xl:mt-[10rem]">
              <span className="mb-2 xl:text-4xl lg:text-3xl md:text-xl sm:text-lg font-bold tracking-tight text-gray-900 dark:text-white">Noteworthy technology acquisitions 2021</span>
              <p className="mb-3 font-normal text-gray-700 dark:text-gray-400 lg:text-lg md:text-md text-sm">I want to share my perspective on OpenSea’s efforts to protect against plagiarism, IP infringement, and fraud. We at OpenSea feel a huge responsibility to ensu...</p>
              <Button href="/announcements/dfg" variant="light" className="inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg">
                      Read more
                <svg aria-hidden="true" className="w-4 h-4 ml-2 -mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd"></path></svg>
              </Button>
            </div>
          </div>
        </SwiperSlide>
        <SwiperSlide >
          <div className='grid md:grid-cols-2  grid-rows-1'>
            <img
              src={'/asset/images/<EMAIL>'}
              alt={'slider'}
              className="lg:h-[350px] 2xl:h-[600px] md:h-[100] place-content-right"
            />
            <div className="p-5 md:text-start 2xl:mt-[10rem]">
              <span className="mb-2 xl:text-4xl lg:text-3xl md:text-xl sm:text-lg font-bold tracking-tight text-gray-900 dark:text-white">Noteworthy technology acquisitions 2021</span>
              <p className="mb-3 font-normal text-gray-700 dark:text-gray-400 lg:text-lg md:text-md text-sm">I want to share my perspective on OpenSea’s efforts to protect against plagiarism, IP infringement, and fraud. We at OpenSea feel a huge responsibility to ensu...</p>
              <Button href="/announcements/dfg" variant="light" className="inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg">
                      Read more
                <svg aria-hidden="true" className="w-4 h-4 ml-2 -mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd"></path></svg>
              </Button>
            </div>
          </div>
        </SwiperSlide>

      </Swiper>
    </>
  )
}

export default AnnouncementsCarousal
