import { useEffect, useState } from 'react'
import { durationDateOptions } from 'presentation/controllers/mockData/options'
import Tooltip from 'presentation/components/tooltip'
import Select from 'presentation/components/select'
import Input from 'presentation/components/input'
import Switch from 'presentation/components/switch'
import SelectTab from 'presentation/components/select/select-tab'
import DatetimeRange from 'presentation/components/datetimeRange'

const SellTimedAuctionTab = (props:any) => {
  const {
    paymentTokens = [],
    auctionMethods = [],
    startingPrice = 0,
    hasReservePrice = true,
    reservePrice = 0,
    startAt,
    endAt,
    onChangePaymentToken = () => {},
    onChangeAuctionMethod = () => {},
    onChangeStartingPrice = () => {},
    onChangeDuration = () => {},
    onChangeReservePrice = () => {},
    onChangeHasReservePrice = () => {},
    onChangeStartAt = () => {},
    onChangeEndAt = () => {},
  } = props

  const [isPaymentTokensReady, setIsPaymentTokensReady] = useState(false)
  const [duration, setDuration] = useState(durationDateOptions[0])

  useEffect(() => {
    if(paymentTokens.length > 0) {
      setIsPaymentTokensReady(true)
    }
  }, [paymentTokens])

  return (
    <div>
      <div className="mb-10p flex items-center">
        <p className="font-medium text-dark-primary dark:text-white">Method</p>
        <Tooltip
          contentWidth="lg:w-[295px]"
          iconSize="w-4 h-4 -mt-0.5"
        >Method</Tooltip>
      </div>
      <SelectTab
        className="w-7/12 dark:bg-blue-8"
        options={auctionMethods}
        defaultOption={auctionMethods[0]}
        onChange={(option:any) => onChangeAuctionMethod(option)}
      />

      {/* Starting price */}
      <p className="mt-7 font-medium">Starting price</p>
      <div className="flex sm:flex-nowrap flex-wrap mt-10p">
        {isPaymentTokensReady && (
          <Select
            options={paymentTokens}
            showOptionIcon rounded
            onSelect={(option) => onChangePaymentToken(option)}
            className="sm:mb-0 mb-2"
          />
        )}
        <div>
          <Input
            type="number"
            name="startingPrice"
            width="w-36"
            className="no-arrow ml-5"
            value={startingPrice}
            onChange={(e) => onChangeStartingPrice(e.target.value)}
          />
          <p className="mt-1 text-sm text-gray-1 text-right">$27,408.40 Total</p>
        </div>
      </div>

      {/* Duration */}
      <div className="md2:w-3/4 w-full mt-5">
        <p className="mb-3 font-medium text-dark-primary dark:text-white">Duration</p>
        <Input
          className="w-full cursor-pointer"
          type="text"
          value={duration.name}
          prefixIcon="calendar"
          prefixIconClassName="absolute left-7 top-3 w-6 h-6 stroke-none fill-primary cursor-pointer"
          suffixIcon="chevronDownSelect"
          suffixIconViewBox="0 0 12.962 7.411"
          suffixIconClassName="absolute right-7 top-5 w-[13px] h-[8px] fill-primary-2 dark:fill-white stroke-none cursor-pointer"
        />
        <DatetimeRange
          inline
          onSelect={(option: any) => {
            onChangeDuration(option)
            setDuration(option)
          }}
          onChangeStartDatetime={(value:any) => onChangeStartAt(value)}
          onChangeEndDatetime={(value:any) => onChangeEndAt(value)}
          className="custom xs3:max-w-[485px] xs3:min-w-[485px] max-w-[245px] min-w-[245px]"
        />
      </div>

      {/* Include reserve price */}
      <div className="mt-5 flex items-center">
        <p className="font-medium mr-1">Include reserve price</p>
        <Tooltip contentWidth="lg:w-[295px]" iconSize='w-4 h-4'>
          Include reserve price
        </Tooltip>
        <Switch
          className="ml-5"
          defaultActive={hasReservePrice}
          onClick={(value) => onChangeReservePrice(value)}
        />
      </div>
      <div className="mt-3 flex sm:flex-nowrap flex-wrap">
        {isPaymentTokensReady && (
          <Select
            options={paymentTokens}
            showOptionIcon rounded
            onSelect={(option) => onChangePaymentToken(option)}
            className="sm:mb-0 mb-2"
          />
        )}
        <div>
          <Input
            type="number" name="reservePrice" width="w-36"
            className="no-arrow ml-5" value={reservePrice}
            onChange={(e) => onChangeHasReservePrice(e.target.value)}
          />
          <p className="mt-1 text-sm text-gray-1 text-right">$27,408.40 Total</p>
        </div>
      </div>
    </div>
  )
}

export default SellTimedAuctionTab
