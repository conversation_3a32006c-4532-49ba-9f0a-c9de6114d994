import { useState, useEffect } from 'react'
import Image from 'next/image'
import Tooltip from 'presentation/components/tooltip'
import Select from 'presentation/components/select'
import Input from 'presentation/components/input'
import Switch from 'presentation/components/switch'
import DatetimeRange from 'presentation/components/datetimeRange'
import SvgIcon from 'presentation/components/svg-icon'
import DatetimePicker from 'presentation/components/datetimePicker'
import { formatDate } from 'lib/utils'
import { durationDateOptions } from 'presentation/controllers/mockData/options'

const SellFixedPriceTab = (props: any) => {
  const {
    mysteryBox = {},
    revealDatetime = '',
    paymentTokens = [],
    price = 0,
    isBundle = true,
    bundleName = '',
    bundleDescription = '',
    isReservedForBuyer = true,
    reservedBuyerAddress = '',
    startAt,
    endAt,
    onUploadFile = () => { },
    onChangeRevealDatetime = () => { },
    onChangePaymentToken = () => { },
    onChangePrice = () => { },
    onChangeDuration = () => { },
    onChangeIsBundle = () => { },
    onChangeBundleName = () => { },
    onChangeBundleDescription = () => { },
    onChangeIsReservedForBuyer = () => { },
    onChangeReservedBuyerAddress = () => { },
    onChangeStartAt = () => { },
    onChangeEndAt = () => { },
  } = props

  const [isPaymentTokensReady, setIsPaymentTokensReady] = useState(false)
  const [duration, setDuration] = useState(durationDateOptions[0])

  const [selectedHour, setSelectedHour] = useState<number | string>('00')
  const [selectedMinute, setSelectedMinute] = useState<number | string>('00')
  const [selectedDate, setSelectedDate] = useState<string>(formatDate(new Date(), true))

  useEffect(() => {
    onChangeRevealDatetime(`${selectedDate}   ${selectedHour} : ${selectedMinute}   UTC`)
  }, [selectedHour, selectedMinute, selectedDate])

  useEffect(() => {
    if (paymentTokens.length > 0) {
      setIsPaymentTokensReady(true)
    }
  }, [paymentTokens])

  return (
    <div>
      {/* Mystery Box */}
      <div className="w-1/2">
        <div className="flex items-center">
          <p className="font-medium  text-dark-primary dark:text-white">Mystery Box</p>
          <Tooltip className="-mt-0.5">Mystery Box</Tooltip>
        </div>
        <div className="mt-10p flex items-center">
          <p className="text-sm text-gray-1 dark:text-gray-3 text-stroke-thin">
            Image, Video, Audio, or 3D Model *
          </p>
          <Tooltip className="-mt-0.5">Image, Video, Audio, or 3D Model *</Tooltip>
        </div>
        <div className="relative w-fit mt-10p">
          <div className="absolute top-2 right-1">
            <input
              className="w-5 h-5 opacity-0 absolute"
              type="file" id="img" name="mysteryBox" accept="image/*"
              onChange={(e: any) => onUploadFile(e.target.files[0])}
            />
            <SvgIcon
              name="plusCircle"
              className="w-5 h-5 stroke-none fill-primary-2 cursor-pointer"
            />
          </div>
          <div className="upload-image-supersquare bg-gray-5 dark:bg-blue-1">
            {mysteryBox.url && (
              <Image
                alt="" fill
                style={{ objectFit: 'cover' }}
                className="rounded-[10px]"
                src={mysteryBox.url}
              />
            )}
          </div>
        </div>
      </div>

      {/* Reveal Time */}
      <div className="w-full mt-5">
        <p className="text-sm text-gray-1 dark:text-gray-3 text-stroke-thin mb-1">
          Reveal Time
        </p>
        <Input
          wrapClass="md2:w-3/4 w-full"
          className="cursor-pointer font-medium"
          type="text"
          value={revealDatetime}
          prefixIcon="calendar"
          prefixIconClassName="absolute left-7 top-3 w-6 h-6 stroke-none fill-primary cursor-pointer"
          suffixIcon="chevronDownSelect"
          suffixIconViewBox="0 0 12.962 7.411"
          suffixIconClassName="absolute right-7 top-5 w-[13px] h-[8px] fill-primary-2 dark:fill-white stroke-none cursor-pointer"
          readOnly={true}
        />
        <DatetimePicker
          inline showTimeInput
          onChangeDate={(date: Date) => {
            setSelectedDate(formatDate(date, true))
          }}
          onSelectTime={(option: any) => {
            setSelectedHour(option.hour)
            setSelectedMinute(option.minute)
          }}
          className="custom xs3:max-w-[485px] xs3:min-w-[485px] max-w-[245px] min-w-[245px]"
        />
      </div>

      {/* Price */}
      <div className="flex items-center mt-5">
        <p className="font-medium  text-dark-primary dark:text-white">Price</p>
        <Tooltip contentWidth="lg:w-[295px]" iconSize="w-4 h-4 -mt-0.5">
          Price
        </Tooltip>
      </div>
      <div className="flex sm:flex-nowrap flex-wrap mt-10p">
        {isPaymentTokensReady && (
          <Select
            options={paymentTokens}
            showOptionIcon rounded
            onSelect={(option) => onChangePaymentToken(option)}
            className="sm:mb-0 mb-2"
          />
        )}
        <div>
          <Input
            type="number" name="price" width="w-36" className="no-arrow ml-5"
            value={price} onChange={(e) => onChangePrice(e.target.value)}
          />
          <p className="mt-1 text-sm text-gray-1 text-right">$27,408.40</p>
        </div>
      </div>

      {/* Duration */}
      <div className="md2:w-3/4 w-full mt-5">
        <p className="mb-3 font-medium  text-dark-primary dark:text-white">Duration</p>
        <Input
          className="w-full cursor-pointer"
          type="text"
          value={duration.name}
          prefixIcon="calendar"
          prefixIconClassName="absolute left-7 top-3 w-6 h-6 stroke-none fill-primary-2 cursor-pointer"
          suffixIcon="chevronDownSelect"
          suffixIconViewBox="0 0 12.962 7.411"
          suffixIconClassName="absolute right-7 top-5 w-[13px] h-[8px] fill-primary-2 stroke-none cursor-pointer"
          readOnly={true}
        />
        <DatetimeRange
          inline
          onSelect={(option: any) => {
            onChangeDuration(option)
            setDuration(option)
          }}
          onChangeStartDatetime={(value: any) => onChangeStartAt(value)}
          onChangeEndDatetime={(value: any) => onChangeEndAt(value)}
          className="custom xs3:max-w-[485px] xs3:min-w-[485px] max-w-[245px] min-w-[245px]"
        />
      </div>

      {/* Bundle */}
      <div className="mt-5">
        <div className="flex items-center">
          <p className="font-medium mr-5  text-dark-primary dark:text-white">Sell as a bundle</p>
          <Switch defaultActive={isBundle} onClick={(value) => onChangeIsBundle(value)} />
        </div>
        <Input type="text" name="bundle-name" placeholder="Bundle name" className="mt-3"
          value={bundleName}
          onChange={(e) => onChangeBundleName(e.target.value)}
        />
        <textarea
          name="bundle-description"
          className="px-5 py-3 mt-3 rounded-3xl w-full outline-none bg-gray-4 dark:bg-blue-1"
          rows={3}
          placeholder="Bundle description"
          value={bundleDescription}
          onChange={(value) => onChangeBundleDescription(value)}
        />
      </div>

      {/* Reserve for specific buyer */}
      <div className="mt-5">
        <div className="flex items-center">
          <p className="font-medium mr-5  text-dark-primary dark:text-white">Reserve for specific buyer</p>
          <Switch
            defaultActive={isReservedForBuyer}
            onClick={(value) => onChangeIsReservedForBuyer(value)}
          />
        </div>
        <p className="text-sm text-gray-1">This item can be purchased as soon as it’s listed.</p>
        <textarea
          name="reservedBuyerAddress"
          className="px-5 py-3 mt-10p rounded-3xl w-full outline-none bg-gray-4 dark:bg-blue-1"
          rows={1}
          placeholder="0xc4c16a645…b21a"
          value={reservedBuyerAddress}
          onChange={(value) => onChangeReservedBuyerAddress(value)}
        />
      </div>
    </div>
  )
}

export default SellFixedPriceTab
