import clsx from 'clsx'
import { useState, useEffect } from 'react'
import BadgeText from 'presentation/components/badge/badgeText'
import Input from 'presentation/components/input'
import SvgIcon from 'presentation/components/svg-icon'

type LayerOption = {
  name: string,
  childrenOptions?: Array<ChildrenLayerOption> | undefined
}

type ChildrenLayerOption = {
  name?: string,
  isBlocked?: boolean,
}

const SelectLayer = (props:any) => {
  const {
    data = [],
    onClickBadge = () => {},
    onClickLayerOption = () => {},
  } = props

  const [layer, setLayer] = useState('')
  const [activeIndex, setActiveIndex] = useState(0)
  const [isShowLayerOptions, setIsShowLayerOptions] = useState(false)
  const [isRenderChildOption, setIsRenderChildOption] = useState(false)
  const [childrenActiveIndex, setChildrenActiveIndex] = useState<any>('')

  const [layerList, setLayerList] = useState<any>(data)

  const removeLayer = (id: number) => {
    setLayerList((prev:any) => (
      prev.filter((layer:any) => (
        layer.id !== id
      ))
    ))
  }

  const addLayer = () => {
    setLayerList([...layerList, {
      id: layerList.length + 1,
      name: layer,
      value: 5,
      percentage: 100,
      options: [
        {
          name: 'Body',
          childrenOptions: [
            { name: 'Eye > eye', isBlocked: true },
            { name: 'Eye > eye', isBlocked: false},
            { name: 'Eye > eye', isBlocked: false},
          ],
        },
      ],
    }])
  }

  const toggleLayerOption = (index: number) => {
    if (activeIndex === index) {
      setIsShowLayerOptions(!isShowLayerOptions)
    } else {
      setActiveIndex(index)
      setIsShowLayerOptions(true)
    }
  }

  const renderChildrenSelect = (option: LayerOption) => {
    if (option.childrenOptions?.length) {
      return (
        <ul className="min-w-[50%] py-3 absolute z-1 top-8 right-0 rounded-10 border border-gray-3 bg-white dark:bg-dark-primary">
          { option.childrenOptions.map((childrenOption, index) => (
            <li
              key={index}
              className="py-[5px] hover:bg-green-2 dark:hover:bg-blue-1 text-center text-sm"
            >
              <span className={ childrenOption.isBlocked ? 'line-through' : ''}>{childrenOption.name}</span>
            </li>
          ))}
        </ul>
      )
    }
  }

  useEffect(() => {
    if(!isRenderChildOption) {
      setChildrenActiveIndex('')
    }
  }, [isRenderChildOption])

  return (
    <>
      <div className="relative">
        <Input
          value={layer}
          placeholder="New Layer"
          onChange={(e) => setLayer(e.target.value)}
        />
        <SvgIcon
          name="plus"
          className="w-6 h-6 stroke-none fill-primary-2 bg-inherit absolute right-5 top-1/2 -translate-y-1/2 cursor-pointer"
          onClick={addLayer}
        />
      </div>
      {layerList.map((layer:any, index:number) => (
        <div
          key={index}
          className="mt-4 rounded-[7px] border border-gray-3 hover:border-primary-1 transition-all text-dark-primary dark:text-white"
        >
          <div className="py-2 px-5 relative flex items-center justify-between">
            <SvgIcon
              name="xCircle"
              className="w-5 h-5 stroke-none fill-primary-2 absolute top-1 right-1 cursor-pointer"
              onClick={() => removeLayer(layer.id)}
            />
            <div className="flex items-center">
              <SvgIcon
                name="chevronDown"
                className={clsx('w-6 h-6 stroke-none fill-primary-2 cursor-pointer transition',
                  (isShowLayerOptions && activeIndex === index) ? 'rotate-180' : '')}
                onClick={() => toggleLayerOption(index)}
              />
              <div className="ml-3">
                <p className="font-bold select-none">{layer.name}</p>
                <BadgeText
                  size="xs"
                  className="cursor-pointer select-none"
                  onClick={() => onClickBadge(layer)}
                >
                  Rarity
                </BadgeText>
              </div>
            </div>
            <div className="flex gap-3">
              <BadgeText
                rounded="rounded-25"
                color="gray-3"
                size="base"
                className="shrink-0 h-fit min-w-[2.3rem]"
              >
                {layer.value}
              </BadgeText>
              <BadgeText
                rounded="rounded-25"
                color="gray-3"
                size="base"
                className="shrink-0 h-fit min-w-[3.3rem]"
              >
                {layer.percentage}
              </BadgeText>
            </div>
          </div>
          <ul className={clsx('pb-[5px]', (isShowLayerOptions && activeIndex === index) ? 'block' : 'hidden')}>
            {layer.options.map((option:any, index:number) => (
              <li
                key={index}
                className={clsx(
                  'py-[7px] pl-7 flex items-center justify-between hover:bg-green-2 dark:hover:bg-blue-1 transition-all cursor-pointer',
                  childrenActiveIndex === index ? 'relative bg-green-2 bg-blue-1' : ''
                )}
                onClick={() => {
                  setIsRenderChildOption(option.childrenOptions ? !isRenderChildOption : false)
                  setChildrenActiveIndex(index)
                  onClickLayerOption(layer)
                }}
              >
                <p>{option.name}</p>
                {childrenActiveIndex === index && option.childrenOptions ? (
                  <div className="inline-flex items-center space-x-2 pr-5 pl-2">
                    <SvgIcon
                      name="chevronDownSelect"
                      className="w-3 h-3 fill-primary-2 stroke-none cursor-pointer"
                      viewBox="0 0 12.962 7.411"
                    />
                    <span className="text-sm">Block List (1)</span>
                  </div>
                ) : ''}
                {childrenActiveIndex === index && option.childrenOptions && isRenderChildOption && renderChildrenSelect(option)}
              </li>
            ))}
          </ul>
        </div>
      ))}
    </>
  )
}

export default SelectLayer
