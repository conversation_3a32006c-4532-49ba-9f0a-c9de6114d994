import Image from 'next/image'
import Modal from 'presentation/components/modal'
import BadgeText from 'presentation/components/badge/badgeText'

const AttributesModal = (props: any) => {
  const {
    isShow = false,
    onClose = () => { },
    record = {},
  } = props

  return (
    <Modal
      title="Attributes"
      isShow={isShow}
      contentWrapClass="rounded-b-10 dark:bg-black"
      onClose={onClose}
      modalWidthClass="min-w-3/4 md:min-w-2/4 xl:min-w-1/4 lg:w-fit w-11/12"
    >
      <div className="sm:px-[13px] pt-2 pb-10">
        {record.thumbnail?.url && (
          <div className="flex justify-center">
            <div className="relative w-[150px] h-[150px] rounded-20 bg-white">
              <Image
                alt="" fill
                style={{ objectFit: 'contain' }}
                src={record.thumbnail?.url}
              />
            </div>
          </div>
        )}
        <div className="mt-5 md:px-[103px] grid sm:grid-cols-3 grid-cols-2 gap-4 lg:gap-5">
          {record.attributes?.map((attribute: any, i: number) => (
            <div key={i} className="pt-6p pb-3 px-2 md:w-[156px] flex flex-col items-center rounded-10 border border-gray-3">
              <p className="font-bold">{attribute.type}</p>
              <p className="mt-1 font-medium text-sm">{attribute.name}</p>
              <p className="mt-10p">
                <BadgeText size="base" color="black" rounded="rounded-25">{attribute.percentage}%</BadgeText>
                <span className="text-xs text-gray-1 ml-1">of {attribute.quantity}</span>
              </p>
            </div>
          ))}
        </div>
      </div>
    </Modal>
  )
}

export default AttributesModal
