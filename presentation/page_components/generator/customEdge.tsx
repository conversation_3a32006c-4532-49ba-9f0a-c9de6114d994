import React, { FC } from 'react'
import { EdgeProps, getBezierPath, EdgeLabelRenderer } from 'reactflow'

const CustomEdge: FC<EdgeProps> = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  data,
}) => {
  const [edgePath, labelX, labelY] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  })

  return (
    <>
      <path id={id} className="react-flow__edge-path" d={edgePath} style={{ stroke: 'green' }} />
      <EdgeLabelRenderer>
        <div
          style={{
            position: 'absolute',
            transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
            background: '#fff',
            padding: '1px 8px 0px',
            border: '1px solid #ddd',
            borderRadius: 99,
            fontSize: 14,
            fontWeight: 500,
            top: 20,
          }}
          className="nodrag nopan"
        >
          {data.text}
        </div>
      </EdgeLabelRenderer>
    </>
  )
}

export default CustomEdge
