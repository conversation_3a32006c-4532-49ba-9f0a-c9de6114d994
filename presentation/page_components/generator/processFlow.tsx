import { useCallback } from 'react'
import React<PERSON>low, {
  Controls,
  ControlButton,
  useNodesState,
  useEdgesState,
  addEdge,
} from 'reactflow'
import 'reactflow/dist/style.css'
import CustomEdge from './customEdge'
import SvgIcon from 'presentation/components/svg-icon'

const initialNodes: any = [
  {
    id: '1',
    position: { x: 50, y: 50 },
    sourcePosition: 'right',
    targetPosition: 'right',
    data: { label: 'Start' },
  },
  {
    id: '2',
    position: { x: 300, y: 50 },
    sourcePosition: 'right',
    targetPosition: 'left',
    data: { label: 'Background' },
  },
  {
    id: '3',
    position: { x: 550, y: 50 },
    sourcePosition: 'right',
    targetPosition: 'left',
    data: { label: 'Body' },
  },
  {
    id: '4',
    position: { x: 800, y: 50 },
    sourcePosition: 'left',
    targetPosition: 'left',
    data: { label: 'Eye' },
  },
]

const initialEdges = [
  {
    id: 'e1-2',
    source: '1',
    target: '2',
    animated: true,
    type: 'custom',
    data: { text: '100%' },
  },
  {
    id: 'e2-3',
    source: '2',
    target: '3',
    animated: true,
    type: 'custom',
    data: { text: '100%' },
  },
  {
    id: 'e3-4',
    source: '3',
    target: '4',
    animated: true,
    type: 'custom',
    data: { text: '100%' },
  },
]

const edgeTypes = {
  custom: CustomEdge,
}

const ProcessFlow = () => {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes)
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges)

  const onConnect = useCallback((params: any) => setEdges((eds) => addEdge(params, eds)), [setEdges])

  return (
    <ReactFlow
      className="process-flow-wrapper"
      nodes={nodes}
      edges={edges}
      edgeTypes={edgeTypes}
      onNodesChange={onNodesChange}
      onEdgesChange={onEdgesChange}
      onConnect={onConnect}
      fitView
    >
      <Controls position='top-left'>
        <ControlButton onClick={() => {}}>
          <SvgIcon name="reload2" viewBox="0 0 21.999 20" className="fill-primary-2 stroke-none cursor-pointer" />
        </ControlButton>
      </Controls>
    </ReactFlow>
  )
}

export default ProcessFlow
