import { useState, useEffect } from 'react'
import Image from 'next/image'
import Input from 'presentation/components/input'
import Modal from 'presentation/components/modal'

const RaritySettingModal = (props: any) => {
  const {
    isShow = false,
    onClose = () => { },
    record = {},
  } = props

  const [layer, setLayer] = useState<any>(null)

  const updateLayerPercentage = (value: any) => {
    setLayer({
      ...layer,
      percentage: value,
    })
  }

  const updateAsset = (value: any, index: number, key: string) => {
    if (layer.assets && layer.assets[index]) {
      const newAssets = [...layer.assets]
      newAssets[index][key] = value
      setLayer({
        ...layer,
        assets: newAssets,
      })
    }
  }

  useEffect(() => {
    if (record) {
      setLayer(record)
    }
  }, [record])

  return (
    <Modal
      title={`Rarity Settings - ${layer?.name}`}
      contentWrapClass="rounded-b-10 dark:bg-black"
      isShow={isShow}
      onClose={onClose}
      modalWidthClass="min-w-3/4 md:min-w-2/4 xl:min-w-1/4 lg:w-fit w-11/12"
    >
      {layer && (
        <div className="px-[13px] pt-2 pb-10">
          <div className="flex sm:flex-nowrap flex-wrap justify-center items-baseline gap-30p">
            <div className="relative w-24">
              <Input
                type="number"
                className="no-arrow w-24 text-center pr-4"
                value={layer.percentage}
                onChange={(e) => updateLayerPercentage(parseInt(e.target.value))}
              />
              <span className="absolute right-4 top-3">%</span>
            </div>
            <div className="flex flex-wrap justify-center max-w-[150px]">
              <input
                className="input-range"
                type="range" min="1" max="100" value={layer.percentage}
                onChange={(e) => updateLayerPercentage(parseInt(e.target.value))}
                style={{ backgroundSize: `${layer.percentage}% 100%` }}
              />
              <p className="text-xs text-gray-1 flex justify-between w-full">
                <span>1%</span>
                <span className="-mr-2.5">100%</span>
              </p>
            </div>
          </div>

          <p className="mt-10p font-medium">Assets</p>
          <div className="mt-10p rounded-10 border border-gray-3 w-full overflow-x-auto">
            <div className="min-w-[720px]">
              <div className="py-6p px-8 grid grid-cols-10 gap-4 bg-blue-2 dark:bg-blue-1 rounded-t-10">
                <div className="text-sm text-gray-1">Images</div>
                <div className="text-sm text-gray-1">Name</div>
                <div className="col-span-2 text-sm text-right text-gray-1 pr-4">%</div>
                <div className="col-span-3 text-sm text-gray-1"></div>
                <div className="col-span-3 text-sm text-gray-1">Quantity</div>
              </div>
              {layer.assets?.map((asset: any, i: number) => (
                <div key={i} className="py-6p px-8 grid grid-cols-10 gap-4 items-center">
                  <div>
                    {asset.thumbnail?.url && (
                      <Image alt='' width={48} height={50} src={asset.thumbnail?.url} />
                    )}
                  </div>
                  <div className="text-sm font-medium">{asset.name}</div>
                  <div className="col-span-2 flex justify-end">
                    <div className="relative w-24">
                      <Input
                        type="number"
                        className="no-arrow text-center pr-4"
                        value={asset.percentage}
                        onChange={(e) => updateAsset(parseInt(e.target.value), i, 'percentage')}
                        onBlur={() => {
                          if (asset.percentage > 100) {
                            updateAsset(100, i, 'percentage')
                          } else if (asset.percentage < 0) {
                            updateAsset(0, i, 'percentage')
                          }
                        }}
                      />
                      <span className="absolute right-4 top-3">%</span>
                    </div>
                  </div>
                  <div className="flex flex-wrap justify-center col-span-3 text-sm text-gray-1 max-w-[184px]">
                    <input
                      className="input-range"
                      type="range" step="0.01"
                      min="0.01" max="100"
                      value={asset.percentage}
                      onChange={(e) => updateAsset(parseInt(e.target.value), i, 'percentage')}
                      style={{ backgroundSize: `${asset.percentage}% 100%` }}
                    />
                    <p className="text-xs text-gray-1 flex justify-between w-full">
                      <span>Rare</span>
                      <span>Common</span>
                    </p>
                  </div>
                  <div className="col-span-3 flex items-center">
                    <Input
                      type="number"
                      className="no-arrow w-24 text-center"
                      value={asset.quantity}
                      onChange={(e) => updateAsset(parseInt(e.target.value), i, 'quantity')}
                    />
                    <p className="ml-3 text-xs text-gray-1 shrink-0">out of {layer.quantity}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </Modal>
  )
}

export default RaritySettingModal
