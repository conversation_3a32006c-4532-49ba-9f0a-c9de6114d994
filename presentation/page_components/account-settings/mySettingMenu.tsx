import { useRouter } from 'next/router'
import SvgIcon from 'presentation/components/svg-icon'

const MySettingMenu = ({ activeTab }: any) => {
  const router = useRouter()

  const changeRequestTab = (tab: string) => {
    router.push(tab)
  }

  return (
    <>
      <div
        className={`user-setting-nav xl:min-w-[335px] xl:w-[335px] lg:min-w-[230px] lg:max-w-[230px]
                  ${activeTab === 'offers' ? 'offers' : ''}`}
      >
        <p className="text-lg xl:text-26 pl-5 pt-[10px] pb-[4px] font-medium text-dark-primary dark:text-white">
          Settings
        </p>
        <ul className="">
          <li
            className={`nav-item ${
              activeTab === 'profileSettings' && 'active'
            }`}
            onClick={() => changeRequestTab('/account/profileSettings')}
          >
            <div className="flex">
              <SvgIcon name="user" className="icon-22 stroke-none" />
              <p className="ml-3 xl:text-lg font-medium text-dark-primary dark:text-white">
                Profile
              </p>
            </div>
            <SvgIcon name="chevronRight" className="icon-22 stroke-none" />
          </li>
          {/* <li
            className={`nav-item ${
              activeTab === 'notificationSettings' && 'active'
            }`}
            onClick={() => changeRequestTab('/account/notificationSettings')}
          >
            <div className="flex">
              <SvgIcon name="bell" className="icon-22 stroke-none" />
              <p className="ml-3 xl:text-lg font-medium text-dark-primary dark:text-white">
                Notifications
              </p>
            </div>
            <SvgIcon name="chevronRight" className="icon-22 stroke-none" />
          </li>
          <li
            className={`nav-item ${activeTab === 'offerSettings' && 'active'}`}
            onClick={() => changeRequestTab('/account/offerSettings')}
          >
            <div className="flex">
              <SvgIcon name="tag" className="icon-22 stroke-none" />
              <p className="ml-3 xl:text-lg font-medium text-dark-primary dark:text-white">
                Offers
              </p>
            </div>
            <SvgIcon name="chevronRight" className="icon-22 stroke-none" />
          </li> */}
          <li
            className={`nav-item ${activeTab === 'support' && 'active'}`}
            onClick={() => changeRequestTab('/account/support')}
          >
            <div className="flex">
              <SvgIcon name="alertOctagon" className="icon-22 stroke-none" />
              <p className="ml-3 xl:text-lg font-medium text-dark-primary dark:text-white">
                Account Support
              </p>
            </div>
            <SvgIcon name="chevronRight" className="icon-22 stroke-none" />
          </li>
        </ul>
      </div>
    </>
  )
}

export default MySettingMenu
