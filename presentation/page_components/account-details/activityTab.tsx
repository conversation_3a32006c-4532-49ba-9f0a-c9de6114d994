import FilterEventType from 'domain/repositories/activity_repository/filter_event_type'
import { Query as ActivityQuery } from 'domain/repositories/activity_repository/query'
import SortBy from 'domain/repositories/activity_repository/sort_by'
import Chain from 'domain/value_objects/chain'
import { pageSizes } from 'lib/hook/customHook'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import Image from 'next/image'
import Link from 'next/link'
import NoData from 'presentation/components/no-data'
import PaginationDot from 'presentation/components/pagination/pagination-dot'
import Select from 'presentation/components/select'
import LoadingData from 'presentation/components/skeleton/loading'
import SvgIcon from 'presentation/components/svg-icon'
import Switch from 'presentation/components/switch'
import Table from 'presentation/components/table'
import ToggleWrap from 'presentation/components/toggle-wrap'
import ToggleVertical from 'presentation/components/toggle-wrap/toggle-vertical'
import ToggleVerticalFilterChains from 'presentation/components/toggle-wrap/toggle-vertical-filter-chains'
import ActivityUseCase from 'presentation/use_cases/activity_use_case'
import { useEffect, useState } from 'react'
import { FilterParams } from 'types/type'
import ActivityModel from 'domain/models/activity'
import AssetThumbnail from 'presentation/components/asset/thumbnail'
import { isAddress } from 'lib/validate'
import { NetworkStateProvider } from 'presentation/providers/network_state_provider'
//Todo remove mock data
//End todo remove mock data

const activityUseCase = new ActivityUseCase()

const filterChains = Chain.getResourceArray().map((chain) => {
  return Chain.fromName<Chain>(chain.name)
})

const filterEventTypes = FilterEventType.getResourceArray().map((type: any) => {
  return {
    id: type.id,
    name: type.label,
    value: false,
    type: type.name,
  }
})

let sortList = SortBy.getResourceArray().filter((sort: any) => sort.name !== 'endingSoon' && sort.name !== 'highestLastSale').map((sort: any) => {
  return {
    id: sort.id,
    name: sort.label,
    value: sort.name,
  }
})
sortList = sortList.filter((sort: any) => {
  return sort.value === 'recentlyListed' || sort.value === 'priceLowToHigh' || sort.value === 'priceHighToLow' || sort.value === 'highestLastSale'
})
const activityTableHead = [
  {
    title: 'Event',
    key: 'event',
    render: (event: any) => (
      <div className="px-2">

        <SvgIcon
          name={event.icon}
          className="w-5 h-5 stroke-none fill-dark-primary dark:fill-white"
        />
        <span className="ml-2 align-middle">{event.label} </span>
      </div>
    ),
  },
  {
    title: 'Item',
    key: 'asset',
    render: (asset: any) => (
      <Link
        href={`/assets/${asset?.id}`}
        className="py-3 flex items-center relative w-max"
      >
        {asset && <AssetThumbnail asset={asset}
          className="h-[60px] !w-[60px] !rounded-none"
          classNameImage="!rounded-none !border-none" />}
        <div className="max-w-[7rem] py-1 ml-2">
          <p className="font-medium truncate text-dark-primary  dark:text-white">{asset?.name}</p>
          <span className="text-sm font-medium text-primary-2 break-words">
            {asset?.collection?.name}
          </span>
        </div>
      </Link>
    ),
  },
  {
    title: 'Unit Price',
    key: 'unitPrice',
    render: (unitPrice : any) => (
      <div className="px-2 uppercase  dark:text-white">
        {unitPrice?.price} {unitPrice?.paymentToken?.name}
      </div>
    ),
  },
  {
    title: 'Quantity',
    key: 'quantity',
    render: (quantity : any) => (
      <div className="px-2 uppercase  dark:text-white">
        {quantity}
      </div>
    ),
  },
  {
    title: 'From',
    key: 'fromUser',
    render: (fromUser: any) => (
      <>
        {fromUser?.username ? (
          <Link href={`/users/${fromUser?.username}`} className="text-primary-2 px-2 py-4 block truncate w-[120px]">
            {fromUser?.username}
          </Link>
        ) : (
          <div className=" px-2 py-4 block truncate w-[120px] break-all  dark:text-white">
            {fromUser?.username || fromUser?.recipient}
          </div>
        )}
       
      </>
      
    ),
  },
  {
    title: 'To',
    key: 'toUser',
    render: (toUser: any) => (
      <>
        {toUser?.username && !isAddress(toUser?.username) ? (
          <Link href={`/users/${toUser?.username}`} className="text-primary-2 px-2 py-4 block truncate w-[120px]">
            {toUser?.username}
          </Link>
        ) : (
          <div className=" px-2 py-4 block truncate w-[120px]  dark:text-white">
            {toUser?.username || toUser?.offerer}
          </div>
        )}
       
      </>
    
    ),
  },
  {
    title: 'Time',
    key: 'time',
    render: (time: any) => (
      <div  className="text-dark-primary px-2 py-4  dark:text-white">
        {time}
      </div>
    ),
  },
]

type Props = {
  userId: string;
};

let PageSize = pageSizes.pageSize25

const ActivityTab = ({ userId = '' }: Props) => {
  // Add NetworkStateProvider
  const networkStateProvider = NetworkStateProvider((state) => ({
    network: state.network,
  }))
  
  // Add state to track if network is ready
  const [isNetworkReady, setIsNetworkReady] = useState<boolean>(false)
  
  // Add state to track previous chainId to detect changes
  const [prevChainId, setPrevChainId] = useState<string>('')

  const initActivityParams: FilterParams = {
    userId: userId,
    filterEventTypes: [],
    chains: [],
    page: 1,
    limit: PageSize,
    sortBy: 'recentlyListed', // Not empty
  }

  const {
    filterParams: activityFilterParams,
    changeFilterParam: changeActivityFilterParam,
  } = useChangeFilterParams(initActivityParams)
  const [currentPage, setCurrentPage] = useState(1)

  const [activities, setActivities] = useState<any>([])
  const [total, setTotal] = useState(0)
  const [isActivitiesReady, setIsActivitiesReady] = useState(true)
  const [activitiesTable, setActivitiesTable] = useState<any>([])

  // Monitor network state changes and reset page when chainId changes
  useEffect(() => {
    if (networkStateProvider.network !== undefined) {
      setIsNetworkReady(true)
      
      const currentChainId = networkStateProvider.network?.chainId || ''
      
      // Only reset if chainId actually changed and is not the initial load
      if (prevChainId !== currentChainId) {
        // Reset page to 1 when chainId changes
        setCurrentPage(1)
        changeActivityFilterParam('page', 1, false)
      }
      
      // Update previous chainId
      setPrevChainId(currentChainId)
    }
  }, [networkStateProvider.network])

  const fetchActivities = async () => {
    // Only proceed if network is ready
    if (!isNetworkReady) return
    
    setIsActivitiesReady(false)
    console.log('activityFilterParams',activityFilterParams)
    
    // Get chainId from network provider
    const chainId = networkStateProvider.network?.chainId || ''
    
    const query = new ActivityQuery({
      ...activityFilterParams,
      userId: activityFilterParams.userId,
      SortBy: activityFilterParams.sortBy,
      filterEventTypes: activityFilterParams.filterEventTypes,
      chains: activityFilterParams.chains,
      chainId: chainId, // Add chainId to query
    })
    
    console.log('🚀 ~ fetchActivities ~ query:', query)
    
    activityUseCase
      .totalCount(query)
      .then((resTotal) => {
        setTotal(resTotal)
      })
      .catch(() => {
        setTotal(0)
      })
    activityUseCase
      .search(query)
      .then((res) => {
        setActivities(res)
      })
      .catch(() => {
        setActivities([])
      })
  }
  const mapActivities = (activities: ActivityModel[]) => {
    try {
      
      const rs: any = activityUseCase.mapActivitiesForAssetV2(activities)
      setActivitiesTable(rs)
    }catch (err) {
      console.log('🚀 ~ file: activities.tsx:mapActivities ~ err:', err)
    }
  }

  useEffect(() => {
    setIsActivitiesReady(true)
    mapActivities(activities)
  }, [activities])

  useEffect(() => {
    if (isNetworkReady) {
      fetchActivities()
    }
  }, [activityFilterParams, isNetworkReady])

  return (
    <div className="product-layout mt-7">
      <div className="product-layout-filter">
        <ToggleWrap title="Filter" contentClass="border-t-0">
          {/* Filter event type */}
          <ToggleVertical toggleTitle="Event Type" defaultShow>
            <ul className="fist-mt-none">
              {filterEventTypes.map((type: any, index: number) => (
                <li
                  key={index}
                  className="mt-5 font-medium flex items-center justify-between"
                >
                  {type.name}
                  <Switch
                    onClick={(activeState) => {
                      setCurrentPage(1)
                      changeActivityFilterParam('page', 1)
                      changeActivityFilterParam(
                        'filterEventTypes',
                        type.type,
                        activeState
                      )
                    }}
                  />
                </li>
              ))}
            </ul>
          </ToggleVertical>
          {/* <ToggleVerticalFilterChains
            defaultShow
            chains={filterChains}
            changeFilterParam={changeActivityFilterParam}
          /> */}
        </ToggleWrap>
      </div>

      <div className="flex flex-col xl:w-9/12 md:w-3/5 md2:w-4/6 w-full pr-third">
        <div className="flex flex-wrap justify-between mt-8 mb-3">
          <div className="xl:w-auto w-full">
            {!isActivitiesReady ? (
              <>
                <p className="w-36 h-4 bg-gray-3 rounded-lg animate-pulse"></p>
                <p className="mt-2 w-36 h-4 bg-gray-3 rounded-lg animate-pulse"></p>
              </>
            ) : (
              <>
                <p className="text-gray-1 dark:text-gray-3 text-stroke-thin">
                  Results：{total.toLocaleString()}
                </p>
              </>
            )}
          </div>
          <div className="mt-2 xl:mt-0 flex flex-wrap lg:flex-nowrap gap-5">
            {/* <SelectSearch options={options} className="shrink-0" /> */}
            <Select
              options={sortList}
              onSelect={(option) => {
                changeActivityFilterParam('sortBy', option.value)
              }}
              className="h-fit"
            />
          </div>
        </div>

        {!isActivitiesReady ? (
          <LoadingData />
        ) : (
          <>
            {activitiesTable?.length > 0 ? (
              <div className="activity-board-wrapper">
                <div className="w-full overflow-x-auto border-b">
                  <Table
                    head={activityTableHead}
                    data={activitiesTable}
                    className="border-b min-w-[760px]"
                  />
                </div>
                <div className="flex justify-center w-full px-9 mt-5 pb-9">
                  <PaginationDot
                    currentPage={currentPage}
                    totalCount={total}
                    pageSize={PageSize}
                    onPageChange={(page: number) => {
                      setCurrentPage(page),
                      changeActivityFilterParam('page', page)
                    }}
                  />
                </div>
              </div>
            ) : (
              <div className="w-full rounded-lg border flex justify-center items-center py-24">
                <NoData />
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}

export default ActivityTab
