import { getFullMonth, shortenAddress } from 'lib/utils'
import Image from 'next/image'
import { useRouter } from 'next/router'
import ButtonIcon from 'presentation/components/button/button-icon'
import ButtonTag from 'presentation/components/button/button-tag'
import Copy from 'presentation/components/copy'
import SvgIcon from 'presentation/components/svg-icon'
import { useEffect, useRef, useState } from 'react'
import SocialNetworks from '../socialNetworks'
import { useAccount } from 'wagmi'
import Link from 'next/link'
import BtnSocialShare from './btnSocialShare'
const MyProfileSection = ({ activeTab, owner, isMe, path }: any) => {
  const router = useRouter()
  const textRef = useRef(null)
  const [showOffersSelect, setShowOffersSelect] = useState(false)
  const [shortenedAddress, setShortenedAddress] = useState('')
  const [joinDate, setJoinDate] = useState('')
  const {address} = useAccount()
  const [urlShare, setUrlShare] = useState<string>('')
  const addressLength = 11

  const changeRequestTab = (tab: string) => {
    router.push(tab)
    if (showOffersSelect) {
      setShowOffersSelect(!showOffersSelect)
    }
  }
  const getUrlShare = () => {
    setUrlShare(
      window.location.href || ''
    )
  }
  useEffect(() => {
    const createdAt = new Date(owner?.createdAt)
    setJoinDate(
      `Joined ${getFullMonth(createdAt.getMonth())} ${createdAt.getFullYear()}`
    )
    setShortenedAddress(shortenAddress(owner?.publicAddresses[0], addressLength))
    getUrlShare()
  }, [])

  return (
    <main>
      <div className="relative w-full h-52">
        <Image
          className="rounded-lg"
          alt={`${owner?.username} cover`}
          src={owner?.background?.url || '/asset/images/owner-banner.png'}
          fill
          style={{ objectFit: 'cover' }}
        />
        {/* {isMe && (
          <SvgIcon
            name="plusCircle"
            className="icon-22 stroke-none cursor-pointer fill-primary-2 absolute -bottom-[-10px] md:right-8 right-4 z-40 bg-white rounded-full"
          />
        )} */}
      </div>

      <div className="relative">
        <div
          className={`pt-16 md:pt-4 flex justify-center md:justify-end
                    ${isMe ? 'h-0 md:h-fit invisible -z-1' : 'visible z-1'}`}
        >
          <SocialNetworks {...owner} />
          <BtnSocialShare urlShare={urlShare}/>
         
          {owner?.publicAddresses[0] === address && (
            <Link href={'/account/profileSettings'}>
              <ButtonIcon
                className="mr-3 h-fit"
                svgIcon="setting"
                iconClassName="w-5 h-5 stroke-none fill-primary-2"
              />
            </Link>
          )}
        </div>

        <div className="absolute w-[100px] h-[100px] top-0 left-1/2 -translate-y-1/2 -translate-x-1/2">
          <Image
            className="rounded-full"
            src={owner?.thumbnail?.url || '/asset/images/owner-avatar.png'}
            alt={owner?.username}
            fill
          />
          {/* {isMe && (
            <SvgIcon
              name="plusCircle"
              className="icon-22 stroke-none cursor-pointer fill-primary-2 absolute bottom-[11px] -right-[18px] z-40"
            />
          )} */}
        </div>
        {isMe && (
          <div className="md:absolute -top-8 md:top-4 right-0 flex md:justify-start justify-center xl:mr-16 mr-0">
            <SocialNetworks 
              twitterUsername={owner?.twitterUsername} 
              instagramUsername={owner?.instagramUsername} 
            />
            {owner?.webUrl && (
              <ButtonIcon
                className="mr-3 h-fit"
                svgIcon="externalLink"
                iconClassName="w-5 h-5 stroke-none fill-primary-2"
                href={owner?.webUrl}
              />
            )}
            {/* <ButtonIcon
              className="h-fit"
              svgIcon="moreVertical"
              iconClassName="w-5 h-5 stroke-none fill-primary-2"
            /> */}
          </div>
        )}
        <div
          className={`md:w-3/4 lg:w-1/2 mx-auto flex flex-col items-center ${isMe ? 'relative' : ''
          }`}
        >
          <div className="text-center">
            <p className="mt-3 font-medium text-lg">{owner?.username}</p>
            <div className="flex mt-2 justify-center leading-4">
              <Copy ref={textRef} copyContent={owner?.publicAddresses[0]}>
                <p ref={textRef} className="text-sm font-medium">
                  {shortenedAddress} 
                </p>
              </Copy>
              <SvgIcon
                name="yellowCoin"
                viewBox="0 0 13.333 13.333"
                className="w-[13px] h-[13px] ml-2 stroke-none"
              />
            </div>
            <p className="mt-4 text-gray-1 dark:text-gray-3 text-stroke-thin">
              {owner?.description}
            </p>
            <p className="text-sm text-gray-1 dark:text-gray-3 text-stroke-thin">
              {joinDate}
            </p>
          </div>

          <div className="flex flex-wrap lg:flex-nowrap mt-4 gap-2 md:gap-4 xl:gap-5 justify-center lg:justify-start">
            <ButtonTag
              className="shrink-0"
              active={activeTab === ''}
              onClick={() => changeRequestTab(path)}
            >
              Collected
            </ButtonTag>

            <ButtonTag
              className="shrink-0"
              active={activeTab === 'created'}
              onClick={() => changeRequestTab(path + '/created')}
            >
              Created
            </ButtonTag>

            <ButtonTag
              className="shrink-0"
              active={activeTab === 'favorited'}
              onClick={() => changeRequestTab(path + '/favorited')}
            >
              Favorited
            </ButtonTag>

            <ButtonTag
              className="shrink-0"
              active={activeTab === 'activities'}
              onClick={() => changeRequestTab(path + '/activities')}
            >
              Activity
            </ButtonTag>

            {owner?.publicAddresses[0] && (
              <div className="relative">
                <ButtonTag
                  className="shrink-0 btn-icon"
                  active={
                    activeTab === 'received' ||
                    activeTab === 'made' ||
                    showOffersSelect
                  }
                  onClick={() => setShowOffersSelect(!showOffersSelect)}
                >
                  Offers
                  <SvgIcon
                    name="chevronDown"
                    className="w-6 h-6 ml-1 stroke-none fill-dark-primary"
                  />
                </ButtonTag>
                {showOffersSelect && (
                  <div className="mt-2 px-2 py-3 rounded-25 bg-gray-5 w-fit absolute z-1">
                    <ButtonTag
                      shadow="none"
                      background="none"
                      round="rounded-none"
                      className="btn-icon w-max"
                      active={activeTab === 'received'}
                      onClick={() =>
                        changeRequestTab(path + '/offers/received')
                      }
                    >
                      <SvgIcon
                        name="arrowDownLeft"
                        className="w-5 h-5 mr-1 stroke-none fill-dark-primary"
                      />
                      Offers received
                    </ButtonTag>
                    <ButtonTag
                      shadow="none"
                      background="none"
                      round="rounded-none"
                      className="btn-icon w-max"
                      active={activeTab === 'made'}
                      onClick={() => changeRequestTab(path + '/offers/made')}
                    >
                      <SvgIcon
                        name="arrowUpRight"
                        className="w-5 h-5 mr-1 stroke-none fill-dark-primary"
                      />
                      Offers made
                    </ButtonTag>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </main>
  )
}

export default MyProfileSection
