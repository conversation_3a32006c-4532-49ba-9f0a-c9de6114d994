import FilterStatus from 'domain/repositories/asset_repository/filter_status'
import { Query as AssetQuery } from 'domain/repositories/asset_repository/query'
import SortBy from 'domain/repositories/asset_repository/sort_by'
import { Query as CategoryQuery } from 'domain/repositories/category_repository/query'
import { pageSizes } from 'lib/hook/customHook'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import AssetComponent from 'presentation/components/asset'
import NoData from 'presentation/components/no-data'
import PaginationDot from 'presentation/components/pagination/pagination-dot'
import Select from 'presentation/components/select'
import SkeletonAsset from 'presentation/components/skeleton/assets'
import ToggleWrap from 'presentation/components/toggle-wrap'
import ToggleVerticalFilterCategories from 'presentation/components/toggle-wrap/toggle-vertical-filter-categories'
import ToggleVerticalFilterPrice from 'presentation/components/toggle-wrap/toggle-vertical-filter-price'
import ToggleVerticalFilterStatues from 'presentation/components/toggle-wrap/toggle-vertical-filter-statues'
import ToggleVerticalSearchCollection from 'presentation/components/toggle-wrap/toggle-vertical-search-collections'
import AssetUseCase from 'presentation/use_cases/asset_use_case'
import CategoryUseCase from 'presentation/use_cases/category_use_case'
import { useEffect, useState } from 'react'
import { FilterParams } from 'types/type'
import { Query as AssetLikeQuery } from 'domain/repositories/asset_like_repository/query'
import AssetLikeUseCase from 'presentation/use_cases/asset_like_use_case'
import { NetworkStateProvider } from 'presentation/providers/network_state_provider'
const assetUseCase = new AssetUseCase()
const categoryUseCase = new CategoryUseCase()
const assetLikeUseCase = new AssetLikeUseCase()
const sortList = SortBy.getResourceArray().map((sort) => {
  return {
    id: sort.id,
    name: sort.label,
    value: sort.name,
  }
})
const filterStatuses = FilterStatus.getResourceArray()

//Todo remove mock data
import Asset from 'domain/models/asset'
import Category from 'domain/models/category'
import { toast } from 'react-toastify'
import AssetLike from 'domain/models/asset_like'
//End todo remove mock data

type Props = {
  userId: string;
  isMe: boolean;
};

let PageSize = pageSizes.pageSize25

const FavoritedTab = ({ userId = '', isMe = false }: Props) => {
  // Add NetworkStateProvider
  const networkStateProvider = NetworkStateProvider((state) => ({
    network: state.network,
  }))
  
  // Add state to track if network is ready
  const [isNetworkReady, setIsNetworkReady] = useState<boolean>(false)
  
  // Add state to track previous chainId to detect changes
  const [prevChainId, setPrevChainId] = useState<string>('')

  const initParams: FilterParams = {
    filterStatuses: [],
    minPrice: '',
    maxPrice: '',
    collectionIds: [],
    categoryIds: [],
    userId: userId,
    page: 1,
    limit: PageSize,
    //sortBy: 'recentlyListed',
  }

  const { filterParams, changeFilterParam } = useChangeFilterParams(initParams)
  const [currentPage, setCurrentPage] = useState(1)
  const [assets, setAssets] = useState<Asset[]>([])
  const [isFetchingAssets, setIsFetchingAssets] = useState(false)
  const [totalAsset, setTotalAsset] = useState(0)
  const [categories, setCategories] = useState<Category[]>([])

  // Monitor network state changes and reset page when chainId changes
  useEffect(() => {
    if (networkStateProvider.network !== undefined) {
      setIsNetworkReady(true)
      
      const currentChainId = networkStateProvider.network?.chainId || ''
      
      // Only reset if chainId actually changed and is not the initial load
      if (prevChainId !== currentChainId) {
        // Reset page to 1 when chainId changes
        setCurrentPage(1)
        changeFilterParam('page', 1, false)
      }
      
      // Update previous chainId
      setPrevChainId(currentChainId)
    }
  }, [networkStateProvider.network])

  const fetchAssets = async () => {
    // Only proceed if network is ready
    if (!isNetworkReady) return
    
    setIsFetchingAssets(true)
    
    // Get chainId from network provider
    const chainId = networkStateProvider.network?.chainId || ''
    
    const query = new AssetLikeQuery({
      ...filterParams,
      sortBy: filterParams.sortBy,
      chainId: chainId, // Add chainId to query
    })
    
    assetLikeUseCase.totalCount(query).then((resTotal) => {
      setTotalAsset(resTotal || 0)
    })
    
    assetLikeUseCase.search(query).then((res) => {
      let assets : any = res.map((assetLike) => {
        return assetLike.asset
      }) || []
      setAssets(assets)
    })
    
    setIsFetchingAssets(false)
  }
  
  const fetchCategories = async () => {
    const query = new CategoryQuery()
    categoryUseCase
      .search(query)
      .then((res) => {
        setCategories(res)
      })
      .catch((_error) => {
        toast.error('Failed to fetch categories.')
      })
  }

  useEffect(() => {
    if (isNetworkReady) {
      fetchAssets()
      fetchCategories()
    }
  }, [filterParams, isNetworkReady])

  return (
    <div className="product-layout owner mt-7">
      {/* <div className="product-layout-filter">
        <ToggleWrap title="Filter" contentClass="border-t-0 overflow-hidden">
          <ToggleVerticalFilterStatues
            defaultShow
            filterStatues={filterStatuses}
            changeFilterParam={changeFilterParam}
          />
          <ToggleVerticalFilterPrice
            defaultShow
            minPrice={filterParams.minPrice || ''}
            maxPrice={filterParams.maxPrice || ''}
            changeFilterParam={changeFilterParam}
          />
          <ToggleVerticalSearchCollection
            defaultShow
            changeFilterParam={changeFilterParam}
          />
          <ToggleVerticalFilterCategories
            defaultShow
            categories={categories}
            changeFilterParam={changeFilterParam}
          />
        </ToggleWrap>
      </div> */}

      <div className="w-full">
        <div className="mb-5 flex flex-wrap justify-between">
          <div>
            {isFetchingAssets ? (
              <>
                <p className="w-36 h-4 bg-gray-3 rounded-lg animate-pulse"></p>
                <p className="mt-2 w-36 h-4 bg-gray-3 rounded-lg animate-pulse"></p>
              </>
            ) : (
              <>
                <p className="text-gray-1 dark:text-gray-3 text-stroke-thin">
                  Results: {totalAsset.toLocaleString()}
                </p>
                {/* {!isMe && (
                  <p className="text-gray-1 dark:text-gray-3 mt-[9px] text-stroke-thin">
                    Floor Price: {'0.0897'.toLocaleString()}
                  </p>
                )} */}
              </>
            )}
          </div>
          <div className="mt-2 lg:mt-0 flex flex-wrap lg:flex-nowrap gap-5">
            {/* {!isMe && (
              <SelectSearch
                options={selectSearchOptions}
                className="shrink-0"
              />
            )} */}
            {/* <Select
              options={sortList}
              onSelect={(option) => {
                changeFilterParam('sortBy', option.value)
              }}
              className="h-fit"
            /> */}
          </div>
        </div>
        <div
          className={`product-wrap no-gap ${!isFetchingAssets && assets.length === 0 ? 'no-data' : ''
          } mt-0`}
        >
          {isFetchingAssets ? (
            [...Array(5)].map((_, id) => <SkeletonAsset key={id} />)
          ) : (
            <>
              {assets.length > 0 ? (
                assets.map((asset: Asset, index: number) => (
                  <AssetComponent
                    key={asset.id + '-' + index}
                    record={asset}
                    showDaysLeft={false}
                  />
                ))
              ) : (
                <div className="w-full rounded-lg border flex justify-center items-center py-24">
                  <NoData />
                </div>
              )}
            </>
          )}
        </div>
        {totalAsset > PageSize && !isFetchingAssets && (
          <div className="mt-6 flex justify-center">
            <PaginationDot
              currentPage={currentPage}
              totalCount={totalAsset}
              pageSize={PageSize}
              onPageChange={(page: number) => {
                setCurrentPage(page), changeFilterParam('page', page)
              }}
            />
          </div>
        )}
      </div>
    </div>
  )
}

export default FavoritedTab
