import { Query } from 'domain/repositories/offer_repository/query'
import { pageSizes } from 'lib/hook/customHook'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import Link from 'next/link'
import Button from 'presentation/components/button'
import PaginationDot from 'presentation/components/pagination/pagination-dot'
import LoadingData from 'presentation/components/skeleton/loading'
import SvgIcon from 'presentation/components/svg-icon'
import Switch from 'presentation/components/switch'
import Table from 'presentation/components/table'
import AssetTableItem from 'presentation/components/table/asset-item'
import ToggleWrap from 'presentation/components/toggle-wrap'
import ToggleVerticalSearchCollection from 'presentation/components/toggle-wrap/toggle-vertical-search-collections'
import OfferUseCase from 'presentation/use_cases/offer_use_case'
import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import { FilterParams } from 'types/type'
import Offer from 'domain/models/offer'
import AssetThumbnail from 'presentation/components/asset/thumbnail'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'
import { isAddress } from 'lib/validate'
import { Status as OfferStatus } from 'domain/models/offer/status'
import { NetworkStateProvider } from 'presentation/providers/network_state_provider'

const offerMadeTableHead = [
  {
    title: 'Item',
    key: 'asset',
    render: (asset: any) => (
      <Link
        href={`/assets/${asset?.id}`}
        className="py-3 flex items-center relative w-max"
      >
        <AssetThumbnail asset={asset}
          className="h-[60px] !w-[60px] !rounded-none"
          classNameImage="!rounded-none !border-none"/> 
        <div className="max-w-[7rem] py-1 ml-2">
          <p className="font-medium truncate text-dark-primary dark:text-white">{asset?.name}</p>
          <span className="text-sm font-medium text-primary-2 break-words">
            {asset?.collection?.name}
          </span>
        </div>
      </Link>
    ),
  },
  {
    title: 'Unit Price',
    key: 'unitPrice',
    render: ({ price, paymentToken }: any) => (
      <div className="px-2 uppercase">
        {price} {paymentToken?.name}
      </div>
    ),
  },
  {
    title: 'USD Price',
    key: 'usdPrice',
  },
  {
    title: 'Floor Difference',
    key: 'floorDifference',
    render: (floorDifference: any) => (
      <>
        {floorDifference ? (
          <div className="px-2">
            {`${floorDifference.percent}% ${floorDifference.text}`}
          </div>
        ) : (
          <div  className="text-dark-primary px-2 py-4 dark:text-white">--</div>
        )}
      </>
    ),
  },
  {
    title: 'From',
    key: 'userFrom',
    render: (userFrom: any) => (
      <>
        {userFrom?.username && !isAddress(userFrom?.username) ? (
          <Link href={`/users/${userFrom?.username}`} className="text-primary-2 px-2 py-4 block truncate w-[120px]">
            {userFrom?.username}
          </Link>
        ) : (
          <div className=" px-2 py-4 block truncate w-[120px] break-all dark:text-white">
            {userFrom?.username || userFrom?.recipient}
          </div>
        )}
      </>
      
    ),
  },
  {
    title: 'Expiration',
    key: 'expiration',
  },
  {
    title: 'Made',
    key: 'createdAt',
  },
  {
    title: 'Status',
    key: 'status',
    render: (status: any) => (
      <p className="font-medium max-w-[100px] w-14 text-center truncate capitalize">
        {status ? status.name : 'ー'}
      </p>
    ),
    tdClass: 'truncate max-w-[100px]',
  },
  {
    title: '',
    key: 'action',

    render: ({ text, onClick, isHide, offer }: any) => (
      <>
        {!isHide && (
          <Button variant="light" size="xs" onClick={() => {onClick(offer) }}>
            {text}
          </Button>
        )}
       
      </>
     
    ),
  },
]

type Props = {
  userId: string;
  isMe?: boolean;
};

let PageSize:any = pageSizes.pageSize25

const OfferMadeTab = ({ userId = '', isMe = false }: Props) => {
  // Add NetworkStateProvider
  const networkStateProvider = NetworkStateProvider((state) => ({
    network: state.network,
  }))
  
  // Add state to track if network is ready
  const [isNetworkReady, setIsNetworkReady] = useState<boolean>(false)
  
  // Add state to track previous chainId to detect changes
  const [prevChainId, setPrevChainId] = useState<string>('')

  const initParams: FilterParams = {
    userId: userId,
    collectionIds: [],
    page: 1,
    limit: PageSize,
  }

  const { filterParams, changeFilterParam } = useChangeFilterParams(initParams)
  const [currentPage, setCurrentPage] = useState(1)
  const [offers, setOffers] = useState([])
  const [total, setTotal] = useState(0)
  const [isFetching, setIsFetching] = useState(true)
  const offerUseCase = new OfferUseCase()
  const authStateProvider = AuthStateProvider((state) => ({me: state.me}))

  // Monitor network state changes and reset page when chainId changes
  useEffect(() => {
    if (networkStateProvider.network !== undefined) {
      setIsNetworkReady(true)
      
      const currentChainId = networkStateProvider.network?.chainId || ''
      
      // Only reset if chainId actually changed and is not the initial load
      if (prevChainId !== currentChainId) {
        // Reset page to 1 when chainId changes
        setCurrentPage(1)
        changeFilterParam('page', 1, false)
      }
      
      // Update previous chainId
      setPrevChainId(currentChainId)
    }
  }, [networkStateProvider.network])

  const handleClick = () => {
    toast.success('Canceled the offer successfully.')
    setCurrentPage(1), changeFilterParam('page', 1)
    fetchOffers()
  }
  const cancelOffer = async (offer:Offer) => {
    try {
      await offerUseCase.cancel(offer)
      await fetchOffers()
    } catch (error) {
      toast.error('Failed to cancel offer')
    }
  }
  
  const acceptOffer = async (offer:Offer) => {
    try {
      //setOffer(offer)
      //setShowModalAcceptOffer(true)
    } catch (error) {
      toast.error('Failed to cancel offer')
    }
  }
  const fetchOffers = async () => {
    // Only proceed if network is ready
    if (!isNetworkReady) return
    
    setIsFetching(true)
    console.log('🚀 ~ fetchOffers ~ filterParams:', filterParams)
    
    // Get chainId from network provider
    const chainId = networkStateProvider.network?.chainId || ''
    
    const query = new Query({
      ...filterParams,
      page: currentPage,
      limit: PageSize,
      statusId: OfferStatus.active().getId(),
      chainId: chainId, // Add chainId to query
    })
    
    offerUseCase
      .totalCount(query)
      .then((resTotal) => {
        setTotal(resTotal)
      })
      .catch(() => {
        toast.error('Failed to get total count')
      })
    
    offerUseCase
      .search(query)
      .then((res) => {
        const data: any = offerUseCase.mapOffersTableV2(res, cancelOffer, acceptOffer, 'makeOffer', isMe)
        console.log('🚀 ~ .then ~ data:', data)
        setOffers(data)
      })
      .catch(() => {
        toast.error('Failed to get offers')
      })
    
    setIsFetching(false)
  }
  
  useEffect(() => {
    if (isNetworkReady) {
      fetchOffers()
    }
  }, [currentPage, filterParams, isNetworkReady])

  return (
    <div className="product-layout owner mt-7">
      <div className="product-layout-filter">
        <ToggleWrap title="Filter" contentClass="border-t-0 overflow-hidden">
          <ToggleVerticalSearchCollection
            defaultShow
            changeFilterParam={(key, value, activeState) => {
              // reset collectionIds
              if( filterParams?.collectionIds && filterParams?.collectionIds?.length > 0){
                for (let i = 0; i < filterParams?.collectionIds?.length; i++) {
                  changeFilterParam('collectionIds', filterParams.collectionIds[i], false)
                }
              }
              changeFilterParam(key, value, activeState)
              setCurrentPage(1)
              changeFilterParam('page', 1)
            }}
          />
        </ToggleWrap>
      </div>

      <div className="w-full owner-board-wrap">
        <div className="mb-7 flex justify-between">
          <p>
            <SvgIcon
              name="arrowUpRight"
              className="w-5 h-5 stroke-none fill-dark-primary dark:fill-white"
            />
            <span className="ml-1 font-medium">Offer Made</span>
          </p>
          <div className="flex items-center">
            <p className="mr-5 font-medium leading-none">Show all offers</p>
            <Switch onClick={(value) => {
             
              if(value) {
                PageSize = 99999

              } else {
                PageSize = pageSizes.pageSize25
              }
              changeFilterParam('page', 1)
              setCurrentPage(1)
            }} className="mr-5" />
            <Link href="/account/profileSettings" className="lg:mr-5">
              <SvgIcon
                name="cog"
                className="w-7 h-7 stroke-primary-2 stroke-[2]"
              />
            </Link>
          </div>
        </div>
        <div className="w-full">
          {isFetching ? (
            <LoadingData />
          ) : (
            <>
              <div className="w-full overflow-x-auto">
                <Table head={offerMadeTableHead} data={offers} />
              </div>
              <div className="mt-6 flex justify-center">
                <PaginationDot
                  currentPage={currentPage}
                  totalCount={total}
                  pageSize={PageSize}
                  onPageChange={(page: number) => {
                    setCurrentPage(page), changeFilterParam('page', page)
                  }}
                />
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default OfferMadeTab
