
import ButtonIcon from 'presentation/components/button/button-icon'
import DropdownWrap from 'presentation/components/dropdown/dropdown-wrap'
import DropdownItem from 'presentation/components/dropdown/dropdown-item'
import { toast } from 'react-toastify'
import { useState } from 'react'
interface BtnSocialShareProps {
  urlShare: string;
  isCopy?: boolean;
  isFacebook?: boolean;
  isTwitter?: boolean;
}

const BtnSocialShare = ({
  urlShare = '',
  isCopy = true,
  isFacebook = true,
  isTwitter = true,
}: BtnSocialShareProps) => {
  
  /**
   * STATES
   */
  const [copied, setCopied] = useState(false)
  /**
   * FUNCTIONS
   */
  const copyToClipboard = () => {
    navigator.clipboard.writeText(urlShare).then(() => {
      setCopied(true)
      setTimeout(() => setCopied(false), 2000) // Reset the copied state after 2 seconds
      toast.success('Copied to clipboard')
    }).catch((err) => {
      console.error('Failed to copy: ', err)
    })
  }
  const copyLink = () => {
    copyToClipboard()
  }
  const shareOnFacebook = () => {
    const facebookShareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(urlShare)}`
    window.open(facebookShareUrl, '_blank')
  }
  const shareOnTwitter = () => {
    const twitterShareUrl = `https://x.com/intent/tweet?url=${encodeURIComponent(urlShare)}`
    window.open(twitterShareUrl, '_blank')
  }
  /**
   * RENDERS
   */
  return (
    <div className="btn-social-share">
      <DropdownWrap
        head={
          <ButtonIcon
            className="mr-3 h-fit"
            svgIcon="share"
            iconClassName="w-5 h-5 stroke-none fill-primary-2"
          />
        }
        dropdownClass="pt-3.5 pb-4 pl-4 pr-3"
        content={
          <ul className="fist-mt-none">
            {isCopy && <li className="mt-[18px]">
              <DropdownItem
                title="Copy Link"
                icon="logout"
                iconSize="w-[20px] h-[20px]"
                className="mt-4 font-bold items-center"
                onClick={() => copyLink()}
              />
            </li>}
            {isFacebook && <li className="mt-[18px]">
              <DropdownItem
                title="Share on Facebook"
                icon="facebook"
                iconSize="w-[20px] h-[20px]"
                className="mt-4 font-bold items-center"
                onClick={() => shareOnFacebook()}
              >
              </DropdownItem>
            </li>}
            {isTwitter && <li className="mt-[18px]">
              <DropdownItem
                title="Share on Twitter"
                icon="twitter"
                iconSize="w-[20px] h-[20px]"
                className="mt-4 font-bold items-center"
                onClick={() => shareOnTwitter()}
              >
              </DropdownItem>
            </li>}
          </ul>
        }
      />
    </div>
  )
}
export default BtnSocialShare