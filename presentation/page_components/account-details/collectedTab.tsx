import { Query as AssetQuery } from 'domain/repositories/asset_repository/query'
import { Query as CategoryQuery } from 'domain/repositories/category_repository/query'
import { pageSizes } from 'lib/hook/customHook'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import Asset from 'presentation/components/asset'
import NoData from 'presentation/components/no-data'
import PaginationDot from 'presentation/components/pagination/pagination-dot'
import Select from 'presentation/components/select'
import SelectSearch from 'presentation/components/select/select-search'
import SkeletonAsset from 'presentation/components/skeleton/assets'
import ToggleWrap from 'presentation/components/toggle-wrap'
import ToggleVerticalFilterCategories from 'presentation/components/toggle-wrap/toggle-vertical-filter-categories'
import ToggleVerticalFilterPrice from 'presentation/components/toggle-wrap/toggle-vertical-filter-price'
import ToggleVerticalFilterStatuesV2 from 'presentation/components/toggle-wrap/toggle-vertical-filter-statues-v2'
import ToggleVerticalSearchCollection from 'presentation/components/toggle-wrap/toggle-vertical-search-collections'
import AssetUseCase from 'presentation/use_cases/asset_use_case'
import CategoryUseCase from 'presentation/use_cases/category_use_case'
import { useEffect, useState } from 'react'
import { FilterParams } from 'types/type'
import { NetworkStateProvider } from 'presentation/providers/network_state_provider'

const assetUseCase = new AssetUseCase()
const categoryUseCase = new CategoryUseCase()

//Todo remove mock data
import AssetModel from 'domain/models/asset'
import Category from 'domain/models/category'
import FilterStatus from 'domain/repositories/asset_repository/filter_status'
import SortBy from 'domain/repositories/asset_repository/sort_by'
import { selectSearchOptions } from 'presentation/controllers/mockData/common_mock_data'
import { toast } from 'react-toastify'
//End todo remove mock data

type Props = {
  userId: string;
  isMe: boolean;
};

let PageSize = pageSizes.pageSize25

const CollectedTab = ({ userId = '', isMe = false }: Props) => {
  // Add NetworkStateProvider
  const networkStateProvider = NetworkStateProvider((state) => ({
    network: state.network,
  }))
  
  // Add state to track if network is ready
  const [isNetworkReady, setIsNetworkReady] = useState<boolean>(false)
  
  // Add state to track previous chainId to detect changes
  const [prevChainId, setPrevChainId] = useState<string>('')

  const initParams: FilterParams = {
    filterStatuses: [],
    minPrice: '',
    maxPrice: '',
    collectionIds: [],
    categoryIds: [],
    userId: userId,
    page: 1,
    limit: PageSize,
    sortBy: 'recentlyListed',
  }

  const { filterParams, changeFilterParam } = useChangeFilterParams(initParams)
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [assets, setAssets] = useState<AssetModel[]>([])
  const [isFetchingAssets, setIsFetchingAssets] = useState<boolean>(false)
  const [totalAsset, setTotalAsset] = useState<number>(0)
  const [showFilterStatuses, setShowFilterStatuses] = useState<boolean>(false)
  const [sortList, setSortList] = useState<any>([])
  const [showSortList, setShowSortList] = useState<boolean>(false)
  const [categories, setCategories] = useState<Category[]>([])
  const [showCategories, setShowCategories] = useState<boolean>(false)

  // Monitor network state changes and reset page when chainId changes
  useEffect(() => {
    if (networkStateProvider.network !== undefined) {
      setIsNetworkReady(true)
      
      const currentChainId = networkStateProvider.network?.chainId || ''
      
      // Only reset if chainId actually changed and is not the initial load
      if (prevChainId !== currentChainId) {
        // Reset page to 1 when chainId changes
        setCurrentPage(1)
        changeFilterParam('page', 1, false)
      }
      
      // Update previous chainId
      setPrevChainId(currentChainId)
    }
  }, [networkStateProvider.network])

  const fetchCategories = async () => {
    const query = new CategoryQuery()
    await categoryUseCase
      .search(query)
      .then((res) => {
        setCategories(res)
      })
      .catch((_error) => {
        toast.error('Failed to fetch categories')
      })
  }

  const fetchAssets = async () => {
    // Only proceed if network is ready
    if (!isNetworkReady) return
    
    setIsFetchingAssets(true)
    console.log('🚀 ~ filterStatuses:filterParams.filterStatuses?.map ~ filterParams:', filterParams)
    
    // Get chainId from network provider
    const chainId = networkStateProvider.network?.chainId || ''
    
    const query = new AssetQuery({
      // filterStatuses: filterParams.filterStatuses?.map((status) => {
      //   return FilterStatus.fromName(status)
      // }),  
      filterStatuses: filterParams.filterStatuses,
      minPrice: filterParams.minPrice,
      maxPrice: filterParams.maxPrice,
      categoryIds: filterParams.categoryIds,
      userId: filterParams.userId,
      page: filterParams.page,
      limit: filterParams.limit,
      //sortBy: SortBy.fromName(filterParams.sortBy!),
      sortBy: filterParams.sortBy,
      priceTokenUnit: filterParams.priceTokenUnit,
      collectionIds: filterParams?.collectionIds || [],
      ownerId: userId || '',
      chainId: chainId, // Add chainId to query
    })
    await assetUseCase
      .totalCount(query)
      .then((res) => {
        setTotalAsset(res)
      })
      .catch((_error) => {
        setTotalAsset(0)
      })

    await assetUseCase
      .search(query)
      .then((res) => {
        setAssets(res)
      })
      .catch((_error) => {
        setAssets([])
      })
    setIsFetchingAssets(false)
  }

  const formatSortList = () => {
    let sortList = SortBy.getResourceArray().map((sort: any) => {
      return {
        id: sort.id,
        name: sort.label,
        value: sort.name,
      }
    })
    sortList = sortList.filter((sort: any) => {
      return sort.value === 'recentlyListed' || sort.value === 'priceLowToHigh' || sort.value === 'priceHighToLow' || sort.value === 'highestLastSale'
    })
    setSortList(sortList)
    setShowSortList(true)
  }

  useEffect(() => {
    if (categories.length > 0) {
      setShowCategories(true)
    }
  }, [categories])

  useEffect(() => {
    fetchAssets()
  }, [filterParams, isNetworkReady])

  useEffect(() => {
    setShowFilterStatuses(true)
    fetchCategories()
    formatSortList()
  }, [])

  return (
    <div className="product-layout owner mt-7">
      <div className="product-layout-filter">
        <ToggleWrap title="Filter" contentClass="border-t-0 overflow-hidden">
          {showFilterStatuses && (
            <ToggleVerticalFilterStatuesV2
              defaultShow
              filterStatues={FilterStatus.getResourceArray()}
              changeFilterParam={(key, value, activeState) => {
                // reset filterStatuses
                if( filterParams?.filterStatuses && filterParams?.filterStatuses?.length > 0){
                  for (let i = 0; i < filterParams?.filterStatuses?.length; i++) {
                    changeFilterParam('filterStatuses', filterParams.filterStatuses[i], false)
                  }
                }
                changeFilterParam(key, value, activeState)
                
                setCurrentPage(1)
                changeFilterParam('page', 1)
              }}
            />
          )}
          <ToggleVerticalFilterPrice
            defaultShow
            minPrice={filterParams.minPrice || ''}
            maxPrice={filterParams.maxPrice || ''}
            changeFilterParam={(key, value, activeState) => {
              changeFilterParam(key, value, activeState)
              setCurrentPage(1)
              changeFilterParam('page', 1)
            }}
          />
          <ToggleVerticalSearchCollection
            defaultShow
            changeFilterParam={(key, value, activeState) => {
              // reset collectionIds
              if( filterParams?.collectionIds && filterParams?.collectionIds?.length > 0){
                for (let i = 0; i < filterParams?.collectionIds?.length; i++) {
                  changeFilterParam('collectionIds', filterParams.collectionIds[i], false)
                }
              }
              changeFilterParam(key, value, activeState)
              setCurrentPage(1)
              changeFilterParam('page', 1)
            }}
          />
          {showCategories && (
            <ToggleVerticalFilterCategories
              defaultShow
              categories={categories}
              changeFilterParam={(key, value, activeState) => {
                changeFilterParam(key, value, activeState)
                setCurrentPage(1)
                changeFilterParam('page', 1)
              }}
            />
          )}
        </ToggleWrap>
      </div>

      <div className="w-full md:w-9/12 xl:ml-5 pr-third">
        <div className="mb-5 flex flex-wrap justify-between">
          <div>
            {isFetchingAssets ? (
              <>
                <p className="w-36 h-4 bg-gray-3 rounded-lg animate-pulse"></p>
                <p className="mt-2 w-36 h-4 bg-gray-3 rounded-lg animate-pulse"></p>
              </>
            ) : (
              <>
                <p className="text-gray-1 dark:text-gray-3 text-stroke-thin">
                  Results: {totalAsset.toLocaleString()}
                </p>
                {/* {!isMe && (
                  <p className="text-gray-1 dark:text-gray-3 mt-[9px] text-stroke-thin">
                    Floor Price: {'0.0897'.toLocaleString()}
                  </p>
                )} */}
              </>
            )}
          </div>
          <div className="mt-2 lg:mt-0 flex flex-wrap lg:flex-nowrap gap-5">
            {/* {!isMe && (
              <SelectSearch
                options={selectSearchOptions}
                className="shrink-0"
              />
            )} */}
            {showSortList && (
              <Select
                options={sortList}
                onSelect={(option) => {
                  changeFilterParam('sortBy', option.value)
                  setCurrentPage(1)
                  changeFilterParam('page', 1)
                }}
                className="h-fit"
              />
            )}
          </div>
        </div>
        <div
          className={`product-wrap no-gap ${!isFetchingAssets && assets.length === 0 ? 'no-data' : ''
          } mt-0`}
        >
          {isFetchingAssets ? (
            [...Array(5)].map((_, id) => <SkeletonAsset key={id} />)
          ) : (
            <>
              {assets.length > 0 ? (
                assets.map((asset: any, index: number) => (
                  <Asset
                    key={asset.id + '-' + index}
                    record={asset}
                    showDaysLeft={false}
                  />
                ))
              ) : (
                <div className="w-full rounded-lg border flex justify-center items-center py-24">
                  <NoData />
                </div>
              )}
            </>
          )}
        </div>
        {totalAsset > PageSize && !isFetchingAssets && (
          <div className="mt-6 flex justify-center">
            <PaginationDot
              currentPage={currentPage}
              totalCount={totalAsset}
              pageSize={PageSize}
              onPageChange={(page: number) => {
                setCurrentPage(page), changeFilterParam('page', page)
              }}
            />
          </div>
        )}
      </div>
    </div>
  )
}

export default CollectedTab
