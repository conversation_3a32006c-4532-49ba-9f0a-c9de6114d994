import { useState, useEffect } from 'react'
import Heading from 'presentation/components/heading'
import Button from 'presentation/components/button'
import SvgIcon from 'presentation/components/svg-icon'
import Tooltip from 'presentation/components/tooltip'
import Table from 'presentation/components/table'
import Image from 'next/image'
import CollectionTableItem from 'presentation/components/collection/table-item'
import InputPrice from 'presentation/components/input/input-price'
import LoadingData from 'presentation/components/skeleton/loading'
import Offer from 'domain/models/offer'
import OfferUseCase from 'presentation/use_cases/offer_use_case'
import { Query } from 'domain/repositories/offer_repository/query'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'

const OfferSettingsTab = () => {
  const [offers, setOffers] = useState<Offer[]>([])
  const [isFetchingOffers, setIsFetchingOffers] = useState(true)
  const offerUseCase = new OfferUseCase()

  const authStateProvider = AuthStateProvider((state) => ({
    me: state.me,
  }))

  const settingOfferHeader = [
    {
      title: 'Collection',
      key: 'collection',
      render: ({ imageSrc, name, nickName }: any) => (
        <CollectionTableItem
          imagesrc={imageSrc}
          name={name}
          nickname={nickName}
        />
      ),
    },
    {
      title: 'Item',
      key: 'item',
    },
    {
      title: 'Floor Price',
      key: 'floorPrice',
      render: ({ priceSymbol, price, symboliconsrc }: any) => (
        <div className="flex items-center">
          <Image src={symboliconsrc} width={18} height={18} alt="" />
          <p className="text-primary-2 font-bold">
            {price} {priceSymbol}
          </p>
        </div>
      ),
    },
    {
      title: 'Minimum Offer',
      key: 'minimumOffer',
      render: (data: any) => (
        <InputPrice type="number" inputClass="input-price" {...data} />
      ),
    },
    {
      title: '',
      key: 'usdPrice',
      render: ({ price }: any) => (
        <p className="flex items-center">
          <Image src="/asset/icons/dollar.png" width={18} height={18} alt="" />
          <span className="usd-price">{price}</span>
        </p>
      ),
    },
    {
      title: '',
      key: 'action',
      render: () => (
        <Button variant="dark" onClick={() => { }}>
          Save
        </Button>
      ),
    },
  ]

  const fetchOffers = async () => {
    setIsFetchingOffers(true)
    const query = new Query({
      userId: authStateProvider.me?.id,
    })
    offerUseCase.search(query).then((res) => {
      setOffers(res)
    }).catch((_error) => {
      //Todo
    })
    setIsFetchingOffers(false)
  }

  useEffect(() => {
    fetchOffers()
  }, [])

  return (
    <>
      <div className="profile-setting items-center xl:pl-5 lg:pr-28 xl:min-w-[765px] lg:min-w-[700px]">
        <div className="flex flex-wrap xl:w-1/2 w-full">
          <Heading className="text-heading">Offers Settings</Heading>
          <div className="flex items-center mt-5 w-full">
            <p className="text-gray-1 dark:text-gray-3 xl:whitespace-nowrap">
              Set a minimum offer for collections to ignore low offers.
            </p>
            <Tooltip>
              Set a minimum offer for collections to ignore low offers.
            </Tooltip>
          </div>
        </div>
        <div className="xl:w-1/2 xl:mt-0 mt-5 xl:pl-10">
          <Button variant="light">
            View my offers
            <SvgIcon
              name="eye"
              className="w-5 h-5 ml-2 stroke-none fill-primary-2 dark:fill-white"
            />
          </Button>
        </div>
      </div>
      <div className="w-full mt-5 overflow-x-auto lg:pr-28 xl:pl-5">
        {isFetchingOffers ? (
          <LoadingData />
        ) : (
          <>
            {offers.length > 0 ? (
              <Table
                head={settingOfferHeader}
                data={offers}
                className="setting-offer-table"
              />
            ) : (
              <div className="flex items-center flex-col lg:w-[80%] min-h-[50vh]">
                <div className="flex items-center mt-8">
                  <SvgIcon
                    name="alertTriangle"
                    className="w-5 h-5 stroke-none fill-primary-2"
                  />
                  <p className="ml-10p text-2xl">
                    No collections to manage offers
                  </p>
                </div>
                <p className="text-gray-1 mt-30p">
                  You currently don’t have any collections and items to manage
                  offers.
                </p>
              </div>
            )}
          </>
        )}
      </div>
    </>
  )
}

export default OfferSettingsTab
