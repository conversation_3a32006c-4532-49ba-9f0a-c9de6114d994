import Button from 'presentation/components/button'
import Heading from 'presentation/components/heading'
import InputPrice from 'presentation/components/input/input-price'
import Switch from 'presentation/components/switch'
import { useForm } from 'lib/hook/useForm'
import { shortenAddress } from 'lib/utils'
import { useEffect, useState } from 'react'

export const notificationSettings = [
  {
    setting: 'Item Sold',
    description: 'When someone purchased one of your items',
    key: 'onItemSold',
  },
  {
    setting: 'Bid Activity',
    description: 'When someone bids on one of your items',
    key: 'onBidActivity',
  },
  {
    setting: 'Price Change',
    description: 'When an item you made an offer on changes in price',
    key: 'onPriceChange',
  },
  {
    setting: 'Auction Expiration',
    description: 'When a Dutch or English auction you created ends',
    key: 'onAuctionExpiration',
  },
  {
    setting: 'Outbid',
    description: 'When an offer you placed is exceeded by another user',
    key: 'onOutbid',
  },
  {
    setting: 'Referral Successful',
    description: 'When an item you referred is purchased',
    key: 'onReferralSuccess',
  },
  {
    setting: 'Owned Asset Updates',
    description:
      'When a significant update occurs for one of the items you have purchased',
    key: 'onOwnedAssetUpdates',
  },
  {
    setting: 'Successful Purchase',
    description: 'When you successfully buy an item',
    key: 'onSuccessfullyPurchase',
  },
  {
    setting: 'Newsletter',
    description: 'Occasional updates from the NFTLAND team',
    key: 'onNewsLetter',
  },
  {
    setting: 'Now Sold',
    description: 'When there is a sale in real time',
    key: 'minimumBidThresholdJpy',
  },
]

const NotificationSettingsTab = (props: any) => {
  const { address = '', settings = null, onSubmit = () => {} } = props

  const initFormData = {
    onItemSold: settings.onItemSold,
    onBidActivity: settings.onBidActivity,
    onPriceChange: settings.onPriceChange,
    onAuctionExpiration: settings.onAuctionExpiration,
    onOutbid: settings.onOutbid,
    onReferralSuccess: settings.onReferralSuccess,
    onOwnedAssetUpdates: settings.onOwnedAssetUpdates,
    onSuccessfullyPurchase: settings.onSuccessfullyPurchase,
    onNewsLetter: settings.onNewsLetter,
  }

  const { formData, setFormValue, setFormData } = useForm(initFormData)
  const [minimumBidThresholdJpy, setminimumBidThresholdJpy] = useState(settings.minimumBidThresholdJpy || 0.01)
  const shortenedAddress = shortenAddress(address)

  return (
    <div className="notifications-setting w-full">
      <Heading className="text-heading text-dark-primary dark:text-white">
        Notification Settings
      </Heading>
      <div className="mt-8">
        <p className="text-gray-1 dark:text-gray-3 text-stroke-thin">
          Select which notifications you would like to receive for{' '}
          {shortenedAddress}
        </p>
        {notificationSettings.map((setting, index) => (
          <div key={index} className="flex items-baseline mt-5">
            <div className="mr-4 xl:mr-10 flex justify-between items-center">
              <p className="w-[160px] min-w-[158px] mr-3 xl:mr-5 truncate font-medium text-dark-primary dark:text-white">
                {setting.setting}
              </p>
              <div className="w-max">
                <Switch
                  defaultActive={formData[setting.key]}
                  onClick={(value) => setFormValue(setting.key, value)}
                />
              </div>
            </div>
            <p className="text-gray-1 dark:text-gray-3 text-stroke-thin">
              {setting.description}
            </p>
          </div>
        ))}

        <p className="text-dark-primary dark:text-white font-medium mt-5">
          Minimum Bid Threshold
        </p>
        <p className="text-gray-1 dark:text-gray-3 text-stroke-thin mt-2">
          Receive notifications only when you receive offers with a value
          greater than or equal to this amount of ETH.
        </p>
        <div className="mt-5">
          <InputPrice
            symbolIconSrc="/asset/icons/ETH.svg"
            priceSymbol="eth"
            value={minimumBidThresholdJpy}
            type="number"
            onChange={(e: any) => setminimumBidThresholdJpy(e.target.value)}
          />
        </div>

        <Button
          variant="dark"
          htmltype="submit"
          className="mt-6"
          onClick={(e: any) => {
            e.preventDefault()
            onSubmit(formData, minimumBidThresholdJpy)
          }}
        >
          Save
        </Button>
      </div>
    </div>
  )
}

export default NotificationSettingsTab
