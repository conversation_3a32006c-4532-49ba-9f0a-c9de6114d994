import Link from 'next/link'
import Heading from 'presentation/components/heading'
import But<PERSON> from 'presentation/components/button'
import SvgIcon from 'presentation/components/svg-icon'
import Select from 'presentation/components/select'
import Switch from 'presentation/components/switch'

//Todo remove mock data
import { settingIssueOptions } from 'presentation/controllers/mockData/user_settings_mock_data'
import { useState } from 'react'
//End todo remove mock data

const AccountSupportTab = () => {
  const [selected, setSelected] = useState<any>(settingIssueOptions[0])
  const [consent, setConsent] = useState(false)

  return (
    <div className="acccount-support min-h-[50vh]">
      <Heading className="text-heading text-dark-primary dark:text-white">
        Account Support
      </Heading>
      <p className="text-gray-1 mt-30p dark:text-gray-3 text-stroke-thin">
        If you need help related to your account, we can help you.
      </p>
      {/* <div className="pb-5 border-b border-gray-3">
        <p className="mt-30p font-medium text-dark-primary dark:text-white">
          General Help
        </p>
        <p className="mt-10p text-gray-1 dark:text-gray-3 text-stroke-thin">
          Visit our{' '}
          <Link href="/help" className="text-primary-2">
            help center
          </Link>{' '}
          to learn how to get started with buying, selling, and creating.
        </p>
      </div> */}
      <div className="pb-5 border-b border-gray-3">
        <p className="mt-3 font-medium text-dark-primary dark:text-white">
          Contact NFTLand Support
        </p>
        <p className="mt-10p text-gray-1 dark:text-gray-3 text-stroke-thin">
          Can’t find the answers you’re looking for? You can{' '}
          <a 
            href="mailto:<EMAIL>" 
            className="text-primary-2 mr-1"
          >
            submit a request
          </a>
          here.
        </p>
      </div>
      <div>
        <p className="font-medium mt-3 text-dark-primary dark:text-white">
          Help with a compromised account
        </p>
        <p className="mt-10p text-gray-1 dark:text-gray-3 text-stroke-thin">
          If you believe your account has been compromised, let us know and we
          can lock your account. This will disable items in your wallet from
          being bought, sold, or transferred using NFTLand.{' '}
          {/* <Link href="/" className="text-primary-2">
            Learn more.
          </Link> */}
        </p>
      </div>
      <div className="pb-6 border-b border-gray-3">
        <Select
          className="issue-list mt-4"
          selectClassName="xl:w-[372px] xl:justify-between"
          options={settingIssueOptions}
          rounded
          onSelect={(option) => {
            setSelected(option)
            if (option === settingIssueOptions[0]) {
              setConsent(false)
            }
          }}
          arrowIconClass="stroke-none fill-primary-2"
        />
        {selected != settingIssueOptions[0] && (
          <>
            <div className="mt-8 flex flex-row">
              <p className="text-gray-1 dark:text-gray-3 text-stroke-thin mr-2">
                I understand I must provide a sworn statement certified by a
                notary public to unlock my account.
              </p>
              <Switch
                onClick={(value) => setConsent(value)}
                className="my-auto"
              />
            </div>
          </>
        )}
        <Button
          variant="light"
          disable={!consent || selected === settingIssueOptions[0]}
          className="mt-6"
        >
          <a className="flex items-center">
            <SvgIcon
              name="alertTriangle"
              className="w-5 h-5 mr-4 stroke-none fill-primary-2 dark:fill-white"
            />
            <span>Lock Account</span>
          </a>
        </Button>
      </div>
    </div>
  )
}

export default AccountSupportTab
