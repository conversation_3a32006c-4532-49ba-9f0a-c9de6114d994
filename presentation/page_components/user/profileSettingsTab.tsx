import Image from 'next/image'
import { useRef, useEffect, useState } from 'react'
import Heading from 'presentation/components/heading'
import Button from 'presentation/components/button'
import Copy from 'presentation/components/copy'
import SvgIcon from 'presentation/components/svg-icon'
import Input from 'presentation/components/input'
import Tooltip from 'presentation/components/tooltip'
import { useForm } from 'lib/hook/useForm'
import clsx from 'clsx'
import MultimediaUseCase from 'presentation/use_cases/multimedia_use_case'
import UserUseCase from 'presentation/use_cases/user_use_case'
import { NullableBoolean } from 'types/type'
import { isUsername, isURL } from '../../../lib/validate'


const multimediaUseCase = new MultimediaUseCase()
const userUseCase = new UserUseCase()
const maxLengthUsername = 42
const maxLengthBio = 1200

const ProfileSettingsTab = (props: any) => {
  const { me = {}, address = '', onSubmit = () => { } } = props

  const [isUsernameValid, setUsernameValid] = useState<NullableBoolean>(null)
  const [errorUsername, setErrorUsername] = useState<String>('')
  const [errorBio, setErrorBio] = useState<String>('')
  const [isEmailValid, setEmailValid] = useState<NullableBoolean>(null)
  const [errorEmail, setErrorEmail] = useState<String>('')
  const [isWebUrlValid, SetWebUrlValid] = useState<NullableBoolean>(null)

  const initFormData = {
    username: '',
    email: '',
    thumbnail: null,
    background: null,
    description: '',
    twitterUsername: '',
    instagramUsername: '',
    webUrl: '',
  }

  const textRef = useRef(null)
  const { formData, setFormValue, setFormData } = useForm(initFormData)

  const uploadFile = async (file: any, key: string) => {
    if (file) {
      multimediaUseCase.upload(file).then((res) => {
        setFormValue(key, res)
      })
    }
  }

  const checkEmailValid = async (email: string) => {
    setErrorEmail('')
    setEmailValid(false)
    if (!email) {
      // setErrorEmail('This Email is required')
      setFormValue('email', email)
      setEmailValid(null)
      setErrorEmail('')
      return true
    } else {
      const regex = /\S+@\S+\.\S+/
      if (regex.test(email) && email.length < 250) {
        setFormValue('email', email)
        const res = await userUseCase.isEmailAvailable(email)
        if (res) {
          setEmailValid(true)
          setErrorEmail('')
          return true
        } else {
          setErrorEmail('This Email is registered already')
        }
      } else {
        setFormValue('email', email)
        setErrorEmail('This Email is invalid')
      }
    }
    return false
  }

  const checkWebUrlValid = async (webUrl: string) => {
    setFormValue('webUrl', webUrl)
    if (!webUrl) {
      SetWebUrlValid(null)
      return
    } 
    const isValid = isURL(webUrl)
    SetWebUrlValid(isValid)
    return isValid
  }
  
  const checBioValid = async (text: string) => {
    setFormValue('description', text)
    if (text?.length > maxLengthBio) {
      setErrorBio(`The Bio field may not be greater than ${maxLengthBio} characters`)
    } else {
      setErrorBio('')
    }  
  }

  const checkUsernameValid = async (username: string) => {
    setFormValue('username', username)
    if(!username) {
      setErrorUsername('This username is required.')
      setUsernameValid(false)
      return false
    }
    const isValid = isUsername(username)
    if(!isValid){
      setErrorUsername('Can only contain letters, numbers, hyphens (-), and underscores (_).')
      setUsernameValid(false)
      return false
    }
    
    userUseCase.isUsernameAvailable(username).then((res) => {
      if (res && username.length <= maxLengthUsername) {
        setUsernameValid(true)
        return false
      } else {
        setErrorUsername('The username is already taken.')
        setUsernameValid(false)
        return false
      }
    })
  }

  const handleSubmit = async () => {
    const _isUsernameValid = await checkUsernameValid(formData.username)
    const _isEmailValid = await checkEmailValid(formData.email)
    const _isWebUrlValid = await checkWebUrlValid(formData.webUrl)
    if(_isUsernameValid !== false && _isEmailValid !== false && _isWebUrlValid !== false)  {
      onSubmit(formData)
    }
  }

  useEffect(() => {
    if (me.id) {
      checBioValid(me.description)
      setFormData({
        username: me.username || '',
        email: me.email || '',
        thumbnail: me.thumbnail,
        background: me.background,
        description: me.description || '',
        twitterUsername: me.twitterUsername || '',
        instagramUsername: me.instagramUsername || '',
        webUrl: me.webUrl || '',
      })
    }
  }, [])

  return (
    <div className="profile-setting flex flex-col items-start">
      <div className="flex flex-wrap justify-between w-full lg:max-w-[620px] md:max-w-[580px]">
        <Heading className="text-heading">Profile Settings</Heading>
        {/* * Preview function is removed with v1.1. */}
        {/* <div className="text-right md2:mt-7 mt-0">
          <Button variant="light" size="sm">
            Preview
            <SvgIcon
              name="eye"
              className="w-5 h-5 ml-2 stroke-none fill-primary-2 dark:fill-white"
            />
          </Button>
        </div> */}
      </div>

      <form className="md2:mt-0 mt-5">
        <p>
          <span className="text-red-1">*</span>
          <span className="text-gray-1 dark:text-white">Required Fields</span>
        </p>

        <div className="mt-5 flex lg:flex-nowrap flex-wrap lg:gap-10 gap-5">
          <div className="lg:w-1/2 w-full">
            <p className="font-medium text-dark-primary dark:text-white">
              Name <span className="text-red-1">*</span>
            </p>
            <Input
              type="text"
              name="username"
              maxLength={maxLengthUsername}
              className={clsx(
                'border mt-[10px]',
                isUsernameValid == true
                  ? 'border-green-1'
                  : isUsernameValid == false
                    ? 'border-red-1'
                    : ''
              )}
              value={formData.username}
              onChange={(e) => checkUsernameValid(e.target.value)}
            />
            {isUsernameValid && (
              <div className="inline-flex items-center mt-1">
                <SvgIcon name="check" className="w-5 h-5 stroke-green-1 mr-1" />
                <span className="text-sm text-gray-1">
                  This Username is available.
                </span>
              </div>
            )}
            {!isUsernameValid && isUsernameValid != null && (
              <div className="inline-flex items-center">
                <span className="text-sm text-red">
                  {errorUsername ? errorUsername :'This Username is not available.'}
                </span>
              </div>
            )}
          </div>
          <div className="lg:w-1/2 w-full">
            <p className="font-medium text-dark-primary dark:text-white">
              Email 
            </p>
            <Input
              type="text"
              name="email"
              maxLength={250}
              className={clsx(
                'border mt-[10px]',
                isEmailValid == true ? 'border-green-1' : '',
                errorEmail ? 'border-red-1' : '',
              )}
              disabled={!!me.email}
              value={formData.email}
              onChange={(e) => checkEmailValid(e.target.value)}
            />
            {isEmailValid && (
              <div className="inline-flex items-center mt-1">
                <SvgIcon name="check" className="w-5 h-5 stroke-green-1 mr-1" />
                <span className="text-sm text-gray-1">
                  This Email is available.
                </span>
              </div>
            )}
            {errorEmail  && (
              <div className="inline-flex items-center mt-1">
                <span className="text-sm text-red">
                  {errorEmail}
                </span>
              </div>
            )}
          </div>
        </div>

        <div className="mt-5">
          <p className="font-medium text-dark-primary dark:text-white">Bio</p>
          <textarea
            className="px-5 py-3 mt-2 rounded-3xl w-full outline-none bg-gray-5 dark:bg-blue-1"
            rows={4}
            maxLength={maxLengthBio}
            value={formData.description}
            onChange={(e) => checBioValid(e.target.value)}
          />
          {errorBio && (
            <div className="inline-flex items-center">
              <span className="text-sm text-red">
                {errorBio}
              </span>
            </div>
          )}
        </div>

        <div className="mt-5 flex md:flex-nowrap flex-wrap">
          {/* Image */}
          <div className="pr-8 pb-5">
            <div className="flex justify-between items-center">
              <p className="font-medium mr-2 w-max text-dark-primary dark:text-white">
                Profile Image
              </p>
              <Tooltip>Image description here</Tooltip>
            </div>
            <div className="relative w-fit pt-3 pr-3 mt-3">
              <div className="absolute top-0 right-0">
                <input
                  className="w-5 h-5 opacity-0 absolute"
                  type="file"
                  id="img"
                  name="thumbnail"
                  accept="image/*"
                  onChange={(e: any) =>
                    uploadFile(e.target.files[0], 'thumbnail')
                  }
                />
                <SvgIcon
                  name="plusCircle"
                  className="w-5 h-5 stroke-none fill-primary-2 cursor-pointer"
                />
              </div>
              <div className="upload-image-circle bg-gray-5 dark:bg-blue-1">
                {formData.thumbnail && (
                  <Image
                    alt=""
                    fill
                    style={{ objectFit: 'cover' }}
                    className="rounded-10"
                    src={formData.thumbnail?.url}
                  />
                )}
              </div>
            </div>
          </div>

          <div className="w-full">
            <div className="flex items-center mb-5">
              <p className="font-medium mr-2 w-max text-dark-primary dark:text-white">
                Profile Banner
              </p>
              <Tooltip>
                <p>Image description here</p>
              </Tooltip>
            </div>
            <div className="relative mt-2">
              <div className="absolute top-3 right-3 z-40">
                <input
                  className="w-5 h-5 opacity-0 absolute"
                  type="file"
                  id="img"
                  name="background"
                  accept="image/*"
                  onChange={(e: any) =>
                    uploadFile(e.target.files[0], 'background')
                  }
                />
                <SvgIcon
                  name="plusCircle"
                  className="w-5 h-5 stroke-none fill-primary-2 cursor-pointer bg-white rounded-full"
                />
              </div>
              <div className="upload-image-reactangle bg-gray-5 dark:bg-blue-1">
                {formData.background && (
                  <Image
                    alt=""
                    fill
                    style={{ objectFit: 'cover' }}
                    className="rounded-10"
                    src={formData.background?.url}
                  />
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="mt-5">
          <p className="font-medium text-dark-primary dark:text-white">Links</p>
          <div className="pl-4">
            <div className="flex items-center">
              <SvgIcon
                name="twitter"
                className="w-7 h-5 mr-3 stroke-none fill-primary-2"
              />
              <p className="mr-7 w-20 font-medium text-dark-primary dark:text-white">
                Twitter
              </p>
              <Input
                type="text"
                width="lg:w-[224px] w-full"
                placeholder="Your Handle"
                value={formData.twitterUsername}
                onChange={(e) =>
                  setFormValue('twitterUsername', e.target.value)
                }
              />
            </div>
            <div className="mt-[10px] flex items-center">
              <SvgIcon
                name="instagram"
                className="w-7 h-5 mr-3 stroke-none fill-primary-2"
              />
              <p className="mr-7 w-20 font-medium text-dark-primary dark:text-white">
                Instagram
              </p>
              <Input
                type="text"
                width="lg:w-[224px] w-full"
                placeholder="Your Handle"
                value={formData.instagramUsername}
                onChange={(e) =>
                  setFormValue('instagramUsername', e.target.value)
                }
              />
            </div>
            <div className="mt-[10px] flex items-center">
              <SvgIcon
                name="globe"
                className="w-7 h-5 mr-3 stroke-none fill-primary-2"
              />
              <p className="mr-7 w-20 font-medium">Your Site</p>
              <div>
                <Input
                  type="text"
                  width="lg:w-[312px] w-full"
                  placeholder="Your Site"
                  className={clsx(
                    'border mt-[10px]',
                    isWebUrlValid == true
                      ? 'border-green-1'
                      : isWebUrlValid == false
                        ? 'border-red-1'
                        : ''
                  )}
                  value={formData.webUrl}
                  onChange={(e) => checkWebUrlValid(e.target.value)}
                />
                {!isWebUrlValid && isWebUrlValid != null && (
                  <div className="inline-flex items-center">
                    <span className="text-sm text-red">
                      The Your Site invalid
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="mt-5">
          <p className="font-medium mb-2 text-dark-primary dark:text-white">
            Wallet Address
          </p>
          <Copy ref={textRef} clickIcon>
            <Input
              ref={textRef}
              type="text"
              className="pr-12 bg-blue-2"
              value={address}
              readOnly
            />
          </Copy>
        </div>

        <Button
          variant="dark"
          className="mt-6"
          disable={isUsernameValid === false || isEmailValid === false || isWebUrlValid === false || errorBio !== ''}
          onClick={(e: any) => {
            e.preventDefault()
            handleSubmit()
          }}
        >
          Save
        </Button>
      </form>
    </div>
  )
}

export default ProfileSettingsTab
