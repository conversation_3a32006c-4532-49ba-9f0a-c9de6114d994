import ButtonTag from 'presentation/components/button/button-tag'

const analyticTabs = [
  {
    name: 'Collection',
    slug: '',
  },
  {
    name: 'NFT',
    slug: 'nft',
  },
  {
    name: 'Mint',
    slug: 'mint',
  },
  {
    name: 'Leaderboard',
    slug: 'leaderboard',
  },
  {
    name: 'New',
    slug: 'new',
  },
]

const AnalyticsTabs = ({
  activeTab = '',
  onChangeTab = () => {},
}:any) => {

  return (
    <div className="flex flex-wrap justify-center lg:justify-start lg:flex-nowrap gap-2 md:gap-4 xl:gap-5">
      {analyticTabs.map((tab, key) => (
        <ButtonTag
          key={key}
          className="shrink-0"
          active={activeTab === tab.slug}
          onClick={() => onChangeTab(tab.slug)}
        >
          {tab.name}
        </ButtonTag>
      ))}
    </div>
  )
}

export default AnalyticsTabs
