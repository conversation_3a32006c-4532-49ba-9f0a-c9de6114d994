import { useState } from 'react'
import Pagination from 'presentation/components/pagination'
import FilterTime from 'presentation/components/filter-time'
import Table from 'presentation/components/table'
import LoadingData from 'presentation/components/skeleton/loading'

const TopSellerBoard = (props:any) => {
  const {
    header = [],
    filterTimeOptions = [],
    defaultTimeFilter,
    currentPage = 1,
    setCurrentPage = () => {},
    currentLimit = 10,
    tableData = [],
    total = 0,
    onChangeFilterParam = () => {},
    isFetching = true,
  } = props

  const [filterTime, setFilterTime] = useState(defaultTimeFilter ? defaultTimeFilter : filterTimeOptions[0])

  return (
    <div>
      <div className="flex flex-wrap gap-y-3 justify-between">
        <p className="font-bold text-lg text-dark-primary dark:text-white">
          Top Sellers({filterTime?.name})
        </p>
        <FilterTime
          options={filterTimeOptions}
          defaultSelected={defaultTimeFilter ? defaultTimeFilter : filterTimeOptions[3]}
          onSelect={(option) => {
            setFilterTime(option)
            onChangeFilterParam('sortBy', option.value)
          }}
        />
      </div>
      <div className="table-wrap mt-5 border rounded-20">
        {isFetching ? (
          <LoadingData/>
        ) : (
          <>
            <div className="w-full overflow-x-auto pb-3">
              <Table
                head={header}
                data={tableData}
                className={`common-table top-seller
                  min-w-[${header.length === 4 ? '560' : '600'}px]`}
              />
            </div>
            <div className="mt-2 px-5 mb-9">
              <Pagination
                enableSelectRow={false}
                currentPage={currentPage}
                totalCount={total}
                pageSize={currentLimit}
                onPageChange={(page: number) => {
                  setCurrentPage(page),
                  onChangeFilterParam('page', page)
                }}
                isShortened={true}
              />
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default TopSellerBoard
