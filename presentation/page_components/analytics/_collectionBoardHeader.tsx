import dynamic from 'next/dynamic'
import CollectionTableItem from 'presentation/components/table/collection-item'
const TinyColumn = dynamic(() => import('@ant-design/plots').then(({ TinyColumn }) => TinyColumn), { ssr: false })

export const collectionTableHeader = [
  {
    title: '#',
    key: 'id',
  },
  {
    title: 'Collection',
    key: 'collection',
    render: (record: any) => (
      <CollectionTableItem record={record} />
    ),
  },
  {
    title: 'Market Cap',
    key: 'marketCap',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: ({price, percentage}: any) => (
      <div className="flex flex-wrap justify-end w-full" style={{ paddingRight: '14px' }}>
        <p>{price}</p>
        <div className="percentage-bar">
          <div className="percentage-track" style={{width: percentage}}></div>
        </div>
      </div>
    ),
  },
  {
    title: '24H',
    key: 'h24',
    sortable: true,
    render: ({percentage}: any) => (
      <div className="flex flex-wrap justify-end w-full">
        <span className="text-red">{percentage}</span>
      </div>
    ),
  },
  {
    title: 'Volume(24H)',
    key: 'volumn24h',
    sortable: true,
    render: ({price, percentage}: any) => (
      <div className="flex flex-wrap justify-end w-full">
        <p>{price}</p>
        <div className="percentage-bar">
          <div className="percentage-track" style={{width: percentage}}></div>
        </div>
      </div>
    ),
  },
  {
    title: 'Volume(7D)',
    key: 'volumn7d',
    render: ({config}: any) => (
      <>
        <TinyColumn {...config} />
      </>
    ),
  },
  {
    title: 'Floor Price',
    key: 'floorprice',
    description: 'Floor price description',
    sortable: true,
    options: {
      tooltipPosition: 'right',
    },
    render: ({price, percentage}: any) => (
      <div className="text-right" style={{ paddingRight: '14px' }}>
        <p>{price}</p>
        <p className="text-red">{percentage}</p>
      </div>
    ),
  },
  {
    title: 'Avg Price(24H)',
    key: 'avgprice24h',
    sortable: true,
    render: ({price, percentage}: any) => (
      <div className="text-right">
        <p>{price}</p>
        <p className="text-green">{percentage}</p>
      </div>
    ),
  },
  {
    title: 'Liquidity(24H)',
    key: 'liquidity24h',
    description: 'Liquidity description',
    sortable: true,
    options: {
      sortKey: 'price',
      tooltipPosition: 'right',
    },
    render: ({price, percentage}: any) => (
      <div className="text-right" style={{ paddingRight: '14px' }}>
        <p>{price}</p>
        <p className="text-red">{percentage}</p>
      </div>
    ),
  },
  {
    title: 'Sales(24H)',
    key: 'sales24h',
    sortable: true,
    render: ({price, percentage}: any) => (
      <div className="text-right">
        <p >{price}</p>
        <p className="text-red">{percentage}</p>
      </div>
    ),
  },
  {
    title: 'Whales',
    key: 'whales',
    description: 'Whale alert',
    sortable: true,
    options: {
      tooltipPosition: 'right',
    },
    render: ({price, percentage}: any) => (
      <div className="text-right">
        <p >{price}</p>
        <p className="text-red">{percentage}</p>
      </div>
    ),
  },
  {
    title: 'Holders',
    key: 'holders',
    sortable: true,
    render: ({price, percentage}: any) => (
      <div className="text-right">
        <p >{price}</p>
        <p className="text-red">{percentage}</p>
      </div>
    ),
  },
  {
    title: 'Buyers(24H)',
    key: 'buyers',
    sortable: true,
    render: ({price, percentage}: any) => (
      <div className="text-right">
        <p >{price}</p>
        <p className="text-red">{percentage}</p>
      </div>
    ),
  },
  {
    title: 'Sellers(24H)',
    key: 'sellers24h',
    sortable: true,
    render: ({price, percentage}: any) => (
      <div className="text-right" style={{ paddingRight: '9px' }}>
        <p >{price}</p>
        <p className="text-red">{percentage}</p>
      </div>
    ),
  },
]
