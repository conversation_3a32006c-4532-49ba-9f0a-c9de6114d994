import Container from 'presentation/components/container'
import Heading from 'presentation/components/heading'
import LeaderBoardTable from './leaderBoardTable'
import TopBuyerBoard from './topBuyerBoard'
import TopSellerBoard from './topSellerBoard'
import { topSellerHeader } from './_leaderBoardTableHeader'

const LeaderboardGUI = (props:any) => {
  const {
    showHeadline = true,
    analyticTabs,
    filterTimeOptions,

    leaderBoardFilterParams = {},
    leaderBoardCurrentPage = 1,
    setLeaderBoardCurrentPage = () => {},
    leaderBoardCurrentLimit = 20,
    setLeaderBoardCurrentLimit = () => {},
    leaderBoardData = [],
    leaderBoardTotal = 0,
    onChangeLeaderBoardFilterParam = () => {},
    onChangeLeaderBoardFilterParams = () => {},
    isFetchingLeaderBoard = true,

    topBuyerCurrentPage = 1,
    setTopBuyerCurrentPage = () => {},
    topBuyerData = [],
    topBuyerTotal = 0,
    onChangeTopBuyerFilterParams = () => {},
    isFetchingTopBuyer = true,

    topSellerCurrentPage = 1,
    setTopSellerCurrentPage = () => {},
    topSellerData = [],
    topSellerTotal = 0,
    onChangeTopSellerFilterParams = () => {},
    isFetchingTopSeller = true,
  } = props

  return (
    <Container className={`${showHeadline && 'pt-9'} overflow-visible`}>
      {showHeadline && (
        <div className="text-center">
          <Heading>Leaderboard</Heading>
          <p className="mt-4 text-gray-1 dark:text-gray-3 text-stroke-thin">
            Leaderboard for overall profit and top traders over the selected time range.
          </p>
        </div>
      )}

      {analyticTabs && (
        <div className="mt-30p gap-5 flex flex-col justify-center lg:flex-row lg:justify-between items-center">
          {analyticTabs}
        </div>
      )}

      <LeaderBoardTable
        filterParams={leaderBoardFilterParams}
        currentPage={leaderBoardCurrentPage}
        onChangePage={setLeaderBoardCurrentPage}
        currentLimit={leaderBoardCurrentLimit}
        onChangeCurrentLimit={setLeaderBoardCurrentLimit}
        tableData={leaderBoardData}
        total={leaderBoardTotal}
        onChangeFilterParam={onChangeLeaderBoardFilterParam}
        onChangeFilterParams={onChangeLeaderBoardFilterParams}
        isFetching={isFetchingLeaderBoard}
      />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-10 mt-10">
        <TopBuyerBoard
          filterTimeOptions={filterTimeOptions}
          defaultTimeFilter={filterTimeOptions[3]}
          currentPage={topBuyerCurrentPage}
          setCurrentPage={setTopBuyerCurrentPage}
          tableData={topBuyerData}
          total={topBuyerTotal}
          onChangeFilterParam={onChangeTopBuyerFilterParams}
          isFetching={isFetchingTopBuyer}
        />
        <TopSellerBoard
          header={topSellerHeader}
          filterTimeOptions={filterTimeOptions}
          defaultTimeFilter={filterTimeOptions[3]}
          currentPage={topSellerCurrentPage}
          setCurrentPage={setTopSellerCurrentPage}
          tableData={topSellerData}
          total={topSellerTotal}
          onChangeFilterParam={onChangeTopSellerFilterParams}
          isFetching={isFetchingTopSeller}
        />
      </div>
    </Container>
  )
}

export default LeaderboardGUI
