import Image from 'next/image'
import CollectionTableItem from 'presentation/components/table/collection-item'

export const nftTableHead = [
  {
    title: '#',
    key: 'id',
  },
  {
    title: 'NFT',
    key: 'nft',
    render: ({ imageSrc, name }: any) => (
      <div className="flex items-center w-max">
        <Image alt={name} src={imageSrc} width={32} height={32} />
        <p className="font-medium ml-1">{name}</p>
      </div>
    ),
  },
  {
    title: 'Collection',
    key: 'collection',
    render: (record: any) => (
      <CollectionTableItem record={record} />
    ),
  },
  {
    title: 'Owner',
    key: 'owner',
  },
  {
    title: 'Last Price(24H)',
    key: 'lastprice24h',
    sortable: true,
    options: {
      sortKey: 'price',
    },
    render: ({ price, priceSymbol, usd }: any) => (
      <>
        <div className="w-full" style={{ paddingRight: '14px' }}>
          <p className="uppercase font-medium w-full text-right">{price} {priceSymbol}</p>
          <p className="text-sm w-full text-right">{usd}</p>
        </div>
      </>
    ),
  },
  {
    title: 'Highest Price(24H)',
    key: 'highestprice24h',
    render: ({ price, priceSymbol, usd }: any) => (
      <>
        <div className="w-full" style={{ paddingRight: '14px' }}>
          <p className="uppercase font-medium text-right">{price} {priceSymbol}</p>
          <p className="text-sm text-right">{usd}</p>
        </div>
      </>
    ),
  },
  {
    title: 'Sale (24H)',
    key: 'sale24h',
    render: ({ count }: any) => (
      <div className="w-full" style={{ paddingRight: '14px' }}>
        <p className="uppercase font-medium text-right">{count}</p>
      </div>
    ),
  },
  {
    title: 'Last Deal',
    key: 'lastdeal',
    render: ({ datetime }: any) => (
      <div className="w-full" style={{ paddingRight: '14px' }}>
        <p className="text-right">{datetime}</p>
      </div>
    ),
  },
]
