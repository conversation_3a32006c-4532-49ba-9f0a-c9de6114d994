import Link from 'next/link'
import {
  rowOptions,
  hourOptions,
} from 'presentation/controllers/mockData/options'
import Container from 'presentation/components/container'
import Heading from 'presentation/components/heading'
import Select from 'presentation/components/select'
import Table from 'presentation/components/table'
import Pagination from 'presentation/components/pagination'
import LoadingData from 'presentation/components/skeleton/loading'
import { collectionTableHeader } from './_collectionBoardHeader'

const CollectionBoardGUI = (props: any) => {
  const {
    filterParams,
    currentPage = 1,
    onChangePage = () => {},
    currentLimit = 20,
    onChangeCurrentLimit = () => {},
    tableData = [],
    total = 0,
    categories = [],
    onChangeFilterParam = () => {},
    onChangeFilterParams = () => {},
    isFetching = true,
    analyticTabs,
  } = props

  return (
    <Container className="pt-9 overflow-visible">
      <div className="text-center">
        <Heading>Top Collections</Heading>
        <p className="mt-4 text-gray-1 dark:text-gray-3 text-stroke-thin">
          Top collections by marketcap and other key indicators.
          <Link href="/collections" className="text-primary-2">
            View More
          </Link>
        </p>
      </div>
      <div className="mt-30p gap-5 flex flex-col justify-center lg:flex-row lg:justify-between items-center">
        {analyticTabs}
        <div className="flex flex-wrap lg:justify-end justify-center gap-2 md:gap-4 xl:gap-5">
          {categories.length > 0 && (
            <Select
              options={categories}
              onSelect={(option) =>
                onChangeFilterParam('categoryId', option.value)
              }
              className="shrink-0"
            />
          )}
          <Select options={hourOptions} />
          <Select
            defaultSelected={rowOptions[0]}
            options={rowOptions}
            onSelect={(option) => {
              onChangePage(1)
              onChangeCurrentLimit(option.value)
              onChangeFilterParams({
                ...filterParams,
                limit: option.value,
                page: 1,
              })
            }}
          />
        </div>
      </div>
      <div className="table-wrap analytics-table-wrap mt-5 border border-gray-3 rounded-20 text-dark-primary dark:text-white">
        {isFetching ? (
          <LoadingData />
        ) : (
          <>
            <div className="w-full overflow-x-auto pb-3">
              <Table
                head={collectionTableHeader}
                data={tableData}
                className="common-table collection min-w-[1785px]"
              />
            </div>
            <div className="px-9 lg:mt-0.5 mt-5 pb-9">
              <Pagination
                currentPage={currentPage}
                totalCount={total}
                pageSize={currentLimit}
                onPageChange={(page: number) => {
                  onChangePage(page)
                  onChangeFilterParams({ ...filterParams, page })
                }}
                onChangePageSize={(limit: number) => {
                  onChangePage(1)
                  onChangeCurrentLimit(limit)
                  onChangeFilterParams({ ...filterParams, limit, page: 1 })
                }}
              />
            </div>
          </>
        )}
      </div>
    </Container>
  )
}

export default CollectionBoardGUI
