import CollectionTableItem from 'presentation/components/table/collection-item'

export const newTableHeader = [
  {
    title: '#',
    key: 'id',
  },
  {
    title: 'Collection',
    key: 'collection',
    render: (record: any) => (
      <CollectionTableItem record={record} />
    ),
  },
  {
    title: 'Market Cap',
    key: 'marketCap',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    render: ({amount}: any) => (
      <div className="flex flex-wrap justify-end w-full" style={{ paddingRight: '14px' }}>
        <p>{amount}</p>
      </div>
    ),
  },
  {
    title: '24H',
    key: 'day',
    render: ({percentage}: any) => (
      <div className="flex flex-wrap justify-end w-full">
        <p className="text-red-1">{percentage}</p>
      </div>
    ),
  },
  {
    title: 'Traders(24H)',
    key: 'traders24h',
    render: ({amount}: any) => (
      <div className="flex flex-wrap justify-end w-full">
        <p>{amount}</p>
      </div>
    ),
  },
  {
    title: 'Volume(24H)',
    key: 'volume24h',
    render: ({amount}: any) => (
      <div className="flex flex-wrap justify-end w-full">
        <p>{amount}</p>
      </div>
    ),
  },
  {
    title: 'Liquidity(24H)',
    key: 'liquidity24h',
    render: ({amount}: any) => (
      <div className="flex flex-wrap justify-end w-full">
        <p>{amount}</p>
      </div>
    ),
  },
  {
    title: 'Added',
    key: 'added',
    sortable: true,
    render: ({datetime}: any) => (
      <div className="flex flex-wrap justify-end w-full" style={{ paddingRight: '14px' }}>
        <p>{datetime}</p>
      </div>
    ),
  },
]
