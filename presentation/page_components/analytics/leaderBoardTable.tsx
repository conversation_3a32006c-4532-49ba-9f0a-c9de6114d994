import { rowOptions } from 'lib/valueObjects'
import Select from 'presentation/components/select'
import Table from 'presentation/components/table'
import Pagination from 'presentation/components/pagination'
import LoadingData from 'presentation/components/skeleton/loading'
import Switch from 'presentation/components/switch'
import Tooltip from 'presentation/components/tooltip'
import { leaderBoardTableHeader } from './_leaderBoardTableHeader'

const LeaderBoardTable = (props:any) => {
  const {
    filterParams,
    currentPage = 1,
    onChangePage = () => {},
    currentLimit = 10,
    onChangeCurrentLimit = () => {},
    tableData = [],
    total = 0,
    onChangeFilterParam = () => {},
    onChangeFilterParams = () => {},
    isFetching = true,
  } = props

  const options = [{
    name: '10 rows',
    value: '10',
  }].concat(rowOptions)

  return (
    <>
      <div className="mt-5 flex gap-y-4 flex-wrap justify-between items-center">
        <div className="flex">
          <p className="text-lg font-bold">Profit Leaderboard</p>
          <Tooltip>Ananlytics profit leaderboard</Tooltip>
        </div>
        <div className="flex items-center">
          {/* TODO */}
          <Switch onClick={() => {}}/>
          {/* End TODO */}
          <div className="ml-1 mr-5 flex items-center">
            <p className="text-sm font-bold">Contact</p>
            <Tooltip>Contact information</Tooltip>
          </div>
          <Select
            defaultSelected={options[0]}
            options={options}
            onSelect={(option) => {
              onChangePage(1)
              onChangeCurrentLimit(option.value)
              onChangeFilterParams({...filterParams, limit: option.value, page: 1})
            }}
          />
        </div>
      </div>

      <div className="table-wrap mt-5 border border-gray-3 rounded-20 text-dark-primary dark:text-white">
        {isFetching ? (
          <LoadingData/>
        ) : (
          <>
            <div className="w-full overflow-x-auto pb-3">
              <Table
                head={leaderBoardTableHeader}
                data={tableData}
                className="common-table leaderboard min-w-[1180px]"
              />
            </div>
            <div className="px-9 lg:mt-0.5 mt-5 pb-9">
              <Pagination
                selectRowOptions={options}
                currentPage={currentPage}
                totalCount={total}
                pageSize={currentLimit}
                onPageChange={(page: number) => {
                  onChangePage(page)
                  onChangeFilterParams({...filterParams, page})
                }}
                onChangePageSize={(limit: number) => {
                  onChangePage(1)
                  onChangeCurrentLimit(limit)
                  onChangeFilterParams({...filterParams, limit, page: 1})
                }}
              />
            </div>
          </>
        )}
      </div>
    </>
  )
}

export default LeaderBoardTable
