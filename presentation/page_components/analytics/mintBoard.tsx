import Link from 'next/link'
import {
  rowOptions,
  filterMinHoursDay,
} from 'presentation/controllers/mockData/options'
import Container from 'presentation/components/container'
import Heading from 'presentation/components/heading'
import Select from 'presentation/components/select'
import Table from 'presentation/components/table'
import Pagination from 'presentation/components/pagination'
import LoadingData from 'presentation/components/skeleton/loading'
import Switch from 'presentation/components/switch'
import Tooltip from 'presentation/components/tooltip'
import FilterTime from 'presentation/components/filter-time'
import { mintTableHeader } from './_mintBoardHeader'

const MintBoardGUI = (props: any) => {
  const {
    filterParams,
    currentPage = 1,
    onChangePage = () => {},
    currentLimit = 20,
    onChangeCurrentLimit = () => {},
    tableData = [],
    total = 0,
    onChangeFilterParam = () => {},
    onChangeFilterParams = () => {},
    isFetching = true,
    analyticTabs,
  } = props

  return (
    <Container className="pt-9 overflow-visible">
      <div className="text-center">
        <Heading>Top Mints</Heading>
        <p className="mt-4 text-gray-1 dark:text-gray-3 text-stroke-thin">
          Top collections most addresses minted over the selected time range.
          <Link href="/analytics/mint" className="text-primary-2">
            View what whales minted.
          </Link>
        </p>
      </div>

      <div className="mt-30p gap-5 flex flex-col flex-wrap justify-center lg:flex-row lg:justify-between items-center">
        {analyticTabs}
        <div className="flex items-center flex-wrap gap-y-4">
          <Switch onClick={() => {}} />
          <div className="ml-1 mr-5 flex items-center">
            <p className="text-sm font-bold">Listed</p>
            <Tooltip>Contact information</Tooltip>
          </div>
          {/* TODO correct query param */}
          <FilterTime
            options={filterMinHoursDay}
            onSelect={(option) => onChangeFilterParam('sortBy', option.value)}
            defaultSelected={filterMinHoursDay[0]}
            className="mr-5"
          />
          {/* End todo correct query param */}
          <Select
            defaultSelected={rowOptions[0]}
            options={rowOptions}
            onSelect={(option) => {
              onChangePage(1)
              onChangeCurrentLimit(option.value)
              onChangeFilterParams({
                ...filterParams,
                limit: option.value,
                page: 1,
              })
            }}
          />
        </div>
      </div>

      <div className="table-wrap analytics-table-wrap mt-5 border border-gray-3 rounded-20 text-dark-primary dark:text-white">
        {isFetching ? (
          <LoadingData />
        ) : (
          <>
            <div className="w-full overflow-x-auto pb-3">
              <Table
                head={mintTableHeader}
                data={tableData}
                className="common-table mint min-w-[1200px]"
              />
            </div>
            <div className="px-9 lg:mt-0.5 mt-5 pb-9">
              <Pagination
                currentPage={currentPage}
                totalCount={total}
                pageSize={currentLimit}
                onPageChange={(page: number) => {
                  onChangePage(page)
                  onChangeFilterParams({ ...filterParams, page })
                }}
                onChangePageSize={(limit: number) => {
                  onChangePage(1)
                  onChangeCurrentLimit(limit)
                  onChangeFilterParams({ ...filterParams, limit, page: 1 })
                }}
              />
            </div>
          </>
        )}
      </div>
    </Container>
  )
}

export default MintBoardGUI
