export const leaderBoardTableHeader = [
  {
    title: '#',
    key: 'id',
  },
  {
    title: 'Address',
    key:'address',
    render: ({imageSrc, address}: any) => (
      <div className="max-w-[6rem] flex items-center">
        {/* <Image alt='' src={imageSrc} width={21} height={21}/> */}
        <p className="font-medium truncate">{address}</p>
      </div>
    ),
  },
  {
    title: 'PnL',
    key: 'pnl',
    description: 'PnL Description',
    sortable: true,
    options: {
      tooltipPosition: 'right',
    },
    render: ({price, percentage}: any) => (
      <>
        <div className="flex flex-wrap justify-end" style={{ paddingRight: '14px' }}>
          <p className="w-full text-right">{price}</p>
          <div className="percentage-bar">
            <div className="percentage-track" style={{width: percentage}}></div>
          </div>
        </div>
      </>
    ),
  },
  {
    title: 'Buy Volume',
    key: 'buyvolume',
    description: 'Buy Volume description',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: ({price, percentage}: any) => (
      <>
        <div className="flex flex-wrap justify-end" style={{ paddingRight: '14px' }}>
          <p>{price}</p>
          <div className="percentage-bar">
            <div className="percentage-track" style={{width: percentage}}></div>
          </div>
        </div>
      </>
    ),
  },
  {
    title: 'Sell Volume',
    key: 'sellvolume',
    description: 'Sell Volume description',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: ({price, percentage}: any) => (
      <>
        <div className="flex flex-wrap justify-end" style={{ paddingRight: '14px' }}>
          <p>{price}</p>
          <div className="percentage-bar">
            <div className="percentage-track" style={{width: percentage}}></div>
          </div>
        </div>
      </>
    ),
  },
  {
    title: 'Sent',
    key: 'sent',
    description: 'Sent description',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: ({count}: any) => (
      <>
        <div className="flex flex-wrap justify-end" style={{ paddingRight: '14px' }}>
          <p>{count}</p>
        </div>
      </>
    ),
  },
  {
    title: 'Received',
    key: 'received',
    description: 'Received description',
    options: {
      tooltipPosition: 'bottomRight',
    },
    sortable: true,
    render: ({count}: any) => (
      <>
        <div className="flex flex-wrap justify-end" style={{ paddingRight: '14px' }}>
          <p>{count}</p>
        </div>
      </>
    ),
  },
  {
    title: 'Minted',
    key: 'minted',
    description: 'Minted description',
    options: {
      tooltipPosition: 'bottomRight',
    },
    sortable: true,
    render: ({count}: any) => (
      <>
        <div className="flex flex-wrap justify-end" style={{ paddingRight: '14px' }}>
          <p>{count}</p>
        </div>
      </>
    ),
  },
]

export const topBuyerHeader = [
  {
    title: '#',
    key: 'id',
  },
  {
    title: 'Address',
    key:'address',
    render: ({imageSrc, address}: any) => (
      <div className="max-w-[6rem] flex items-center">
        {/* <Image alt='' src={imageSrc} width={21} height={21}/> */}
        <p className="font-medium truncate">{address}</p>
      </div>
    ),
  },
  {
    title: 'Buy Volume',
    key: 'buyvolume',
    sortable: true,
    description: 'Buy volume description',
    options: {
      tooltipPosition: 'right',
    },
    render: ({price, percentage}: any) => (
      <div className="flex flex-wrap justify-end w-full" style={{ paddingRight: '14px' }}>
        <p>{price}</p>
        <div className="percentage-bar">
          <div className="percentage-track" style={{width: percentage}}></div>
        </div>
      </div>
    ),
  },
  {
    title: 'Bought',
    key: 'bought',
    description: 'Bought description',
    sortable: true,
    options: {
      tooltipPosition: 'bottomRight',
    },
    render: ({count}: any) => (
      <div className="w-full" style={{ paddingRight: '14px' }}>
        <p className="text-right">{count}</p>
      </div>
    ),
  },
]

export const topSellerHeader = [
  {
    title: '#',
    key: 'id',
  },
  {
    title: 'Whale',
    key:'address',
    render: ({imageSrc, address}: any) => (
      <div className="max-w-[6rem] flex items-center">
        {/* <Image alt='' src={imageSrc} width={21} height={21}/> */}
        <p className="font-medium truncate">{address}</p>
      </div>
    ),
  },
  {
    title: 'Sell Volume',
    key: 'sellvolume',
    sortable: true,
    description: 'Buy volume description',
    options: {
      tooltipPosition: 'right',
    },
    render: ({price, percentage}: any) => (
      <div className="flex flex-wrap justify-end w-full" style={{ paddingRight: '14px' }}>
        <p>{price}</p>
        <div className="percentage-bar">
          <div className="percentage-track" style={{width: percentage}}></div>
        </div>
      </div>
    ),
  },
  {
    title: 'Sold',
    key: 'sold',
    description: 'Bought description',
    sortable: true,
    options: {
      tooltipPosition: 'bottomRight',
    },
    render: ({count}: any) => (
      <div className="w-full">
        <p className="text-right">{count}</p>
      </div>
    ),
  },
]
