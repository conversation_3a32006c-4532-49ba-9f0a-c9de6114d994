import CollectionTableItem from 'presentation/components/table/collection-item'

export const mintTableHeader = [
  {
    title: '#',
    key: 'id',
  },
  {
    title: 'Collection',
    key: 'collection',
    render: (record: any) => (
      <CollectionTableItem record={record} />
    ),
  },
  {
    title: 'Minted',
    key: 'minted',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: ({count}: any) => (
      <div className="flex flex-wrap justify-end w-full" style={{ paddingRight: '14px' }}>
        <p className="text-right">{count}</p>
      </div>
    ),
  },
  {
    title: 'Mint Volume',
    key: 'mintVolume',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: ({price, percentage}: any) => (
      <div className="flex flex-wrap justify-end w-full" style={{ paddingRight: '14px' }}>
        <p>{price}</p>
        <div className="percentage-bar">
          <div className="percentage-track" style={{width: percentage}}></div>
        </div>
      </div>
    ),
  },
  {
    title: 'Minters',
    key: 'minters',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: ({count}: any) => (
      <div className="flex flex-wrap justify-end w-full" style={{ paddingRight: '14px' }}>
        <p className="text-right">{count}</p>
      </div>
    ),
  },
  {
    title: 'Whales',
    key: 'whales',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: ({count}: any) => (
      <div className="flex flex-wrap justify-end w-full" style={{ paddingRight: '14px' }}>
        <p className="text-right">{count}</p>
      </div>
    ),
  },
  {
    title: 'Total Gas',
    key: 'totalGas',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: ({price, percentage}: any) => (
      <div className="flex flex-wrap justify-end w-full" style={{ paddingRight: '14px' }}>
        <p>{price}</p>
        <div className="percentage-bar">
          <div className="percentage-track" style={{width: percentage}}></div>
        </div>
      </div>
    ),
  },
  {
    title: 'First Mint',
    key: 'firstMint',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'bottomRight',
    },
    sortable: true,
    render: ({datetime}: any) => (
      <div className="flex flex-wrap justify-end w-full" style={{ paddingRight: '14px' }}>
        <p className="text-right">{datetime}</p>
      </div>
    ),
  },
  {
    title: 'FOMO',
    key: 'fomo',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'bottomRight',
    },
    sortable: true,
    render: ({level}: any) => (
      <div className="flex flex-wrap justify-end w-full" style={{ paddingRight: '14px' }}>
        <p className={`capitalize text-${level}`}>{level}</p>
      </div>
    ),
  },
]
