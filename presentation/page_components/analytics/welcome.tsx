import Container from 'presentation/components/container'
import Image from 'next/image'

const Welcome = () => {
  return (
    <Container className="wellcome-section">
      <div className="flex gap-5 flex-col-reverse md:flex-row items-center">
        <div className="w-full md:w-1/2 lg:pr-32">
          <div className="relative w-full h-[17rem]">
            <Image alt="wellcome" src="/asset/images/marketplace-mall-preview-img.svg" fill style={{ objectFit: 'contain' }} />
          </div>
        </div>
        <div className="md:w-1/2 content-right">
          <div>
            <h1 className="heading-brandon text-dark-primary dark:text-white">Welcome！NFT Marketplace Mall</h1>
            <p className="mt-8 text-gray-1 dark:text-gray-3 xl:pr-12">
              VIEW YOUR COLLECTION<br />In the Mythical Marketplace you can now view all your items and manage your inventory via My Collection.
            </p>
          </div>
        </div>
      </div>
    </Container>
  )
}

export default Welcome
