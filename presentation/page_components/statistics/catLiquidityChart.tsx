import dynamic from 'next/dynamic'
import FilterTime from 'presentation/components/filter-time'
import { filterDay } from 'presentation/controllers/mockData/options'
const Column = dynamic(() => import('@ant-design/plots').then(({ Column }) => Column), { ssr: false })

const CatLiquidityChart = (props:any) => {
  const {
    chartData = [],
    filterTimeOptions = [],
    onChangeFilterParam = () => {},
  } = props

  const config: any = {
    height: 350,
    data: chartData,
    xField: 'type',
    yField: 'sales',
    label: false,
    columnStyle: {
      fill: 'l(90) 0:#9DDEE0 1:rgba(157,222,224,0.1)',
      fillOpacity: 1,
      radius: 10,
    },
    xAxis: {
      tickCount: 14,
      label: {
        autoHide: true,
        autoRotate: false,
      },
    },
    yAxis: {
      position: 'right',
    },
    meta: false,
  }

  return (
    <div className="rounded-10 py-4 mt-10 border border-gray-3">
      <div className="px-5 flex justify-between items-center">
        <p className="text-lg font-bold">Category Liquidity</p>
        <FilterTime
          options={filterTimeOptions}
          defaultSelected={filterTimeOptions[0]}
          showDatepicker={true}
          onSelect={(option) => {
            //TODO onChangeFilterParam(option)
          }}
        />
      </div>
      <div className="mt-10 chart-space">
        <Column {...config} />
      </div>
    </div>
  )
}

export default CatLiquidityChart
