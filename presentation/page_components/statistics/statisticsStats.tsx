import clsx from 'clsx'
import FilterTime from 'presentation/components/filter-time'
import { filterDays } from 'presentation/controllers/mockData/options'
import Tooltip from 'presentation/components/tooltip'

//Todo remove mock data
import { statisticCards, statisticCardsRight } from 'presentation/controllers/mockData/statistics_mock_data'
//End todo remove mock data

const StatisticsStats = () => {
  return (
    <div className="rounded-10 pt-10p px-5 mt-30p border border-gray-3">
      <div className="flex flex-wrap justify-between sm:space-y-0 space-y-3">
        <p className="text-23 font-bold sm:w-auto w-full">Statistics</p>
        <FilterTime options={filterDays} defaultSelected={filterDays[0]} onSelect={() => {}}/>
      </div>
      <div className="statistic-cont">
        <div className="statistic-left">
          <div className="sm:w-2/6 w-full">
            <div className="flex items-center">
              <p className="text-stroke-thin">Market Cap</p>
              <Tooltip>Market cap</Tooltip>
            </div>
            <p className="text-dark-primary dark:text-white sm:text-lg lg:text-xl font-bold mt-1">
              $16.95B
            </p>
            <p className="text-increase font-medium">+0.02%</p>
          </div>

          <div className="sm:w-4/6 w-full grid sm:grid-cols-2 grid-cols-1 gap-5">
            {statisticCards.map((card, index) => (
              <div key={index} className="flex flex-col justify-between items-center px-5 py-[13px] rounded-10 bg-blue-2 dark:bg-blue-1">
                <p className={clsx('font-bold sm:text-lg lg:text-xl',
                  card.textColor ? card.textColor : 'text-dark-primary dark:text-white')}>
                  {card.totalValue}
                </p>
                <p className={`font-medium text-${card.type} ${!card.percentage && 'py-3'}`}>
                  {card.percentage}
                </p>
                <div className="inline-flex items-center justify-end">
                  <p className="text-sm text-gray-1 dark:text-gray-3 whitespace-nowrap text-stroke-thin">
                    {card.field}
                  </p>
                  <Tooltip className="-mt-0.5">{card.description}</Tooltip>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="statistic-right">
          <div>
            <div className="flex items-center">
              <p className="text-stroke-thin">Volume(24H)</p>
              <Tooltip>Market cap</Tooltip>
            </div>
            <p className="text-dark-primary dark:text-white sm:text-lg lg:text-xl font-bold mt-1">
              $140,757,128.46
            </p>
            <p className="text-decrease font-medium">-9.88%</p>
          </div>
          {statisticCardsRight.map((card, index) => (
            <div key={index} className="flex flex-col justify-between items-center px-5 py-[13px] rounded-10 bg-blue-2 dark:bg-blue-1">
              <p className="font-bold sm:text-lg lg:text-xl text-dark-primary dark:text-white">
                {card.totalValue}
              </p>
              <p className={`font-medium text-${card.type} ${!card.percentage && 'py-3'}`}>
                {card.percentage}
              </p>
              <div className="flex items-center justify-end">
                <p className="text-sm text-gray-1 dark:text-gray-3 whitespace-nowrap text-stroke-thin">
                  {card.field}
                </p>
                <Tooltip className="-mt-0.5">{card.description}</Tooltip>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default StatisticsStats

