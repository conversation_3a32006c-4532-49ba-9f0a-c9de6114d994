import dynamic from 'next/dynamic'
import FilterTime from 'presentation/components/filter-time'
const Area = dynamic(() => import('@ant-design/plots').then(({ Area }) => Area), { ssr: false })

const TrendChart = (props:any) => {
  const {
    chartData = [],
    filterTimeOptions = [],
    filterTypes = [],
    onChangeFilterParam = () => {},
  } = props

  const config: any = {
    height: 378,
    data: chartData,
    xField: 'Date',
    yField: 'scales',
    xAxis: {
      tickCount: 13,
    },
    yAxis: {
      position: 'right',
    },
    areaStyle: {
      fill: 'l(90) 0:#B3B9ED 0.7:rgba(179,185,237, 0.7) 1:rgba(179,185,237, 0.1)',
      fillOpacity: 0.7,
    },
    animation: true,
    slider: {
      start: 0,
      end: 1,
      trendCfg: {
        isArea: true,
      },
      height: 30,
      textStyle: false,
    },
  }

  return (
    <div className="rounded-10 py-4 mt-10 border border-gray-3">
      <div className="px-5 flex justify-between items-center">
        <p className="text-lg font-bold">Trends</p>
        <FilterTime
          options={filterTimeOptions}
          defaultSelected={filterTimeOptions[0]}
          showDatepicker={true}
          onSelect={(option) => {
            //TODO onChangeFilterParam(option)
          }}
        />
      </div>
      <div className="mt-4 chart-space">
        <FilterTime
          className="w-fit mb-2 ml-11"
          options={filterTypes}
          defaultSelected={filterTypes[0]}
          onSelect={(option) => {
            //TODO onChangeFilterParam(option)
          }}
        />
        <Area {...config} />
      </div>
    </div>
  )
}

export default TrendChart
