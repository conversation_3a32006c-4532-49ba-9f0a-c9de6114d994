import dynamic from 'next/dynamic'
import { useState, useRef, memo } from 'react'
import FilterTime from 'presentation/components/filter-time'
const Pie = dynamic(() => import('@ant-design/plots').then(({ Pie }) => Pie), { ssr: false })

const MemoPlot = memo(({ config, getChartRef}: any) => {
  return <Pie {...config} onReady={(chartRef) => getChartRef(chartRef)} />
}, (pre, next) => {
  return JSON.stringify(pre.config) === JSON.stringify(next.config)
})

MemoPlot.displayName = 'MemoPlot'

const CatByValuePie = (props:any) => {
  const {
    filterCollectionCategory = [],
    filterValueVolume = [],
    chartData = [],
  } = props

  const ref = useRef<any>(null)
  const [legendData, setLegendData] = useState([])

  const hasWindow = typeof window !== 'undefined'
  function getWindowDimensions () {
    const width = hasWindow ? window.innerWidth : 0
    const height = hasWindow ? window.innerHeight : 0
    return {
      width,
      height,
    }
  }
  let windowDimensions = getWindowDimensions()
  const [chartWidth] = useState(windowDimensions.width < 768 ? 280 : 369)
  const [chartHeight] = useState(windowDimensions.width < 768 ? 280 : 369)

  const getChartRef = (chartRef: any) => {
    if (!chartRef) return
    ref.current = chartRef

    const chart = ref.current.chart
    const legendData = chart.geometries[0]?.dataArray?.map((item: any) => {
      const origin = item[0]._origin
      origin.color = item[0].color
      origin.checked = true
      return origin
    })
    setLegendData(legendData)
  }

  const handleLegendClick = (item: any, i: number) => {
    const newItem = item
    newItem.checked = !newItem.checked
    const currentLegend: any = [...legendData]
    currentLegend[i] = newItem

    const filteredLegendData = currentLegend
      .filter((l: any) => l.checked)
      .map((l: any) => l.type)

    const chart = ref.current.chart
    if (chart) {
      chart.filter('type', (val:any) => {
        return filteredLegendData.indexOf(val) > -1
      })
      chart.render()
    }
    setLegendData(currentLegend)
  }

  const config: any = {
    width: chartWidth,
    height: chartHeight,
    appendPadding: 10,
    data: chartData,
    angleField: 'value',
    colorField: 'type',
    color: (...args:any) => {
      const type = args[0].type
      switch (type) {
        case 'PFP':
          return '#6C26FF'
        case 'Collectibles':
          return '#3747FF'
        case 'Art':
          return '#3F89FF'
        case 'Game':
          return '#3ECBFF'
        case 'Utility':
          return '#59E6EF'
        case 'Metaverse':
          return '#F47FE0'
        case 'Land':
          return '#FF6696'
        case 'IP':
          return '#FF9889'
        case 'Social':
          return '#FFBD9A'
        case 'Music':
          return '#FFE19D'
        default:
          return 'transparent'
      }
    },
    radius: 1,
    innerRadius: 0.64,
    meta: false,
    label: false,
    statistic: false,
    legend: false,
    interactions: [
      {
        type: 'element-selected',
      },
      {
        type: 'element-active',
      },
      {
        type: 'pie-statistic-active',
      },
    ],
    pieStyle: {
      lineWidth: 0,
    },
  }

  return (
    <div className="rounded-10 py-4 mt-10 border border-gray-3">
      <div className="px-5 flex flex-wrap justify-between items-center sm:space-y-0 space-y-3">
        <p className="text-lg font-bold">Category Share by Value</p>
        <div className="flex flex-wrap gap-5">
          <FilterTime
            options={filterCollectionCategory}
            defaultSelected={filterCollectionCategory[0]}
            onSelect={() => {}}
          />
          <FilterTime
            options={filterValueVolume}
            defaultSelected={filterValueVolume[0]}
            onSelect={() => {}}
          />
        </div>
      </div>

      <div className="flex flex-wrap mt-30p md:gap-0 gap-3">
        <div className="flex items-center justify-center md:w-4/6 w-full">
          <MemoPlot config={config} getChartRef={getChartRef} />
        </div>
        <div className="flex items-center md:justify-start justify-center md:w-2/6 w-full pr-4">
          <ul className="flex flex-col md:space-y-3.5 space-y-2 w-40 flex-none">
            {legendData?.map((item: any, i: number) => (
              <li
                key={i} onClick={() => handleLegendClick(item, i)}
                className={'inline-flex justify-between cursor-pointer'}
              >
                <div className="inline-flex items-center truncate">
                  <span
                    className="rounded-full w-2 h-2 mr-2 flex-none"
                    style={{ backgroundColor: item.checked ? item.color : '#aaa' }}
                  ></span>
                  <span className="font-medium text-sm truncate">{item.type}</span>
                </div>
                <span className="font-medium w-16 flex-none text-right">{item.value}%</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  )
}

export default CatByValuePie
