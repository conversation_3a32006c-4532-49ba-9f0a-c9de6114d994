import dynamic from 'next/dynamic'
import FilterTime from 'presentation/components/filter-time'
const Area = dynamic(() => import('@ant-design/plots').then(({ Area }) => Area), { ssr: false })

const TradersChart = (props:any) => {
  const {
    chartData = [],
    filterTimeOptions = [],
    onChangeFilterParam = () => {},
  } = props

  const config: any = {
    data: chartData,
    xField: 'date',
    yField: 'value',
    xAxis: {
      tickCount: 10,
    },
    yAxis: {
      position: 'right',
      tickCount: 7,
      label: {
        formatter: (v: any) => `${v}`.replace(/\d{1,3}(?=(\d{3})+$)/g, (s) => `${s},`),
      },
    },
    seriesField: 'type',
    color: ['#5AC8CB', '#AC75F7', '#5A69F0'],
    legend: {
      offsetX: 30,
      marker: {
        symbol: 'circle',
      },
    },
    areaStyle: ({type}: any) => {
      switch (type) {
        case 'All':
          return {
            fill: 'l(90) 0:#9DDEE0 1:rgba(157,222,224,0.1)',
            fillOpacity: 0.7,
          }
        case 'Seller':
          return {
            fill: 'l(90) 0:#D2D0F7 1:rgba(210,208,247,0.1)',
            fillOpacity: 0.7,
          }
        default:
          return {
            fill: 'l(90) 0:#B3B9ED 1:rgba(179,185,237,0.1)',
            fillOpacity: 0.7,
          }
      }

    },
    slider: {
      start: 0,
      end: 1,
      height: 30,
      textStyle: false,
      handlerStyle: {
        fill: '#ffffff',
      },
    },
  }

  return (
    <div className="rounded-10 py-4 mt-10 border border-gray-3">
      <div className="px-5 flex justify-between items-center">
        <p className="text-lg font-bold">Traders</p>
        <FilterTime
          options={filterTimeOptions}
          defaultSelected={filterTimeOptions[0]}
          showDatepicker={true}
          onSelect={(option) => {
            //TODO onChangeFilterParam(option)
          }}
        />
      </div>
      <div className="mt-4 chart-space">
        <Area {...config} />
      </div>
    </div>
  )
}

export default TradersChart
