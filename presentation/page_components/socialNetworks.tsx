import { useState, useEffect } from 'react'
import ButtonIcon from 'presentation/components/button/button-icon'
import { isURL, isUrlDiscord, isUrlInstagram, isUrlXUser, isUrlMedium, isUrlTelegram} from '../../lib/validate'

const SocialNetworks = (props: any) => {
  const {
    webUrl = '',
    discordUrl = '',
    twitterUrl = '',
    instagramUsername = '',
    twitterUsername = '',
    telegramUrl = '',
    mediumUsername = '',
    mintUrl = '',
    iconSize = 'w-5 h-5',
    spacing = 'mr-3',
    hasShadow = true,
  } = props
  const [linkTelegram, setLinkTelegram] = useState<string>('')
  const [linkInstagram, setLinkInstagram] = useState<string>('')
  const [linkTwitter, setLinkTwitter] = useState<string>('')
  const [linkMedium, setLinkMedium] = useState<string>('')

  const formatLinkTelegram = () => {
    if(!telegramUrl) return setLinkTelegram('')
    if(isUrlTelegram(telegramUrl)) return setLinkTelegram(telegramUrl)
    return setLinkTelegram(`https://t.me/${telegramUrl}`)
  }
  const formatLinkTwitter = () => {
    if(!twitterUsername) return setLinkTwitter('')
    if(isUrlXUser(twitterUsername)) return setLinkTwitter(twitterUsername)
    return setLinkTwitter(`https://x.com/${twitterUsername}`)
  }
  const formatLinkInstagram = () => {
    if(!instagramUsername) return setLinkInstagram('')
    if(isUrlInstagram(instagramUsername)) return setLinkInstagram(instagramUsername)
    return setLinkInstagram(`https://www.instagram.com/${instagramUsername}`)
  }
  const formatLinkMedium = () => {
    if(!mediumUsername) return setLinkMedium('')
    if(isUrlMedium(mediumUsername)) return setLinkMedium(mediumUsername)
    return setLinkMedium(`https://medium.com/@${mediumUsername}`)
  }
  useEffect(() => {
    formatLinkTwitter()
  }, [twitterUsername])
  
  useEffect(() => {
    formatLinkInstagram()
  }, [instagramUsername])

  useEffect(() => {
    formatLinkMedium()
  }, [mediumUsername])
  useEffect(() => {
    formatLinkTelegram()
  }, [telegramUrl])
  return (
    <>
      {isURL(webUrl) && (
        <ButtonIcon
          href={webUrl} hasShadow={hasShadow} svgIcon="globe"
          className={`${spacing} h-fit ${hasShadow && 'bg-white'}`}
          iconClassName={`${iconSize} stroke-none fill-primary-2`}
        />
      )}
      {isUrlDiscord(discordUrl) && (
        <ButtonIcon
          href={discordUrl} hasShadow={hasShadow} svgIcon="discord"
          className={`${spacing} h-fit ${hasShadow && 'bg-white'}`}
          iconClassName={`${iconSize} stroke-none fill-primary-2`}
        />
      )}
      {isUrlXUser(twitterUrl) && (
        <ButtonIcon
          href={twitterUrl} hasShadow={hasShadow} svgIcon="twitter"
          className={`${spacing} h-fit ${hasShadow && 'bg-white'}`}
          iconClassName={`${iconSize} stroke-none fill-primary-2`}
        />
      )}
      {isUrlInstagram(linkInstagram) && (
        <ButtonIcon
          href={linkInstagram}
          hasShadow={hasShadow} svgIcon="instagram"
          className={`${spacing} h-fit ${hasShadow && 'bg-white'}`}
          iconClassName={`${iconSize} stroke-none fill-primary-2`}
        />
      )}
      {isUrlXUser(linkTwitter) && (
        <ButtonIcon
          href={linkTwitter}
          hasShadow={hasShadow} svgIcon="twitter"
          className={`${spacing} h-fit ${hasShadow && 'bg-white'}`}
          iconClassName={`${iconSize} stroke-none fill-primary-2`}
        />
      )}
      {isUrlTelegram(linkTelegram) && (
        <ButtonIcon
          href={linkTelegram} hasShadow={hasShadow} svgIcon="telegram"
          className={`${spacing} h-fit ${hasShadow && 'bg-white'}`}
          iconClassName={`${iconSize} stroke-none fill-primary-2`}
        />
      )}
      {isUrlMedium(linkMedium) && (
        <ButtonIcon
          href={linkMedium} hasShadow={hasShadow} svgIcon="medium"
          className={`${spacing} h-fit ${hasShadow && 'bg-white'}`} iconClassName={`${iconSize} stroke-none fill-primary-2`}
        />
      )}
      {isURL(mintUrl) && (
        <ButtonIcon
          href={mintUrl} hasShadow={hasShadow} svgIcon="coin"
          className={`${spacing} h-fit ${hasShadow && 'bg-white'}`}
          iconClassName={`${iconSize} stroke-none fill-primary-2`}
        />
      )}
    </>
  )
}

export default SocialNetworks
