import { useAuthentication } from 'lib/hook/auth_hook'
import Image from 'next/image'
import Modal from 'presentation/components/modal'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'
import UserUseCase from 'presentation/use_cases/user_use_case'

const userUseCase = new UserUseCase()

const SelectWalletModal = ({ isShow, onClose }: any) => {
  const { login } = useAuthentication()

  const authStateProvider = AuthStateProvider((state) => ({
    init: state.init,
  }))
  const handleConnectWallet = async (walletType: string = 'metamask') => {
    onClose()
    await login(walletType)
  }

  return (
    <Modal title="Connect Wallet" isShow={isShow} onClose={onClose}>
      <div className="py-3 w-full sm:w-[400px] flex justify-around">
        <button
          className="text-center hover:-translate-y-1 transition-all"
          onClick={() => handleConnectWallet('metamask')}
        >
          <Image
            alt=""
            src="/asset/images/metamask.png"
            width={70}
            height={70}
          />
          <span className="mt-2 font-medium text-dark-primary">MetaMask</span>
        </button>
        <button
          className="text-center hover:-translate-y-1 transition-all flex justify-center items-center flex-col"
          onClick={() => handleConnectWallet('walletconnect')}
        >
          <Image
            alt=""
            src="/asset/images/walletconnect.png"
            width={60}
            height={60}
          />
          <span className="mt-2 font-medium text-dark-primary">Wallet Connect</span>
        </button>
      </div>
    </Modal>
  )
}

export default SelectWalletModal
