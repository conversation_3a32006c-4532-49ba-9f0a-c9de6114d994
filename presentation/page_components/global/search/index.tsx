import clsx from 'clsx'
import React, { useRef, useState, useEffect, MutableRefObject } from 'react'
import Search from 'presentation/components/search'
import SearchResultHeader from 'presentation/components/search/search-result-header'
import AssetUseCase from 'presentation/use_cases/asset_use_case'
import CollectionUseCase from 'presentation/use_cases/collection_use_case'
import UserUseCase from 'presentation/use_cases/user_use_case'
import { Query as AssetQuery } from 'domain/repositories/asset_repository/query'
import { useRouter } from 'next/router'
import Asset from 'domain/models/asset'
import Collection from 'domain/models/collection'
import User from 'domain/models/user'
import { NetworkStateProvider } from 'presentation/providers/network_state_provider'
import { isAddress } from 'lib/validate'
import { getChainName } from 'lib/utils'
import { toast } from 'react-toastify'
const IS_TESTNET = process.env.NEXT_PUBLIC_API_URL !== 'https://api.quillnft.net/v1'
const assetUseCase = new AssetUseCase()
const collectionUseCase = new CollectionUseCase()
const userUseCase = new UserUseCase()

const GlobalSearch = () => {
  const router = useRouter()

  const [isLoading, setIsLoading] = useState(false)
  const [showResult, setShowResult] = useState(false)
  const [searchResultAssets, setSearchResultAssets] = useState<Asset[]>([])
  const [searchResultCollections, setSearchResultCollections] = useState<Collection[]>([])
  const [searchResultUsers, setSearchResultUsers] = useState<User[]>([])
  let keyword: string

  // Get network state from provider
  const networkStateProvider = NetworkStateProvider((state) => ({
    network: state.network,
  }))

  const searchResultRef: MutableRefObject<null | any> = useRef(null)

  const handleSearch = async (e: any) => {
    keyword = e.target.value
    keyword.trim().length === 0 ? setShowResult(false) : setShowResult(true)
    setIsLoading(true)
    const isExact = false
    
    // Get chainId from network provider
    const chainId = networkStateProvider.network?.chainId || ''
    console.log('🚀 ~ handleSearch ~ chainId:', chainId)
    
    await collectionUseCase
      .findBySlug(keyword, isExact, chainId)
      .then((res) => {
        if (isExact) {
          setSearchResultCollections([res])
        } else { 
          setSearchResultCollections(res)
        }
      })
      .catch(() => {
        setSearchResultCollections([])
      })
    const query = new AssetQuery({ keywords: [keyword], chainId: chainId })
    await assetUseCase
      .search(query)
      .then((res) => {
        setSearchResultAssets(res.slice(0, 5))
      })
      .catch(() => {
        setSearchResultAssets([])
      })
    await userUseCase
      .findByUsername(keyword, isExact)
      .then((res) => {
        if (isExact) {
          setSearchResultUsers([res])
        } else { 
          setSearchResultUsers(res)
        }
      })
      .catch(() => {
        setSearchResultUsers([])
      })
    setIsLoading(false)
    setShowResult(true)
  }
  const changeToImportCollectionFromAddress = (keyword: string) => {
    const chainId = networkStateProvider.network?.chainId || ''
    console.log('🚀 ~ handleSearch ~ chainId:', chainId)
    let chainName = getChainName(chainId)
    console.log('🚀 ~ changeToImportCollectionFromAddress ~ chainName:', chainName)
    if(chainName === 'unknown') {
      chainName = 'ethereum'
    }
    console.log('🚀 ~ changeToImportCollectionFromAddress ~ chainNameAfter:', chainName)
    // go to import collection from address page
    router.push(`/collections/${chainName}/${keyword}`)
   
  }
  const handleEnter = async (e: any) => {
    if(isLoading) {
      return
    } 
    const keyword = e.target.value
    const isInputAddress = keyword.startsWith('0x') && isAddress(keyword)
    if(searchResultAssets.length ===0 && isInputAddress) {
      changeToImportCollectionFromAddress(keyword)
      return
    }
    setIsLoading(true)
    router.push({
      pathname: '/assets',
      query: { search: keyword.toString() },
    })
    e.target.value = ''
    setShowResult(false)
  }

  useEffect(() => {
    function handleClickOutside (e: any) {
      if (
        searchResultRef.current &&
        !searchResultRef.current.contains(e.target)
      ) {
        setShowResult(false)
      }
    }
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [searchResultRef])

  return (
    <>
      <div className="search">
        <div className="relative">
          <Search
            onChange={handleSearch}
            handleEnter={handleEnter}
            inputClass="search-input"
          />
          <SearchResultHeader
            searchResultAssets={searchResultAssets}
            searchResultCollections={searchResultCollections}
            searchResultUsers={searchResultUsers}
            showResult={showResult}
            ref={searchResultRef}
            loading={isLoading}
            className={clsx(
              'bg-slate-50 max-h-44 overflow-y-scroll transition duration-200 ease-in',
              showResult
                ? 'visible translate-y-0 opacity-100'
                : 'invisible -translate-y-2 opacity-0'
            )}
          />
        </div>
      </div>
    </>
  )
}

export default GlobalSearch
