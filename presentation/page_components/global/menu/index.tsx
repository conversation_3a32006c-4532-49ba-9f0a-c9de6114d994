import { useState, useEffect } from 'react'
import DropdownWrap from 'presentation/components/dropdown/dropdown-wrap'
import DropdownHead from 'presentation/components/dropdown/dropdown-head'
import MenuItem from './menuItem'
import SvgIcon from 'presentation/components/svg-icon'
import { NetworkStateProvider } from 'presentation/providers/network_state_provider'
import Chain from 'domain/value_objects/chain'

const Menu = ({ menu = [] }:any) => {
  const [displayMenu, setDisplayMenu] = useState(false)
  const [availableChains, setAvailableChains] = useState<any[]>([])
  
  // Get current network from provider
  const networkStateProvider = NetworkStateProvider((state) => ({
    network: state.network,
  }))
  
  // Get all available chains for the current environment
  useEffect(() => {
    const chains = Chain.getResourceArray()
    setAvailableChains(chains)
  }, [])
  
  // Filter menu items based on network requirements
  const filteredMenu = menu.filter((item: any) => {
    // If item is not network specific, always show it
    if (!item.isNetworkSpecific) {
      return true
    }
    
    // If network info isn't available yet, hide network specific items
    if (!networkStateProvider.network || !availableChains.length) {
      return false
    }
    
    // Get the current network info
    const currentNetworkName = networkStateProvider.network.name || ''
    const currentChain = availableChains.find((chain) => chain.name === currentNetworkName)
    
    if (!currentChain) {
      return false
    }
    
    // Check if the current network chainId is in the supported networks list
    return item.supportedNetworks?.some((supportedChainId: number) => 
      supportedChainId === currentChain.id
    )
  })

  return (
    <>
      <div className="menu md:mr-5 md:block hidden">
        <DropdownWrap
          head={<DropdownHead
            title="Menu" hideTitleMobile icon="hamburger1"
            iconClass="icon-21 stroke-none fill-dark-primary dark:fill-white"
          />}
          content={
            <ul className="menu-ul">
              {filteredMenu.map((record:any, index:number) => (
                <li key={index} className="mt-4">
                  <MenuItem record={record} />
                </li>
              ))}
            </ul>
          }
        />
      </div>
      <div className="menu -mt-0.5 md:mr-5 md:hidden block">
        <span onClick={() => setDisplayMenu(true)}>
          <SvgIcon name="hamburger1" className="icon-21 stroke-none fill-dark-primary dark:fill-white" />
        </span>
        {displayMenu && (
          <div className="fixed top-0 left-0 w-screen h-screen bg-white dark:bg-dark-primary overflow-hidden z-[90]">
            <div className="flex flex-col justify-start items-center overflow-y-auto w-full h-full">
              <div className="container-df header-wrapper w-full flex justify-end">
                <SvgIcon
                  onClick={() => setDisplayMenu(false)}
                  name="plus"
                  className="w-9 h-9 stroke-none fill-dark-primary dark:fill-white rotate-45 xs2:mt-0 -mt-1 -mr-2"
                />
              </div>
              <div className="flex justify-center px-4 pb-4 w-full">
                <ul className="menu-ul text-center w-full">
                  {filteredMenu.map((record:any, index:number) => (
                    <li key={index} className="mt-4">
                      <MenuItem record={record} closeMenu={() => setDisplayMenu(false)} />
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  )
}

export default Menu
