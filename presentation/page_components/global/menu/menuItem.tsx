import { useState } from 'react'
import clsx from 'clsx'
import Link from 'next/link'
import SvgIcon from 'presentation/components/svg-icon'

interface MenuItemProps {
  record: any;
  closeMenu?: () => void;
}

const MenuItem = ({ record, closeMenu }: MenuItemProps) => {
  const [displayChildren, setDisplayChildren] = useState(false)

  const handleLinkClick = () => {
    if (closeMenu) {
      closeMenu()
    }
  }

  return (
    <>
      <div className="inline-flex items-center justify-between gap-0.5 w-full">
        <Link
          href={record.link}
          className={clsx(
            'font-bold block transition md:text-base text-xl',
            'text-dark-primary dark:text-white hover:text-primary-1 dark:hover:text-primary-1',
            !record.children && 'md:pr-0 pr-4'
          )}
          onClick={handleLinkClick}
        >
          {record.title}
        </Link>
        {record.children?.length > 0 && (
          <SvgIcon
            name="arrowDown2"
            viewBox="0 0 15 10.5"
            className={clsx(
              'ml-1.5 md:mr-0 xs2:mr-5 mr-2 md:w-[12px] w-[15px] md:h-[8px] h-[11px] transition duration-300 shrink-0',
              'fill-dark-primary dark:fill-white stroke-none cursor-pointer',
              displayChildren && 'rotate-180'
            )}
            onClick={() => setDisplayChildren(!displayChildren)}
          />
        )}
      </div>
      {record.children?.length > 0 && displayChildren && (
        <ul className="pl-4 md:pr-0 pr-4 w-full">
          {record.children.map((children: any, index: number) => (
            <li key={index} className="mt-4">
              <Link
                href={children.link}
                className={clsx(
                  'font-bold block transition w-full md:text-base text-xl',
                  'md:text-dark-primary dark:md:text-white text-left',
                  'text-gray-1 dark:text-gray-3 hover:text-primary-1 dark:hover:text-primary-1'
                )}
                onClick={handleLinkClick}
              >
                {children.title}
              </Link>
            </li>
          ))}
        </ul>
      )}
    </>
  )
}

export default MenuItem
