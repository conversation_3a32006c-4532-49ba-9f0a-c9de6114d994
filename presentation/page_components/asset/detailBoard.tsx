import Link from 'next/link'
import { shortenAddress } from 'lib/utils'

const DetailBoard = (props: any) => {
  const { address, token, tokenUri, tokenStandard, block<PERSON>hain, explorerUrl, metadata } = props

  let shortenedAddress = ''
  if(address) {
    shortenedAddress = shortenAddress(address,  11)
  }

  return (
    <div className="-mt-3.5 py-0.5">
      <p className="mt-3.5 font-medium flex justify-between">
        <span>Address</span>
        {explorerUrl ? (
          <Link href={`${explorerUrl}/address/${address}`} target="_blank">
            <span className="max-w-[45%] truncate text-primary-2">{shortenedAddress}</span>
          </Link>
        ): (
          <span className="max-w-[45%] truncate">{shortenedAddress}</span>
        )}
      </p>
      <p className="mt-3.5 font-medium flex justify-between">
        <span>Token ID</span>
        {tokenUri ? (
          <Link href={tokenUri} target="_blank">
            <span className="max-w-[45%] truncate text-primary-2">{token}</span>
          </Link>
        ): (
          <span className="max-w-[45%] truncate">{token}</span>
        )}
      </p>
      <p className="mt-3.5 font-medium flex justify-between">
        <span>Token Standard</span>
        <span className="max-w-[45%] truncate">{tokenStandard}</span>
      </p>
      <p className="mt-3.5 font-medium flex justify-between">
        <span>Blockchain</span>
        <span className="max-w-[45%] truncate">{blockChain}</span>
      </p>
      <p className="mt-3.5 font-medium flex justify-between">
        <span>Metadata</span>
        <span className="max-w-[45%] truncate">{metadata}</span>
      </p>
    </div>
  )
}

export default DetailBoard
