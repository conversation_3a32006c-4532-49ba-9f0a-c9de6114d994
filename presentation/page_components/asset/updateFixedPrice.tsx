import { useState, useEffect } from 'react'
import Tooltip from 'presentation/components/tooltip'
import Input from 'presentation/components/input'
import DatetimeRange from 'presentation/components/datetimeRange'
import { durationDateOptions } from 'presentation/controllers/mockData/options'

const UpdateFixedPrice = (props: any) => {
  const {
    paymentTokens = 'Eth',
    price = 0,
    onChangePrice = () => { },
    onChangeDuration = () => { },
    onChangeStartAt = () => { },
    onChangeEndAt = () => { },
  } = props

  const [isPaymentTokensReady, setIsPaymentTokensReady] = useState(false)
  const [duration, setDuration] = useState(durationDateOptions[0])

  return (
    <>
      <div className="mt-5">
        {/* Price */}
        <div className="flex items-center">
          <p className="font-medium text-dark-primary dark:text-white">Price</p>
          <Tooltip contentWidth="lg:w-[295px]" iconSize="w-4 h-4 -mt-0.5">
                        Price
          </Tooltip>
        </div>
        <div className="flex sm:flex-nowrap flex-wrap mt-10p">
          <Input
            className="w-24 cursor-pointer"
            type="text"
            readOnly={true}
            value={props.paymentToken}
          />
          <div>
            <Input
              type="number"
              name="price"
              width="w-36"
              className="no-arrow ml-5"
              value={price}
              onChange={(e) => onChangePrice(e.target.value)}
            />
            <p className="mt-1 text-sm text-gray-1 text-right">$27,408.40</p>
          </div>
        </div>

        {/* Duration */}
        <div className="md2:w-3/4 w-full mt-5">
          <p className="mb-3 font-medium text-dark-primary dark:text-white">
                        Duration
          </p>
          <Input
            className="w-full cursor-pointer"
            type="text"
            readOnly={true}
            value={duration.name}
            prefixIcon="calendar"
            prefixIconClassName="absolute left-7 top-3 w-6 h-6 stroke-none fill-primary-2 cursor-pointer"
            suffixIcon="chevronDownSelect"
            suffixIconViewBox="0 0 12.962 7.411"
            suffixIconClassName="absolute right-7 top-5 w-[13px] h-[8px] fill-primary-2 stroke-none cursor-pointer"
          />
          <DatetimeRange
            inline
            onSelect={(option: any) => {
              onChangeDuration(option)
              setDuration(option)
            }}
            onChangeStartDatetime={(value: any) => onChangeStartAt(value)}
            onChangeEndDatetime={(value: any) => onChangeEndAt(value)}
            className="custom xs3:max-w-[485px] xs3:min-w-[485px] max-w-[245px] min-w-[245px]"
          />
        </div>

      </div>
    </>
  )
}

export default UpdateFixedPrice
