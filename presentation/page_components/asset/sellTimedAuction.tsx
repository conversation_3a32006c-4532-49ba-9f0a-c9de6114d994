import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import { DateTime } from 'luxon'
import { toast } from 'react-toastify'
import { useAccount } from 'wagmi'
import { useForm } from 'lib/hook/useForm'
import { formatNumber } from 'lib/number'
import {  isNumber, validatePrice } from 'lib/validate'
import { useAuthentication } from 'lib/hook/auth_hook'

import Loader from 'presentation/components/loader'
import Button from 'presentation/components/button'
import Tooltip from 'presentation/components/tooltip'
import Select from 'presentation/components/select'
import Input from 'presentation/components/input'
import Switch from 'presentation/components/switch'
import DatetimeRange from 'presentation/components/datetimeRange'
import { durationDateOptions } from 'presentation/controllers/mockData/options'
import ListingUseCase from 'presentation/use_cases/listing_use_case'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'

import Listing from 'domain/models/listing'
import ListingType from 'domain/models/listing/listing_type'
import AuctionMethod from 'domain/models/listing/auction_method'

const listingUseCase = new ListingUseCase()

const SellTimedAuction = (props: any) => {
  const {
    paymentTokens = [],
    asset = null,
    contracts = null,
    creators = null,
    onChangeData = () => {},
  } = props

  const FEE_SALES = {
    address: contracts.feeSalesAddress,
    percent: contracts.feeSalesPercent,
  }

  const initFormData = {
    quantity: 1,
    paymentToken: null,
    price: '',
    duration: durationDateOptions[0],
    startAt: DateTime.now(),
    endAt: DateTime.now(),
    hasReservePrice: false,
    reservePrice: 0,
  }

  /**
   * DATA
   */
  const { formData, setFormValue } = useForm(initFormData)
  const [isDisableForm, setIsDisableForm] = useState(true)
  const [isLoading, setIsLoading] = useState(false)
  const [errorPrice, setErrorPrice] = useState('')
  const [errorReservePrice, setErrorReservePrice] = useState('')
  
  /**
   * HOOKS
   */
  const { getProviderEthers, switchNetwork} = useAuthentication()
  const { chainId: currentChainId } = useAccount()
  const router = useRouter()
  const authStateProvider = AuthStateProvider((state) => ({me: state.me}))

  /**
   * FUNCTIONS
   */

  const validateForm = () => {
    if(formData?.price == '') {
      setErrorPrice('')
      setIsDisableForm(true)
      return false
    }
    const maxDecimals = Number(formData.paymentToken?.decimals) - 4

    const errPrice = validatePrice({
      value: formData.price, 
      fieldName: 'price', 
      min: 0,
      greaterNumber: 0,
      maxDecimals: maxDecimals, 
    })
    setErrorPrice(errPrice?.message || '')
    
    let errReservePrice:any = null 
    if(formData.hasReservePrice) {
      errReservePrice = validatePrice({
        value: formData.reservePrice, 
        fieldName: 'price', 
        min: formData.price || 0, 
        maxDecimals: maxDecimals, 
      })
      setErrorReservePrice(errReservePrice?.message || '')
      if(errReservePrice?.code == 'INVALID_MIN_AMOUNT') {
        setErrorReservePrice('Reserve price must be greater than starting price')
      }
    }
    
    if(errPrice || errReservePrice || !formData.paymentToken) {
      setIsDisableForm(true)  
      return false
    }
    
    setIsDisableForm(false)
    return true
  }
  
  const submitForm = async (e: any) => {
    try {
      e.preventDefault()
      const success = validateForm()
      if(!success) return 
      
      setIsLoading(true)
      if (!contracts?.domainRegistryAddress || !contracts?.marketplaceAddress) {
        throw new Error('Domain registry or marketplace address is not support in this network')
      }
      if (!authStateProvider?.me?.publicAddresses?.length) {
        throw new Error('Please login')
      }
      
      let fees:any[] = []
      if(FEE_SALES.address && FEE_SALES.percent) {
        fees.push(FEE_SALES)
      }
      if(creators?.totalRateFee) {
        fees.push(...creators.list)
      }
      
      const newListing = Listing.newEmtyListing()
      newListing.user = authStateProvider?.me ? authStateProvider?.me : null
      newListing.listingType = ListingType.timedAuction()
      newListing.auctionMethod = AuctionMethod.sellToHighestBidder()
      newListing.assets = [asset]
      newListing.quantities = [formData.quantity]
      newListing.paymentToken = formData.paymentToken
      newListing.price = Number(formData.price)
      newListing.startAt = formData.startAt
      newListing.endAt = formData.endAt
      newListing.hasReservePrice = formData.hasReservePrice
      newListing.reservePrice = Number(formData.reservePrice)
      newListing.fees = fees
      const provider = await getProviderEthers()
      await listingUseCase.save(
        newListing, 
        provider, 
        authStateProvider?.me?.publicAddresses[0],
        contracts
      )
      toast.success('Listing item successfully.')
      setIsLoading(false)
      router.push(`/assets/${asset.id}`)
    } catch (error: any) {
      console.log('------error', error)
      toast.error(error?.showMsgErrors ? error?.showMsgErrors() : error?.message || error)
      setIsLoading(false)
    }
  }

  /**
   * LIFECYCLES
   */
  useEffect(() => {
    if (paymentTokens.length > 0) {
      setFormValue('paymentToken', paymentTokens[0]) 
    }
  }, [paymentTokens])
  
  useEffect(() => {
    onChangeData(formData)
    validateForm()
  }, [formData])

  return (
    <form className="create-form md2:w-3/4 w-full" onSubmit={submitForm}>
      <div className="mt-5">
        {/* Price */}
        <div className="flex items-center">
          <p className="font-medium text-dark-primary dark:text-white">Starting price</p>
          {/* <Tooltip contentWidth="lg:w-[295px]" iconSize="w-4 h-4 -mt-0.5">
            Starting price
          </Tooltip> */}
        </div>
        <div className="flex sm:flex-nowrap flex-wrap mt-10p w-full">
          {paymentTokens?.length > 0 && formData.paymentToken && (
            <Select
              className="sm:mb-0 mb-2"
              options={paymentTokens}
              defaultSelected={formData.paymentToken}
              showOptionIcon
              rounded
              onSelect={(option) => {
                setFormValue('paymentToken', option)
              }}
            />
          )}
          {paymentTokens?.length == 0 && (
            <div className="text-sm text-red text-stroke-thin mt-1 text-right">
              Not token yet
            </div>
          )}
          <div className='flex-1 ml-5'>
            <Input
              type="number"
              name="price"
              className="no-arrow "
              value={formData.price}
              onChange={(e) =>
                setFormValue('price', e.target.value)
              }
            />
            {errorPrice && (
              <div className="text-sm text-red text-stroke-thin mt-1 text-right">
                {errorPrice}
              </div>
            )}
            <p className="mt-1 text-sm text-gray-1 text-right">
              ~${formatNumber(Number(formData?.price || 0)*Number(formData?.paymentToken?.priceUsd || 0))}
            </p>
          </div>
        </div>

        
        {/* Duration */}
        <div className="w-full mt-5">
          <p className="mb-3 font-medium text-dark-primary dark:text-white">
            Duration
          </p>
          <div className="relative w-full z-9">
            <Input
              wrapClass="w-full"
              className="w-full cursor-pointer"
              type="text"
              value={formData?.duration?.name || ''}
              readOnly={true}
              prefixIcon="calendar"
              prefixIconClassName="absolute left-7 top-3 w-6 h-6 stroke-none fill-primary-2 cursor-pointer"
              suffixIcon="chevronDownSelect"
              suffixIconViewBox="0 0 12.962 7.411"
              suffixIconClassName="absolute right-7 top-5 w-[13px] h-[8px] fill-primary-2 stroke-none cursor-pointer"
            />
            <DatetimeRange
              inline
              onSelect={(option: any) => setFormValue('duration', option)}
              onChangeStartDatetime={(value: any) => setFormValue('startAt', value)}
              onChangeEndDatetime={(value: any) => setFormValue('endAt', value)}
              className="custom xs3:max-w-[485px] xs3:min-w-[485px] max-w-[245px] min-w-[245px] ml-auto"
            />
          </div>
        </div>

        {/* Include reserve price */}
        <div className="mt-6">
          <div className="flex items-center">
            <p className="font-medium mr-1">Include reserve price</p>
            <Tooltip contentWidth="lg:w-[295px]" iconSize="w-4 h-4">
              {'If you don\'t receive any bids equal to or greater than your reserve, the auction will end without a sale.'}
            </Tooltip>
            <Switch
              className='ml-5'
              defaultActive={formData.hasReservePrice}
              onClick={(value) => {
                setFormValue('hasReservePrice', value)
                if(!value) {
                  setFormValue('reservePrice', 0)
                }
              }}
            />
          </div>
          {formData.hasReservePrice && (
            <div className="flex sm:flex-nowrap flex-wrap mt-10p w-full">
              {paymentTokens?.length && formData.paymentToken && (
                <Select
                  className="sm:mb-0 mb-2"
                  options={paymentTokens}
                  defaultSelected={formData.paymentToken}
                  showOptionIcon
                  rounded
                  onSelect={(option) => {
                    setFormValue('paymentToken', option)
                  }}
                />
              )}
              <div className='flex-1 ml-5'>
                <Input
                  type="number"
                  name="price"
                  className="no-arrow "
                  value={formData.reservePrice}
                  onChange={(e) =>
                    setFormValue('reservePrice', e.target.value)
                  }
                />
                {errorReservePrice && (
                  <div className="text-sm text-red text-stroke-thin mt-1 text-right">
                    {errorReservePrice}
                  </div>
                )}
                <p className="mt-1 text-sm text-gray-1 text-right">
                  ~${formatNumber(Number(formData?.reservePrice || 0)*Number(formData?.paymentToken?.priceUsd || 0))}
                </p>
              </div>
            </div>
          )}
        </div>

        {/* fee */}
        <div className="mt-6 max-w-44">
          <p className="font-medium mr-5">Fee</p>
          {FEE_SALES.address && FEE_SALES.percent > 0 && (
            <div className='flex justify-between text-gray-1 mt-10p'>
              <span className="mr-4">Service Fee</span>
              <span>{FEE_SALES.percent}%</span>
            </div>
          )}
          {Number(creators?.totalRateFee) > 0 && (
            <div className='flex justify-between text-gray-1 mt-10p'>
              <span className="mr-4">Creator earnings</span>
              <span>{creators?.totalRateFee}%</span>
            </div>
          )}
        </div>
        {currentChainId !== asset.chainId ? 
          (
            <Button
              variant="dark"
              type="button"
              className="mt-6"
              onClick={() => switchNetwork({chainId: asset.chainId})}
            >
              Switch network
            </Button>
          ) : (
            <Button
              disable={isLoading || isDisableForm}
              htmltype="submit"
              variant="dark"
              className="mt-6 flex"
              onClick={submitForm}
            >
              { isLoading && <Loader className='mr-2' /> } {isLoading? 'Please waiting' : 'Complete listing'}
            </Button>
          )
        }
      </div>
    </form>
  )
}

export default SellTimedAuction
