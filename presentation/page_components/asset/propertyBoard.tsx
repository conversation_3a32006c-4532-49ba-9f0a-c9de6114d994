import NoData from 'presentation/components/no-data'

type AssetAttributeProperty = {
  type: string,
  name: string
}

type Props = {
  properties: AssetAttributeProperty[],
}

const PropertyBoard = ({
  properties = [],
}: Props) => {
  return (
    <div className="p-4">
      {properties ? properties.map((property, index) => (
        <div key={index} className="px-4 py-2 mt-3 first:mt-0 border rounded-md flex justify-between items-center">
          <div className="font-bold">
            <p>{property.type}</p>
            <p className="mt-0.5 text-xs text-primary-2">{property.name}</p>
          </div>
          {/* <p className="text-sm text-green-1 text-stroke-thin">4% have this trait</p> */}
        </div>
      )) : (
        <NoData/>
      )}
    </div>
  )
}

export default PropertyBoard
