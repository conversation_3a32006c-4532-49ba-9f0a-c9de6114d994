import { offerTableHeader } from './_tableHeaders'

//Todo remove mock data
import { offers } from 'presentation/controllers/mockData/asset_mock_data'
//End todo remove mock data

const OfferBoard = () => {
  return (
    <>
      <div className="flex gap-2 py-2 border-b">
        {offerTableHeader.map((item, index) => (
          <p key={index} className="w-1/5 font-medium"></p>
        ))}
      </div>
      <div className="max-h-56 overflow-y-scroll">
        {offers && offers.map((offer, index) => (
          <div key={index} className="flex gap-2 py-2 border-b last:border-b-0 last:pb-0">
            <p className="w-1/5 font-medium">{offer.unitPrice}</p>
            <p className="w-1/5 font-medium">{offer.usdPrice}</p>
            <p className="w-1/5 font-medium">{offer.floorDifference}</p>
            <p className="w-1/5 font-medium">{offer.expiration}</p>
            <p className="w-1/5 font-medium text-primary-1">{offer.from}</p>
          </div>
        ))}
      </div>
    </>
  )
}

export default OfferBoard
