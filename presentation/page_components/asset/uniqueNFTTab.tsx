import Image from 'next/image'
import Link from 'next/link'
import Button from 'presentation/components/button'
import ButtonIcon from 'presentation/components/button/button-icon'
import ButtonTag from 'presentation/components/button/button-tag'
import ToggleWrap from 'presentation/components/toggle-wrap'
import ToggleVertical from 'presentation/components/toggle-wrap/toggle-vertical'
import Table from 'presentation/components/table'
import { toast } from 'react-toastify'

const AssetDetailUniqueNFTTab = (props: any) => {
  const {
    asset = {},
    activeIndex = 0,
    onChangeTab = () => {},
    saleData = [],
    isFetching = true,
    onRefresh = () => {},
  } = props

  const tableHeader = [
    {
      title: 'Price',
      key: 'price',
      sortable: true,
      options: {
        showFullIcons: true,
        viewBox: '0 0 15 10.5',
        iconSize: 'w-3 h-3',
        arrowRight: true,
      },
      render: ({ price }: any) => <span>{price ? price.toFixed(4) : ''}</span>,
    },
    {
      title: 'Properties',
      key: 'properties',
      sortable: true,
      options: {
        direction: 'ASC',
        arrowIcon: 'chevronDownSelect',
        viewBox: '0 0 12.962 7.411',
        iconSize: 'w-3.5 h-3.5',
        arrowRight: true,
      },
      render: ({ link, properties }: any) => (
        <Link
          href={link}
          className="text-primary-2 max-w-[100px] truncate py-4"
        >
          {properties}
        </Link>
      ),
      tdClass: 'truncate max-w-[100px] pr-8 text-primary-2',
    },
    {
      title: 'Buy',
      key: 'buy',
      render: ({ text }: any) => {
        return (
          <Button
            variant="dark"
            className="py-md"
            onClick={() => {
              toast.success('Bought successfully.')
              onRefresh()
            }}
          >
            {text}
          </Button>
        )
      },
    },
  ]

  return (
    <div className="container-lg pt-8 pb-16">
      <div className="flex flex-wrap xl:flex-nowrap xl:justify-start">
        <div className="w-full md:w-[50%] lg:w-[296px] px-2 xl:px-10 md:mt-0 mt-4">
          <div className="flex flex-col items-center w-fit">
            <div className="w-[150px] h-[150px] relative rounded-20 overflow-hidden">
              <Image
                alt={asset.name || ''}
                fill
                style={{ objectFit: 'contain' }}
                src={
                  asset.thumbnail?.url || '/asset/images/<EMAIL>'
                }
              />
            </div>
            <p className="mt-5 font-medium">{asset.name}</p>
          </div>
        </div>

        <div className="xl:w-[785px] w-full xl:mt-0 mt-5">
          <div className="flex sm:flex-nowrap flex-wrap items-center gap-5">
            <ButtonTag
              onClick={() => onChangeTab(0)}
              active={activeIndex === 0}
            >
              Same NFT
            </ButtonTag>
            <ButtonTag
              onClick={() => onChangeTab(1)}
              active={activeIndex === 1}
            >
              Unique NFT
            </ButtonTag>
            <div onClick={() => onRefresh()}>
              <ButtonIcon
                svgIcon="refresh"
                className="h-fit bg-white"
                iconClassName="w-5 h-5 stroke-primary-2"
              />
              <span className="ml-1 align-middle text-sm text-dark-blue text-stroke-thin">
                Refresh
              </span>
            </div>
          </div>

          <div className="mt-4 lg:mt-9 xl:ml-4">
            <div className="text-xl font-medium">
              <span className="mr-[6px] text-gray-2">Created By</span>
              <Link href="Mesmerizer" className="text-primary-2">
                Mesmerizer
              </Link>
            </div>
            <p className="mt-4 text-stroke-thin">
              Bloodsucking blockchain CryptoBatz, out scanning the metaverse
              skies for a taste of some non-fungible blood. Spawned by the
              Prince of Darkness and his scientists, Sutter Systems.
            </p>
          </div>
        </div>
      </div>

      <div className="flex justify-center w-full mt-9">
        {!isFetching && (
          <ToggleWrap contentClass="rounded-t-10" className="max-w-[560px]">
            <ToggleVertical
              toggleTitle="For sale"
              defaultShow
              className="rounded-t-10"
              toggleTitleWrapClass="rounded-t-10"
              toggleTitleWrapClassWhenClose="rounded-b-10"
              contentClass="p-2 overflow-x-auto"
              contentWrapClass="rounded-b-10"
            >
              <Table
                head={tableHeader}
                data={saleData}
                className="for-sale-table min-w-[480px]"
              />
            </ToggleVertical>
          </ToggleWrap>
        )}
      </div>
    </div>
  )
}

export default AssetDetailUniqueNFTTab
