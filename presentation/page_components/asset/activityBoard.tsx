import Select from 'presentation/components/select'
import NoData from 'presentation/components/no-data'
import Table from 'presentation/components/table'
import Activity from 'domain/models/activity'
import FilterEventType from 'domain/repositories/activity_repository/filter_event_type'
import { activitiesBoardHeader } from './_tableHeaders'

type Props = {
  activities: Activity[],
  filterOptions: FilterEventType[]
}

const ActivityBoard = ({
  activities = [],
  filterOptions = [],
}: Props) => {

  return (
    <>
      {/* <div className="flex justify-end">
        <Select
          rounded
          options={filterOptions}
          isActivityFilter={true}
          placeholder="Filter"
          arrowIconClass="fill-primary-1 stroke-none"
          className="w-[290px] max-w-[290px]"
          selectClassName="w-[290px] max-w-[290px]"
          dropdownClassName="w-[290px] max-w-[290px]"
        />
      </div> */}
      <div className="flex w-full overflow-x-auto mt-2">
        {activities.length > 0 ? (
          <Table
            head={activitiesBoardHeader}
            data={activities}
            stickyHeader={true}
            className="min-w-[1020px] border-b"
          />
        ) : (
          <div className="w-full rounded-lg border flex justify-center items-center py-24">
            <NoData/>
          </div>
        )}
      </div>
    </>
  )
}

export default ActivityBoard
