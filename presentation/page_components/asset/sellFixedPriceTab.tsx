import { useState, useEffect } from 'react'
import Tooltip from 'presentation/components/tooltip'
import Select from 'presentation/components/select'
import Input from 'presentation/components/input'
import Switch from 'presentation/components/switch'
import DatetimeRange from 'presentation/components/datetimeRange'
import { durationDateOptions } from 'presentation/controllers/mockData/options'

const SellFixedPriceTab = (props: any) => {
  const {
    paymentTokens = [],
    price = 0,
    isBundle = true,
    bundleName = '',
    bundleDescription = '',
    isReservedForBuyer = true,
    reservedBuyerAddress = '',
    startAt,
    endAt,
    onChangePaymentToken = () => {},
    onChangePrice = () => {},
    onChangeDuration = () => {},
    onChangeIsBundle = () => {},
    onChangeBundleName = () => {},
    onChangeBundleDescription = () => {},
    onChangeIsReservedForBuyer = () => {},
    onChangeReservedBuyerAddress = () => {},
    onChangeStartAt = () => {},
    onChangeEndAt = () => {},
  } = props

  const [isPaymentTokensReady, setIsPaymentTokensReady] = useState(false)
  const [duration, setDuration] = useState(durationDateOptions[0])

  useEffect(() => {
    if (paymentTokens.length > 0) {
      setIsPaymentTokensReady(true)
    }
  }, [paymentTokens])

  return (
    <>
      <div className="mt-5">
        {/* Price */}
        <div className="flex items-center">
          <p className="font-medium text-dark-primary dark:text-white">Price</p>
          <Tooltip contentWidth="lg:w-[295px]" iconSize="w-4 h-4 -mt-0.5">
            Price
          </Tooltip>
        </div>
        <div className="flex sm:flex-nowrap flex-wrap mt-10p">
          {isPaymentTokensReady && (
            <Select
              options={paymentTokens}
              showOptionIcon
              rounded
              onSelect={(option) => onChangePaymentToken(option)}
              className="sm:mb-0 mb-2"
            />
          )}
          <div>
            <Input
              type="number"
              name="price"
              width="w-36"
              className="no-arrow ml-5"
              value={price}
              onChange={(e) => onChangePrice(e.target.value)}
            />
            <p className="mt-1 text-sm text-gray-1 text-right">$27,408.40</p>
          </div>
        </div>

        {/* Duration */}
        <div className="md2:w-3/4 w-full mt-5">
          <p className="mb-3 font-medium text-dark-primary dark:text-white">
            Duration
          </p>
          <Input
            className="w-full cursor-pointer"
            type="text"
            value={duration.name}
            readOnly={true}
            prefixIcon="calendar"
            prefixIconClassName="absolute left-7 top-3 w-6 h-6 stroke-none fill-primary-2 cursor-pointer"
            suffixIcon="chevronDownSelect"
            suffixIconViewBox="0 0 12.962 7.411"
            suffixIconClassName="absolute right-7 top-5 w-[13px] h-[8px] fill-primary-2 stroke-none cursor-pointer"
          />
          <DatetimeRange
            inline
            onSelect={(option: any) => {
              onChangeDuration(option)
              setDuration(option)
            }}
            onChangeStartDatetime={(value: any) => onChangeStartAt(value)}
            onChangeEndDatetime={(value: any) => onChangeEndAt(value)}
            className="custom xs3:max-w-[485px] xs3:min-w-[485px] max-w-[245px] min-w-[245px]"
          />
        </div>

        {/* Bundle */}
        <div className="mt-5">
          <div className="flex items-center">
            <p className="font-medium mr-5">Sell as a bundle</p>
            <Switch
              defaultActive={isBundle}
              onClick={(value) => onChangeIsBundle(value)}
            />
          </div>
          {isBundle && (
            <>
              <Input
                type="text"
                name="bundle-name"
                placeholder="Bundle name"
                className="mt-3"
                value={bundleName}
                onChange={(e) => onChangeBundleName(e.target.value)}
              />
              <textarea
                name="bundle-description"
                className="px-5 py-3 mt-3 rounded-3xl w-full outline-none bg-gray-4 dark:bg-blue-1"
                rows={3}
                placeholder="Bundle description"
                value={bundleDescription}
                onChange={(e) => onChangeBundleDescription(e.target.value)}
              />
            </>
          )}
        </div>

        {/* Reserve for specific buyer */}
        <div className="mt-5">
          <div className="flex items-center">
            <p className="font-medium mr-5">Reserve for specific buyer</p>
            <Switch
              defaultActive={isReservedForBuyer}
              onClick={(value) => onChangeIsReservedForBuyer(value)}
            />
          </div>
          <p className="text-sm text-gray-1">
            This item can be purchased as soon as it’s listed.
          </p>
          {isReservedForBuyer && (
            <textarea
              name="reservedBuyerAddress"
              className="px-5 py-3 mt-10p rounded-3xl w-full outline-none bg-gray-4 dark:bg-blue-1"
              rows={3}
              placeholder="0xc4c16a645…b21a"
              value={reservedBuyerAddress}
              onChange={(e) => onChangeReservedBuyerAddress(e.target.value)}
            />
          )}
        </div>
      </div>
    </>
  )
}

export default SellFixedPriceTab
