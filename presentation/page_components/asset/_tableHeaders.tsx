import Link from 'next/link'
import SvgIcon from 'presentation/components/svg-icon'
import Button from 'presentation/components/button'

export const offerTableHeader = [
  {
    title: 'Unit Price',
    key: 'unitPrice',
    render: ({ price, paymentToken }: any) => (
      <div className="px-2 uppercase">
        {price} {paymentToken?.symbol || paymentToken?.label}
      </div>
    ),
  },
  {
    title: 'USD Price',
    key: 'usdPrice',
  },
  {
    title: 'Quantity',
    key: 'quantity',
  },
  {
    title: 'Floor Difference',
    key: 'floorDifference',
    render: (floorDifference: any) => (
      <>
        {floorDifference ? (
          <div className="px-2">
            {`${floorDifference.percent}% ${floorDifference.text}`}
          </div>
        ) : (
          <div  className="text-dark-primary px-2 py-4">--</div>
        )}
      </>
    ),
  },
  {
    title: 'From',
    key: 'userFrom',
    render: ({ username }: any) => (
      <Link href={`/users/${username}`} className="text-primary-2 px-2 py-4">
        {username}
      </Link>
    ),
    tdClass: 'truncate max-w-[100px]',
  },
  {
    title: 'Expiration',
    key: 'expiration',
  },
  {
    title: '',
    key: 'action',
    tdClass: 'text-right',
    render: (action: any) => (
      <>
        {action && (
          <Button 
            variant={action.isAccept ? 'light' : 'gray4'} 
            size="xs" 
            className='!py-1.5' 
            onClick={() => {action.onClick(action.offer) }}
          >
            {action.text}
          </Button>
        )}
      </>
    ),
  },
]

export const listingTableHeader = [
  {
    title: 'Unit Price',
    key: 'unitPrice',
    render: ({ price, paymentToken }: any) => (
      <div className="px-2 uppercase">
        {price} {paymentToken?.symbol || paymentToken?.label}
      </div>
    ),
  },
  {
    title: 'USD Price',
    key: 'usdPrice',
  },
  {
    title: 'Quantity',
    key: 'quantity',
  },
  {
    title: 'From',
    key: 'userFrom',
    render: ({ username }: any) => (
      <Link href={`/users/${username}`} className="text-primary-2 px-2 py-4">
        {username}
      </Link>
    ),
    tdClass: 'truncate max-w-[100px]',
  },
  {
    title: 'Expiration',
    key: 'expiration',
  },
  {
    title: '',
    key: 'action',
    tdClass: 'text-right',
    render: (action: any) => (
      <>
        {action && (
          <Button 
            disable={(action?.isPrivateListing && !action?.isPrivateListingForMe)}
            variant={action.isAccept ? 'light' : 'gray4'} 
            size="xs" 
            className='!py-1.5'
            type="button" 
            onClick={() => {action.onClick(action.listing) }}
          >
            {action.text}
          </Button>
        )}
      </>
    ),
  },
]

export const activitiesBoardHeader = [
  {
    title: 'Event',
    key: 'event',
    render: (event: any) => (
      <div className="px-2">
        <SvgIcon
          name={event.icon}
          className="w-5 h-5 stroke-none fill-dark-primary dark:fill-white"
        />
        <span className="ml-2 align-middle">{event.label} </span>
      </div>
    ),
  },
  {
    title: 'Unit Price',
    key: 'unitPrice',
    render: (unitPrice: any) => (
      <>
        {unitPrice ? (
          <div className="px-2 uppercase">
            {`${unitPrice.price} ${unitPrice.paymentToken?.symbol || ''} `} 
          </div>
        ) : (
          <div  className="text-dark-primary px-2 py-4">--</div>
        )}
      </>
    ),
  },
  {
    title: 'Quantity',
    key: 'quantity',
  },
  {
    title: 'From',
    key: 'fromUser',
    render: (fromUser: any) => (
      <> 
        {
          fromUser?.username && 
          <Link href={`/users/${fromUser?.username}`} className="text-primary-2 px-2 py-4 block truncate w-[10rem]">
            {fromUser?.username || fromUser?.recipient}
          </Link>
        }
        {
          !fromUser?.username && <div className=" px-2 py-4 block truncate w-[10rem]">
            {fromUser?.username || fromUser?.recipient}
          </div>
        }
      </>
    ),
  },
  {
    title: 'To',
    key: 'toUser',
    render: (toUser: any) => (
      <> 
        {toUser && (
          <>
            {
              toUser?.username && 
          <Link href={`/users/${toUser?.username}`} className="text-primary-2 px-2 py-4 block truncate w-[10rem]">
            {toUser?.username || toUser?.offerer}
          </Link>
            }
            {
              !toUser?.username && <div className=" px-2 py-4 block truncate w-[10rem]">
                {toUser?.username || toUser?.offerer}
              </div>
            }
          </>
          
        )}
        {!toUser && (
          <div  className="text-dark-primary px-2 py-4">--</div>
        )}
      </>
    ),
  },
  {
    title: 'Time',
    key: 'time',
    render: (time: any) => (
      <div  className="text-dark-primary px-2 py-4">
        {time}
      </div>
    ),
  },
]
