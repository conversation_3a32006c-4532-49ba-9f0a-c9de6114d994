import Image from 'next/image'
import { useState } from 'react'
import clsx from 'clsx'
import ButtonIcon from 'presentation/components/button/button-icon'
import ButtonTag from 'presentation/components/button/button-tag'
import Button from 'presentation/components/button'
import ToggleWrap from 'presentation/components/toggle-wrap'
import ToggleVertical from 'presentation/components/toggle-wrap/toggle-vertical'
import Table from 'presentation/components/table'
import SvgIcon from 'presentation/components/svg-icon'
import Link from 'next/link'

const AssetDetailSameNFTTab = (props: any) => {
  const {
    asset = {},
    activeIndex = 0,
    onChangeTab = () => {},
    askData = [],
    buyData = [],
    isFetching = true,
    onRefresh = () => {},
  } = props

  const tableHeader = [
    {
      title: 'Size',
      key: 'size',
    },
    {
      title: 'Properties',
      key: 'properties',
      sortable: true,
      options: {
        direction: 'ASC',
        arrowIcon: 'chevronDownSelect',
        viewBox: '0 0 12.962 7.411',
        iconSize: 'w-3.5 h-3.5',
        arrowRight: true,
      },
      render: ({ link, properties }: any) => (
        <Link
          href={link}
          className="text-primary-2 max-w-[300px] truncate py-4"
        >
          {properties}
        </Link>
      ),
      tdClass: 'truncate max-w-[300px] pr-8 text-primary-2',
    },
    {
      title: 'Price',
      key: 'price',
      sortable: true,
      options: {
        showFullIcons: true,
        viewBox: '0 0 15 10.5',
        iconSize: 'w-3 h-3',
        arrowRight: true,
      },
      render: ({ price }: any) => (
        <span className="font-medium">{price ? price.toFixed(2) : ''}</span>
      ),
    },
    {
      title: 'Token',
      key: 'token',
      render: ({ token }: any) => (
        <div className="inline-flex space-x-4">
          <span className="font-medium">{token}</span>
          <Image
            alt={token}
            src="/asset/icons/ETH.svg"
            className="flex-none ml-1"
            width={20}
            height={14}
          />
        </div>
      ),
    },
    {
      title: '',
      key: 'action',
      render: ({ icon, viewBox, className }: any) => (
        <SvgIcon name={icon} viewBox={viewBox} className={className} />
      ),
    },
  ]

  const [size, setSize] = useState(1)
  const [isSell, setIsSell] = useState(false)

  return (
    <div className="container-lg pt-8 pb-16">
      <div className="flex flex-wrap xl:flex-nowrap xl:justify-start">
        <div className="w-full md:w-[50%] lg:w-[296px] px-2 xl:px-10 md:mt-0 mt-4">
          <div className="flex flex-col items-center w-fit">
            <div className="w-[150px] h-[150px] relative rounded-20 overflow-hidden">
              <Image
                alt={asset.name || ''}
                fill
                style={{ objectFit: 'contain' }}
                src={
                  asset.thumbnail?.url || '/asset/images/<EMAIL>'
                }
              />
            </div>
            <p className="mt-5 font-medium">{asset.name}</p>
          </div>
        </div>

        <div className="xl:w-[785px] w-full xl:mt-0 mt-5">
          <div className="flex sm:flex-nowrap flex-wrap items-center gap-5">
            <ButtonTag
              onClick={() => onChangeTab(0)}
              active={activeIndex === 0}
            >
              Same NFT
            </ButtonTag>
            <ButtonTag
              onClick={() => onChangeTab(1)}
              active={activeIndex === 1}
            >
              Unique NFT
            </ButtonTag>
            <div onClick={() => onRefresh()}>
              <ButtonIcon
                svgIcon="refresh"
                className="h-fit bg-white"
                iconClassName="w-5 h-5 stroke-primary-2"
              />
              <span className="ml-1 align-middle text-sm text-dark-blue text-stroke-thin">
                Refresh
              </span>
            </div>
          </div>

          <div className="mt-5 lg:mt-10 border border-gray-3 rounded-lg">
            <div className="px-2 py-1.5 flex justify-between items-center bg-blue-7 font-medium rounded-t-lg">
              <span className="pl-3">{isSell ? 'Sell' : 'Buy'}</span>
              <div>
                <ButtonTag
                  active={!isSell}
                  onClick={() => setIsSell(!isSell)}
                  className="mr-5 w-[63px]"
                >
                  Buy
                </ButtonTag>
                <ButtonTag
                  active={isSell}
                  onClick={() => setIsSell(!isSell)}
                  className="mr-3 w-[73px]"
                >
                  Sell
                </ButtonTag>
              </div>
            </div>
            <div className="px-5 pt-6 pb-7 xl:inline-flex flex xl:flex-nowrap flex-wrap gap-5 items-end bg-white dark:bg-dark-primary rounded-b-lg">
              <div>
                <p className="font-medium mb-2">Token</p>
                <div className="inline-flex justify-center items-center space-x-2.5 w-full rounded-full bg-gray-4 px-3 py-3 h-[50px] min-w-[156px]">
                  <SvgIcon
                    name="chevronDownSelect"
                    viewBox="0 0 12.962 7.411"
                    className={clsx(
                      'w-[14px] h-[8px] transition duration-300 shrink-0 fill-primary-2 stroke-none flex-none'
                    )}
                  />
                  <div className="inline-flex items-center radio-tag-wrapper">
                    <input
                      type="radio"
                      name="coin"
                      id="eth"
                      value="eth"
                      checked
                      readOnly
                    />
                    <label
                      className="ml-1 font-medium text-dark-2"
                      htmlFor="eth"
                    >
                      ETH
                    </label>
                    <span className="ml-1 leading-none icon-wrapper flex-none">
                      <Image
                        alt="coin"
                        src={'/asset/icons/ETH.svg'}
                        width={20}
                        height={20}
                      />
                    </span>
                  </div>
                </div>
              </div>
              <div>
                <p className="font-medium mb-2">Size</p>
                <div className="inline-flex items-center space-x-2.5 w-full rounded-full bg-gray-4 px-5 py-2 h-[50px] min-w-[100px]">
                  <div className="flex flex-col w-[15px] space-y-3">
                    <SvgIcon
                      name="arrowUp2"
                      viewBox="0 0 15 10.5"
                      className={clsx(
                        'w-[15px] h-[10px] transition duration-300 shrink-0 fill-primary-2 stroke-none'
                      )}
                      onClick={() => setSize(size + 1)}
                    />
                    <SvgIcon
                      name="arrowDown2"
                      viewBox="0 0 15 10.5"
                      className={clsx(
                        'w-[15px] h-[10px] transition duration-300 shrink-0 fill-primary-2 stroke-none'
                      )}
                      onClick={() =>
                        size === 0 ? setSize(0) : setSize(size - 1)
                      }
                    />
                  </div>
                  <span className="font-medium text-dark-2">{size}</span>
                </div>
              </div>
              <div>
                <p className="font-medium mb-2">Price</p>
                <div className="inline-flex justify-center items-center space-x-2.5 w-full rounded-full bg-gray-4 px-5 py-2 h-[50px] min-w-[118px]">
                  <span className="font-medium text-dark-2">214.00</span>
                </div>
              </div>
              <div>
                <p className="font-medium mb-2">Total</p>
                <div className="inline-flex justify-start items-center space-x-2 w-full rounded-full py-2 h-[50px] min-w-[140px]">
                  <Image
                    alt="coin"
                    src={'/asset/icons/ETH.svg'}
                    width={25}
                    height={25}
                  />
                  <p className="font-medium text-xl text-primary-2">0.7 ETH</p>
                </div>
              </div>
              <Button variant="dark" className="px-md min-w-[140px] h-[50px]">
                {isSell ? 'Sell Now' : 'Buy Now'}
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-10">
        {!isFetching && (
          <ToggleWrap contentClass="rounded-t-10">
            <ToggleVertical
              toggleTitle="Ask"
              defaultShow
              className="rounded-t-10"
              toggleTitleWrapClass="rounded-t-10"
              toggleTitleWrapClassWhenClose="rounded-b-10"
              contentWrapClass="rounded-b-10"
              contentClass="no-padding-top xl:px-24 px-4 pb-4 max-h-[465px] overflow-auto"
            >
              <Table
                head={tableHeader}
                data={askData}
                stickyHeader={true}
                className="ask-table min-w-[580px]"
              />
            </ToggleVertical>
          </ToggleWrap>
        )}

        {!isFetching && (
          <ToggleWrap contentClass="rounded-t-10" className="mt-8">
            <ToggleVertical
              toggleTitle="Buy size"
              defaultShow
              className="rounded-t-10"
              toggleTitleWrapClass="rounded-t-10"
              toggleTitleWrapClassWhenClose="rounded-b-10"
              contentWrapClass="rounded-b-10"
              contentClass="no-padding-top xl:px-24 px-4 pb-4 max-h-[465px] overflow-auto"
            >
              <Table
                head={tableHeader}
                data={buyData}
                stickyHeader={true}
                className="ask-table min-w-[580px]"
              />
            </ToggleVertical>
          </ToggleWrap>
        )}
      </div>
    </div>
  )
}

export default AssetDetailSameNFTTab
