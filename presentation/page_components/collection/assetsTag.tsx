import AssetModel from 'domain/models/asset'
import FilterStatus from 'domain/repositories/asset_repository/filter_status'
import { Query as AssetQuery } from 'domain/repositories/asset_repository/query'
import SortBy from 'domain/repositories/asset_repository/sort_by'
import Chain from 'domain/value_objects/chain'
import PaymentToken from 'domain/value_objects/payment_token'
import { pageSizes } from 'lib/hook/customHook'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import Asset from 'presentation/components/asset'
import NoData from 'presentation/components/no-data'
import PaginationDot from 'presentation/components/pagination/pagination-dot'
import Select from 'presentation/components/select'
import SkeletonAsset from 'presentation/components/skeleton/assets'
import ToggleWrap from 'presentation/components/toggle-wrap'
import ToggleVerticalFilterChains from 'presentation/components/toggle-wrap/toggle-vertical-filter-chains'
import ToggleVerticalFilterPaymentTokens from 'presentation/components/toggle-wrap/toggle-vertical-filter-payment-tokens'
import ToggleVerticalFilterPrice from 'presentation/components/toggle-wrap/toggle-vertical-filter-price'
import ToggleVerticalFilterStatuesV2 from 'presentation/components/toggle-wrap/toggle-vertical-filter-statues-v2'
import ToggleVerticalMyNFTs from 'presentation/components/toggle-wrap/toggle-vertical-my-nfts'
import AssetUseCase from 'presentation/use_cases/asset_use_case'
import { useEffect, useState } from 'react'
import User from 'domain/models/user'
import MeUseCase from 'presentation/use_cases/me_use_case'
import { paymentTokenImages } from 'lib/valueObjects'
import PaymentTokensUseCase from 'presentation/use_cases/payment_tokens_use_case'
import ToggleVerticalFilterTraits from 'presentation/components/toggle-wrap/toggle-vertical-filter-traits'
import { mockTraitData } from 'presentation/controllers/mockData/common_mock_data'
const paymentTokensUseCase = new PaymentTokensUseCase()
const assetUseCase = new AssetUseCase()
import {FilterParams} from 'types/type'
//Todo remove mock data
//End todo remove mock data

type Props = {
  collectionId: string;
  collectionDetail: any;
};

let PageSize = pageSizes.pageSize25

const AssetsTag = ({ collectionId = '', collectionDetail = {} }: Props) => {
  const initParams: FilterParams = {
    collectionIds: [collectionId],
    filterStatuses: [],
    paymentTokens: [],
    priceTokenUnit: '',
    minPrice: '',
    maxPrice: '',
    chains: [],
    page: 1,
    limit: PageSize,
    sortBy: SortBy.recentlyListed().getName(),
    ownerId: '',  // Add empty string as default value
    traits: [],
  }

  const { filterParams, setFilterParams, changeFilterParam } =
    useChangeFilterParams(initParams)
  const [currentPage, setCurrentPage] = useState(1)
  const [assets, setAssets] = useState<AssetModel[]>([])
  const [isFetchingAssets, setIsFetchingAssets] = useState(false)
  const [totalAsset, setTotalAsset] = useState(0)
  const [sortList, setSortList] = useState<any>([])
  const [showSortList, setShowSortList] = useState(false)
  const [filterChains, setFilterChains] = useState<Chain[]>([])
  const [isFilterChainsReady, setIsFilterChainsReady] = useState(false)
  const [paymentTokens, setPaymentTokens] = useState<any>([])
  const [isFilterPaymentTokensReady, setIsFilterPaymentTokensReady] =
    useState(false)
  const [isAllFiltersReady, setIsAllFiltersReady] = useState(false)
  const [initialLoadDone, setInitialLoadDone] = useState(false)
  // add selected traits
  const [traits, setTraits] = useState<Array<any>>([])
  const [selectedTraits, setSelectedTraits] = useState<Array<{name: string, value: string}>>([])
  
  // Custom change filter param for traits
  const handleTraitSelection = (traitValues: Array<{name: string, value: string}>) => {
    console.log('🚀 ~ handleTraitSelection ~ traitValues:', traitValues)
    // Directly set the state with the structured trait data
    setSelectedTraits(traitValues)
  }
  const fetchAssets = async () => {
    setIsFetchingAssets(true)

    // Transform selectedTraits into the desired format Array<{name: string, values: string[]}>
    const traitsForQuery = selectedTraits.reduce((acc, trait) => {
      const existingTrait = acc.find((t) => t.name === trait.name)
      if (existingTrait) {
        existingTrait.values.push(trait.value)
      } else {
        acc.push({ name: trait.name, values: [trait.value] })
      }
      return acc
    }, [] as Array<{name: string, values: string[]}>)
    console.log('🚀 ~ traitsForQuery ~ traitsForQuery:', traitsForQuery)

    const query = new AssetQuery({
      ...filterParams,
      ownerId: filterParams.ownerId || null,
      traits: traitsForQuery, // Use the transformed traits
    })
    console.log('🚀 ~ fetchAssets ~ query:', query)
    await assetUseCase
      .totalCount(query)
      .then((res) => {
        setTotalAsset(res)
      })
      .catch((_error) => {
        setTotalAsset(0)
      })
    await assetUseCase
      .search(query)
      .then((res) => {
        setAssets(res)
      })
      .catch((_error) => {
        setAssets([])
      })
    setIsFetchingAssets(false)
  }

  const formatChains = () => {
    const chains = Chain.getResourceArray().map((chain) => {
      return Chain.fromName<Chain>(chain.name)
    })
    setFilterChains(chains)
    setIsFilterChainsReady(true)
  }
  // const formatPaymentTokens = async () => {
  //   try {
  //     let rs:any = await  paymentTokensUseCase
  //       .getPaymentTokens()
  //     console.log('🚀 ~ formatPaymentTokens ~ rs:', rs)
  //     // remove same symbol
  //     const uniqueTokens: any = []
  //     const seenSymbols = new Set()

  //     rs.forEach((token : any) => {
  //       if (!seenSymbols.has(token.symbol)) {
  //         uniqueTokens.push(token)
  //         seenSymbols.add(token.symbol)
  //       }
  //     })
  //     const paymentTokens: any = uniqueTokens.map((token: any) => {
  //       let name = token.name
  //       return {
  //         id: token.id,
  //         symbol: token.symbol,
  //         value: token.id,
  //         type: name,
  //         address: token.address,
  //         name: token.symbol,
  //         icon: paymentTokenImages[name] || '',
  //       }
  //     })
  //     setPaymentTokens(paymentTokens)
  //     setIsFilterPaymentTokensReady(true)
  //   } catch (error) {
  //     console.log('🚀 ~ formatPaymentTokens ~ error:', error)
      
  //   }
  // }
  const formatPaymentTokens = () => {
    // const tokens = PaymentToken.getResourceArray().map((token: any) => {
    //   return {
    //     id: token.id,
    //     name: token.label,
    //     value: false,
    //     type: token.name,
    //   }
    // })
    const tokens = collectionDetail?.paymentTokens?.map((token: any) => {
      return {
        id: token.id,
        name: token.label,
        value: false,
        type: token.name,
        icon: paymentTokenImages[token.name] || '',
      }
    }) || []
    setPaymentTokens(tokens)
    setIsFilterPaymentTokensReady(true)
  }

  const formatSortList = () => {
    let sortList = SortBy.getResourceArray().map((sort: any) => {
      return {
        id: sort.id,
        name: sort.label,
        value: sort.name,
      }
    })
    // filter sortList by client request 
    sortList = sortList.filter((sort: any) => {
      return sort.value === 'recentlyListed' || sort.value === 'priceLowToHigh' || sort.value === 'priceHighToLow'
    })
    setSortList(sortList)
    setShowSortList(true)
  }
  const formatTraits = () => {
    console.log('🚀 ~ traits ~ collectionDetail:', collectionDetail)
    const traits = collectionDetail?.traitTypes?.map((trait: any) => {
      return {
        name: trait.traitType,
        values: trait.values.map((value: any) => {
          return {
            value: value.value,
            count: value.amount,
          }
        }),
      }
    }) || []
    console.log('🚀 ~ formatTraits ~ traits:', traits)
    setTraits(traits)
  }
  
  useEffect(() => {
    formatSortList()
    formatChains()
    formatPaymentTokens()
    formatTraits()
  }, [])

  useEffect(() => {
    if (!isFilterChainsReady || !isFilterPaymentTokensReady) return
    
    // // Set initial filter values
    // const statuses = FilterStatus.getResourceArray()
    // statuses.forEach((status:any)  => {
    //   changeFilterParam('filterStatuses', status.name, true)
    // })
    
    // filterChains.forEach((chain:any) => {
    //   changeFilterParam('chains', chain, true)
    // })
    
    // paymentTokens.forEach((token : any) => {
    //   changeFilterParam('paymentTokens', token, true)
    // })

    setIsAllFiltersReady(true)
  }, [isFilterChainsReady, isFilterPaymentTokensReady])

  useEffect(() => {
    if (isAllFiltersReady && !initialLoadDone) {
      fetchAssets()
      setInitialLoadDone(true)
    }
  }, [isAllFiltersReady])

  useEffect(() => {
    if (!initialLoadDone) return
    fetchAssets()
  }, [filterParams, selectedTraits])

  return (
    <div className="product-layout mt-7">
      <div className="product-layout-filter">
        <ToggleWrap title="Filter" contentClass="border-t-0">
          <ToggleVerticalFilterStatuesV2
            defaultShow
            filterStatues={FilterStatus.getResourceArray()}
            changeFilterParam={changeFilterParam}
          />
          <ToggleVerticalMyNFTs
            defaultShow
            changeFilterParam={changeFilterParam}
          />
          <ToggleVerticalFilterPrice
            defaultShow
            minPrice={filterParams.minPrice || ''}
            maxPrice={filterParams.maxPrice || ''}
            changeFilterParam={changeFilterParam}
          />
          {/* {isFilterChainsReady && (
            <ToggleVerticalFilterChains
              defaultShow
              chains={filterChains}
              changeFilterParam={changeFilterParam}
            />
          )} */}
          {isFilterPaymentTokensReady && (
            <ToggleVerticalFilterPaymentTokens
              defaultShow
              tokens={paymentTokens}
              isActiveAll={false}
              changeFilterParam={changeFilterParam}
            />
          )}
          {traits?.length > 0 && <ToggleVerticalFilterTraits
            defaultShow
            traits={traits}
            onToggleTrait={handleTraitSelection}
          />}
        </ToggleWrap>
      </div>

      <div className="flex flex-col xl:w-9/12 md:w-3/5 md2:w-4/6 w-full pr-third">
        <div className="flex flex-wrap justify-between mb-5">
          <div className="xl:w-auto w-full">
            {isFetchingAssets ? (
              <>
                <p className="w-36 h-4 bg-gray-3 rounded-lg animate-pulse"></p>
              </>
            ) : (
              <>
                <p className="text-gray-1 dark:text-gray-3 text-stroke-thin">
                  Results：{totalAsset.toLocaleString()}
                </p>
              </>
            )}
          </div>
          <div className="mt-2 xl:mt-0 flex flex-wrap lg:flex-nowrap gap-5">
            {/* <SelectSearch options={options} className="shrink-0" /> */}
            {showSortList && (
              <Select
                options={sortList}
                onSelect={(option) => {
                  changeFilterParam('sortBy', option.value)
                }}
                className="h-fit"
              />
            )}
          </div>
        </div>

        <div
          className={`product-wrap no-gap ${!isFetchingAssets && assets.length === 0 ? 'no-data' : ''
          }
            justify-center md:justify-start`}
        >
          {isFetchingAssets ? (
            [...Array(5)].map((_, id) => <SkeletonAsset key={id} />)
          ) : (
            <>
              {assets.length > 0 ? (
                assets.map((asset: any, index: number) => (
                  <Asset
                    key={asset.id + '-' + index}
                    record={asset}
                    showDaysLeft={false}
                  />
                ))
              ) : (
                <div className="w-full rounded-lg border flex justify-center items-center py-24">
                  <NoData />
                </div>
              )}
            </>
          )}
        </div>
        {totalAsset > PageSize && !isFetchingAssets && (
          <div className="flex justify-center mt-5">
            <PaginationDot
              currentPage={currentPage}
              totalCount={totalAsset}
              pageSize={PageSize}
              onPageChange={(page: number) => {
                setCurrentPage(page),
                setFilterParams({ ...filterParams, page })
              }}
            />
          </div>
        )}
      </div>
    </div>
  )
}


export default AssetsTag
