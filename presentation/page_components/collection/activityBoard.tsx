import SvgIcon from 'presentation/components/svg-icon'
import Select from 'presentation/components/select'
import NoData from 'presentation/components/no-data'
import { activityBoardHeader } from './_tableHeaders'

//Todo remove mock data
import { activityBoardData, priceChartSelect } from 'presentation/controllers/mockData/collection_mock_data'
//End todo remove mock data

const ActivityBoard = () => {
  return (
    <>
      <div className="text-right">
        <Select options={priceChartSelect}/>
      </div>
      <div className="flex gap-2 py-2 border-b">
        {activityBoardHeader.map((item, index) => (
          <p key={index} className="w-1/5 font-medium">{item}</p>
        ))}
      </div>
      {activityBoardData.length ? activityBoardData.map((item, index) => (
        <div key={index} className="flex gap-2 py-2 border-b last:border-b-0 last:pb-0">
          <p className="w-1/5">
            <SvgIcon name={item.icon}/><span className="align-middle ml-2">{item?.transactionType}</span>
          </p>
          <p className="w-1/5">{item.unitPrice}</p>
          <p className="w-1/5">{item.quantity}</p>
          <p className="w-1/5 text-primary-1">{item.from}</p>
          <p className="w-1/5">{item.to}</p>
          <p className="w-1/5">{item.time}</p>
        </div>
      )) : (
        <NoData/>
      )}
    </>
  )
}

export default ActivityBoard
