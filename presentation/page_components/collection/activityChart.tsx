import dynamic from 'next/dynamic'
const DualAxes = dynamic(() => import('@ant-design/plots').then(({ DualAxes }) => DualAxes), { ssr: false })

type Props = {
  data: any[],
}

const ActivityChart = ({
  data = [],
}: Props) => {
  data = data.map(item => { 
    return {
      ...item,
      volume: Number(item.volume),
      price:  Number(item.price),
    }
  })
  const config: any = {
    legend: true,
    height: 200,
    data: [data, data],
    xField: 'time',
    yField: ['volume', 'price'],
    yAxis: {
      // Left col
      volume: {
        min: 0,
        label: {
          formatter: (val: number) => `${val}`,
        },
      },
      // Right col
      price: false,
    },
    geometryOptions: [
      {
        geometry: 'column',
        color: '#C0DBF6',
        columnWidthRatio: 0.5,
      },
      {
        geometry: 'line',
        smooth: true,
        color: '#2081E2',
      },
    ],
  }

  return <div className="mb-5">
    <DualAxes {...config} />
  </div>
}

export default ActivityChart
