import Activity from 'domain/models/activity'

import FilterEventTypes from 'domain/repositories/activity_repository/filter_event_type'
import { Query } from 'domain/repositories/activity_repository/query'
import { Query as AssetDailyStatQuery } from 'domain/repositories/asset_daily_stat_repository/query'
import PeriodRange from 'domain/repositories/collection_daily_stat_repository/period_range'
import Chain from 'domain/value_objects/chain'
import { pageSizes } from 'lib/hook/customHook'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import Image from 'next/image'
import Link from 'next/link'
import NoData from 'presentation/components/no-data'
import PaginationDot from 'presentation/components/pagination/pagination-dot'
import PeriodRangesSelect from 'presentation/components/select/period-ranges-select'
import LoadingData from 'presentation/components/skeleton/loading'
import SvgIcon from 'presentation/components/svg-icon'
import Switch from 'presentation/components/switch'
import Table from 'presentation/components/table'
import ToggleWrap from 'presentation/components/toggle-wrap'
import ToggleVertical from 'presentation/components/toggle-wrap/toggle-vertical'
import ToggleVerticalFilterChains from 'presentation/components/toggle-wrap/toggle-vertical-filter-chains'
import ToggleVerticalFilterTraits from 'presentation/components/toggle-wrap/toggle-vertical-filter-traits'
import ToggleVerticalFilterUnitPrice from 'presentation/components/toggle-wrap/toggle-vertical-filter-unit-price'
import ActivityUseCase from 'presentation/use_cases/activity_use_case'
import AssetDailyStatUseCase from 'presentation/use_cases/asset_daily_stat_use_case'
import { useEffect, useState } from 'react'
import { FilterParams } from 'types/type'
import ActivityChart from '../collection/activityChart'
import AssetThumbnail from 'presentation/components/asset/thumbnail'
import { isAddress } from 'lib/validate'
import BigNumber from 'bignumber.js'
import { formatNumber } from 'lib/number'
import CollectionDailyStatUseCase from 'presentation/use_cases/collection_daily_stat_use_case'
import { Query as CollectionDailyStatQuery } from 'domain/repositories/collection_daily_stat_repository/query'
// import { mockTraitData } from 'presentation/controllers/mockData/common_mock_data'
const activityUseCase = new ActivityUseCase()
const assetDailyStatUseCase = new AssetDailyStatUseCase()
const collectionDailyStatUseCase = new CollectionDailyStatUseCase()
const filterEventTypes = FilterEventTypes.getResourceArray().map((status) => {
  return {
    id: status.id,
    name: status.label,
    value: false,
    type: status.name,
  }
})
const periodRanges = PeriodRange.getResourceArray().map((range) => {
  return {
    id: range.id,
    name: range.name,
    value: range.name,
    label: range.label,
  }
})
const chains = Chain.getResourceArray().map((chain) => {
  return Chain.fromName<Chain>(chain.name)
})

type Props = {
  collection: any;
  
};

let PageSize = pageSizes.pageSize25



const activityTableHead = [
  {
    title: 'Event',
    key: 'event',
    render: (event: any) => (
      <div className="px-2">

        <SvgIcon
          name={event.icon}
          className="w-5 h-5 stroke-none fill-dark-primary dark:fill-white"
        />
        <span className="ml-2 align-middle">{event.label} </span>
      </div>
    ),
  },
  {
    title: 'Item',
    key: 'asset',
    render: (asset: any) => (
      <Link
        href={`/assets/${asset?.id}`}
        className="py-3 flex items-center relative w-max"
      >
        {asset && <AssetThumbnail asset={asset}
          className="h-[60px] !w-[60px] !rounded-none"
          classNameImage="!rounded-none !border-none" />}
        <div className="max-w-[7rem] py-1 ml-2">
          <p className="font-medium truncate text-dark-primary dark:text-white">{asset?.name}</p>
          <span className="text-sm font-medium text-primary-2 break-words">
            {asset?.collection?.name}
          </span>
        </div>
      </Link>
    ),
  },
  {
    title: 'Unit Price',
    key: 'unitPrice',
    render: (unitPrice : any) => (
      <div className="px-2 uppercase  dark:text-white">
        {unitPrice?.price} {unitPrice?.paymentToken?.name}
      </div>
    ),
  },
  {
    title: 'Quantity',
    key: 'quantity',
    render: (quantity : any) => (
      <div className="px-2 uppercase  dark:text-white">
        {quantity}
      </div>
    ),
  },
  {
    title: 'From',
    key: 'fromUser',
    render: (fromUser: any) => (
      <>
        {fromUser?.username ? (
          <Link href={`/users/${fromUser?.username}`} className="text-primary-2 px-2 py-4 block truncate w-[120px]">
            {fromUser?.username}
          </Link>
        ) : (
          <div className=" px-2 py-4 block truncate w-[120px] break-all  dark:text-white">
            {fromUser?.username || fromUser?.recipient}
          </div>
        )}
       
      </>
      
    ),
  },
  {
    title: 'To',
    key: 'toUser',
    render: (toUser: any) => (
      <>
        {toUser?.username && !isAddress(toUser?.username) ? (
          <Link href={`users/${toUser?.username}`} className="text-primary-2 px-2 py-4 block truncate w-[120px]">
            {toUser?.username}
          </Link>
        ) : (
          <div className=" px-2 py-4 block truncate w-[120px] dark:text-white">
            {toUser?.username || toUser?.offerer}
          </div>
        )}
       
      </>
    
    ),
  },
  {
    title: 'Time',
    key: 'time',
    render: (time: any) => (
      <div  className="text-dark-primary px-2 py-4">
        {time}
      </div>
    ),
  },
]

const ActivityTag = ({ collection = {} }: Props) => {
  const initActivityParams: FilterParams = {
    filterEventTypes: [],
    chains: [],
    page: 1,
    limit: PageSize,
    sortBy: 'recentlyListed', // Not empty
    paymentTokens: [],
    traits: [],
    unitPriceFrom: '',
    unitPriceTo: '',
    currencyId: '',
  }

  const initCollectionDailyStatParams: FilterParams = {
    periodRange: 'allTime',
    page: 1,
    limit: 60,
  }

  const {
    filterParams: activityFilterParams,
    changeFilterParam: changeActivityFilterParam,
  } = useChangeFilterParams(initActivityParams)
  const [currentPage, setCurrentPage] = useState(1)
  const {
    filterParams: filterCollectionDailyStatParams,
    changeFilterParam: changeCollectionDailyStatFilterParam,
  } = useChangeFilterParams(initCollectionDailyStatParams)

  const [activities, setActivities] = useState<Activity[]>([])
  const [total, setTotal] = useState(0)
  const [isActivitiesReady, setIsActivitiesReady] = useState(true)
  const [selectedPeriodRangeText, setSelectedPeriodRangeText] = useState('All Time')
  const [collectionDailyStats, setCollectionDailyStats] = useState<any[]>([])
  const [isCollectionDailyStatsReady, setIsCollectionDailyStatsReady] =
    useState(false)
  const [activitiesTable, setActivitiesTable] = useState<any>([])
  const [avgPrice, setAvgPrice] = useState(0)
  const [totalVolume, setTotalVolume] = useState(0)
  const [isFilterPaymentTokensReady, setIsFilterPaymentTokensReady] =
    useState(false)
  const [paymentTokens, setPaymentTokens] = useState<any>([])
  // Add traits states
  const [traits, setTraits] = useState<Array<any>>([])
  const [selectedTraits, setSelectedTraits] = useState<Array<{name: string, value: string}>>([])
  
  // Custom change filter param for traits
  const handleTraitSelection = (traitValues: Array<{name: string, value: string}>) => {
    console.log('🚀 ~ handleTraitSelection ~ traitValues:', traitValues)
    // Directly set the state with the structured trait data
    setSelectedTraits(traitValues)
  }

  const fetchActivities = async () => {
    setIsActivitiesReady(false)
    // Format the payment tokens
    const paymentTokensParams = activityFilterParams.paymentTokens?.map((token: any) => token.name) || []
    
    // Transform selectedTraits into the desired format for API
    const traitsForQuery = selectedTraits.reduce((acc, trait) => {
      const existingTrait = acc.find((t) => t.name === trait.name)
      if (existingTrait) {
        existingTrait.values.push(trait.value)
      } else {
        acc.push({ name: trait.name, values: [trait.value] })
      }
      return acc
    }, [] as Array<{name: string, values: string[]}>)
    console.log('🚀 ~ fetchActivities ~ traitsForQuery:', traitsForQuery)
        
    // Create the query with all parameters
    const query = new Query({
      ...activityFilterParams,
      collectionIds: [collection?.id],
      filterEventTypes: activityFilterParams.filterEventTypes || [],
      chains: activityFilterParams.chains,
      paymentTokens: paymentTokensParams,
      traits: traitsForQuery, // Use properly formatted traits
    })
    console.log('🚀 ~ fetchActivities ~ query:', query)
   
    
    try {
      // Fetch activities
      const res = await activityUseCase.search(query)
      setActivities(res)
      
      // Fetch total count
      const totalCount = await activityUseCase.totalCount(query)
      setTotal(totalCount)
    } catch (error) {
      console.error('Error fetching activities:', error)
      setActivities([])
      setTotal(0)
    } finally {
      setIsActivitiesReady(true)
    }
  }

  const fetchCollectionDailyStats = async () => {
    setIsCollectionDailyStatsReady(false)
    console.log('filterCollectionDailyStatParams',filterCollectionDailyStatParams)
    const query = new CollectionDailyStatQuery({
      ...filterCollectionDailyStatParams,
      periodRange: PeriodRange.fromName(
        filterCollectionDailyStatParams.periodRange!
      ),
      collectionId:collection?.id,
    })
    await collectionDailyStatUseCase
      .search(query)
      .then((res: any) => {      
        const history = res?.collectionDailyStats || []
  
        if (history.length > 0) {
          const avgPriceList = history.filter((item: any) => item.averagePriceUsd > 0)
          const avgPrice = avgPriceList?.reduce((a: any, b: any) => a + Number(b.averagePriceUsd), 0) / avgPriceList.length || 0
          setAvgPrice(avgPrice)
          setTotalVolume(history[history.length - 1].totalVolumeUsd) // total volume end day
          const data: any = history.map((item: any) => {
          
            return {
              time: item.date,
              price: parseFloat(item.averagePriceUsd).toString(),
              volume: parseFloat(item.dailyVolumeUsd).toString(),
            }
          })
          setCollectionDailyStats(data)
        } else { 
          const data: any = []
          setCollectionDailyStats(data)
          setAvgPrice(0)
          setTotalVolume(0)
        }
      
      })
      .catch((_error) => {
        const data: any = []
        setCollectionDailyStats(data)
        setAvgPrice(0)
        setTotalVolume(0)
      })
    setIsCollectionDailyStatsReady(true)
  }
  const getDefaultRanges = () => {
    let rs = periodRanges.filter((range) => range.name === filterCollectionDailyStatParams.periodRange)[0] || null
    return rs
  }
  const mapActivities = (activities: Activity[]) => {
    try {
      
      const rs: any = activityUseCase.mapActivitiesForAssetV2(activities)
      setActivitiesTable(rs)
    }catch (err) {
      console.log('🚀 ~ file: activities.tsx:mapActivities ~ err:', err)
    }
  }
  const formatPaymentTokens = () => {
    if(!collection) return
    console.log('🚀 ~ tokens ~ collection:', collection)
    const tokens = collection?.paymentTokens?.map((token: any) => {
      console.log('🚀 ~ tokens ~ token:', token)
      return {
        id: token.id,
        name: token.label,
        label: token.label,
        value: token.id,
        icon: `${token.name?.toLowerCase()}.png`,
      }
    }) || []
    console.log('🚀 ~ tokens ~ tokensAAA:', tokens)
    setPaymentTokens(tokens)
    setIsFilterPaymentTokensReady(true)
  }
  // Add formatTraits function
  const formatTraits = () => {
    console.log('🚀 ~ formatTraits ~ collection:', collection)
    const traits = collection?.traitTypes?.map((trait: any) => {
      return {
        name: trait.traitType,
        values: trait.values.map((value: any) => {
          return {
            value: value.value,
            count: value.amount,
          }
        }),
      }
    }) || []
    console.log('🚀 ~ formatTraits ~ traits:', traits)
    setTraits(traits)
  }
  useEffect(() => {
    formatPaymentTokens()
    formatTraits() // Add call to format traits
  }, [collection])
  useEffect(() => {
    setIsActivitiesReady(true)
    mapActivities(activities)
  }, [activities])

  useEffect(() => {
    setIsCollectionDailyStatsReady(true)
  }, [collectionDailyStats])

  useEffect(() => {
    if (collection?.id && isFilterPaymentTokensReady) {
      fetchActivities()
    }
  
  }, [activityFilterParams, collection?.id, isFilterPaymentTokensReady, selectedTraits])


  useEffect(() => {
    if (collection?.id && isFilterPaymentTokensReady) {
      fetchCollectionDailyStats()
    }
  }, [activityFilterParams, filterCollectionDailyStatParams, collection?.id, isFilterPaymentTokensReady])

  return (
    <div className="product-layout mt-7">
      <div className="product-layout-filter">
        <ToggleWrap title="Filter" contentClass="border-t-0">
          {/* Filter event type */}
          <ToggleVertical toggleTitle="Event Type" defaultShow>
            <ul className="fist-mt-none">
              {filterEventTypes.map((type: any, index: number) => (
                <li
                  key={index}
                  className="mt-5 font-medium flex items-center justify-between"
                >
                  {type.name}
                  <Switch
                    onClick={(activeState) => {
                      setCurrentPage(1)
                      changeActivityFilterParam('page', 1)
                      changeActivityFilterParam(
                        'filterEventTypes',
                        type.type,
                        activeState
                      )
                    }}
                  />
                </li>
              ))}
            </ul>
          </ToggleVertical>
          <ToggleVerticalFilterUnitPrice
            defaultShow
            minPrice={activityFilterParams.minPrice || ''}
            maxPrice={activityFilterParams.maxPrice || ''}
            tokenList={paymentTokens}
            changeFilterParam={changeActivityFilterParam}
          />
          {traits?.length > 0 && <ToggleVerticalFilterTraits
            defaultShow
            traits={traits}
            onToggleTrait={handleTraitSelection}
          />}
          {/* <ToggleVerticalFilterChains
            defaultShow
            chains={chains}
            changeFilterParam={changeActivityFilterParam}
          /> */}
        </ToggleWrap>
      </div>

      <div className="flex flex-col xl:w-9/12 md:w-3/5 md2:w-4/6 w-full pr-third">
        {isCollectionDailyStatsReady &&
           (
             <div className="flex flex-col w-full">
               <div className="flex flex-wrap lg:flex-nowrap justify-between items-center xl:pl-[36px] xl:pr-[62px] mb-4">
                 <PeriodRangesSelect
                   periodRanges={periodRanges}
                   defaultSelected={getDefaultRanges()}
                   onSelect={(option) => {
                     console.log('🚀 ~ ActivityTag ~ option:', option)
                     setSelectedPeriodRangeText(option.name)
                     changeCollectionDailyStatFilterParam(
                       'periodRange',
                       option.value
                     )
                   }}
                 />
                 <div className="flex flex-wrap justify-end w-full md:w-auto gap-4">
                   <div>
                     <p className="font-medium text-sm">
                       {selectedPeriodRangeText}  Avg. Price
                     </p>
                     <p className="font-bold text-primary-2">{ formatNumber(avgPrice) }</p>
                   </div>
                   <div>
                     <p className="font-medium text-sm">
                       {selectedPeriodRangeText}  Volume
                     </p>
                     <p className="font-bold text-primary-2">{ formatNumber(totalVolume) }</p>
                   </div>
                 </div>
               </div>
               <ActivityChart data={collectionDailyStats} />
             </div>
           )}

        <div className="flex flex-wrap justify-between mt-8 mb-3">
          <div className="xl:w-auto w-full">
            {!isActivitiesReady ? (
              <>
                <p className="w-36 h-4 bg-gray-3 rounded-lg animate-pulse"></p>
                <p className="mt-2 w-36 h-4 bg-gray-3 rounded-lg animate-pulse"></p>
              </>
            ) : (
              <>
                <p className="text-gray-1 dark:text-gray-3 text-stroke-thin">
                  Results：{total.toLocaleString()}
                </p>
              </>
            )}
          </div>
        </div>

        {!isActivitiesReady ? (
          <LoadingData />
        ) : (
          <>
            {activitiesTable.length > 0 ? (
              <div className="activity-board-wrapper">
                <div className="w-full overflow-x-auto border-b">
                  <Table
                    head={activityTableHead}
                    data={activitiesTable}
                    className="border-b min-w-[760px]"
                  />
                </div>
                <div className="flex justify-center w-full px-9 mt-5 pb-9">
                  <PaginationDot
                    currentPage={currentPage}
                    totalCount={total}
                    pageSize={PageSize}
                    onPageChange={(page: number) => {
                      setCurrentPage(page),
                      changeActivityFilterParam('page', page)
                    }}
                  />
                </div>
              </div>
            ) : (
              <div className="w-full rounded-lg border flex justify-center items-center py-24">
                <NoData />
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}

export default ActivityTag
