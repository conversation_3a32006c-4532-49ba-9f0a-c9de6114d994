import Asset from 'domain/models/asset'
import FilterStatus from 'domain/repositories/asset_repository/filter_status'
import { Query as AssetQuery } from 'domain/repositories/asset_repository/query'
import SortBy from 'domain/repositories/asset_repository/sort_by'
import Chain from 'domain/value_objects/chain'
import PaymentToken from 'domain/value_objects/payment_token'
import { pageSizes } from 'lib/hook/customHook'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import AssetComponent from 'presentation/components/asset'
import NoData from 'presentation/components/no-data'
import PaginationDot from 'presentation/components/pagination/pagination-dot'
import Select from 'presentation/components/select'
import SkeletonAsset from 'presentation/components/skeleton/assets'
import ToggleWrap from 'presentation/components/toggle-wrap'
import ToggleVerticalFilterChains from 'presentation/components/toggle-wrap/toggle-vertical-filter-chains'
import ToggleVerticalFilterPaymentTokens from 'presentation/components/toggle-wrap/toggle-vertical-filter-payment-tokens'
import ToggleVerticalFilterPrice from 'presentation/components/toggle-wrap/toggle-vertical-filter-price'
import ToggleVerticalFilterStatues from 'presentation/components/toggle-wrap/toggle-vertical-filter-statues'
import AssetUseCase from 'presentation/use_cases/asset_use_case'
import { useEffect, useState } from 'react'
import { FilterParams } from 'types/type'

const assetUseCase = new AssetUseCase()
const filterStatuses = FilterStatus.getResourceArray()
const sortList = SortBy.getResourceArray()
const chains = Chain.getResourceArray().map((chain) => {
  return Chain.fromName<Chain>(chain.name)
})
const paymentTokens = PaymentToken.getResourceArray()

type Props = {
  collection: any;
};

let PageSize = pageSizes.pageSize25

const BundlesTag = ({ collection = {} }: Props) => {
  const initParams: FilterParams = {
    collectionIds: [collection.id],
    filterStatuses: [],
    paymentTokens: [],
    priceTokenUnit: '',
    minPrice: '',
    maxPrice: '',
    chains: [],
    page: 1,
    limit: PageSize,
    sortBy: 'recentlyListed', // Not empty
    ownerId: '',
  }

  const { filterParams, setFilterParams, changeFilterParam } =
    useChangeFilterParams(initParams)
  const [currentPage, setCurrentPage] = useState(1)
  const [assets, setAssets] = useState<Asset[]>([])
  const [isFetchingAssets, setIsFetchingAssets] = useState(false)
  const [totalAssets, setTotalAssets] = useState(0)

  const fetchAssets = async () => {
    setIsFetchingAssets(true)
    let queryTotalCount = new AssetQuery({
      collectionIds: filterParams.collectionIds,
      filterStatus: filterParams.filterStatuses?.map((status) => {
        return FilterStatus.fromName(status)
      }),
      paymentTokens: filterParams.paymentTokens?.map((token) => {
        return PaymentToken.fromName(token)
      }),
      minPrice: filterParams.minPrice,
      maxPrice: filterParams.maxPrice ,
      chains: filterParams.chains,
      sortBy: SortBy.fromName(filterParams.sortBy as string),
    })
    assetUseCase
      .totalCount(queryTotalCount)
      .then((res) => {
        setTotalAssets(res)
      })
      .catch((err) => {
        setTotalAssets(0)
      })
    let queryAssets = new AssetQuery({
      collectionIds: filterParams.collectionIds,
      filterStatus: filterParams.filterStatuses?.map((status) => {
        return FilterStatus.fromName(status)
      }),
      paymentTokens: filterParams.paymentTokens?.map((token) => {
        return PaymentToken.fromName(token)
      }),
      minPrice: filterParams.minPrice,
      maxPrice: filterParams.maxPrice,
      chains: filterParams.chains,
      page: filterParams.page,
      limit: PageSize,
      sortBy: SortBy.fromName(filterParams.sortBy as string),
    })
    assetUseCase
      .search(queryAssets)
      .then((res) => {
        setAssets(res)
      })
      .catch(() => {
        setAssets([])
      })
    setIsFetchingAssets(false)
  }

  useEffect(() => {
    fetchAssets()
  }, [filterParams])

  return (
    <div className="product-layout mt-7">
      <div className="product-layout-filter">
        <ToggleWrap title="Filter" contentClass="border-t-0">
          <ToggleVerticalFilterStatues
            defaultShow
            filterStatues={filterStatuses}
            changeFilterParam={changeFilterParam}
          />
          <ToggleVerticalFilterPrice
            defaultShow
            minPrice={filterParams.minPrice || ''}
            maxPrice={filterParams.maxPrice || ''}
            changeFilterParam={changeFilterParam}
          />
          <ToggleVerticalFilterChains
            defaultShow
            chains={chains}
            changeFilterParam={changeFilterParam}
          />
          <ToggleVerticalFilterPaymentTokens
            defaultShow
            tokens={paymentTokens}
            changeFilterParam={changeFilterParam}
          />
        </ToggleWrap>
      </div>

      <div className="flex flex-col xl:w-9/12 md:w-3/5 md2:w-4/6 w-full pr-third">
        <div className="flex flex-wrap justify-between mb-5">
          <div className="xl:w-auto w-full">
            {isFetchingAssets ? (
              <>
                <p className="w-36 h-4 bg-gray-3 rounded-lg animate-pulse"></p>
                <p className="mt-2 w-36 h-4 bg-gray-3 rounded-lg animate-pulse"></p>
              </>
            ) : (
              <>
                <p className="text-gray-1 dark:text-gray-3 text-stroke-thin">
                  Results：{totalAssets.toLocaleString()}
                </p>
                <p className="text-gray-1 dark:text-gray-3 text-stroke-thin mt-2">
                  Floor Price：{collection.floorPrice?.toLocaleString() || 0}
                </p>
              </>
            )}
          </div>
          <div className="mt-2 xl:mt-0 flex flex-wrap lg:flex-nowrap gap-5">
            {/* <SelectSearch options={options} className="shrink-0" /> */}
            <Select
              options={sortList}
              onSelect={(option) => {
                changeFilterParam('sortBy', option.value)
              }}
              className="h-fit"
            />
          </div>
        </div>

        <div
          className={`product-wrap no-gap ${!isFetchingAssets && assets.length === 0 ? 'no-data' : ''
          }
            justify-center md:justify-start`}
        >
          {isFetchingAssets ? (
            [...Array(5)].map((_, id) => <SkeletonAsset key={id} />)
          ) : (
            <>
              {assets.length > 0 ? (
                assets.map((asset: any, index: number) => (
                  <AssetComponent
                    key={asset.id + '-' + index}
                    record={asset}
                    showDaysLeft={false}
                    isBundle
                  />
                ))
              ) : (
                <div className="w-full rounded-lg border flex justify-center items-center py-24">
                  <NoData />
                </div>
              )}
            </>
          )}
        </div>
        {assets.length > 20 && (
          <div className="flex justify-center mt-5">
            <PaginationDot
              currentPage={currentPage}
              totalCount={totalAssets}
              pageSize={PageSize}
              onPageChange={(page: number) => {
                setCurrentPage(page),
                setFilterParams({ ...filterParams, page })
              }}
            />
          </div>
        )}
      </div>
    </div>
  )
}

export default BundlesTag
