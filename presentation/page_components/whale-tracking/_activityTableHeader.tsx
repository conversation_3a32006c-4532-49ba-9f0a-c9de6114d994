import SvgIcon from 'presentation/components/svg-icon'
import AssetTableItem from 'presentation/components/table/asset-item'

export const activityTableHeader = [
  {
    title: '#',
    key: 'id',
  },
  {
    title: 'NFT',
    key: 'nft',
    render: (data: any) => (
      <AssetTableItem
        asset={data}
        imageSize={32}
        imageRounded="rounded-[10px]"
        nameOnly
      />
    ),
  },
  {
    title: 'Action',
    key: 'action',
    render: ({icon, name}: any) => (
      <div className="flex items-center">
        <SvgIcon name={icon} className="w-5 h-5 stroke-none fill-dark-primary"/>
        <p className="font-medium ml-2">{name}</p>
      </div>
    ),
  },
  {
    title: 'Price',
    key: 'price',
    render: ({coinPrice, usdPrice}: any) => (
      <div className="text-right font-medium">
        <p>{coinPrice}</p>
        <p>{usdPrice}</p>
      </div>
    ),
  },
  {
    title: 'From',
    key: 'from',
    render: ({address}: any) => (
      <div className="text-right text-primary-2">
        <p>📃 {address}</p>
      </div>
    ),
  },
  {
    title: 'To',
    key: 'to',
    render: ({address}: any) => (
      <div className="text-right text-primary-2">
        <p>🐋 {address}</p>
      </div>
    ),
  },
  {
    title: 'Gas',
    key: 'gas',
    render: ({amount}: any) => (
      <div className="text-right">
        <p>{amount} ETH</p>
      </div>
    ),
  },
  {
    title: 'Date',
    key: 'date',
    render: ({datetime}: any) => (
      <div className="flex justify-end">
        <p className="pl-2 w-max font-medium text-primary-2">{datetime}</p>
      </div>
    ),
  },
]
