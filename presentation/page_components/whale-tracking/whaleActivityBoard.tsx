import Link from 'next/link'
import { useState, useEffect } from 'react'
import { rowOptions } from 'presentation/controllers/mockData/options'
import Switch from 'presentation/components/switch'
import Tooltip from 'presentation/components/tooltip'
import Select from 'presentation/components/select'
import ButtonIcon from 'presentation/components/button/button-icon'
import Table from 'presentation/components/table'
import Pagination from 'presentation/components/pagination'
import LoadingData from 'presentation/components/skeleton/loading'
import DatetimeRange from 'presentation/components/datetimeRange'

import { activityTableHeader } from './_activityTableHeader'

const WhaleActivityBoard = (props: any) => {
  const {
    currentPage = 1,
    currentLimit = 10,
    tableData = [],
    total = 0,
    filterParams,
    onChangePage = () => {},
    onChangeCurrentLimit = () => {},
    onChangeFilterParam = () => {},
    onChangeFilterParams = () => {},
    isFetching = true,
    collections = [],
    isFilterCollectionsReady = false,
    categories = [],
    isFilterCategoriesReady = false,
  } = props

  const [filterCollections, setFilterCollections] = useState<any>([])
  const [filterCategories, setFilterCategories] = useState<any>([])
  const [showDateRange, setShowDateRange] = useState(false)

  const mergeCollections = () => {
    const mergedCollections = [
      {
        name: 'Collection',
        value: '',
      },
    ].concat(collections)
    setFilterCollections(mergedCollections)
  }

  const mergeCategories = () => {
    const mergedCategories = [
      {
        name: 'All',
        value: '',
      },
    ].concat(categories)
    setFilterCategories(mergedCategories)
  }

  useEffect(() => {
    if (collections.length > 0) {
      mergeCollections()
    }
  }, [collections])

  useEffect(() => {
    if (categories.length > 0) {
      mergeCategories()
    }
  }, [categories])

  return (
    <div className="mt-11">
      <p className="text-23 font-bold text-dark-primary dark:text-white">
        Whale Activities
      </p>
      <div className="mt-10p flex justify-between items-center flex-wrap gap-y-5">
        <p className="w-full lg:w-1/3 text-gray-1 dark:text-gray-3 text-stroke-thin">
          During the past 1 hour, 🐋 9 whales generated 48 activities, most of
          them are from{' '}
          <Link href="/" className="text-primary-2">
            MekaApes Game by OogaVerse.
          </Link>
        </p>
        <div className="w-full lg:w-2/3">
          <div className="flex flex-wrap items-center justify-end gap-5">
            <div className="flex items-center">
              <Switch
                defaultActive={filterParams.listed}
                onClick={(active) => onChangeFilterParam('listed', active)}
              />
              <div className="ml-1 flex items-center">
                <p className="text-sm font-bold text-dark-primary dark:text-white">
                  Listed
                </p>
                <Tooltip className="-mt-0.5">Listed description</Tooltip>
              </div>
            </div>

            {isFilterCollectionsReady && (
              <Select
                customWidth={true}
                rounded
                options={filterCollections}
                className="w-44"
                arrowIconClass="stroke-none fill-primary-2"
                isCollectionList={true}
                onSelect={(option) =>
                  onChangeFilterParam('collectionId', option.id)
                }
                defaultSelected={{
                  name: 'Collection',
                  value: '',
                }}
              />
            )}
            {isFilterCategoriesReady && (
              <Select
                customWidth={true}
                options={filterCategories}
                onSelect={(option) =>
                  onChangeFilterParam('categoryId', option.value)
                }
                className="w-44"
                defaultSelected={{
                  name: 'All',
                  value: '',
                }}
              />
            )}

            <div className="inline-flex items-center gap-3">
              <div className="flex relative w-10 h-10 z-50">
                <ButtonIcon
                  svgIcon="calendar"
                  hasShadow={true}
                  iconClassName="w-5 h-5 stroke-none fill-primary"
                  onClick={() => setShowDateRange(!showDateRange)}
                />
                {showDateRange && (
                  <div className="absolute top-10 -right-[500%]">
                    <DatetimeRange
                      showTimeInput={false}
                      showDuration={false}
                      onCloseDateRange={() => {
                        setShowDateRange(false)
                      }}
                    />
                  </div>
                )}
              </div>
              <ButtonIcon
                svgIcon="refresh"
                hasShadow={true}
                iconClassName="w-5 h-5 stroke-none fill-primary"
                onClick={() => {
                  onChangePage(1)
                  onChangeFilterParam('page', 1)
                }}
              />
              <Select
                options={rowOptions}
                onSelect={(option) => {
                  onChangePage(1)
                  onChangeCurrentLimit(option.value)
                  onChangeFilterParams({
                    ...filterParams,
                    limit: option.value,
                    page: 1,
                  })
                }}
              />
            </div>
          </div>
        </div>
      </div>

      <div className="mt-5 table-wrap border rounded-20">
        {isFetching ? (
          <LoadingData />
        ) : (
          <>
            <div className="w-full overflow-x-auto pb-3">
              <Table
                head={activityTableHeader}
                data={tableData}
                className="common-table whale-activity-analytics min-w-[1200px]"
              />
            </div>
            <div className="mt-2 px-5 mb-9">
              <Pagination
                selectRowOptions={rowOptions}
                currentPage={currentPage}
                totalCount={total}
                pageSize={currentLimit}
                onPageChange={(page: number) => {
                  onChangePage(page)
                  onChangeFilterParams({ ...filterParams, page })
                }}
                onChangePageSize={(limit: number) => {
                  onChangePage(1)
                  onChangeCurrentLimit(limit)
                  onChangeFilterParams({ ...filterParams, limit, page: 1 })
                }}
              />
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default WhaleActivityBoard
