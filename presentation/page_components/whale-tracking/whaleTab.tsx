import { useState, useEffect } from 'react'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import { rowOptions } from 'presentation/controllers/mockData/options'
import Pagination from 'presentation/components/pagination'
import Table from 'presentation/components/table'
import Tooltip from 'presentation/components/tooltip'
import Switch from 'presentation/components/switch'
import LoadingData from 'presentation/components/skeleton/loading'
import { whaleTableHeader } from './_whaleTableHeader'
import { pageSizes } from 'lib/hook/customHook'

//Todo remove mock data
import {
  whaleTempData,
} from 'presentation/controllers/mockData/whale_tracking_mock_data'
//End todo remove mock data

const PageSize = pageSizes.pageSize10

const WhaleTab = () => {
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [currentLimit, setCurrentLimit] = useState<number>(PageSize)
  const [total, setTotal] = useState<number>(0)
  const [isFetching, setIsFetching] = useState<boolean>(true)
  const [tableData, setTableData] = useState<any>([])
  const { filterParams, changeFilterParam, setFilterParams } = useChangeFilterParams({
    sortBy: '',
    page: 1,
    limit: PageSize,
  })

  const options = [{
    name: '10 rows',
    value: '10',
  }].concat(rowOptions)

  const fetchTableData = () => {
    setIsFetching(true)
    //TODO
    setTotal(whaleTempData.length)
    let data = whaleTempData.slice((currentPage - 1) * currentLimit, currentPage * currentLimit)
    setTableData(data)
    setTimeout(() => {
      setIsFetching(false)
    }, 300)
  }

  useEffect(() => {
    fetchTableData()
  }, [filterParams])

  return (
    <div>
      <div className="mt-7 flex justify-between">
        <div className="flex items-center shrink-0">
          <p className="text-lg font-bold text-dark-primary dark:text-white">All Whales</p>
          <Tooltip className="-mt-0.5">All Whales</Tooltip>
        </div>

        <div className="flex items-center">
          <Switch onClick={(value) => changeFilterParam('sortBy', value)} />
          <div className="ml-1 flex items-center shrink-0">
            <p className="text-sm font-bold text-dark-primary dark:text-white">Contact</p>
            <Tooltip className="-mt-1">Contact</Tooltip>
          </div>
        </div>
      </div>
      <div className="table-wrap mt-5 border rounded-20">
        {isFetching ? (
          <LoadingData />
        ) : (
          <>
            <div className="w-full overflow-x-auto pb-3">
              <Table
                head={whaleTableHeader}
                data={tableData}
                className="table-no-border all-whales-board min-w-[1820px]"
              />
            </div>
            <div className="mt-2 px-5 mb-9">
              <Pagination
                enableSelectRow={true}
                selectRowOptions={options}
                currentPage={currentPage}
                totalCount={total}
                pageSize={currentLimit}
                onPageChange={(page: number) => {
                  setCurrentPage(page)
                  setFilterParams({ ...filterParams, page })
                }}
                onChangePageSize={(limit: number) => {
                  setCurrentPage(1)
                  setCurrentLimit(limit)
                  setFilterParams({ ...filterParams, limit, page: 1 })
                }}
              />
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default WhaleTab
