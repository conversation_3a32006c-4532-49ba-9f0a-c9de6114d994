import { useState, useEffect } from 'react'
import WhaleInvolvedBoard from '../whale-tracking/whaleInvolvedBoard'
import BigWhalesBoughtBoard from '../whale-tracking/bigWhalesBoughtBoard'
import WhalesBoughtBoard from '../whale-tracking/whalesBoughtBoard'
import TopSellerBoard from '../analytics/topSellerBoard'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import { topSellerHeader } from './_tradeTableHeader'

//Todo remove mock data
import { filterTimeOptions, filterBuySell } from 'presentation/controllers/mockData/options'
import {
  whaleInvolvedTempData,
  bigWhalesBoughtTempData,
  topBuyerData as topBuyerTableData,
  topSellerData as topSellerTableData,
} from 'presentation/controllers/mockData/whale_tracking_mock_data'
import { pageSizes } from 'lib/hook/customHook'
//End todo remove mock data

const PageSize = pageSizes.pageSize10

const WhaleTrackingTradeTab = () => {
  const [whaleInvolvedCurrentPage, setWhaleInvolvedCurrentPage] = useState(1)
  const [whaleInvolvedCurrentLimit, setWhaleInvolvedCurrentLimit] = useState(PageSize)
  const [whaleInvolvedData, setWhaleInvolvedData] = useState<any>([])
  const [whaleInvolvedTotal, setWhaleInvolvedTotal] = useState(0)
  const {
    filterParams: whaleInvolvedFilterParams,
    changeFilterParam: onChangeWhaleInvolvedFilterParam,
    setFilterParams: onChangeWhaleInvolvedFilterParams,
  } = useChangeFilterParams({
    listed: false,
    sortBy: filterBuySell[0]?.value,
    page: 1,
    limit: PageSize,
  })
  const [isFetchingWhaleInvolved, setIsFetchingWhaleInvolved] = useState(true)

  const [bigWhalesBoughtCurrentPage, setBigWhalesBoughtCurrentPage] = useState(1)
  const [bigWhalesBoughtCurrentLimit, setBigWhalesBoughtCurrentLimit] = useState(PageSize)
  const [bigWhalesBoughtData, setBigWhalesBoughtData] = useState<any>([])
  const [bigWhalesBoughtTotal, setBigWhalesBoughtTotal] = useState(0)
  const {
    filterParams: bigWhalesBoughtFilterParams,
    changeFilterParam: onChangeBigWhalesBoughtFilterParam,
    setFilterParams: onChangeBigWhalesBoughtFilterParams,
  } = useChangeFilterParams({
    listed: false,
    sortBy: filterBuySell[0]?.value,
    page: 1,
    limit: PageSize,
  })
  const [isFetchingBigWhalesBought, setIsFetchingBigWhalesBought] = useState(true)

  const [whalesBoughtCurrentPage, setWhalesBoughtCurrentPage] = useState(1)
  const [whalesBoughtData, setWhalesBoughtData] = useState<any>([])
  const [whalesBoughtTotal, setWhalesBoughtTotal] = useState(0)
  const {
    filterParams: whalesBoughtFilterParams,
    changeFilterParam: onChangeWhalesBoughtFilterParams,
  } = useChangeFilterParams({
    listed: false,
    sortBy: filterTimeOptions[0]?.value,
    page: 1,
    limit: PageSize,
  })
  const [isFetchingWhalesBought, setIsFetchingWhalesBought] = useState(true)

  const [topSellerCurrentPage, setTopSellerCurrentPage] = useState(1)
  const [topSellerData, setTopSellerData] = useState<any>([])
  const [topSellerTotal, setTopSellerTotal] = useState(0)
  const {
    filterParams: topSellerFilterParams,
    changeFilterParam: onChangeTopSellerFilterParams,
  } = useChangeFilterParams({
    listed: false,
    sortBy: filterTimeOptions[0]?.value,
    page: 1,
    limit: PageSize,
  })
  const [isFetchingTopSeller, setIsFetchingTopSeller] = useState(true)

  const fetchWhaleInvolvedTableData = () => {
    setIsFetchingWhaleInvolved(true)
    //TODO
    setWhaleInvolvedTotal(whaleInvolvedTempData.length)
    let data = whaleInvolvedTempData.slice((whaleInvolvedCurrentPage - 1) * whaleInvolvedCurrentLimit,
      whaleInvolvedCurrentPage * whaleInvolvedCurrentLimit)
    setWhaleInvolvedData(data)
    setTimeout(() => {
      setIsFetchingWhaleInvolved(false)
    }, 300)
  }

  const fetchBigWhalesBoughtTableData = () => {
    setIsFetchingBigWhalesBought(true)
    //TODO
    setBigWhalesBoughtTotal(bigWhalesBoughtTempData.length)
    let data = bigWhalesBoughtTempData.slice((bigWhalesBoughtCurrentPage - 1) * bigWhalesBoughtCurrentLimit,
      bigWhalesBoughtCurrentPage * bigWhalesBoughtCurrentLimit)
    setBigWhalesBoughtData(data)
    setTimeout(() => {
      setIsFetchingBigWhalesBought(false)
    }, 300)
  }

  const fetchWhalesBoughtTableData = () => {
    setIsFetchingWhalesBought(true)
    //TODO
    setWhalesBoughtTotal(topBuyerTableData.length)
    let data = topBuyerTableData.slice((whalesBoughtCurrentPage - 1) * PageSize, whalesBoughtCurrentPage * PageSize)
    setWhalesBoughtData(data)
    setTimeout(() => {
      setIsFetchingWhalesBought(false)
    }, 300)
  }

  const fetchTopSellerTableData = () => {
    setIsFetchingTopSeller(true)
    //TODO
    setTopSellerTotal(topSellerTableData.length)
    let data = topSellerTableData.slice((whalesBoughtCurrentPage - 1) * PageSize, whalesBoughtCurrentPage * PageSize)
    setTopSellerData(data)
    setTimeout(() => {
      setIsFetchingTopSeller(false)
    }, 300)
  }

  useEffect(() => {
    fetchWhaleInvolvedTableData()
  }, [whaleInvolvedFilterParams])

  useEffect(() => {
    fetchBigWhalesBoughtTableData()
  }, [bigWhalesBoughtFilterParams])

  useEffect(() => {
    fetchWhalesBoughtTableData()
  }, [whalesBoughtFilterParams])

  useEffect(() => {
    fetchTopSellerTableData()
  }, [topSellerFilterParams])

  return (
    <>
      <WhaleInvolvedBoard
        filterBuySell={filterBuySell}
        defaultBuySellFilter={filterBuySell[0]}
        filterTimeOptions={filterTimeOptions}
        defaultTimeFilter={filterTimeOptions[0]}
        currentPage={whaleInvolvedCurrentPage}
        currentLimit={whaleInvolvedCurrentLimit}
        tableData={whaleInvolvedData}
        total={whaleInvolvedTotal}
        filterParams={whaleInvolvedFilterParams}
        onChangePage={setWhaleInvolvedCurrentPage}
        onChangeCurrentLimit={setWhaleInvolvedCurrentLimit}
        onChangeFilterParam={onChangeWhaleInvolvedFilterParam}
        onChangeFilterParams={onChangeWhaleInvolvedFilterParams}
        isFetching={isFetchingWhaleInvolved}
      />
      <BigWhalesBoughtBoard
        filterBuySell={filterBuySell}
        defaultBuySellFilter={filterBuySell[0]}
        filterTimeOptions={filterTimeOptions}
        defaultTimeFilter={filterTimeOptions[0]}
        currentPage={bigWhalesBoughtCurrentPage}
        currentLimit={bigWhalesBoughtCurrentLimit}
        tableData={bigWhalesBoughtData}
        total={bigWhalesBoughtTotal}
        filterParams={bigWhalesBoughtFilterParams}
        onChangePage={setBigWhalesBoughtCurrentPage}
        onChangeCurrentLimit={setBigWhalesBoughtCurrentLimit}
        onChangeFilterParam={onChangeBigWhalesBoughtFilterParam}
        onChangeFilterParams={onChangeBigWhalesBoughtFilterParams}
        isFetching={isFetchingBigWhalesBought}
      />
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-10 mt-10">
        <WhalesBoughtBoard
          filterTimeOptions={filterTimeOptions}
          defaultTimeFilter={filterTimeOptions[0]}
          currentPage={whalesBoughtCurrentPage}
          setCurrentPage={setWhalesBoughtCurrentPage}
          tableData={whalesBoughtData}
          total={whalesBoughtTotal}
          onChangeFilterParam={onChangeWhalesBoughtFilterParams}
          isFetching={isFetchingWhalesBought}
        />
        <TopSellerBoard
          header={topSellerHeader}
          filterTimeOptions={filterTimeOptions}
          defaultTimeFilter={filterTimeOptions[0]}
          currentPage={topSellerCurrentPage}
          setCurrentPage={setTopSellerCurrentPage}
          tableData={topSellerData}
          total={topSellerTotal}
          onChangeFilterParam={onChangeTopSellerFilterParams}
          isFetching={isFetchingTopSeller}
        />
      </div>
    </>
  )
}

export default WhaleTrackingTradeTab
