import CollectionTableItem from 'presentation/components/table/collection-item'
import SvgIcon from 'presentation/components/svg-icon'
import PricePercentTableItem from 'presentation/components/table/price-percentage'
import PriceBarTableItem from 'presentation/components/table/price-bar'
import AssetTableItem from 'presentation/components/table/asset-item'

export const whaleInvolvedTableHeader = [
  {
    title: '#',
    key: 'id',
  },
  {
    title: 'Collection',
    key: 'collection',
    render: (data: any) => (<CollectionTableItem record={data} />),
  },
  {
    title: 'Whales',
    key: 'whales',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: ({ amount }: any) => (
      <div className="text-right" style={{ paddingRight: '26px' }}>
        <p>{amount}</p>
      </div>
    ),
  },
  {
    title: 'Bought',
    key: 'bought',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: ({qty}: any) => (
      <div className="flex justify-end items-center space-x-1" style={{ paddingRight: '26px' }}>
        <span className="text-primary-2 leading-none mt-1">{qty}</span>
        <SvgIcon
          name="columnChart"
          className="w-4 fill-primary-2"
          viewBox="0 0 16 14.546"
        />
      </div>
    ),
  },
  {
    title: 'Whale volume',
    key: 'whaleVolume',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: (data: any) => (<PriceBarTableItem {...data}/>),
  },
  {
    title: 'Total Volume(1H)',
    key: 'totalVolume',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: (data: any) => (<PricePercentTableItem {...data}/>),
  },
  {
    title: 'Floor Price',
    key: 'floorPrice',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: (data: any) => (<PricePercentTableItem {...data}/>),
  },
  {
    title: 'Avg Price(24Hl)',
    key: 'avgPrice',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'bottomRight',
    },
    sortable: true,
    render: (data: any) => (<PricePercentTableItem {...data} paddingRight="40px" />),
  },
]

export const bigWhalesBoughtTableHeader = [
  {
    title: '#',
    key: 'id',
  },
  {
    title: 'NFT',
    key: 'nft',
    render: (data: any) => (
      <AssetTableItem
        asset={data}
        imageSize={32}
        imageRounded="rounded-[10px]"
        nameOnly
      />
    ),
  },
  {
    title: 'Collection',
    key: 'collection',
    render: (data: any) => (<CollectionTableItem record={data} />),
  },
  {
    title: 'Price',
    key: 'price',
    render: ({ amount }: any) => (
      <div className="text-right" style={{ paddingRight: '7px' }}>
        <p>{amount} ETH</p>
      </div>
    ),
  },
  {
    title: 'Change',
    key: 'change',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: (data: any) => (<PricePercentTableItem {...data}/>),
  },
  {
    title: 'Seller',
    key: 'seller',
    render: ({ address }: any) => (
      <div className="text-right" style={{ paddingRight: '7px' }}>
        <p>{address}</p>
      </div>
    ),
  },
  {
    title: 'Buyer',
    key: 'buyer',
    render: ({ address }: any) => (
      <div className="text-right" style={{ paddingRight: '7px' }}>
        <p>🐋 {address}</p>
      </div>
    ),
  },
  {
    title: 'Time',
    key: 'time',
    render: ({ datetime }: any) => (
      <div className="text-right" style={{ paddingRight: '18px' }}>
        <p>{datetime}</p>
      </div>
    ),
  },
]

export const whalesBoughtHeader = [
  {
    title: '#',
    key: 'id',
  },
  {
    title: 'Whale',
    key:'address',
    render: ({imageSrc, address}: any) => (
      <div className="max-w-[6rem] flex items-center">
        {/* <Image alt='' src={imageSrc} width={21} height={21}/> */}
        <p className="font-medium truncate">{address}</p>
      </div>
    ),
  },
  {
    title: 'Buy Volume',
    key: 'buyvolume',
    sortable: true,
    description: 'Buy volume description',
    options: {
      tooltipPosition: 'right',
    },
    render: ({price, percentage}: any) => (
      <div className="flex flex-wrap justify-end w-full" style={{ paddingRight: '14px' }}>
        <p>{price}</p>
        <div className="percentage-bar">
          <div className="percentage-track" style={{width: percentage}}></div>
        </div>
      </div>
    ),
  },
  {
    title: 'Bought',
    key: 'bought',
    description: 'Bought description',
    sortable: true,
    options: {
      tooltipPosition: 'bottomLeft',
    },
    render: ({count}: any) => (
      <div className="w-full" style={{ paddingRight: '14px' }}>
        <p className="text-right">{count}</p>
      </div>
    ),
  },
  {
    title: 'Collections',
    key: 'collections',
    description: 'Collections description',
    sortable: true,
    options: {
      tooltipPosition: 'bottomRight',
    },
    render: ({count}: any) => (
      <div className="w-full" style={{ paddingRight: '8px' }}>
        <p className="text-right">{count}</p>
      </div>
    ),
  },
]

export const topSellerHeader = [
  {
    title: '#',
    key: 'id',
  },
  {
    title: 'Whale',
    key:'address',
    render: ({imageSrc, address}: any) => (
      <div className="max-w-[6rem] flex items-center">
        {/* <Image alt='' src={imageSrc} width={21} height={21}/> */}
        <p className="font-medium truncate">{address}</p>
      </div>
    ),
  },
  {
    title: 'Sell Volume',
    key: 'sellvolume',
    sortable: true,
    description: 'Buy volume description',
    options: {
      tooltipPosition: 'right',
    },
    render: ({price, percentage}: any) => (
      <div className="flex flex-wrap justify-end w-full" style={{ paddingRight: '14px' }}>
        <p>{price}</p>
        <div className="percentage-bar">
          <div className="percentage-track" style={{width: percentage}}></div>
        </div>
      </div>
    ),
  },
  {
    title: 'Sold',
    key: 'sold',
    description: 'Bought description',
    sortable: true,
    options: {
      tooltipPosition: 'right',
    },
    render: ({count}: any) => (
      <div className="w-full">
        <p className="text-right">{count}</p>
      </div>
    ),
  },
  {
    title: 'Collections',
    key: 'collections',
    sortable: true,
    description: 'Collections description',
    options: {
      tooltipPosition: 'bottomRight',
    },
    render: ({count}: any) => (
      <div className="w-full" style={{ paddingRight: '8px' }}>
        <p className="text-right">{count}</p>
      </div>
    ),
  },
]

export const whaleMintedTableHeader = [
  {
    title: '#',
    key: 'id',
  },
  {
    title: 'Collection',
    key: 'collection',
    render: (data: any) => (<CollectionTableItem record={data} />),
  },
  {
    title: 'Whales',
    key: 'whales',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: ({ amount }: any) => (
      <div className="text-right" style={{ paddingRight: '26px' }}>
        <p>{amount}</p>
      </div>
    ),
  },
  {
    title: 'Minted',
    key: 'minted',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: ({qty}: any) => (
      <div className="flex justify-end items-center space-x-1" style={{ paddingRight: '26px' }}>
        <span className="text-primary-2 leading-none mt-1">{qty}</span>
        <SvgIcon
          name="columnChart"
          className="w-4 fill-primary-2"
          viewBox="0 0 16 14.546"
        />
      </div>
    ),
  },
  {
    title: 'Mint Volume',
    key: 'mintVolume',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: (data: any) => (<PriceBarTableItem {...data}/>),
  },
  {
    title: 'Minters',
    key: 'minters',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: ({amount}: any) => (
      <div className="text-right" style={{ paddingRight: '26px' }}>
        <p className="text-primary-2">{amount}</p>
      </div>
    ),
  },
  {
    title: 'Total Gas',
    key: 'totalGas',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: (data: any) => (<PriceBarTableItem {...data}/>),
  },
  {
    title: 'First Mint',
    key: 'firstMint',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'bottomRight',
    },
    sortable: true,
    render: ({date}: any) => (
      <div className="text-right" style={{ paddingRight: '26px' }}>
        <p className="text-primary-2">{date}</p>
      </div>
    ),
  },
  {
    title: 'FOMO',
    key: 'fomo',
    render: ({level}: any) => (
      <div className="text-right" style={{ paddingRight: '18px' }}>
        <p className={`capitalize text-${level}`}>{level}</p>
      </div>
    ),
  },
]

export const whaleMintersTableHeader = [
  {
    title: '#',
    key: 'id',
  },
  {
    title: 'Whale',
    key: 'whale',
  },
  {
    title: 'Minted',
    key: 'minted',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: ({ amount }: any) => (
      <div className="text-right" style={{ paddingRight: '26px' }}>
        <p>{amount}</p>
      </div>
    ),
  },
  {
    title: 'Collection',
    key: 'collection',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: ({ amount }: any) => (
      <div className="text-right" style={{ paddingRight: '26px' }}>
        <p>{amount}</p>
      </div>
    ),
  },
  {
    title: 'Mint Volume',
    key: 'mintVolume',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: (data: any) => (<PriceBarTableItem {...data}/>),
  },
  {
    title: 'Gas Fee',
    key: 'gasFee',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: ({ amount }: any) => (
      <div className="text-right" style={{ paddingRight: '26px' }}>
        <p>{amount?.toLocaleString()} ETH</p>
      </div>
    ),
  },
  {
    title: 'Last Mint',
    key: 'lastMint',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'bottomRight',
    },
    sortable: true,
    render: ({datetime}: any) => (
      <div className="text-right" style={{ paddingRight: '36px' }}>
        <p className="text-primary-2">{datetime}</p>
      </div>
    ),
  },
]
