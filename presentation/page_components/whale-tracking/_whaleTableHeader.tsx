import CollectionTableItem from 'presentation/components/table/collection-item'
import PriceBarTableItem from 'presentation/components/table/price-bar'

export const whaleTableHeader = [
  {
    title: '#',
    key: 'id',
  },
  {
    title: 'Whale',
    key: 'whale',
    render: ({address}: any) => (<p className="wallet-address">{address}</p>),
  },
  {
    title: 'Most Holding',
    key: 'mostHolding',
    description: 'Description',
    options: {
      tooltipPosition: 'right',
    },
    render: (data: any) => (<CollectionTableItem record={data} />),
  },
  {
    title: 'Holding Value',
    key: 'holdingValue',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: (data: any) => (<PriceBarTableItem {...data}/>),
  },
  {
    title: 'Est HV',
    key: 'estHv',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: (data: any) => (<PriceBarTableItem {...data}/>),
  },
  {
    title: 'Pnl',
    key: 'pnl',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: (data: any) => (<PriceBarTableItem {...data}/>),
  },
  {
    title: 'Buy Volume',
    key: 'buyVolume',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: (data: any) => (<PriceBarTableItem {...data}/>),
  },
  {
    title: 'Sell Volume',
    key: 'sellVolume',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: (data: any) => (<PriceBarTableItem {...data}/>),
  },
  {
    title: 'Collections',
    key: 'collections',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: ({ amount }: any) => (
      <div className="text-right" style={{ paddingRight: '26px' }}>
        <p>{amount?.toLocaleString()}</p>
      </div>
    ),
  },
  {
    title: 'NFTs',
    key: 'nfts',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'right',
    },
    sortable: true,
    render: ({ amount }: any) => (
      <div className="text-right" style={{ paddingRight: '26px' }}>
        <p>{amount?.toLocaleString()}</p>
      </div>
    ),
  },
  {
    title: 'Activities',
    key: 'activities',
    description: 'Some discription here',
    options: {
      tooltipPosition: 'bottomRight',
    },
    sortable: true,
    render: ({ amount }: any) => (
      <div className="text-right" style={{ paddingRight: '26px' }}>
        <p>{amount?.toLocaleString()}</p>
      </div>
    ),
  },
  {
    title: 'Last Deal',
    key: 'lastDeal',
    sortable: true,
    render: ({datetime}: any) => (
      <div className="text-right" style={{ paddingRight: '18px' }}>
        <p className="text-primary-2">{datetime}</p>
      </div>
    ),
  },
]
