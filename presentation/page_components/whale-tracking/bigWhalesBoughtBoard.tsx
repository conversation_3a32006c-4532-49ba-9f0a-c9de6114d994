import Table from 'presentation/components/table'
import { useState } from 'react'
import { rowOptions } from 'presentation/controllers/mockData/options'
import Tooltip from 'presentation/components/tooltip'
import FilterTime from 'presentation/components/filter-time'
import Pagination from 'presentation/components/pagination'
import Select from 'presentation/components/select'
import LoadingData from 'presentation/components/skeleton/loading'
import {bigWhalesBoughtTableHeader} from './_tradeTableHeader'

const BigWhalesBoughtBoard = (props:any) => {
  const {
    filterBuySell = [],
    defaultBuySellFilter,
    filterTimeOptions = [],
    defaultTimeFilter,
    currentPage = 1,
    currentLimit = 10,
    tableData = [],
    total = 0,
    filterParams,
    onChangePage = () => {},
    onChangeCurrentLimit = () => {},
    onChangeFilterParam = () => {},
    onChangeFilterParams = () => {},
    isFetching = true,
  } = props

  const options = [{
    name: '10 rows',
    value: '10',
  }].concat(rowOptions)

  const [filterTime, setFilterTime] = useState(defaultTimeFilter ? defaultTimeFilter : filterTimeOptions[0])

  return (
    <div className="mt-10">
      <div className="flex flex-wrap justify-between items-center gap-y-5">
        <div className="flex items-center">
          <p className="text-lg font-bold text-dark-primary dark:text-white">
            Whales Bought({filterTime?.name})
          </p>
          <Tooltip>Description</Tooltip>
        </div>
        <div className="flex flex-wrap gap-5">
          {/* TODO correct param */}
          <FilterTime
            options={filterBuySell}
            defaultSelected={defaultBuySellFilter}
            onSelect={(option) => {onChangeFilterParam('sortBy', option.value)}}
          />
          <FilterTime
            options={filterTimeOptions}
            defaultSelected={defaultTimeFilter}
            onSelect={(option) => {
              setFilterTime(option)
              onChangeFilterParam('sortBy', option.value)
            }}
          />
          {/* TODO correct param */}
          <Select
            defaultSelected={options[0]}
            options={options}
            onSelect={(option) => {
              onChangePage(1)
              onChangeCurrentLimit(option.value)
              onChangeFilterParams({...filterParams, limit: option.value, page: 1})
            }}
          />
        </div>
      </div>

      <div className="table-wrap mt-5 border rounded-20">
        {isFetching ? (
          <LoadingData/>
        ) : (
          <>
            <div className="w-full overflow-x-auto pb-3">
              <Table
                head={bigWhalesBoughtTableHeader}
                data={tableData}
                className="table-no-border whale-bought min-w-[1200px]"
              />
            </div>
            <div className="mt-2 px-5 mb-9">
              <Pagination
                enableSelectRow={true}
                selectRowOptions={options}
                currentPage={currentPage}
                totalCount={total}
                pageSize={currentLimit}
                onPageChange={(page: number) => {
                  onChangePage(page)
                  onChangeFilterParams({...filterParams, page})
                }}
                onChangePageSize={(limit: number) => {
                  onChangePage(1)
                  onChangeCurrentLimit(limit)
                  onChangeFilterParams({...filterParams, limit, page: 1})
                }}
              />
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default BigWhalesBoughtBoard
