import { useState, useEffect } from 'react'
import WhaleMintedBoard from '../whale-tracking/whaleMintedBoard'
import TopWhaleMinterBoard from '../whale-tracking/topWhaleMinterBoard'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'

//Todo remove mock data
import { filterMinHours } from 'presentation/controllers/mockData/options'
import {
  whaleMintedTempData,
  whaleMintersTempData,
} from 'presentation/controllers/mockData/whale_tracking_mock_data'
import { pageSizes } from 'lib/hook/customHook'
//End todo remove mock data

const PageSize = pageSizes.pageSize10

const MintTab = () => {
  const [whaleMintedCurrentPage, setWhaleMintedCurrentPage] = useState(1)
  const [whaleMintedCurrentLimit, setWhaleMintedCurrentLimit] = useState(PageSize)
  const [whaleMintedData, setWhaleMintedData] = useState<any>([])
  const [whaleMintedTotal, setWhaleMintedTotal] = useState(0)
  const {
    filterParams: whaleMintedFilterParams,
    changeFilterParam: onChangeWhaleMintedFilterParam,
    setFilterParams: onChangeWhaleMintedFilterParams,
  } = useChangeFilterParams({
    listed: false,
    sortBy: filterMinHours[0]?.value,
    page: 1,
    limit: PageSize,
  })
  const [isFetchingWhaleMinted, setIsFetchingWhaleMinted] = useState(true)

  const [topWhaleMinterCurrentPage, setTopWhaleMinterCurrentPage] = useState(1)
  const [topWhaleMinterCurrentLimit, setTopWhaleMinterCurrentLimit] = useState(PageSize)
  const [topWhaleMinterData, setTopWhaleMinterData] = useState<any>([])
  const [topWhaleMinterTotal, setTopWhaleMinterTotal] = useState(0)
  const {
    filterParams: topWhaleMinterFilterParams,
    changeFilterParam: onChangeTopWhaleMinterFilterParam,
    setFilterParams: onChangeTopWhaleMinterFilterParams,
  } = useChangeFilterParams({
    sortBy: filterMinHours[0]?.value,
    page: 1,
    limit: PageSize,
  })
  const [isFetchingTopWhaleMinter, setIsFetchingTopWhaleMinter] = useState(true)

  const fetchWhaleMintedTableData = () => {
    setIsFetchingWhaleMinted(true)
    //TODO
    setWhaleMintedTotal(whaleMintedTempData.length)
    let data = whaleMintedTempData.slice((whaleMintedCurrentPage - 1) * whaleMintedCurrentLimit,
      whaleMintedCurrentPage * whaleMintedCurrentLimit)
    setWhaleMintedData(data)
    setTimeout(() => {
      setIsFetchingWhaleMinted(false)
    }, 300)
  }

  const fetchTopWhaleMinterTableData = () => {
    setIsFetchingTopWhaleMinter(true)
    //TODO
    setTopWhaleMinterTotal(whaleMintersTempData.length)
    let data = whaleMintersTempData.slice((topWhaleMinterCurrentPage - 1) * topWhaleMinterCurrentLimit,
      topWhaleMinterCurrentPage * topWhaleMinterCurrentLimit)
    setTopWhaleMinterData(data)
    setTimeout(() => {
      setIsFetchingTopWhaleMinter(false)
    }, 300)
  }

  useEffect(() => {
    fetchWhaleMintedTableData()
  }, [whaleMintedFilterParams])

  useEffect(() => {
    fetchTopWhaleMinterTableData()
  }, [topWhaleMinterFilterParams])

  return (
    <>
      <WhaleMintedBoard
        filterTimeOptions={filterMinHours}
        defaultTimeFilter={filterMinHours[0]}
        currentPage={whaleMintedCurrentPage}
        currentLimit={whaleMintedCurrentLimit}
        tableData={whaleMintedData}
        total={whaleMintedTotal}
        filterParams={whaleMintedFilterParams}
        onChangePage={setWhaleMintedCurrentPage}
        onChangeCurrentLimit={setWhaleMintedCurrentLimit}
        onChangeFilterParam={onChangeWhaleMintedFilterParam}
        onChangeFilterParams={onChangeWhaleMintedFilterParams}
        isFetching={isFetchingWhaleMinted}
      />
      <TopWhaleMinterBoard
        filterTimeOptions={filterMinHours}
        defaultTimeFilter={filterMinHours[0]}
        currentPage={topWhaleMinterCurrentPage}
        currentLimit={topWhaleMinterCurrentLimit}
        tableData={topWhaleMinterData}
        total={topWhaleMinterTotal}
        filterParams={topWhaleMinterFilterParams}
        onChangePage={setTopWhaleMinterCurrentPage}
        onChangeCurrentLimit={setTopWhaleMinterCurrentLimit}
        onChangeFilterParam={onChangeTopWhaleMinterFilterParam}
        onChangeFilterParams={onChangeTopWhaleMinterFilterParams}
        isFetching={isFetchingTopWhaleMinter}
      />
    </>
  )
}

export default MintTab
