import { useState, useEffect } from 'react'
import WhaleTrendsChart from '../whale-tracking/whaleTrendsChart'
import WhaleActivityBoard from '../whale-tracking/whaleActivityBoard'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import { filterDay } from 'presentation/controllers/mockData/options'
import CategoryUseCase from 'presentation/use_cases/category_use_case'
import CollectionUseCase from 'presentation/use_cases/collection_use_case'
import { pageSizes } from 'lib/hook/customHook'
import { Query as CategoryQuery } from 'domain/repositories/category_repository/query'
import { Query as CollectionQuery } from 'domain/repositories/collection_repository/query'
const collectionUseCase = new CollectionUseCase()
const categoryUseCase = new CategoryUseCase()

//Todo remove mock data
import {
  activityAnalyticsTempData,
  whaleTrendTempData1,
  whaleTrendTempData2,
  legendColumnChartSubData,
} from 'presentation/controllers/mockData/whale_tracking_mock_data'
import { toast } from 'react-toastify'
import Category from 'domain/models/category'
//End todo remove mock data

const PageSize = pageSizes.pageSize20

const ActivityTab = () => {
  const [isFilterCategoriesReady, setIsFilterCategoriesReady] = useState<boolean>(false)
  const [isFilterCollectionsReady, setIsFilterCollectionsReady] = useState<boolean>(false)
  const [isFetchingWhaleTrends, setIsFetchingWhaleTrends] = useState<boolean>(true)
  const [isFetchingWhaleActivity, setIsFetchingWhaleActivity] = useState<boolean>(true)
  const [whaleActivityTotal, setWhaleActivityTotal] = useState<number>(0)
  const [whaleActivityCurrentPage, setWhaleActivityCurrentPage] = useState<number>(1)
  const [whaleActivityCurrentLimit, setWhaleActivityCurrentLimit] = useState<number>(PageSize)
  const [categories, setCategories] = useState<Category[]>([])
  const {
    filterParams: whaleTrendsFilterParams,
    changeFilterParam: onChangeWhaleTrendsFilterParam,
  } = useChangeFilterParams({ periodRange: '' })

  //TODO remove any
  const [filterCollections, setFilterCollections] = useState<any>([])
  const [filterCategories, setFilterCategories] = useState<any>([])
  const [activeWhaleTrendsData, setActiveWhaleTrendsData] = useState<any>([])
  const [whaleActivityTrendsData, setWhaleActivityTrendsData] = useState<any>([])
  const [legendColumnChartData, setLegendColumnChartSubData] = useState<any>([])
  const [whaleActivityData, setWhaleActivityData] = useState<any>([])
  //End TODO

  const {
    filterParams: whaleActivityFilterParams,
    changeFilterParam: onChangeWhaleActivityFilterParam,
    setFilterParams: onChangeWhaleActivityFilterParams,
  } = useChangeFilterParams({
    listed: false,
    collectionIds: [],
    categoryId: '',
    page: 1,
    limit: PageSize,
  })

  const fetchCategories = async () => {
    const query = new CategoryQuery()
    await categoryUseCase.search(query).then((res) => {
      setCategories(res)
    }).catch((_error) => {
      toast.error('Error while fetching categories')
    })
  }

  const fetchCollections = async () => {
    const query = new CollectionQuery()
    await collectionUseCase.search(query).then((res) => {
      setFilterCollections(res)
    }).catch((_error) => {
      toast.error('Error while fetching collections')
    })
    setIsFilterCollectionsReady(true)
  }

  const formatCategories = () => {
    const formattedCategories = categories.map((category: any) => {
      return {
        id: category.id,
        name: category.name,
        value: category.slug,
      }
    })
    setFilterCategories(formattedCategories)
    setIsFilterCategoriesReady(true)
  }

  const fetchWhaleTrendsTableData = () => {
    setIsFetchingWhaleTrends(true)
    //TODO
    setActiveWhaleTrendsData(whaleTrendTempData2)
    setWhaleActivityTrendsData(whaleTrendTempData1)
    setLegendColumnChartSubData(legendColumnChartSubData)
    //End TODO
    setTimeout(() => {
      setIsFetchingWhaleTrends(false)
    }, 300)
  }

  const fetchWhaleActivityTableData = () => {
    setIsFetchingWhaleActivity(true)
    //TODO
    setWhaleActivityTotal(activityAnalyticsTempData.length)
    let data = activityAnalyticsTempData.slice((whaleActivityCurrentPage - 1) * whaleActivityCurrentLimit,
      whaleActivityCurrentPage * whaleActivityCurrentLimit)
    setWhaleActivityData(data)
    setTimeout(() => {
      setIsFetchingWhaleActivity(false)
    }, 300)
  }

  useEffect(() => {
    if (categories.length > 0) {
      formatCategories()
    }
  }, [categories])

  useEffect(() => {
    fetchWhaleTrendsTableData()
  }, [whaleTrendsFilterParams])

  useEffect(() => {
    fetchWhaleActivityTableData()
  }, [whaleActivityFilterParams])

  useEffect(() => {
    fetchCollections()
    fetchCategories()
  }, [])

  return (
    <>
      <div className="container-lg">
        <WhaleTrendsChart
          filterTimeOptions={filterDay}
          defaultTimeFilter={filterDay[1]}
          lineData={activeWhaleTrendsData}
          columnData={whaleActivityTrendsData}
          legendColumnChartData={legendColumnChartData}
          onChangeFilterParam={onChangeWhaleTrendsFilterParam}
          isFetching={isFetchingWhaleTrends}
        />
      </div>
      <WhaleActivityBoard
        currentPage={whaleActivityCurrentPage}
        currentLimit={whaleActivityCurrentLimit}
        tableData={whaleActivityData}
        total={whaleActivityTotal}
        filterParams={whaleActivityFilterParams}
        onChangePage={setWhaleActivityCurrentPage}
        onChangeCurrentLimit={setWhaleActivityCurrentLimit}
        onChangeFilterParam={onChangeWhaleActivityFilterParam}
        onChangeFilterParams={onChangeWhaleActivityFilterParams}
        isFetching={isFetchingWhaleActivity}
        collections={filterCollections}
        isFilterCollectionsReady={isFilterCollectionsReady}
        categories={filterCategories}
        isFilterCategoriesReady={isFilterCategoriesReady}
      />
    </>
  )
}

export default ActivityTab
