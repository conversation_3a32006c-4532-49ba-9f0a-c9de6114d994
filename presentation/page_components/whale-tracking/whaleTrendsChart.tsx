import dynamic from 'next/dynamic'
import { useState, useRef, memo } from 'react'
import FilterTime from 'presentation/components/filter-time'
import Tooltip from 'presentation/components/tooltip'
const DualAxes = dynamic(() => import('@ant-design/plots').then(({ DualAxes }) => DualAxes), { ssr: false })

const MemoPlot = memo(({ config, getChartRef}: any) => {
  return <DualAxes {...config} onReady={(chartRef) => getChartRef(chartRef)} />
}, (pre, next) => {
  return JSON.stringify(pre.config) === JSON.stringify(next.config)
})

MemoPlot.displayName = 'MemoPlot'

const WhaleTrendsChart = (props:any) => {
  const {
    filterTimeOptions = [],
    defaultTimeFilter,
    columnData = [],
    lineData = [],
    legendColumnChartData = [],
    onChangeFilterParam = () => {},
    isFetching = true,
  } = props

  const config: any = {
    data: [lineData, columnData], //[uvBillData, transformData],
    height: 255,
    xField: 'time',
    yField: ['whaleActive', 'value'],
    geometryOptions: [
      {
        geometry: 'line',
        color: '#72D8DA',
      },
      {
        geometry: 'column',
        isStack: true,
        seriesField: 'type',
        columnWidthRatio: 0.4,
        color: ['#4C965E', '#5A69F0', '#B071D3', '#DA615C'],
      },
    ],
    yAxis: {
      whaleActive: {
        title: {
          text: 'Active Whales',
          style: {
            fontSize: 14,
          },
        },
      },
      value: {
        title: {
          text: 'Activities',
          style: {
            fontSize: 14,
          },
        },
      },
    },
    legend: false,
    smooth: true,
  }

  const ref = useRef<any>(null)
  const [legendColumnsData, setLegendColumnsData] = useState([])
  const [legendLineData, setLegendLineData] = useState([])

  const getChartRef = (chartRef: any) => {
    if (!chartRef) return
    ref.current = chartRef

    const chart = ref.current.chart
    const legendColumnsData = chart.views[0]?.geometries[0]?.dataArray?.map((item: any) => {
      const origin = item[0]._origin
      origin.color = item[0].color
      origin.checked = true
      return origin
    })
    setLegendColumnsData(legendColumnsData)

    const legendLineData = chart.views[1]?.geometries[0]?.dataArray?.map((item: any) => {
      const origin = item[0]._origin
      origin.color = item[0].color
      origin.checked = true
      return origin
    })
    setLegendLineData(legendLineData)
  }

  const handleLegendColumnClick = (item: any, i: number) => {
    const newItem = item
    newItem.checked = !newItem.checked
    const currentLegend: any = [...legendColumnsData]
    currentLegend[i] = newItem

    const filteredLegendData = currentLegend
      .filter((l: any) => l.checked)
      .map((l: any) => l.type)

    const dualAxesChart = ref.current.chart
    if (dualAxesChart) {
      dualAxesChart.views[0]?.filter('type', (val:any) => {
        return filteredLegendData.indexOf(val) > -1
      })
      dualAxesChart.render()
    }
    setLegendColumnsData(currentLegend)
  }

  const handleLegendLineClick = (item: any, i: number) => {
    const newItem = item
    newItem.checked = !newItem.checked
    const currentLegend: any = [...legendLineData]
    currentLegend[i] = newItem

    const filteredLegendData = currentLegend
      .filter((l: any) => l.checked)

    const dualAxesChart = ref.current.chart
    if (dualAxesChart) {
      dualAxesChart.views[1]?.filter('time', () => {
        return filteredLegendData.length > 0
      })
      dualAxesChart.render()
    }
    setLegendLineData(currentLegend)
  }

  return (
    <>
      {!isFetching && (
        <div className="rounded-10 py-4 mt-10 border border-gray-3">
          <div className="px-5 flex flex-wrap justify-between items-center">
            <div className="flex items-center mb-4">
              <p className="text-lg font-bold text-dark-primary dark:text-white">
                Whale Trends
              </p>
              <Tooltip iconSize="w-4 h-4 -mt-0.5">Whale Trends</Tooltip>
            </div>
            {/* TODO */}
            <FilterTime
              className="mb-4"
              options={filterTimeOptions}
              defaultSelected={defaultTimeFilter}
              onSelect={(option) => {
                onChangeFilterParam('periodRange', option.value)
              }}
            />
          </div>
          <div className="md:px-5">
            <ul className="flex flex-wrap items-start">
              {legendLineData?.map((item: any, i: number) => (
                <li
                  key={i} onClick={() => handleLegendLineClick(item, i)}
                  className="flex flex-col mb-5 ml-12 cursor-pointer"
                >
                  <div className="inline-flex items-center">
                    <span
                      className="rounded-full w-2 h-2 mr-2"
                      style={{ backgroundColor: item.checked ? item.color : '#aaa' }}
                    ></span>
                    <span className="font-medium text-sm mr-1 text-dark-3 dark:text-white">
                      Active Whales
                    </span>
                    <Tooltip className="flex items-center -mt-0.5">
                      Description
                    </Tooltip>
                  </div>
                  <div className="inline-flex items-end space-x-2 font-medium">
                    <span className="text-sm text-dark-3 dark:text-white">439</span>
                    <span className="text-xs text-primary-1">+7.86%</span>
                  </div>
                </li>
              ))}
              {legendColumnsData?.map((item: any, i: number) => (
                <li
                  key={i} onClick={() => handleLegendColumnClick(item, i)}
                  className="flex flex-col mb-5 ml-12 cursor-pointer"
                >
                  <div className="inline-flex items-center">
                    <span
                      className="rounded-full w-2 h-2 mr-2"
                      style={{ backgroundColor: item.checked ? item.color : '#aaa' }}
                    ></span>
                    <span className="font-medium text-sm mr-1 text-dark-3 dark:text-white">
                      {item.type}
                    </span>
                    <Tooltip className="flex items-center -mt-0.5">Description</Tooltip>
                  </div>
                  <div className="inline-flex items-end space-x-2 font-medium">
                    <span className="text-sm text-dark-3 dark:text-white">
                      {legendColumnChartData[i].amount}
                    </span>
                    <span className={`text-xs ${legendColumnChartData[i].percent > 0 ? 'text-primary-1' : 'text-red-1'}`}>
                      {legendColumnChartData[i].percent > 0 ? '+' : ''}{legendColumnChartData[i].percent}%
                    </span>
                  </div>
                </li>
              ))}
            </ul>
            <MemoPlot config={config} getChartRef={getChartRef} />
          </div>
        </div>
      )}
    </>
  )
}

export default WhaleTrendsChart
