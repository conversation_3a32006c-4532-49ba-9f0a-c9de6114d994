import React, { useRef, useState, useEffect } from 'react'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Navigation } from 'swiper'
import Image from 'next/image'
import type { Swiper as SwiperType } from 'swiper/types'
import 'swiper/css'
import 'swiper/css/navigation'
import { PackageItem as PackageItemType } from 'domain/entities/vending_machine'
import PackageItem from './PackageItem'

interface PackageListProps {
  packages: PackageItemType[]
}

const PackageList: React.FC<PackageListProps> = ({ packages }) => {
  const swiperRef = useRef<SwiperType | null>(null)
  const [isBeginning, setIsBeginning] = useState(true)
  const [isEnd, setIsEnd] = useState(false)
  const [showNavigation, setShowNavigation] = useState(false)

  // Check if navigation should be shown on initial render
  useEffect(() => {
    // Only show navigation if there are more packages than can fit on screen
    // For initial state, we can check against the largest breakpoint (4.5 slides)
    setShowNavigation(packages.length > 4)
  }, [packages])

  const handlePrev = () => {
    if (swiperRef.current && !isBeginning) {
      swiperRef.current.slidePrev()
    }
  }

  const handleNext = () => {
    if (swiperRef.current && !isEnd) {
      swiperRef.current.slideNext()
    }
  }

  // Update navigation state when the slide changes
  const updateNavigationState = () => {
    if (swiperRef.current) {
      setIsBeginning(swiperRef.current.isBeginning)
      setIsEnd(swiperRef.current.isEnd)
      
      // More reliable check for navigation visibility:
      // 1. Check if we have more slides than the current slidesPerView setting
      // 2. Or check if we're not simultaneously at beginning and end (which would mean all slides are visible)
      const totalSlides = packages.length
      const visibleSlides = swiperRef.current.params.slidesPerView as number
      const needsNavigation = totalSlides > visibleSlides
      
      setShowNavigation(needsNavigation)
    }
  }

  return (
    <div className="relative package-list">
      <Swiper
        modules={[Navigation]}
        spaceBetween={24}
        slidesPerView={4.5}
        onSwiper={(swiper) => {
          swiperRef.current = swiper
          updateNavigationState()
        }}
        onSlideChange={() => {
          updateNavigationState()
        }}
        onResize={() => {
          // Also update on resize as the number of visible slides might change
          updateNavigationState()
        }}
        breakpoints={{
          320: {
            slidesPerView: 1.5,
            spaceBetween: 16,
          },
          640: {
            slidesPerView: 2.5,
            spaceBetween: 20,
          },
          768: {
            slidesPerView: 3.5,
            spaceBetween: 20,
          },
          1024: {
            slidesPerView: 4.5,
            spaceBetween: 24,
          },
        }}
        className="overflow-visible"
      >
        {packages.map((pkg) => (
          <SwiperSlide key={pkg.id} className="flex flex-col h-full">
            <PackageItem item={pkg} />
          </SwiperSlide>
        ))}
      </Swiper>
      
      {/* Only show navigation when needed */}
      {showNavigation && (
        <>
          {/* Previous button */}
          <div 
            className={`absolute top-1/2 -left-4 transform -translate-y-1/2 rotate-180 w-[30px] h-[30px] rounded-full bg-white shadow-md z-20 flex items-center justify-center transition-all duration-300 ${
              isBeginning 
                ? 'opacity-50 cursor-not-allowed' 
                : 'hover:scale-110 cursor-pointer'
            }`}
            onClick={handlePrev}
          >
            <Image 
              src="/asset/images/vending-machine/arrow-right.png" 
              alt="Previous" 
              width={30} 
              height={30} 
              className={isBeginning ? 'opacity-50' : ''}
            />
          </div>
          
          {/* Next button */}
          <div 
            className={`absolute top-1/2 -right-4 transform -translate-y-1/2 w-[30px] h-[30px] rounded-full bg-white shadow-md z-20 flex items-center justify-center transition-all duration-300 ${
              isEnd 
                ? 'opacity-50 cursor-not-allowed' 
                : 'hover:scale-110 cursor-pointer'
            }`}
            onClick={handleNext}
          >
            <Image 
              src="/asset/images/vending-machine/arrow-right.png" 
              alt="Next" 
              width={30} 
              height={30}
              className={isEnd ? 'opacity-50' : ''}
            />
          </div>
        </>
      )}
      
      {/* Gradient overlay for partial item */}
      <div className="absolute top-0 right-0 bottom-0 w-24 bg-gradient-to-r from-transparent to-white pointer-events-none z-10"></div>
    </div>
  )
}

export default PackageList 