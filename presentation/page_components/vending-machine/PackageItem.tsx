import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { PackageItem as PackageItemType } from 'domain/entities/vending_machine'

interface PackageItemProps {
  item: PackageItemType
}

const PackageItem: React.FC<PackageItemProps> = ({ item }) => {
  const [imageError, setImageError] = useState(false)
  const [isHovering, setIsHovering] = useState(false)
  const [currentCardIndex, setCurrentCardIndex] = useState(0)
  
  // Cycle through cards when hovering
  useEffect(() => {
    let interval: ReturnType<typeof setInterval> | null = null
    
    if (isHovering && item.cardItems && item.cardItems.length > 1) {
      interval = setInterval(() => {
        setCurrentCardIndex((prevIndex) => 
          prevIndex >= item.cardItems!.length - 1 ? 0 : prevIndex + 1
        )
      }, 1500) // Change card every 1.5 seconds
    } else {
      setCurrentCardIndex(0)
    }
    
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [isHovering, item.cardItems])
  
  const currentCard = item.cardItems && item.cardItems.length > 0 
    ? item.cardItems[currentCardIndex] 
    : null
  
  return (
    <Link 
      href={`/vending-machine/${item.slug}`}
      className="rounded-xl overflow-hidden bg-white shadow-md hover:shadow-lg transition-shadow block w-full h-full package-item"
    >
      <div className="h-full flex flex-col">
        <div 
          className="w-full aspect-square overflow-hidden relative"
          // onMouseEnter={() => {
          //   setTimeout(() => {
          //     setIsHovering(true)
          //   }, 500)
          // }}
          // onMouseLeave={() => {
          //   setTimeout(() => {
          //     setIsHovering(false)
          //   }, 500)
          // }}
        >
          {/* Main Package Image (when not hovering) */}
          {!isHovering && (
            <>
              {item.image && !imageError ? (
                <Image 
                  src={item.image}
                  alt={item.title}
                  fill
                  sizes="(max-width: 768px) 100vw, 33vw"
                  className="object-contain package-item-image"
                  onError={() => setImageError(true)}
                />
              ) : (
                <div className="bg-gray-200 h-full w-full flex items-center justify-center">
                  <span className="text-gray-400">{item.title}</span>
                </div>
              )}
            </>
          )}
          
          {/* Single Card Item on Hover with Animation */}
          {isHovering && currentCard && (
            <Image
              src={currentCard.image}
              alt={currentCard.rarity || 'Card'}
              fill
              sizes="(max-width: 768px) 100vw, 33vw"
              className="object-contain package-item-image"
            />
      
          )}
        </div>
        <div className="mt-4">
          <div className="package-item-title">{item.title}</div>
          <div className="package-item-price">
            ${item?.price || '0'}
          </div>
        </div>
      </div>
    </Link>
  )
}

export default PackageItem 