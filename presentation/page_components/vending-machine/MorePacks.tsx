import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { useRouter } from 'next/router'
import { PackageType, PackageItem } from 'domain/entities/vending_machine'
import VendingMachineCategoryUseCase from 'presentation/use_cases/vending_machine_category_use_case'
import VendingMachinePackUseCase from 'presentation/use_cases/vending_machine_pack_use_case'
import { Query as CategoryQuery } from 'domain/repositories/vending_machine_category_repository/query'
import { Query as PackQuery } from 'domain/repositories/vending_machine_pack_repository/query'

interface MorePacksProps {
  currentTypeId: string
  relatedPackages?: PackageItem[]
}

const categoryUseCase = new VendingMachineCategoryUseCase()
const packUseCase = new VendingMachinePackUseCase()

const MorePacks: React.FC<MorePacksProps> = ({ currentTypeId, relatedPackages }) => {
  const [packageTypes, setPackageTypes] = useState<PackageType[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [imgError, setImgError] = useState<Record<number, boolean>>({})
  const router = useRouter()
  
  useEffect(() => {
    const fetchPackageTypes = async () => {
      try {
        setIsLoading(true)
        // Load categories from API (equivalent to package types)
        const categoryQuery = new CategoryQuery({
          page: 1,
          limit: 100, // Get all categories
          enable: true,
        })
        
        const categoryResult = await categoryUseCase.list(categoryQuery)
        console.log('🚀 ~ fetchPackageTypes ~ categoryResult:', categoryResult)
        
        // Map categories to PackageType format
        const types: PackageType[] = categoryResult.items.map((category) => ({
          id: category.id!.toString(),
          title: category.name,
        }))
        
        setPackageTypes(types)
      } catch (error) {
        console.error('Error fetching package types:', error)
      } finally {
        setIsLoading(false)
      }
    }
    
    fetchPackageTypes()
  }, [])
  
  const handleTabClick = async (typeId: string, e: React.MouseEvent) => {
    e.preventDefault()
    
    try {
      // Load packs from API filtered by category
      const packQuery = new PackQuery({
        page: 1,
        limit: 10, // Get first 10 packs for the category
        categoryId: typeId,
      })

      const packResult = await packUseCase.list(packQuery)
      
      // Map packs to PackageItem format
      const packages: PackageItem[] = packResult.items.map((pack) => ({
        id: parseInt(pack.id!),
        title: pack.name,
        price: pack.price,
        image: pack.imageUrl,
        typeId: pack.categoryId,
        slug: pack.id!.toString(),
      }))
      
      // If packages exist, redirect to the first one
      if (packages && packages.length > 0) {
        router.push(`/vending-machine/${packages[0].slug}`)
      } else {
        // If no packages, just go to the type page
        throw new Error('No packages found for type: ' + typeId)
      }
    } catch (error) {
      console.error('Error fetching packages for type:', error)
    }
  }
  
  if (isLoading) {
    return (
      <div className="mb-8 more-packs">
        <h2 className="mb-4 more-packs-title">More packs</h2>
        <div className="flex items-center justify-center py-4">
          <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    )
  }
  
  return (
    <div className="mb-8 more-packs">
      <h2 className="mb-4 more-packs-title">More packs</h2>
      
      {/* Package Type Tabs */}
      <div className="flex items-center gap-2 mb-6 overflow-x-auto pb-2 -mx-1 px-1 more-packs-tabs flex-wrap">
        {packageTypes.map((type) => (
          <a 
            key={type.id}
            href={`/vending-machine/${type.id}`}
            onClick={(e) => handleTabClick(type.id, e)}
            className={`${
              type.id === currentTypeId 
                ? 'active' 
                : 'bg-white'
            } more-packs-tab`}
          >
            {type.title.toUpperCase()}
          </a>
        ))}
      </div>
      
      {/* Related NFT Cards */}
      {relatedPackages && relatedPackages.length > 0 ? (
        <div className="grid grid-cols-3 gap-4">
          {relatedPackages.slice(0, 3).map((pkg, index) => (
            <div key={index} className="bg-white rounded-lg overflow-hidden more-packs-card">
              <Link href={`/vending-machine/${pkg.slug}`} className="block">
                <div className="flex flex-col items-center">
                  <div className="w-12 h-12 mb-2 relative">
                    {imgError[index] ? (
                      <Image 
                        src="/asset/images/placeholder.jpg"
                        alt={pkg.title}
                        fill
                        sizes="40px"
                        className="object-contain more-packs-card-image"
                      />
                    ) : (
                      <Image 
                        src={pkg.image || '/asset/images/placeholder.jpg'}
                        alt={pkg.title}
                        fill
                        sizes="40px"
                        className="object-contain more-packs-card-image"
                        onError={() => setImgError((prev) => ({ ...prev, [index]: true }))}
                      />
                    )}
                  </div>
                  <div className="text-center">
                    <h3 className="more-packs-card-title">{pkg.title}</h3>
                    <p className="more-packs-card-price">${pkg.price}</p>
                  </div>
                </div>
              </Link>
            </div>
          ))}
        </div>
      ) : (
        <p className="text-gray-500">No related packs available.</p>
      )}
    </div>
  )
}

export default MorePacks 