import React, { useState, useEffect } from 'react'
import Image from 'next/image'
import But<PERSON> from 'presentation/components/button'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'
import { shortenAddress } from 'lib/utils'
import Copy from 'presentation/components/copy'
import SvgIcon from 'presentation/components/svg-icon'
import { getBalanceAmount } from 'lib/number'
import { POLYGON_TOKENS as POLYGON_MAINNET_TOKENS } from 'lib/singularity/tokens/mainnet/polygon-tokens'
import { POLYGON_TOKENS as POLYGON_TESTNET_TOKENS } from 'lib/singularity/tokens/testnet/polygon-tokens'
import { ethers } from 'ethers'
import { v4 as uuidv4 } from 'uuid'
import * as CryptoJS from 'crypto-js'
import { useAccount } from 'wagmi'
import { useAuthentication } from 'lib/hook/auth_hook'
import {
  VENDING_MACHINE_CONFIG_DEFAULT,
  CHECKOUT_STATUS,
} from 'lib/vendingMachineConfig'
import CheckoutRemoteDatasource from 'data/datasources/remote/checkout_remote_datasource'
import VendingMachineContractService from 'domain/services/vending_machine_contract_service'
import Loader from 'presentation/components/loader'
import RequireConnect from 'presentation/components/require-connect'
import { on } from 'events'
interface PaymentModalProps {
  isShow: boolean;
  onClose: (e: any) => void;
  packageTitle: string;
  packageImage: string;
  packageId: number | string;
  price: number;
  onBuyNow: (cardUrl: string) => void;
  isSingularityLoaded: boolean;
}

const PaymentModal: React.FC<PaymentModalProps> = ({
  isShow,
  onClose,
  packageTitle,
  packageImage,
  packageId,
  price,
  onBuyNow,
  isSingularityLoaded,
}) => {
  const [internalWalletBalance, setInternalWalletBalance] =
    useState<string>('0.00')
  const [publicWalletBalance, setPublicWalletBalance] =
    useState<string>('0.00')
  const [isLoadingBalances, setIsLoadingBalances] = useState(false)
  const [isSingularityOpen, setIsSingularityOpen] = useState(false)
  const [isLoadingAllowance, setIsLoadingAllowance] = useState(false)
  const [receiveAddress, setReceiveAddress] = useState<string>('')
  const [addressError, setAddressError] = useState<string>('')
  const [isProcessingTransaction, setIsProcessingTransaction] = useState(false)
  const [selectedWallet, setSelectedWallet] = useState<'internal' | 'external'>(
    'internal',
  )
  const [isProcessingPayment, setIsProcessingPayment] = useState(false)
  const [isProcessingApproval, setIsProcessingApproval] = useState(false)
  const [allowance, setAllowance] = useState<string>('0')
  const [needsApproval, setNeedsApproval] = useState(false)

  // Network detection and switching
  const { chainId: currentChainId, address } = useAccount()

  const { switchNetwork, getProviderEthers } = useAuthentication()

  const paymentTokenAddress =
    VENDING_MACHINE_CONFIG_DEFAULT.paymentTokenAddress
  const paymentTokenDecimals =
    VENDING_MACHINE_CONFIG_DEFAULT.paymentTokenDecimals
  const paymentNetworkRpc = VENDING_MACHINE_CONFIG_DEFAULT.rpc

  // Determine correct Polygon network based on environment
  const IS_TESTNET = process.env.NEXT_PUBLIC_NODE_ENV !== 'production'
  const isOnCorrectNetwork =
    currentChainId === VENDING_MACHINE_CONFIG_DEFAULT.chainId

  const formattedPrice = `$${price}`
  const authStateProvider = AuthStateProvider((state) => ({
    me: state.me,
  }))

  // Check if user is connected (has wallet address and user data)
  const isWalletConnected = !!(address && authStateProvider.me)

  // Load balances function
  const loadBalances = async () => {
    if (!authStateProvider.me) return
    setIsLoadingBalances(true)
    try {
      // Load internal wallet balance
      if (authStateProvider.me.internalWallet) {
        try {
          const provider = new ethers.providers.JsonRpcProvider(
            paymentNetworkRpc,
          )
          if (paymentTokenAddress) {
            const contract = new ethers.Contract(
              paymentTokenAddress,
              ['function balanceOf(address) view returns (uint256)'],
              provider,
            )
            const balance = await contract.balanceOf(
              authStateProvider.me.internalWallet,
            )
            console.log('🚀 ~ loadBalances ~ balance:', balance)
            const formattedBalance = getBalanceAmount(
              balance.toString(),
              paymentTokenDecimals,
            )
            setInternalWalletBalance(formattedBalance.toFixed(2))
          }
        } catch (error) {
          console.error('Error loading internal wallet balance:', error)
          setInternalWalletBalance('0.00')
        }
      }
      // Load public wallet balance
      if (
        authStateProvider.me.publicAddresses &&
        authStateProvider.me.publicAddresses.length > 0
      ) {
        try {
          const provider = new ethers.providers.JsonRpcProvider(
            paymentNetworkRpc,
          )
          if (paymentTokenAddress) {
            const contract = new ethers.Contract(
              paymentTokenAddress,
              ['function balanceOf(address) view returns (uint256)'],
              provider,
            )
            const balance = await contract.balanceOf(
              authStateProvider.me.publicAddresses[0],
            )
            console.log(
              '🚀 ~ loadBalances ~ authStateProvider.me.publicAddresses[0]:',
              authStateProvider.me.publicAddresses[0],
            )
            console.log('🚀 ~ loadBalances ~ balanceexternal:', balance)
            const formattedBalance = getBalanceAmount(
              balance.toString(),
              paymentTokenDecimals,
            )
            setPublicWalletBalance(formattedBalance.toFixed(2))
          }
        } catch (error) {
          console.error('Error loading public wallet balance:', error)
          setPublicWalletBalance('0.00')
        }
      }
    } catch (error) {
      console.error('Error loading balances:', error)
    } finally {
      setIsLoadingBalances(false)
    }
  }

  // Validate Ethereum address
  const isValidAddress = (address: string): boolean => {
    return ethers.utils.isAddress(address)
  }

  // Handle address input change
  const handleAddressChange = (value: string) => {
    setReceiveAddress(value)
    if (value && !isValidAddress(value)) {
      setAddressError('Invalid Ethereum address')
    } else {
      setAddressError('')
    }
  }

  

  // Check token allowance for external wallet
  const checkTokenAllowance = async () => {
    if (selectedWallet !== 'external' || !address || !currentChainId) {
      return
    }

    try {
      const provider = await getProviderEthers()
      if (!provider) {
        throw new Error('Provider not available')
      }
      const signer = provider.getSigner()
      setIsLoadingAllowance(true)
      const contractService = new VendingMachineContractService()
      const spenderAddress = contractService.getContractAddress(currentChainId)

      const allowanceResult = await contractService.checkAllowance({
        tokenAddress: paymentTokenAddress,
        ownerAddress: address,
        spenderAddress: spenderAddress,
        signer: signer,
      })

      const paymentValue = price * Math.pow(10, paymentTokenDecimals)
      const allowanceFormatted = allowanceResult.toString()
      setAllowance(allowanceFormatted)

      // Check if allowance is sufficient
      const needsApprovalCheck = allowanceResult.lt(paymentValue.toString())
      setNeedsApproval(needsApprovalCheck)

      console.log('🚀 ~ checkTokenAllowance ~ allowance:', allowanceFormatted)
      console.log('🚀 ~ checkTokenAllowance ~ paymentValue:', paymentValue)
      console.log(
        '🚀 ~ checkTokenAllowance ~ needsApproval:',
        needsApprovalCheck,
      )
    } catch (error) {
      console.error('Error checking allowance:', error)
      setNeedsApproval(true) // Default to needing approval if check fails
    } finally {
      setIsLoadingAllowance(false)
    }
  }

  // Handle token approval
  const handleApprove = async () => {
    if (!address || !currentChainId) {
      console.error('Wallet not connected')
      return
    }

    setIsProcessingApproval(true)

    try {
      const provider = await getProviderEthers()
      if (!provider) {
        throw new Error('Provider not available')
      }
      const signer = provider.getSigner()

      const contractService = new VendingMachineContractService()
      const spenderAddress = contractService.getContractAddress(currentChainId)
      const paymentValue = price * Math.pow(10, paymentTokenDecimals)

      console.log('🚀 ~ handleApprove ~ approving amount:', paymentValue)

      const tx = await contractService.approve({
        tokenAddress: paymentTokenAddress,
        spenderAddress: spenderAddress,
        amount: paymentValue.toString(),
        signer: signer,
      })

      console.log('Approval transaction sent:', tx.hash)

      // Wait for transaction confirmation
      const receipt = await contractService.waitForTransaction(tx)
      console.log('Approval confirmed:', receipt.transactionHash)

      // Recheck allowance after approval
      await checkTokenAllowance()
    } catch (error) {
      console.error('Error approving token:', error)
      alert('Token approval failed. Please try again.')
    } finally {
      setIsProcessingApproval(false)
    }
  }

  const handleBuyPackageInternal = async () => {
    console.log('handleBuyPackageInternal')
    if (!authStateProvider.me?.internalWallet) {
      throw new Error('Internal wallet not found')
    }
    // Get the ethers provider and signer
    const provider = await getProviderEthers()
    if (!provider) {
      throw new Error('Provider not available')
    }
    const signer = provider.getSigner()
    const checkoutDatasource = new CheckoutRemoteDatasource()
    const paymentValue =
      price * Math.pow(10, VENDING_MACHINE_CONFIG_DEFAULT.paymentTokenDecimals)
    console.log('🚀 ~ handleBuyPackage ~ paymentValue:', paymentValue)
    const checkoutParams: any = {
      vmGachaBoxId: packageId,
      paymentToken: VENDING_MACHINE_CONFIG_DEFAULT.paymentTokenAddress,
      paymentValue: paymentValue,
      chainId: VENDING_MACHINE_CONFIG_DEFAULT.chainId,
      isInternalWallet: true,
    }
    console.log('Calling checkout API with params:', checkoutParams)
    const checkoutResponse: any =
      await checkoutDatasource.create(checkoutParams)
    console.log('Checkout API response:', checkoutResponse)
    const checkoutId = checkoutResponse.checkoutId

    // call checkoutId API to check tx success and get NFT data
    const cards = await checkTxSuccess(checkoutId)
    const card = cards.length > 0 ? cards[0] : null
    onBuyNow(card)
  }
  const checkTxSuccess = async (checkoutId: string) => {
    const maxAttempts = 30 // Maximum 30 attempts (60 seconds with 2000ms intervals)
    let cards: any[] = []
    const checkoutDatasource = new CheckoutRemoteDatasource()

    for (let i = 0; i < maxAttempts; i++) {
      try {
        const response: any =
        await checkoutDatasource.checkoutTxStatus(checkoutId)
        const data = response.data
        console.log(`Attempt ${i + 1}: Checkout status:`, data.status)

        // Check if transaction is completed or failed
        if (
          data.status === CHECKOUT_STATUS.COMPLETED ||
          data.status === CHECKOUT_STATUS.FAILED
        ) {
          // Extract NFTs/tokens from data if completed successfully
          if (
            data.status === CHECKOUT_STATUS.COMPLETED &&
            data.cards &&
            Array.isArray(data.cards)
          ) {
            cards = data.cards
            console.log('🚀 ~ checkTxSuccess ~ cards:', cards)
          } else if (data.status === CHECKOUT_STATUS.FAILED) {
            console.log('❌ ~ checkTxSuccess ~ Transaction failed')
          }

          // Break the loop when we have a final status
          break
        }

        // If status is still pending/processing, continue polling
        console.log(
          `Transaction still processing... (attempt ${i + 1}/${maxAttempts})`,
        )
      } catch (error) {
        console.error('Error checking transaction status:', error)
        // Don't break on API errors, continue trying
      }

      // Wait 2 seconds before next attempt
      await new Promise((resolve) => setTimeout(resolve, 3000))
    }

    // Log final result
    if (cards.length > 0) {
      console.log('✅ Transaction completed successfully with cards:', cards)
    } else {
      console.log('⏰ Transaction polling completed without success')
    }

    return cards
  }
  const handleBuyPackageExternal = async () => {
    if (address !== authStateProvider.me?.publicAddresses[0]) {
      throw new Error('Address not match')
    }
    // Get the ethers provider and signer
    const provider = await getProviderEthers()
    if (!provider) {
      throw new Error('Provider not available')
    }
    const signer = provider.getSigner()
    // Step 1: Call the checkout API
    const checkoutDatasource = new CheckoutRemoteDatasource()
    const paymentValue =
      price * Math.pow(10, VENDING_MACHINE_CONFIG_DEFAULT.paymentTokenDecimals)
    console.log('🚀 ~ handleBuyPackage ~ paymentValue:', paymentValue)
    const checkoutParams: any = {
      vmGachaBoxId: packageId,
      paymentToken: VENDING_MACHINE_CONFIG_DEFAULT.paymentTokenAddress,
      authorizedPayer: address,
      paymentValue: paymentValue,
      chainId: VENDING_MACHINE_CONFIG_DEFAULT.chainId,
    }
    console.log('Calling checkout API with params:', checkoutParams)
    const checkoutResponse: any =
      await checkoutDatasource.create(checkoutParams)
    console.log('Checkout API response:', checkoutResponse)
    const checkoutId = checkoutResponse.checkoutId
    // Step 2: Call the smart contract
    const contractService = new VendingMachineContractService()
    const contractParams: any = {
      requestData: checkoutResponse.requestData,
      signature: checkoutResponse.requestSignature,
      chainId: currentChainId,
      signer: signer,
    }
    console.log('Calling smart contract with params:', contractParams)
    const tx = await contractService.erc20Checkout(contractParams)
    console.log('Transaction sent:', tx.hash)
    // Wait for transaction confirmation
    const receipt = await contractService.waitForTransaction(tx)
    console.log('Transaction confirmed:', receipt.transactionHash)

    // call checkoutId API to check tx success and get NFT data
    const cards = await checkTxSuccess(checkoutId)
    const card = cards.length > 0 ? cards[0] : null
    onBuyNow(card)
  }
  const handleBuyPackage = async () => {
    if (!address || !currentChainId) {
      console.error('Wallet not connected')
      return
    }
    setIsProcessingPayment(true)
    try {
      if (selectedWallet === 'external') {
        await handleBuyPackageExternal()
      } else {
        await handleBuyPackageInternal()
      }
    } catch (error) {
      console.error('Error processing payment:', error)
      // You might want to show an error message to the user here
      alert('Payment processing failed. Please try again.')
    } finally {
      setIsProcessingPayment(false)
    }
  }
  useEffect(() => {
    if (isShow && authStateProvider.me) {
      loadBalances()
    }
  }, [isShow, authStateProvider.me])

  // Check allowance when external wallet is selected and modal is shown
  useEffect(() => {
    if (
      isShow &&
      selectedWallet === 'external' &&
      address &&
      currentChainId &&
      isOnCorrectNetwork
    ) {
      checkTokenAllowance()
    }
  }, [
    isShow,
    selectedWallet,
    address,
    currentChainId,
    isOnCorrectNetwork,
    price,
  ])

  // Cleanup style on unmount and add DOM observer
  useEffect(() => {
    let observer: MutationObserver | null = null

    if (isSingularityOpen) {
      // Create a mutation observer to watch for Singularity DOM changes
      observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList') {
            // Check if any Singularity elements were removed
            mutation.removedNodes.forEach((node) => {
              if (node instanceof Element) {
                const isSingularityElement =
                  node.id?.includes('SingularityEvent') ||
                  node.className?.toString().includes('singularity') ||
                  node.hasAttribute?.('data-singularity')

                if (isSingularityElement) {
                  console.log('Singularity element removed from DOM')
                  setIsSingularityOpen(false)
                }
              }
            })
          }
        })
      })

      // Start observing
      observer.observe(document.body, {
        childList: true,
        subtree: true,
      })
    }

    return () => {
      const styleElement = document.getElementById(
        'singularity-z-index-override',
      )
      if (styleElement) {
        styleElement.remove()
      }

      if (observer) {
        observer.disconnect()
      }
    }
  }, [isSingularityOpen])

  // Add event listener for Singularity close events
  useEffect(() => {
    const handleSingularityClose = () => {
      console.log('Singularity close event detected')
      setIsSingularityOpen(false)
    }

    // Listen for custom Singularity events
    document.addEventListener('singularity:close', handleSingularityClose)
    document.addEventListener('singularity:cancel', handleSingularityClose)
    document.addEventListener('singularity:complete', handleSingularityClose)

    // Listen for standard iframe events
    const handleWindowMessage = (event: MessageEvent) => {
      if (event.data && typeof event.data === 'object') {
        if (
          event.data.type === 'singularity_close' ||
          event.data.type === 'singularity_cancel' ||
          event.data.action === 'close'
        ) {
          console.log('Singularity message event:', event.data)
          setIsSingularityOpen(false)
        }
      }
    }

    window.addEventListener('message', handleWindowMessage)

    return () => {
      document.removeEventListener('singularity:close', handleSingularityClose)
      document.removeEventListener(
        'singularity:cancel',
        handleSingularityClose,
      )
      document.removeEventListener(
        'singularity:complete',
        handleSingularityClose,
      )
      window.removeEventListener('message', handleWindowMessage)
    }
  }, [])

  // Handle modal close - also close Singularity if open
  const handleModalClose = (e: any) => {
    // Close Singularity iframe if it's open
    if (window.SingularityEvent && isSingularityOpen) {
      try {
        window.SingularityEvent.close()
        console.log('Singularity iframe closed via modal close')
      } catch (error) {
        console.log('Could not close Singularity iframe:', error)
      }
    }

    // Reset Singularity state
    setIsSingularityOpen(false)

    // Call the original onClose handler
    onClose(e)
  }

  // Check balance based on selected wallet
  const selectedBalance =
    selectedWallet === 'internal'
      ? parseFloat(internalWalletBalance)
      : parseFloat(publicWalletBalance)
  const isButtonDisabled = selectedBalance < price

  // Get selected wallet address for receiving USDC
  const getSelectedWalletAddress = () => {
    if (selectedWallet === 'internal') {
      return authStateProvider.me?.internalWallet || ''
    } else {
      return authStateProvider.me?.publicAddresses?.[0] || ''
    }
  }

  // Auto-fill address when wallet selection changes
  useEffect(() => {
    const address = getSelectedWalletAddress()
    if (address) {
      setReceiveAddress(address)
    }
  }, [selectedWallet, authStateProvider.me])

  if (!isShow) {
    return null
  }

  return (
    <div
      className={`fixed inset-0 flex items-center transition-all duration-300 ${
        isShow ? 'opacity-100 visible' : 'opacity-0 invisible'
      } ${isSingularityOpen ? 'justify-start pl-2' : 'justify-center'}`}
      style={{
        zIndex: isSingularityOpen ? 998 : 999,
        backgroundColor: isSingularityOpen
          ? 'rgba(0, 0, 0, 0.8)'
          : 'rgba(0, 0, 0, 0.5)',
        backdropFilter: isSingularityOpen ? 'blur(8px)' : 'blur(4px)',
      }}
      onClick={handleModalClose}
    >
      <div
        className={`m-auto rounded-10 relative bg-white dark:bg-slate-300 transition-all duration-300 max-w-4xl w-full mx-4 ${
          isSingularityOpen ? 'transform -translate-x-0' : ''
        }`}
        onClick={(e) => e.stopPropagation()}
      >
        <p className="py-10p px-14 bg-blue-9 dark:bg-blue-1 text-dark-primary rounded-t-10 text-center text-lg font-medium relative">
          Payment method
          <SvgIcon
            onClick={handleModalClose}
            name="plus"
            className="w-6 h-6 stroke-none fill-gray-1 rotate-45 absolute top-1/2 right-0 -translate-y-1/2 -translate-x-1/2 cursor-pointer"
          />
        </p>
        <div className="p-3">
          {/* Show RequireConnect if wallet is not connected */}
          {!isWalletConnected ? (
            <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
              <div className="text-center mb-6">
                <div className="relative w-40 h-40 mx-auto mb-4">
                  <Image
                    src={packageImage}
                    alt={packageTitle}
                    fill
                    className="object-cover rounded-10"
                  />
                </div>
                <h3 className="text-xl font-bold mb-2 text-dark-primary dark:text-white">
                  {packageTitle}
                </h3>
                <p className="text-3xl font-bold text-primary-2 mb-4">
                  {formattedPrice}
                </p>
              </div>

              <RequireConnect className="!pt-0 !pb-0" innerClassName="!py-8" />

              <p className="text-center text-sm text-gray-1 dark:text-gray-3 max-w-md">
                Connect your wallet to purchase this package and access payment
                features.
              </p>
            </div>
          ) : (
            <div className="grid md:grid-cols-2 gap-6">
              {/* Left Column - Wallet Selection & Add Funds */}
              <div className="space-y-4">
                {/* Wallet Selection */}
                <div className="bg-gray-4 dark:bg-blue-8 rounded-3xl p-4">
                  <h4 className="text-stroke-thin font-medium text-dark-primary dark:text-white mb-3">
                    Select Payment Wallet
                  </h4>
                  {isLoadingBalances ? (
                    <div className="flex justify-center items-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-primary-2"></div>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {authStateProvider.me?.internalWallet && (
                        <div className="radio-tag-wrapper">
                          <input
                            type="radio"
                            name="paymentWallet"
                            id="internal-wallet"
                            value="internal"
                            checked={selectedWallet === 'internal'}
                            onChange={() => setSelectedWallet('internal')}
                          />
                          <label
                            htmlFor="internal-wallet"
                            className="flex items-center justify-between p-3 bg-white dark:bg-blue-3 rounded-3xl cursor-pointer hover:bg-gray-5 dark:hover:bg-blue-9 transition-colors"
                          >
                            <span className="text-stroke-thin font-medium text-dark-primary dark:text-white">
                              Internal Wallet
                            </span>
                            <span
                              className={`text-stroke-thin font-medium ${parseFloat(internalWalletBalance) >= price ? 'text-green-500' : 'text-red'}`}
                            >
                              ${internalWalletBalance} (USDC)
                            </span>
                          </label>
                        </div>
                      )}
                      {authStateProvider.me?.publicAddresses &&
                        authStateProvider.me.publicAddresses.length > 0 && (
                        <div className="radio-tag-wrapper">
                          <input
                            type="radio"
                            name="paymentWallet"
                            id="connected-wallet"
                            value="external"
                            checked={selectedWallet === 'external'}
                            onChange={() => setSelectedWallet('external')}
                          />
                          <label
                            htmlFor="connected-wallet"
                            className="flex items-center justify-between p-3 bg-white dark:bg-blue-3 rounded-3xl cursor-pointer hover:bg-gray-5 dark:hover:bg-blue-9 transition-colors"
                          >
                            <span className="text-stroke-thin font-medium text-dark-primary dark:text-white">
                                Connected Wallet
                            </span>
                            <span
                              className={`text-stroke-thin font-medium ${parseFloat(publicWalletBalance) >= price ? 'text-green-500' : 'text-red'}`}
                            >
                                ${publicWalletBalance} (USDC)
                            </span>
                          </label>
                        </div>
                      )}
                    </div>
                  )}
                  {isButtonDisabled && (
                    <div className="mt-3 p-3 bg-gradient-red-opacity-10 rounded-3xl">
                      <div className="flex items-center space-x-2">
                        <SvgIcon
                          name="exclamationTriangle"
                          className="w-4 h-4 fill-red shrink-0"
                        />
                        <p className="text-red text-stroke-thin font-medium">
                          Insufficient balance. Need $
                          {(price - selectedBalance).toFixed(2)} more USDC.
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Network warning */}
                  {!isOnCorrectNetwork && (
                    <div className="mt-3 p-3 bg-gradient-yellow-opacity-10 rounded-3xl">
                      <div className="flex items-center space-x-2">
                        <SvgIcon
                          name="exclamationTriangle"
                          className="w-4 h-4 fill-yellow-500 shrink-0"
                        />
                        <p className="text-yellow-600 text-stroke-thin font-medium">
                          Please switch to{' '}
                          {IS_TESTNET
                            ? 'Polygon Amoy Testnet'
                            : 'Polygon Mainnet'}{' '}
                          to proceed.
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Add Funds Section - Only show when insufficient balance */}
                {isOnCorrectNetwork && (
                  <div className="bg-gradient-primary-1-opacity-10 dark:bg-blue-8 rounded-3xl p-4">
                    {/* Show selected wallet address */}
                    <div className="mb-4">
                      <label className="block text-stroke-thin font-medium text-gray-1 dark:text-gray-3 mb-2">
                        Selected Wallet
                      </label>
                      <div className="flex items-center space-x-2">
                        <input
                          type="text"
                          value={receiveAddress}
                          readOnly
                          className="flex-1 px-5 py-3 bg-gray-5 dark:bg-blue-8 dark:text-white outline-0 rounded-3xl text-xs font-mono text-dark-primary"
                        />
                        <Copy copyContent={receiveAddress}>
                          <SvgIcon
                            name="copy"
                            className="w-4 h-4 cursor-pointer fill-gray-1 hover:fill-primary-2 transition-colors"
                          />
                        </Copy>
                      </div>
                    </div>

                   
                  </div>
                )}

              </div>

              {/* Right Column - Package Details & Buy Button */}
              <div className="flex flex-col">
                <div className="text-center mb-6">
                  <div className="relative w-40 h-40 mx-auto mb-4">
                    <Image
                      src={packageImage}
                      alt={packageTitle}
                      fill
                      className="object-cover rounded-10"
                    />
                  </div>
                  <h3 className="text-xl font-bold mb-2 text-dark-primary dark:text-white">
                    {packageTitle}
                  </h3>
                  <p className="text-3xl font-bold text-primary-2 mb-4">
                    {formattedPrice}
                  </p>

                  <div className="text-stroke-thin text-gray-1 space-y-1">
                    <p>Quantity: 1</p>
                    <p>Subtotal: {formattedPrice}</p>
                  </div>
                </div>

                {/* Buy Button */}
                <div className="mt-auto px-4 flex justify-center">
                  {!isOnCorrectNetwork ? (
                    <button
                      onClick={() =>
                        switchNetwork({
                          chainId: VENDING_MACHINE_CONFIG_DEFAULT.chainId,
                        })
                      }
                      className="w-full rounded-full font-bold btn-base text-base transition-colors cursor-pointer bg-gradient-primary-1 text-white"
                    >
                      Switch to{' '}
                      {IS_TESTNET ? 'Polygon Amoy' : 'Polygon Mainnet'}
                    </button>
                  ) : selectedWallet === 'external' &&
                    needsApproval &&
                    !isButtonDisabled ? (
                      <Button
                        variant="dark"
                        className="w-[80%] flex justify-center rounded-full font-bold btn-base text-base transition-colors cursor-pointer bg-gradient-yellow-1 text-white"
                        disable={isProcessingApproval}
                        onClick={handleApprove}
                      >
                        {isProcessingApproval && <Loader className="mr-2" />}
                        {isProcessingApproval ? 'Approving...' : 'Approve Token'}
                      </Button>
                    ) : (
                      <Button
                        variant="dark"
                        className={`w-[80%] flex justify-center rounded-full font-bold btn-base text-base transition-colors ${
                          isButtonDisabled ||
                        (selectedWallet === 'external' && needsApproval)
                            ? 'cursor-not-allowed opacity-30 bg-gray-4 text-gray-1'
                            : 'cursor-pointer bg-gradient-primary-1 text-white'
                        }`}
                        disable={
                          isLoadingBalances ||
                          isButtonDisabled || isLoadingAllowance ||
                          isProcessingApproval ||
                        isProcessingPayment ||
                        (selectedWallet === 'external' && needsApproval)
                        }
                        onClick={handleBuyPackage}
                      >
                        {isProcessingPayment && <Loader className="mr-2" />}
                        {isButtonDisabled
                          ? 'Insufficient Balance'
                          : selectedWallet === 'external' && needsApproval
                            ? 'Approve Token First'
                            : 'Buy Now'}
                      </Button>
                    )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default PaymentModal
