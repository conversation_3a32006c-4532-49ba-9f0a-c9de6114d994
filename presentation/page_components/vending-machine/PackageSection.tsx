import React from 'react'
import Link from 'next/link'
import { PackageItem as PackageItemType } from 'domain/entities/vending_machine'
import PackageList from './PackageList'
import { PACKAGES_HOME_LIMIT } from 'lib/vendingMachineConfig'
interface PackageSectionProps {
  id: string
  title: string
  packages: PackageItemType[]
  total?: number
  index?: number
}

const PackageSection: React.FC<PackageSectionProps> = ({ id, title, packages, total, index }) => {
  if (packages.length === 0) {
    return null
  }
  
  const shouldShowSeeMore = total && total > PACKAGES_HOME_LIMIT
  
  return (
    <section id={id} className="mb-16 px-4 package-section">
      <h2 className="package-section-title">{title}</h2>
      <div className="max-w-7xl mx-auto">
        <PackageList packages={packages} />
      </div>
      
      {shouldShowSeeMore && (
        <div className="text-center mt-10">
          <Link href={`/vending-machine/${id}/packages`}>
            <button className="bg-blue-100 text-blue-500 hover:bg-blue-200 transition-colors px-12 py-3 rounded-full text-base font-medium">
              See More
            </button>
          </Link>
        </div>
      )}
    </section>
  )
}

export default PackageSection 