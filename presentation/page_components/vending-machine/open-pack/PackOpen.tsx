'use client'

import { motion, useAnimation, useMotionValue, animate } from 'framer-motion'
import { useEffect, useState } from 'react'
import CardReveal from './CardReveal'

const PACK_W = 250
const PACK_H = 350

export default function PackOpen ({
  packImage,
  card,
  onClose,
  onBuybackConfirm,
}: {
  packImage: string
  card: any
  onClose?: () => void
  onBuybackConfirm?: (card: any) => void
}) {
  const [opened, setOpened] = useState(false)
  const [cardImage, setCardImage] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const [packImageLoaded, setPackImageLoaded] = useState(false)
  const [cardImageLoaded, setCardImageLoaded] = useState(false)
  const [animationCompleted, setAnimationCompleted] = useState(false)

  // --- Shake motion values (liên tục, không theo nhịp) ---
  const shakeX = useMotionValue(0)
  const shakeY = useMotionValue(0)
  const shakeR = useMotionValue(0)
  const shakeOpacity = useMotionValue(1)

  // --- Tear controls (cho hai nửa & mép rách) ---
  const leftControls = useAnimation()
  const rightControls = useAnimation()
  const tearEdgeControls = useAnimation()

  const wait = (ms: number) => new Promise((r) => setTimeout(r, ms))

  // Preload image function
  const preloadImage = (src: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      const img = new window.Image()
      img.onload = () => resolve()
      img.onerror = reject
      img.src = src
    })
  }

  // Handle pack image load
  const handlePackImageLoad = () => {
    setPackImageLoaded(true)
  }

  // Handle card image load
  const handleCardImageLoad = () => {
    setCardImageLoaded(true)
  }

  // Get card image from card data
  const getCardImage = () => {
    return card.selectedTokenMetadata?.metadata?.image || '/asset/images/vending-machine/card.jpg'
  }

  // Mock contract with image preloading
  const prefetchCard = async () => {
    setIsLoading(true)
    try {
      const targetImage = getCardImage()
      
      // Preload the card image first
      await preloadImage(targetImage)
      console.log('🃏 Card image loaded')
      setCardImageLoaded(true)
      
      await wait(500) // Shorter wait since image is already loaded
      setCardImage(targetImage)
    } catch (error) {
      console.error('Error prefetching card:', error)
      setCardImage('/asset/images/vending-machine/card.jpg')
      setCardImageLoaded(true) // Still proceed even if failed
    } finally {
      setIsLoading(false)
    }
  }

  // Jitter noise: 3 sóng sin tần số khác nhau + envelope biên độ
  // Rung liên tục có cấu hình: intensity (biên độ), speed (tốc độ), smooth (lọc mượt)
  const violentShake = (
    durationMs = 1400,
    cfg: { intensity?: number; speed?: number; smooth?: number } = {}
  ) =>
    new Promise<void>((resolve) => {
      const { intensity = 0.35, speed = 0.6, smooth = 0.22 } = cfg
      const start = performance.now()
      const twoPi = Math.PI * 2

      const raf = (now: number) => {
        const elapsed = now - start
        const t = elapsed / 1000

        // envelope mượt (không đổi tần số)
        const p = Math.min(elapsed / durationMs, 1)
        const ease = p < 0.5 ? 2 * p * p : 1 - Math.pow(-2 * p + 2, 2) / 2

        // biên độ theo intensity
        const amp    = 14 * intensity * (0.5 + 0.5 * ease)
        const ampY   =  5 * intensity * (0.5 + 0.5 * ease)
        const rotAmp = 12 * intensity * (0.5 + 0.5 * ease)

        // ↓ TỐC ĐỘ: nhân tần số với speed (0.5 = chậm 1/2)
        const f1 = 7 * speed; const  f2 = 11 * speed
        const f3 = 5 * speed; const  f4 =  9 * speed
        const fr = 13 * speed

        const x = Math.sin(t * twoPi * f1) * amp + Math.sin(t * twoPi * f2 + 0.7) * amp * 0.25
        const y = Math.sin(t * twoPi * f3 + 1.3) * ampY + Math.sin(t * twoPi * f4 + 0.2) * ampY * 0.25
        const r = Math.sin(t * twoPi * fr + 0.8) * rotAmp

        // Low-pass smoothing: smooth ∈ [0..1], lớn hơn = mượt hơn (ít nhấp hơn)
        const s = Math.min(Math.max(smooth, 0), 0.5)
        shakeX.set(shakeX.get() * s + x * (1 - s))
        shakeY.set(shakeY.get() * s + y * (1 - s))
        shakeR.set(shakeR.get() * s + r * (1 - s))

        if (elapsed < durationMs) requestAnimationFrame(raf)
        else resolve()
      }
      requestAnimationFrame(raf)
    })

  // Timeline: shake (liên tục) -> cross-fade -> xé dọc -> reveal
  const runTimeline = async () => {
    // chuẩn bị tear layer: để sẵn opacity 0 (tránh khựng mount)s
    await Promise.all([
      leftControls.set({ opacity: 0, x: 0, rotate: 0, filter: 'blur(0px)' }),
      rightControls.set({ opacity: 0, x: 0, rotate: 0, filter: 'blur(0px)' }),
      tearEdgeControls.set({ opacity: 0 }),
    ])

    // 1) Rung (tăng thời gian để đủ load card image)
    await violentShake(2500, { intensity: 0.3, speed: 0.45, smooth: 0.25 })

    // 2) Cross-fade (rất nhanh): hiện hai nửa & mép rách, đồng thời tắt ảnh rung
    const fadeShake = animate(shakeOpacity, 0, { duration: 0.14 })
    const showHalves = Promise.all([
      leftControls.start({ opacity: 1, transition: { duration: 0.1 } }),
      rightControls.start({ opacity: 1, transition: { duration: 0.1 } }),
      tearEdgeControls.start({
        opacity: [0, 1, 0.7, 0],
        transition: { duration: 0.9, ease: [0.2, 0.8, 0.2, 1] },
      }),
    ])
    await Promise.all([fadeShake, showHalves])

    // 3) Xé DỌC: hai nửa bay sang trái/phải (mượt)
    await Promise.all([
      leftControls.start({
        x: -160,
        rotate: -6,
        opacity: 0,
        filter: 'blur(4px)',
        transition: { duration: 1.0, ease: [0.12, 0.74, 0.26, 0.99] },
      }),
      rightControls.start({
        x: 160,
        rotate: 6,
        opacity: 0,
        filter: 'blur(4px)',
        transition: { duration: 1.0, ease: [0.12, 0.74, 0.26, 0.99] },
      }),
    ])
    
    // Mark animation as completed
    console.log('🎬 Pack animation completed')
    setAnimationCompleted(true)
  }

  // Preload pack image when component mounts
  useEffect(() => {
    preloadImage(packImage)
      .then(() => setPackImageLoaded(true))
      .catch(() => setPackImageLoaded(true)) // Still proceed even if preload fails
  }, [packImage])

  useEffect(() => {
    let mounted = true
    ;(async () => {
      // Wait for pack image to load before starting animation
      if (!packImageLoaded) return
      
      // Start card image preloading and pack animation in parallel
      prefetchCard() // Don't await this, let it run in background
      await runTimeline() // Wait for pack animation to complete
      
      if (!mounted) return
    })()
    return () => {
      mounted = false
    }
  }, [packImageLoaded])

  // Separate effect to handle card reveal - only when both animation and card loading are done
  useEffect(() => {
    if (animationCompleted && cardImageLoaded && !isLoading) {
      // Both pack animation is done and card image is loaded, reveal the card
      console.log('🎉 Revealing card - Animation completed:', animationCompleted, 'Card loaded:', cardImageLoaded, 'Loading:', isLoading)
      setOpened(true)
    }
  }, [animationCompleted, cardImageLoaded, isLoading])

  // Clip-path răng cưa (xé DỌC ở giữa ~ 50% chiều rộng)
  const leftClip = `
    polygon(
      0% 0%, 50% 0%,
      52% 6%, 48% 12%, 52% 18%, 48% 24%, 52% 30%, 48% 36%,
      52% 42%, 48% 48%, 52% 54%, 48% 60%, 52% 66%, 48% 72%,
      52% 78%, 48% 84%, 50% 100%, 0% 100%
    )
  `
  const rightClip = `
    polygon(
      50% 0%, 100% 0%, 100% 100%, 50% 100%,
      48% 94%, 52% 88%, 48% 82%, 52% 76%, 48% 70%, 52% 64%,
      48% 58%, 52% 52%, 48% 46%, 52% 40%, 48% 34%, 52% 28%,
      48% 22%, 52% 16%, 48% 10%, 52% 4%
    )
  `

  return (
    <div className="relative w-[360px] h-[500px] flex items-center justify-center">
      {/* LAYER 1: Ảnh rung (luôn render, cross-fade về 0) */}
      {packImageLoaded && (
        <motion.img
          src={packImage}
          alt="booster pack"
          className="absolute rounded-md shadow-xl pointer-events-none object-cover will-change-transform"
          style={{
            width: PACK_W,
            height: PACK_H,
            x: shakeX,
            y: shakeY,
            rotate: shakeR,
            opacity: shakeOpacity,
            transformOrigin: '50% 50%',
            backfaceVisibility: 'hidden',
            transformStyle: 'preserve-3d',
          }}
          initial={{ opacity: 1 }}
          onLoad={handlePackImageLoad}
        />
      )}

      {/* LAYER 2: Hai nửa xé dọc + rip edge (được cross-fade vào) */}
      {packImageLoaded && (
        <div
          className="absolute pointer-events-none"
          style={{
            width: PACK_W,
            height: PACK_H,
            left: '50%',
            top: '50%',
            transform: 'translate(-50%, -50%)',
          }}
        >
          {/* Nửa trái */}
          <motion.img
            src={packImage}
            alt="left half"
            className="absolute w-full h-full rounded-md object-cover will-change-transform"
            style={{ clipPath: leftClip }}
            initial={false}
            animate={leftControls}
          />
          {/* Nửa phải */}
          <motion.img
            src={packImage}
            alt="right half"
            className="absolute w-full h-full rounded-md object-cover will-change-transform"
            style={{ clipPath: rightClip }}
            initial={false}
            animate={rightControls}
          />

          {/* Mép rách dọc (SVG đứng giữa, chạy toàn chiều cao) */}
          <motion.svg
            viewBox={`0 0 36 ${PACK_H}`}
            className="absolute top-0 left-1/2 -translate-x-1/2 h-full w-[36px] z-20"
            animate={tearEdgeControls}
            style={{
              mixBlendMode: 'screen',
              filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.25))',
              pointerEvents: 'none',
            }}
          >
            <defs>
              <linearGradient id="paperGradV" x1="0" y1="0" x2="1" y2="0">
                <stop offset="0%" stopColor="#ffffff" />
                <stop offset="100%" stopColor="#f3f4f6" />
              </linearGradient>
            </defs>
            <path
              d={`
              M12 0
              L18 10 L8 20 L20 30 L8 40 L20 50 L8 60 L20 70
              L8 80 L20 90 L8 100 L20 110 L8 120 L20 130
              L8 140 L20 150 L8 160 L20 170 L8 180 L20 190
              L8 200 L20 210 L8 220 L20 230 L8 240 L20 250
              L8 260 L20 270 L8 280 L20 290 L8 300 L20 310
              L8 320 L20 330 L12 ${PACK_H}
              L24 ${PACK_H} L16 ${PACK_H-10} L28 ${PACK_H-20} L16 ${PACK_H-30}
              L28 ${PACK_H-40} L16 ${PACK_H-50} L28 ${PACK_H-60} L16 ${PACK_H-70}
              L28 ${PACK_H-80} L16 ${PACK_H-90} L28 ${PACK_H-100} L16 ${PACK_H-110}
              L28 ${PACK_H-120} L16 ${PACK_H-130} L28 ${PACK_H-140} L16 ${PACK_H-150}
              L28 ${PACK_H-160} L16 ${PACK_H-170} L28 ${PACK_H-180} L16 ${PACK_H-190}
              L28 ${PACK_H-200} L16 ${PACK_H-210} L28 ${PACK_H-220} L16 ${PACK_H-230}
              L28 ${PACK_H-240} L16 ${PACK_H-250} L28 ${PACK_H-260} L16 ${PACK_H-270}
              L28 ${PACK_H-280} L16 ${PACK_H-290} L28 ${PACK_H-300} L16 ${PACK_H-310}
              Z
            `}
              fill="url(#paperGradV)"
              stroke="#00000022"
              strokeWidth="0.6"
            />
          </motion.svg>
        </div>
      )}

      {/* Reveal card */}
      {opened && (
        <motion.div
          className="absolute w-full h-full flex items-center justify-center"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, ease: 'easeOut' }}
        >
          <CardReveal 
            card={card}
            onClose={onClose}
            onBuybackConfirm={onBuybackConfirm}
          />
        </motion.div>
      )}
    </div>
  )
}
