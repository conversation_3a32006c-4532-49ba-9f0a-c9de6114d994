'use client'

import { motion, useAnimation } from 'framer-motion'
import confetti from 'canvas-confetti'
import { useEffect, useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import BuybackConfirmModal from 'presentation/components/modal/buyback-confirm'
import Button from 'presentation/components/button'

export default function CardReveal ({
  card,
  backImage = '/asset/images/vending-machine/back-card.jpg', // default back-card
  onClose,
  onBuybackConfirm,
}: {
  card: any;
  backImage?: string;
  onClose?: () => void;
  onBuybackConfirm?: (card: any) => void;
}) {
  const controls = useAnimation()
  const [isLooping, setIsLooping] = useState(false)
  const [initialDone, setInitialDone] = useState(false)
  const [currentBackImage, setCurrentBackImage] = useState(backImage)
  const [frontImageLoaded, setFrontImageLoaded] = useState(false)
  const [backImageLoaded, setBackImageLoaded] = useState(false)
  const [showBuybackModal, setShowBuybackModal] = useState(false)

  // Extract data from card
  const frontImage = card.image || ''
  const cardPrice = card.buyBackPrice || card.convertedPrice || 0
  const cardCurrency = '$'

  // Preload image function
  const preloadImage = (src: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      const img = new window.Image()
      img.onload = () => resolve()
      img.onerror = reject
      img.src = src
    })
  }

  // Preload both images when component mounts
  useEffect(() => {
    const loadImages = async () => {
      try {
        await Promise.all([
          preloadImage(frontImage).then(() => setFrontImageLoaded(true)),
          preloadImage(currentBackImage).then(() => setBackImageLoaded(true)),
        ])
      } catch (error) {
        console.error('Error preloading card images:', error)
        // Still proceed even if preload fails
        setFrontImageLoaded(true)
        setBackImageLoaded(true)
      }
    }
    
    loadImages()
  }, [frontImage, currentBackImage])

  useEffect(() => {
    let mounted = true;

    (async () => {
      // Wait for both images to load before starting animation
      if (!frontImageLoaded || !backImageLoaded) return
      // 1) initial flip: back (0) -> front (180)
      await controls.start({
        rotateY: 180,
        scale: 1,
        transition: { duration: 1.2, ease: 'easeInOut' },
      })

      if (!mounted) return
      setInitialDone(true)

      // 2) confetti once after reveal with high z-index
      const duration = 1500
      const end = Date.now() + duration;
      (function frame () {
        confetti({ 
          particleCount: 6, 
          spread: 55, 
          origin: { x: 0.3 },
          zIndex: 10000, // Ensure confetti appears above everything
        })
        confetti({ 
          particleCount: 6, 
          spread: 55, 
          origin: { x: 0.7 },
          zIndex: 10000, // Ensure confetti appears above everything
        })
        if (Date.now() < end) {
          requestAnimationFrame(frame)
        }
      })()

      // 3) start looping auto-flip front <-> front (change backImage to frontImage)
      setCurrentBackImage(frontImage) // Switch to front image for loop
      setIsLooping(true)
      controls.start({
        rotateY: [180, 360], // 180 -> 360 (360 === 0 mod 360)
        transition: {
          duration: 3, // full front->back time (adjust speed here)
          ease: 'linear',
          repeat: Infinity,
        },
      })
    })()

    return () => {
      mounted = false
      controls.stop()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [frontImageLoaded, backImageLoaded])

  const toggleLoop = () => {
    if (isLooping) {
      // stop the continuous loop and revert to original backImage
      controls.stop()
      setCurrentBackImage(backImage)
      setIsLooping(false)
    } else {
      // resume the continuous loop and switch to frontImage
      setCurrentBackImage(frontImage)
      setIsLooping(true)
      controls.start({
        rotateY: [
          /* start from whatever current value, animate to next */ 0, 180, 360,
        ],
        transition: { duration: 3, ease: 'linear', repeat: Infinity },
      })
    }
  }

  return (
    <div className="w-screen h-screen flex flex-col items-center justify-center relative" style={{ zIndex: 1000 }}>
      {/* wrapper needs perspective so 3D rotate looks good */}
      <div style={{ perspective: 1200 }} className="relative">
        {(frontImageLoaded && backImageLoaded) && (
          <motion.div
            animate={controls}
            initial={{ rotateY: 0, scale: 0.9 }}
            className="relative w-[250px] h-[350px] [transform-style:preserve-3d]"
          >
            {/* FRONT (rotated 180 so it faces viewer at rotateY=180) */}
            <div
              className="absolute inset-0 [backface-visibility:hidden]"
              style={{ transform: 'rotateY(180deg)' }}
            >
              <Image
                src={frontImage}
                alt="card front"
                fill
                className="object-cover rounded-lg shadow-xl"
              />
            </div>

            {/* BACK (visible at rotateY=0) */}
            <div className="absolute inset-0 [backface-visibility:hidden]">
              <Image
                src={currentBackImage}
                alt={currentBackImage === frontImage ? 'card front' : 'card back'}
                fill
                className="object-cover rounded-lg shadow-xl"
              />
            </div>
          </motion.div>
        )}
      </div>

      {/* Card Price Display with Pulse Animation */}
      {cardPrice && initialDone && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 1.5, duration: 0.8, ease: 'easeOut' }}
          className="mt-6 relative"
        >
          {/* Glow Background */}
          <motion.div
            animate={{
              scale: [1, 1.1, 1],
              opacity: [0.7, 1, 0.7],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut',
            }}
            className="absolute inset-0 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 rounded-2xl blur-lg"
          />
          
          {/* Price Container */}
          <motion.div
            animate={{
              boxShadow: [
                '0 0 20px rgba(251, 191, 36, 0.5)',
                '0 0 40px rgba(251, 191, 36, 0.8)',
                '0 0 20px rgba(251, 191, 36, 0.5)',
              ],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut',
            }}
            onClick={() => setShowBuybackModal(true)}
            className="relative cursor-pointer bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 px-8 py-4 rounded-2xl border-2 border-yellow-300"
          >
            <div className="text-center">
              <motion.div
                animate={{
                  scale: [1, 1.05, 1],
                  y: [0, -3, 0],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: 'easeInOut',
                }}
                className="flex items-center justify-center"   
              >
                <Button
                  variant="gradient"
                  size="lg"
                  className="!text-white !font-bold !text-xl !transition-all !duration-200"
                >
                  <span className="text-lg mr-2">💰</span>
                  Sell for {typeof cardPrice === 'number' 
                    ? cardPrice.toLocaleString() 
                    : cardPrice
                  } {cardCurrency}
                  <span className="text-lg ml-2">💰</span>
                </Button>
              </motion.div>
            </div>
            
            {/* Sparkle Effects */}
            <motion.div
              animate={{
                rotate: 360,
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: 'linear',
              }}
              className="absolute -top-2 -right-2 text-yellow-200 text-xl"
            >
              ✨
            </motion.div>
            <motion.div
              animate={{
                rotate: -360,
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: 'linear',
              }}
              className="absolute -bottom-2 -left-2 text-yellow-200 text-xl"
            >
              ⭐
            </motion.div>
            <motion.div
              animate={{
                scale: [1, 1.2, 1],
                rotate: [0, 180, 360],
              }}
              transition={{
                duration: 2.5,
                repeat: Infinity,
                ease: 'easeInOut',
              }}
              className="absolute top-[30%] -left-3 transform -translate-y-1/2 text-yellow-200 text-lg"
            >
              💎
            </motion.div>
            <motion.div
              animate={{
                scale: [1, 1.2, 1],
                rotate: [360, 180, 0],
              }}
              transition={{
                duration: 2.5,
                repeat: Infinity,
                ease: 'easeInOut',
              }}
              className="absolute top-[30%] -right-3 transform -translate-y-1/2 text-yellow-200 text-lg"
            >
              💎
            </motion.div>
          </motion.div>
        </motion.div>
      )}

      {/* View Minted History button */}
      {initialDone && (
        <Link href="/account/vending-machine/minted-history">
          <motion.button
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.0 }}
            className="mt-8 px-8 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 
                       text-white font-bold rounded-full shadow-lg border-2 border-blue-400
                       hover:from-blue-600 hover:to-indigo-700 hover:scale-105 
                       transition-all duration-200"
            whileHover={{
              scale: 1.05,
              boxShadow: '0 0 20px rgba(59, 130, 246, 0.5)',
            }}
            whileTap={{ scale: 0.95 }}
          >
            📜 View Minted History 📜
          </motion.button>
        </Link>
      )}

      {/* Buyback Confirm Modal */}
      {card && (
        <BuybackConfirmModal
          card={card}
          isOpen={showBuybackModal}
          onClose={() => setShowBuybackModal(false)}
          onConfirm={(card) => {
            setShowBuybackModal(false)
            onBuybackConfirm && onBuybackConfirm(card)
          }}
        />
      )}
    </div>
  )
}
