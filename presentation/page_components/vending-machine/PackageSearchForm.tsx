import React, { useState, useEffect, useRef } from 'react'
import ToggleVertical from 'presentation/components/toggle-wrap/toggle-vertical'
import Button from 'presentation/components/button'

interface PackageSearchFormProps {
  searchQuery: string
  onSearchQueryChange: (value: string) => void
  onSearch: () => void
  onClear: () => void
  total: number
  isLoading: boolean
  defaultShow?: boolean
}

const PackageSearchForm: React.FC<PackageSearchFormProps> = ({
  searchQuery,
  onSearchQueryChange,
  onSearch,
  onClear,
  total,
  isLoading,
  defaultShow = true,
}) => {
  const clearTriggeredRef = useRef(false)

  // Use useEffect to ensure searchQuery is cleared before calling onClear
  useEffect(() => {
    if (clearTriggeredRef.current && searchQuery === '') {
      clearTriggeredRef.current = false
      onClear()
    }
  }, [searchQuery, onClear])

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSearch()
  }

  const handleClearSearch = () => {
    clearTriggeredRef.current = true
    onSearchQueryChange('')
  }

  return (
    <ToggleVertical toggleTitle="Search Packages" defaultShow={defaultShow}>
      {/* Search Form */}
      <form onSubmit={handleSearchSubmit} className="xl:pl-10p xl:pr-[5px]">
        <div className="space-y-4">
          <div className="relative">
            <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-2">
              Package Name
            </label>
            <div className="relative">
              <input
                type="text"
                id="search"
                value={searchQuery}
                onChange={(e) => onSearchQueryChange(e.target.value)}
                placeholder="Search by package name..."
                className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent h-[50px]"
              />
              {/* Clear icon inside input */}
              {searchQuery && (
                <button
                  type="button"
                  onClick={handleClearSearch}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                  aria-label="Clear search"
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              )}
            </div>
          </div>
        </div>
      </form>

      {/* Apply Button */}
      <div className="btn-apply">
        <Button
          variant="dark"
          className="mt-2 w-full text-center"
          type="button"
          onClick={onSearch}
        >
          Apply
        </Button>
      </div>

    </ToggleVertical>
  )
}

export default PackageSearchForm