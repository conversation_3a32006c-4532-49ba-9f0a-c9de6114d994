import React from 'react'
import { PackageType } from 'domain/entities/vending_machine'

interface PackageTypeTabsProps {
  packageTypes: PackageType[]
  onTabClick: (id: string) => void
}

const PackageTypeTabs: React.FC<PackageTypeTabsProps> = ({ packageTypes, onTabClick }) => {
  return (
    <div className="flex flex-wrap justify-center gap-3 mb-16">
      {packageTypes.map((type) => (
        <button
          key={type.id}
          className="px-6 py-1.5 bg-white rounded-full
                    text-dark-primary
                    hover:shadow-lg transition-all focus:outline-none package-type-tab"
          onClick={() => onTabClick(type.id)}
        >
          {type.title}
        </button>
      ))}
    </div>
  )
}

export default PackageTypeTabs 