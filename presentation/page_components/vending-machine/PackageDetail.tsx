import React, { useState, useEffect } from 'react'
import { PackageDetail as PackageDetailType, PackageOdds } from 'domain/entities/vending_machine'
import Link from 'next/link'
import eventBus from 'lib/eventBus'
import Image from 'next/image'
import MorePacks from './MorePacks'
import PaymentModal from './PaymentModal'
import OpenPackModal from './OpenPackModal'
import BigNumber from 'bignumber.js'
import { useAccount } from 'wagmi'
import { useAuthentication } from 'lib/hook/auth_hook'

import SettingUseCase from 'presentation/use_cases/setting_use_case'
const settingUseCase = new SettingUseCase()
interface PackageDetailProps {
  packageDetail: PackageDetailType
}

const PackageDetail: React.FC<PackageDetailProps> = ({ packageDetail }) => {
  console.log('🚀 ~ PackageDetail ~ packageDetail:', packageDetail)
  const [imageError, setImageError] = useState(false)
  const [showPaymentModal, setShowPaymentModal] = useState(false)
  const [showOpenPackModal, setShowOpenPackModal] = useState(false)
  const [isSingularityLoaded, setIsSingularityLoaded] = useState(false)
  const [isSettingEnable, setIsSettingEnable] = useState(false)
  const { address, chainId } = useAccount()
  const { getProviderEthers } = useAuthentication()
  const [card, setCard] = useState<any | null>(null) // Card received after opening pack
  

  const handleBuyNow = () => {
    setShowPaymentModal(true)
  }

  const handleBuybackConfirm = async (card: any) => {
    console.log('🔄 Buyback confirmed for card:', card)
    // In real app, this would call the buyback API
    // For now, just show success message
    alert('Buyback confirmed! Your NFT has been sold.')
  }
  
  const handlePaymentComplete = async (cardPayload: any) => {
    // set cardUrl
    cardPayload = {
      ...cardPayload,
      convertedPrice: BigNumber(cardPayload?.convertedPrice).toFixed(2),
    }
    setCard(cardPayload)
    // Close payment modal and open pack opening modal
    setShowPaymentModal(false)
    setShowOpenPackModal(true)
      
   
  }
  // Setup eventBus and listen for Singularity loaded event
  useEffect(() => {
    const handleSingularityLoaded = () => {
      console.log('🎉 Singularity loaded event received!')
      setIsSingularityLoaded(true)
    }

    // Check if already loaded
    if (window.SingularityEvent) {
      console.log('✅ SingularityEvent already exists, setting loaded = true')
      setIsSingularityLoaded(true)
    }

    // Setup listener
    console.log('📡 Setting up emitter listener for SingularityLoaded')
    eventBus.on('SingularityLoaded', handleSingularityLoaded)

    // Test emit after setup
    setTimeout(() => {
      console.log('🧪 Testing emit after 1 second...')
      eventBus?.emit('SingularityLoaded')
    }, 1000)

    return () => {
      console.log('🧹 Cleaning up listener')
      eventBus?.off('SingularityLoaded', handleSingularityLoaded)
    }
  }, [])
  useEffect(() => {
    const fetchSettingEnable = async () => {
      const settingEnable = await settingUseCase.getSettingVMSaleStatus()
      console.log('🚀 ~ fetchSettingEnable ~ settingEnable:', settingEnable)
      setIsSettingEnable(settingEnable)
    }
    fetchSettingEnable()
  }, [])
  return (
    <div className="container mx-auto px-4 py-6 package-detail-container">
      <div className="mb-6">
        <Link href={'/vending-machine/'} className="inline-flex items-center text-blue-500 hover:underline">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          Back to vending machine
        </Link>
      </div>
      
      <div className="flex flex-col lg:flex-row gap-8">
        {/* Left side - Machine Image */}
        <div className="lg:w-1/2">
          <div className="bg-gray-100 rounded-lg overflow-hidden relative aspect-square">
            {imageError ? (
              <Image 
                src="/asset/images/placeholder.jpg"
                alt={packageDetail.title}
                fill
                sizes="(max-width: 768px) 100vw, 50vw"
                className="object-contain"
              />
            ) : (
              <Image 
                src={packageDetail.image}
                alt={packageDetail.title}
                fill
                sizes="(max-width: 768px) 100vw, 50vw"
                className="object-contain"
                onError={() => setImageError(true)}
              />
            )}
          </div>
          
          {/* Card Items Section */}
          {/* {packageDetail.cardItems && packageDetail.cardItems.length > 0 && (
            <div className="mt-6">
              <h2 className="text-xl font-bold mb-4">Cards in this pack</h2>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3">
                {packageDetail.cardItems.map((card) => (
                  <div 
                    key={card.id} 
                    className="relative aspect-[2/3] rounded-lg overflow-hidden border border-gray-300 shadow-sm hover:shadow-md transition-shadow"
                  >
                    <Image
                      src={card.image}
                      alt={card.rarity || 'Card'}
                      fill
                      sizes="(max-width: 768px) 33vw, 20vw"
                      className="object-cover"
                    />
                    {card.rarity && (
                      <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-60 text-white text-xs py-1 px-2 text-center">
                        {card.rarity}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )} */}
        </div>
        
        {/* Right side - Package Details */}
        <div className="lg:w-1/2 package-detail-right">
          <h1 className="mb-2 package-detail-right-title">{packageDetail.title}</h1>
          
          <div className="mb-4">
            <p className="package-detail-right-description">{packageDetail.description}</p>
          </div>
          
          {/* Odds section */}
          <div className="mb-6">
  
            <div className="package-detail-right-odds-title">Odds*</div>
            <div className="mb-2 package-detail-right-odds-expected-value">Expected Value: ${packageDetail.expectedValue}</div>
            
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-2">
              {packageDetail.odds.map((odd, index) => (
                <OddsCard key={index} odd={odd} />
              ))}
            </div>
            
            <div className="mt-3 package-detail-right-odds-description">
              *Average item value is ${packageDetail.expectedValue}. Odds are calculated in real-time. This is not financial advice.
              Market data and prices are subject to change. Comps are informed by recent sales on
              several marketplaces. Terms apply.
            </div>
          </div>
          
          {/* More Packs Component */}
          <MorePacks 
            currentTypeId={packageDetail.typeId}
            relatedPackages={packageDetail.relatedPackages}
          />
          
          {/* Price and Buy Now */}
          <div className="flex justify-between items-center">
            <div className="package-price">
              ${packageDetail.price || 0}
            </div>
            <button 
              disabled={!isSettingEnable}
              
              className={`${!isSettingEnable ? 'opacity-50' : ''} package-buy-now-button text-white px-14 py-4 rounded-full text-lg font-medium transition-colors ${!isSettingEnable ? 'opacity-50 cursor-not-allowed' : ''}`}
              onClick={handleBuyNow}
            >
              {isSettingEnable ? 'Buy Now' : 'Coming Soon'}
            </button>
          </div>
        </div>
      </div>
      
      {/* Payment Modal */}
      <PaymentModal
        isShow={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        packageTitle={packageDetail.title}
        packageImage={packageDetail.image}
        price={packageDetail.price}
        packageId={packageDetail.id}
        onBuyNow={handlePaymentComplete}
        isSingularityLoaded={isSingularityLoaded}
      />
      
      {/* Open Pack Modal */}
      {card && (
        <OpenPackModal
          isShow={showOpenPackModal}
          onClose={() => setShowOpenPackModal(false)}
          packageImage={packageDetail.image}
          card={card}
          onBuybackConfirm={handleBuybackConfirm}
        />
      )}
    </div>
  )
}

// Component for displaying odds card
const OddsCard: React.FC<{odd: PackageOdds}> = ({ odd }) => {
  return (
    <div className="border rounded-md p-3 text-center odds-card">
      <div className="odds-card-range">{odd.range}</div>
      <div className="odds-card-percentage">{odd.percentage}%</div>
    </div>
  )
}

export default PackageDetail 