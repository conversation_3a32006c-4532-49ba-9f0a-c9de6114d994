import React from 'react'
import SvgIcon from 'presentation/components/svg-icon'
import PackOpen from './open-pack/PackOpen'
interface OpenPackModalProps {
  isShow: boolean
  card: any
  packageImage: string
  onClose: () => void
  onBuybackConfirm?: (card: any) => void
}

const OpenPackModal: React.FC<OpenPackModalProps> = ({
  isShow,
  card,
  onClose,
  packageImage,
  onBuybackConfirm,
}) => {
  if (!isShow) {
    return null
  }

  const handleModalClose = () => {
    onClose()
  }

  return (
    <div
      className={`fixed inset-0 flex items-center justify-center transition-all duration-300 ${
        isShow ? 'opacity-100 visible' : 'opacity-0 invisible'
      }`}
      style={{
        zIndex: 999,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        backdropFilter: 'blur(8px)',
      }}
  
    >
      <div
        className="m-auto rounded-10 relativ  transition-all duration-300 max-w-2xl w-full mx-4"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Body */}
        <div className="p-8 flex justify-center">
          <PackOpen 
            packImage={packageImage}
            card={card}
            onClose={onClose}
            onBuybackConfirm={onBuybackConfirm}
          />
        </div>
      </div>
    </div>
  )
}

export default OpenPackModal
