import { generateData } from 'lib/utils'

export const nftDropItem = {
  id: 'bc1ed928-3169-4caf-a0cb-df1a5ef6f290',
  slug: 'o<PERSON><PERSON><PERSON><PERSON>',
  name: 'sa<PERSON><PERSON>_lab_next',
  description: 'Ya<PERSON>~~~ <PERSON><PERSON><PERSON> desune',
  logoImage: {
    id: '27cb73e6-5c67-4fc1-be64-a7fc245cad4b',
    url: 'https://mynftdisplay.s3.ap-northeast-1.amazonaws.com/data/uploads/27cb/73e6/27cb73e6-5c67-4fc1-be64-a7fc245cad4b.jpg',
    storageName: 's3',
    mimeType: 'image/jpeg',
    format: {
      id: 1,
      mime: 'image/jpeg',
      name: 'jpeg',
      label: 'JPEG',
      extension: 'jpg',
    },
    dataSize: 6734,
    height: 120,
    width: 120,
    hash: 'd44864dc6c5164724d8cfc3742f44f277ef0264b2eb99cff8783f6c88dd8eaa4',
    externalId: null,
    createdAt: '2022-11-18T03:46:16.000+00:00',
    updatedAt: '2022-11-18T03:46:16.000+00:00',
  },
  category: {
    id: 'a952f3f6-1fb4-4e4f-904b-69112659ef91',
    slug: 'digitalArt',
    name: 'Digital Art',
    shortenedNamae: 'Art',
    thumbnail: null,
    background: null,
    description: 'Digital Art NFTs',
    order: 1,
    createdAt: '2022-11-18T04:18:11.000+00:00',
    updatedAt: '2022-11-18T04:18:11.000+00:00',
  },
  mintUrl: 'https://opensea.io/collection/osashimichan',
  webUrl: 'https://opensea.io/collection/osashimichan',
  discordUrl: 'ssm_a_u',
  twitterUsername: 'ssm_a_u',
  owner: {
    id: '1859d57c-5e89-4df3-b7a0-c20bf59c0812',
    username: '0x43D65b69949D10061b23708aC6Be268897F6A16f',
    email: null,
    thumbnail: null,
    background: null,
    description: null,
    twitterUsername: null,
    instagramUsername: null,
    webUrl: null,
    lastAccessedAt: null,
    language: {
      id: 1,
      name: 'en',
      label: 'English',
    },
    publicAddresses: [],
    createdAt: '2022-11-18T04:16:34.000+00:00',
    updatedAt: '2022-11-18T04:16:34.000+00:00',
  },
  floorPrice: 0,
  totalSupply: null,
  paymentToken: null,
  createdAt: '2022-11-18T04:18:21.000+00:00',
  updatedAt: '2022-11-18T04:18:21.000+00:00',
  date: 'Mar 05,2022',
  time: '17:00:00',
  countDown: {
    day: 0,
    hrs: 2,
    min: 54,
    sec: 16,
  },
}

export const nftDropTempData = generateData(250, nftDropItem)
