import { generateData } from 'lib/utils'

const listTempRecord = {
  thumbnail: {
    url: '/asset/images/character-gen.png',
    mimeType: 'image/jpeg',
  },
  percentage: 70,
  attributes: [
    {
      type: 'Body',
      name: 'body05',
      percentage: 70,
      quantity: 499,
      thumbnail: {
        url: '/asset/images/generator-2.png',
        mimeType: 'image/jpeg',
      },
    },
    {
      type: 'Eye',
      name: 'eye05',
      percentage: 70,
      quantity: 499,
      thumbnail: {
        url: '/asset/images/generator-2.png',
        mimeType: 'image/jpeg',
      },
    },
    {
      type: 'Hair',
      name: 'body05',
      percentage: 70,
      quantity: 499,
      thumbnail: {
        url: '/asset/images/generator-2.png',
        mimeType: 'image/jpeg',
      },
    },
    {
      type: 'Pans',
      name: 'body05',
      percentage: 70,
      quantity: 499,
      thumbnail: {
        url: '/asset/images/generator-2.png',
        mimeType: 'image/jpeg',
      },
    },
    {
      type: 'Shirt',
      name: 'body05',
      percentage: 70,
      quantity: 499,
      thumbnail: {
        url: '/asset/images/generator-2.png',
        mimeType: 'image/jpeg',
      },
    },
    {
      type: 'Tie',
      name: 'body05',
      percentage: 70,
      quantity: 499,
      thumbnail: {
        url: '/asset/images/generator-2.png',
        mimeType: 'image/jpeg',
      },
    },
  ],
}

export const listTempData = generateData(100, listTempRecord)

export const layersTempData = [
  {
    id: 1,
    name: 'Background',
    value: 5,
    percentage: 70,
    options: [
      {
        name: 'Background 01',
        childrenOptions: [
          { name: 'Background > bg1A', isBlocked: true },
          { name: 'Background > bg1B', isBlocked: false},
          { name: 'Background > bg1C', isBlocked: false},
        ],
      },
      { name: 'Background 02' },
      { name: 'Background 03' },
    ],
    quantity: 652,
    assets: [...Array(5)].map((_, i) => {
      return {
        thumbnail: {
          url: '/asset/images/clothes.png',
          mimeType: 'image/jpeg',
        },
        name: 'body05',
        percentage: 70,
        quantity: 499,
      }
    }),
  },
  {
    id: 2,
    name: 'Body',
    value: 5,
    percentage: 70,
    options: [
      {
        name: 'Body 01',
        childrenOptions: [
          { name: 'Eye > eye', isBlocked: true },
          { name: 'Eye > eye', isBlocked: false},
          { name: 'Eye > eye', isBlocked: false},
        ],
      },
      { name: 'Body 1' },
      { name: 'Body 2' },
    ],
    quantity: 652,
    assets: [...Array(5)].map((_, i) => {
      return {
        thumbnail: {
          url: '/asset/images/clothes.png',
          mimeType: 'image/jpeg',
        },
        name: 'body05',
        percentage: 70,
        quantity: 499,
      }
    }),
  },
  {
    id: 3,
    name: 'Background',
    value: 5,
    percentage: 70,
    options: [
      {
        name: 'Background 01',
        childrenOptions: [
          { name: 'Background > bg1A', isBlocked: true },
          { name: 'Background > bg1B', isBlocked: false},
          { name: 'Background > bg1C', isBlocked: false},
        ],
      },
      { name: 'Background 02' },
      { name: 'Background 03' },
    ],
    quantity: 652,
    assets: [...Array(5)].map((_, i) => {
      return {
        thumbnail: {
          url: '/asset/images/clothes.png',
          mimeType: 'image/jpeg',
        },
        name: 'body05',
        percentage: 70,
        quantity: 499,
      }
    }),
  },
]
