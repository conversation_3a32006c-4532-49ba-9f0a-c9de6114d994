import Chain from 'domain/value_objects/chain'
import { generateData } from 'lib/utils'

const whaleInvolvedRecord = {
  collection: {
    id: 'bc1ed928-3169-4caf-a0cb-df1a5ef6f290',
    slug: 'o<PERSON><PERSON><PERSON><PERSON>',
    name: 'sa<PERSON><PERSON>_lab_next',
    description: 'Ya<PERSON>~~~ <PERSON><PERSON><PERSON> des<PERSON>',
    logoImage: {
      id: '27cb73e6-5c67-4fc1-be64-a7fc245cad4b',
      url: 'https://mynftdisplay.s3.ap-northeast-1.amazonaws.com/data/uploads/27cb/73e6/27cb73e6-5c67-4fc1-be64-a7fc245cad4b.jpg',
      storageName: 's3',
      mimeType: 'image/jpeg',
      format: {
        id: 1,
        mime: 'image/jpeg',
        name: 'jpeg',
        label: 'JPEG',
        extension: 'jpg',
      },
      dataSize: 6734,
      height: 120,
      width: 120,
      hash: 'd44864dc6c5164724d8cfc3742f44f277ef0264b2eb99cff8783f6c88dd8eaa4',
      externalId: null,
      createdAt: '2022-11-18T03:46:16.000+00:00',
      updatedAt: '2022-11-18T03:46:16.000+00:00',
    },
    featuredImage: {
      id: '27cb73e6-5c67-4fc1-be64-a7fc245cad4b',
      url: 'https://mynftdisplay.s3.ap-northeast-1.amazonaws.com/data/uploads/27cb/73e6/27cb73e6-5c67-4fc1-be64-a7fc245cad4b.jpg',
      storageName: 's3',
      mimeType: 'image/jpeg',
      format: {
        id: 1,
        mime: 'image/jpeg',
        name: 'jpeg',
        label: 'JPEG',
        extension: 'jpg',
      },
      dataSize: 6734,
      height: 120,
      width: 120,
      hash: 'd44864dc6c5164724d8cfc3742f44f277ef0264b2eb99cff8783f6c88dd8eaa4',
      externalId: null,
      createdAt: '2022-11-18T03:46:16.000+00:00',
      updatedAt: '2022-11-18T03:46:16.000+00:00',
    },
    bannerImage: {
      id: 'dbf23d24-172e-4725-8da1-aaa2e1485655',
      url: 'https://mynftdisplay.s3.ap-northeast-1.amazonaws.com/data/uploads/dbf2/3d24/dbf23d24-172e-4725-8da1-aaa2e1485655.png',
      storageName: 's3',
      mimeType: 'image/png',
      format: {
        id: 2,
        mime: 'image/png',
        name: 'png',
        label: 'PNG',
        extension: 'png',
      },
      dataSize: 151693,
      height: 400,
      width: 1400,
      hash: '93fa54a2b16bcecfbe8d56a0947d0ecba7e3e271b2e625dae4e20928cf039c33',
      externalId: null,
      createdAt: '2022-11-18T03:45:49.000+00:00',
      updatedAt: '2022-11-18T03:45:49.000+00:00',
    },
    category: {
      id: 'a952f3f6-1fb4-4e4f-904b-69112659ef91',
      slug: 'digitalArt',
      name: 'Digital Art',
      thumbnail: null,
      background: null,
      description: 'Digital Art NFTs',
      order: 1,
      createdAt: '2022-11-18T04:18:11.000+00:00',
      updatedAt: '2022-11-18T04:18:11.000+00:00',
    },
    webUrl: 'https://opensea.io/collection/osashimichan',
    discordUrl: null,
    telegramUrl: null,
    mediumUsername: null,
    twitterUsername: 'ssm_a_u',
    instagramUsername: null,
    isExplicit: 0,
    owner: {
      id: '1859d57c-5e89-4df3-b7a0-c20bf59c0812',
      username: '0x43D65b69949D10061b23708aC6Be268897F6A16f',
      email: null,
      thumbnail: null,
      background: null,
      description: null,
      twitterUsername: null,
      instagramUsername: null,
      webUrl: null,
      lastAccessedAt: null,
      language: {
        id: 1,
        name: 'en',
        label: 'English',
      },
      publicAddresses: [],
      createdAt: '2022-11-18T04:16:34.000+00:00',
      updatedAt: '2022-11-18T04:16:34.000+00:00',
    },
    numAssets: 0,
    numOwners: 0,
    totalVolume: null,
    floorPrice: null,
    paymentTokens: [],
    payoutAddresses: [],
    collaborators: [],
    createdAt: '2022-11-18T04:18:21.000+00:00',
    updatedAt: '2022-11-18T04:18:21.000+00:00',
  },
  whales: {
    amount: 5,
  },
  bought: {
    qty: 16,
  },
  whaleVolume: {
    price: '$8,574,264.63',
    percentage: '70%',
  },
  totalVolume: {
    price: '$10,773,648.18',
    percentage: '+18614.83%',
    type: 'increase',
  },
  floorPrice: {
    price: '3.15 ETH',
    percentage: '-2.16%',
    type: 'decrease',
  },
  avgPrice: {
    price: '144.5909 ETH',
    percentage: '+32.43%',
    type: 'increase',
  },
}

export const whaleInvolvedTempData = generateData(100, whaleInvolvedRecord)

const bigWhalesBoughtRecord = {
  nft: {
    id: '180beb11-0383-4987-9715-229efc80b578',
    collection: {
      name: 'sashimi_lab_next',
    },
    contract: {
      chain: new Chain({name: 'ethereum'}),
    },
    name: 'KEMOMIMI GIRL#20987',
    thumbnail: {
      url: 'https://mynftdisplay.s3.ap-northeast-1.amazonaws.com/data/uploads/bd39/7faa/bd397faa-9d2e-4ddf-ae40-22be5f140666.jpg',
      mimeType: 'image/jpeg',
    },
  },
  collection: {
    id: 'bc1ed928-3169-4caf-a0cb-df1a5ef6f290',
    slug: 'osashimichan',
    name: 'sashimi_lab_next',
    description: 'Yay~~~ Saikou desune',
    logoImage: {
      id: '27cb73e6-5c67-4fc1-be64-a7fc245cad4b',
      url: 'https://mynftdisplay.s3.ap-northeast-1.amazonaws.com/data/uploads/27cb/73e6/27cb73e6-5c67-4fc1-be64-a7fc245cad4b.jpg',
      storageName: 's3',
      mimeType: 'image/jpeg',
      format: {
        id: 1,
        mime: 'image/jpeg',
        name: 'jpeg',
        label: 'JPEG',
        extension: 'jpg',
      },
      dataSize: 6734,
      height: 120,
      width: 120,
      hash: 'd44864dc6c5164724d8cfc3742f44f277ef0264b2eb99cff8783f6c88dd8eaa4',
      externalId: null,
      createdAt: '2022-11-18T03:46:16.000+00:00',
      updatedAt: '2022-11-18T03:46:16.000+00:00',
    },
    featuredImage: {
      id: '27cb73e6-5c67-4fc1-be64-a7fc245cad4b',
      url: 'https://mynftdisplay.s3.ap-northeast-1.amazonaws.com/data/uploads/27cb/73e6/27cb73e6-5c67-4fc1-be64-a7fc245cad4b.jpg',
      storageName: 's3',
      mimeType: 'image/jpeg',
      format: {
        id: 1,
        mime: 'image/jpeg',
        name: 'jpeg',
        label: 'JPEG',
        extension: 'jpg',
      },
      dataSize: 6734,
      height: 120,
      width: 120,
      hash: 'd44864dc6c5164724d8cfc3742f44f277ef0264b2eb99cff8783f6c88dd8eaa4',
      externalId: null,
      createdAt: '2022-11-18T03:46:16.000+00:00',
      updatedAt: '2022-11-18T03:46:16.000+00:00',
    },
    bannerImage: {
      id: 'dbf23d24-172e-4725-8da1-aaa2e1485655',
      url: 'https://mynftdisplay.s3.ap-northeast-1.amazonaws.com/data/uploads/dbf2/3d24/dbf23d24-172e-4725-8da1-aaa2e1485655.png',
      storageName: 's3',
      mimeType: 'image/png',
      format: {
        id: 2,
        mime: 'image/png',
        name: 'png',
        label: 'PNG',
        extension: 'png',
      },
      dataSize: 151693,
      height: 400,
      width: 1400,
      hash: '93fa54a2b16bcecfbe8d56a0947d0ecba7e3e271b2e625dae4e20928cf039c33',
      externalId: null,
      createdAt: '2022-11-18T03:45:49.000+00:00',
      updatedAt: '2022-11-18T03:45:49.000+00:00',
    },
    category: {
      id: 'a952f3f6-1fb4-4e4f-904b-69112659ef91',
      slug: 'digitalArt',
      name: 'Digital Art',
      thumbnail: null,
      background: null,
      description: 'Digital Art NFTs',
      order: 1,
      createdAt: '2022-11-18T04:18:11.000+00:00',
      updatedAt: '2022-11-18T04:18:11.000+00:00',
    },
    webUrl: 'https://opensea.io/collection/osashimichan',
    discordUrl: null,
    telegramUrl: null,
    mediumUsername: null,
    twitterUsername: 'ssm_a_u',
    instagramUsername: null,
    isExplicit: 0,
    owner: {
      id: '1859d57c-5e89-4df3-b7a0-c20bf59c0812',
      username: '0x43D65b69949D10061b23708aC6Be268897F6A16f',
      email: null,
      thumbnail: null,
      background: null,
      description: null,
      twitterUsername: null,
      instagramUsername: null,
      webUrl: null,
      lastAccessedAt: null,
      language: {
        id: 1,
        name: 'en',
        label: 'English',
      },
      publicAddresses: [],
      createdAt: '2022-11-18T04:16:34.000+00:00',
      updatedAt: '2022-11-18T04:16:34.000+00:00',
    },
    numAssets: 0,
    numOwners: 0,
    totalVolume: null,
    floorPrice: null,
    paymentTokens: [],
    payoutAddresses: [],
    collaborators: [],
    createdAt: '2022-11-18T04:18:21.000+00:00',
    updatedAt: '2022-11-18T04:18:21.000+00:00',
  },
  price: {
    amount: 0.32,
  },
  change: {
    price: '-1.2058 ETH',
    percentage: '-79.15%',
    type: 'decrease',
  },
  seller: {
    address: '0x719634',
  },
  buyer: {
    address: '0xE05211',
  },
  time: {
    datetime: '1 hour ago',
  },
}

export const bigWhalesBoughtTempData = generateData(100, bigWhalesBoughtRecord)

const whaleMintedRecord = {
  collection: {
    id: 'bc1ed928-3169-4caf-a0cb-df1a5ef6f290',
    slug: 'osashimichan',
    name: 'sashimi_lab_next',
    description: 'Yay~~~ Saikou desune',
    logoImage: {
      id: '27cb73e6-5c67-4fc1-be64-a7fc245cad4b',
      url: 'https://mynftdisplay.s3.ap-northeast-1.amazonaws.com/data/uploads/27cb/73e6/27cb73e6-5c67-4fc1-be64-a7fc245cad4b.jpg',
      storageName: 's3',
      mimeType: 'image/jpeg',
      format: {
        id: 1,
        mime: 'image/jpeg',
        name: 'jpeg',
        label: 'JPEG',
        extension: 'jpg',
      },
      dataSize: 6734,
      height: 120,
      width: 120,
      hash: 'd44864dc6c5164724d8cfc3742f44f277ef0264b2eb99cff8783f6c88dd8eaa4',
      externalId: null,
      createdAt: '2022-11-18T03:46:16.000+00:00',
      updatedAt: '2022-11-18T03:46:16.000+00:00',
    },
    featuredImage: {
      id: '27cb73e6-5c67-4fc1-be64-a7fc245cad4b',
      url: 'https://mynftdisplay.s3.ap-northeast-1.amazonaws.com/data/uploads/27cb/73e6/27cb73e6-5c67-4fc1-be64-a7fc245cad4b.jpg',
      storageName: 's3',
      mimeType: 'image/jpeg',
      format: {
        id: 1,
        mime: 'image/jpeg',
        name: 'jpeg',
        label: 'JPEG',
        extension: 'jpg',
      },
      dataSize: 6734,
      height: 120,
      width: 120,
      hash: 'd44864dc6c5164724d8cfc3742f44f277ef0264b2eb99cff8783f6c88dd8eaa4',
      externalId: null,
      createdAt: '2022-11-18T03:46:16.000+00:00',
      updatedAt: '2022-11-18T03:46:16.000+00:00',
    },
    bannerImage: {
      id: 'dbf23d24-172e-4725-8da1-aaa2e1485655',
      url: 'https://mynftdisplay.s3.ap-northeast-1.amazonaws.com/data/uploads/dbf2/3d24/dbf23d24-172e-4725-8da1-aaa2e1485655.png',
      storageName: 's3',
      mimeType: 'image/png',
      format: {
        id: 2,
        mime: 'image/png',
        name: 'png',
        label: 'PNG',
        extension: 'png',
      },
      dataSize: 151693,
      height: 400,
      width: 1400,
      hash: '93fa54a2b16bcecfbe8d56a0947d0ecba7e3e271b2e625dae4e20928cf039c33',
      externalId: null,
      createdAt: '2022-11-18T03:45:49.000+00:00',
      updatedAt: '2022-11-18T03:45:49.000+00:00',
    },
    category: {
      id: 'a952f3f6-1fb4-4e4f-904b-69112659ef91',
      slug: 'digitalArt',
      name: 'Digital Art',
      thumbnail: null,
      background: null,
      description: 'Digital Art NFTs',
      order: 1,
      createdAt: '2022-11-18T04:18:11.000+00:00',
      updatedAt: '2022-11-18T04:18:11.000+00:00',
    },
    webUrl: 'https://opensea.io/collection/osashimichan',
    discordUrl: null,
    telegramUrl: null,
    mediumUsername: null,
    twitterUsername: 'ssm_a_u',
    instagramUsername: null,
    isExplicit: 0,
    owner: {
      id: '1859d57c-5e89-4df3-b7a0-c20bf59c0812',
      username: '0x43D65b69949D10061b23708aC6Be268897F6A16f',
      email: null,
      thumbnail: null,
      background: null,
      description: null,
      twitterUsername: null,
      instagramUsername: null,
      webUrl: null,
      lastAccessedAt: null,
      language: {
        id: 1,
        name: 'en',
        label: 'English',
      },
      publicAddresses: [],
      createdAt: '2022-11-18T04:16:34.000+00:00',
      updatedAt: '2022-11-18T04:16:34.000+00:00',
    },
    numAssets: 0,
    numOwners: 0,
    totalVolume: null,
    floorPrice: null,
    paymentTokens: [],
    payoutAddresses: [],
    collaborators: [],
    createdAt: '2022-11-18T04:18:21.000+00:00',
    updatedAt: '2022-11-18T04:18:21.000+00:00',
  },
  whales: {
    amount: 5,
  },
  minted: {
    qty: 16,
  },
  mintVolume: {
    price: '34.88 ETH',
    percentage: '70%',
  },
  minters: {
    amount: 5,
  },
  totalGas: {
    price: '34.88 ETH',
    percentage: '70%',
  },
  firstMint: {
    date: 'Mar 12, 2022',
  },
  fomo: {
    level: 'low',
  },
}

export const whaleMintedTempData = generateData(100, whaleMintedRecord)

const whaleMintersRecord = {
  whale: '🐋 0x668E96757',
  minted: {
    amount: 157,
  },
  collection: {
    amount: 5,
  },
  mintVolume: {
    price: '25.8 ETH',
    percentage: '75%',
  },
  gasFee: {
    amount: '3.18',
  },
  lastMint: {
    datetime: '13 mins ago',
  },
}

export const whaleMintersTempData = generateData(100, whaleMintersRecord)

const activityAnalyticsRecord = {
  nft: {
    id: '180beb11-0383-4987-9715-229efc80b578',
    collection: {
      id: 'bc1ed928-3169-4caf-a0cb-df1a5ef6f290',
      slug: 'osashimichan',
      name: 'sashimi_lab_next',
      description: 'Yay~~~ Saikou desune',
      logoImage: {
        id: '27cb73e6-5c67-4fc1-be64-a7fc245cad4b',
        url: 'https://mynftdisplay.s3.ap-northeast-1.amazonaws.com/data/uploads/27cb/73e6/27cb73e6-5c67-4fc1-be64-a7fc245cad4b.jpg',
        storageName: 's3',
        mimeType: 'image/jpeg',
        format: {
          id: 1,
          mime: 'image/jpeg',
          name: 'jpeg',
          label: 'JPEG',
          extension: 'jpg',
        },
        dataSize: 6734,
        height: 120,
        width: 120,
        hash: 'd44864dc6c5164724d8cfc3742f44f277ef0264b2eb99cff8783f6c88dd8eaa4',
        externalId: null,
        createdAt: '2022-11-18T03:46:16.000+00:00',
        updatedAt: '2022-11-18T03:46:16.000+00:00',
      },
      featuredImage: {
        id: '27cb73e6-5c67-4fc1-be64-a7fc245cad4b',
        url: 'https://mynftdisplay.s3.ap-northeast-1.amazonaws.com/data/uploads/27cb/73e6/27cb73e6-5c67-4fc1-be64-a7fc245cad4b.jpg',
        storageName: 's3',
        mimeType: 'image/jpeg',
        format: {
          id: 1,
          mime: 'image/jpeg',
          name: 'jpeg',
          label: 'JPEG',
          extension: 'jpg',
        },
        dataSize: 6734,
        height: 120,
        width: 120,
        hash: 'd44864dc6c5164724d8cfc3742f44f277ef0264b2eb99cff8783f6c88dd8eaa4',
        externalId: null,
        createdAt: '2022-11-18T03:46:16.000+00:00',
        updatedAt: '2022-11-18T03:46:16.000+00:00',
      },
      bannerImage: {
        id: 'dbf23d24-172e-4725-8da1-aaa2e1485655',
        url: 'https://mynftdisplay.s3.ap-northeast-1.amazonaws.com/data/uploads/dbf2/3d24/dbf23d24-172e-4725-8da1-aaa2e1485655.png',
        storageName: 's3',
        mimeType: 'image/png',
        format: {
          id: 2,
          mime: 'image/png',
          name: 'png',
          label: 'PNG',
          extension: 'png',
        },
        dataSize: 151693,
        height: 400,
        width: 1400,
        hash: '93fa54a2b16bcecfbe8d56a0947d0ecba7e3e271b2e625dae4e20928cf039c33',
        externalId: null,
        createdAt: '2022-11-18T03:45:49.000+00:00',
        updatedAt: '2022-11-18T03:45:49.000+00:00',
      },
      category: {
        id: 'a952f3f6-1fb4-4e4f-904b-69112659ef91',
        slug: 'digitalArt',
        name: 'Digital Art',
        thumbnail: null,
        background: null,
        description: 'Digital Art NFTs',
        order: 1,
        createdAt: '2022-11-18T04:18:11.000+00:00',
        updatedAt: '2022-11-18T04:18:11.000+00:00',
      },
      webUrl: 'https://opensea.io/collection/osashimichan',
      discordUrl: null,
      telegramUrl: null,
      mediumUsername: null,
      twitterUsername: 'ssm_a_u',
      instagramUsername: null,
      isExplicit: 0,
      owner: {
        id: '1859d57c-5e89-4df3-b7a0-c20bf59c0812',
        username: '0x43D65b69949D10061b23708aC6Be268897F6A16f',
        email: null,
        thumbnail: null,
        background: null,
        description: null,
        twitterUsername: null,
        instagramUsername: null,
        webUrl: null,
        lastAccessedAt: null,
        language: {
          id: 1,
          name: 'en',
          label: 'English',
        },
        publicAddresses: [],
        createdAt: '2022-11-18T04:16:34.000+00:00',
        updatedAt: '2022-11-18T04:16:34.000+00:00',
      },
      numAssets: 0,
      numOwners: 0,
      totalVolume: null,
      floorPrice: null,
      paymentTokens: [],
      payoutAddresses: [],
      collaborators: [],
      createdAt: '2022-11-18T04:18:21.000+00:00',
      updatedAt: '2022-11-18T04:18:21.000+00:00',
    },
    contract: {
      chain: new Chain({name: 'ethereum'}),
    },
    name: 'KEMOMIMI GIRL#20987',
    thumbnail: {
      url: 'https://mynftdisplay.s3.ap-northeast-1.amazonaws.com/data/uploads/bd39/7faa/bd397faa-9d2e-4ddf-ae40-22be5f140666.jpg',
      mimeType: 'image/jpeg',
    },
    price: 1.25,
    paymentToken: {
      id: 1,
      name: 'eth',
      label: 'ETH',
      chainName: 'ethereum',
    },
  },
  action: {
    icon: 'cart1',
    name: 'Sale',
  },
  price: {
    coinPrice: '80 ETH',
    usdPrice: '$197,779.31',
  },
  from: {
    address: '0x269616',
  },
  to: {
    address: 'punksotc.eth',
  },
  gas: {
    amount: '0.005',
  },
  date: {
    datetime: 'Feb 25, 2022',
  },
}

export const activityAnalyticsTempData = generateData(100, activityAnalyticsRecord)

export const whaleTrendTempData1 = [
  {
    time: '2019-03',
    value: 350,
    type: 'Mint',
  },
  {
    time: '2019-04',
    value: 900,
    type: 'Mint',
  },
  {
    time: '2019-05',
    value: 300,
    type: 'Mint',
  },
  {
    time: '2019-06',
    value: 450,
    type: 'Mint',
  },
  {
    time: '2019-07',
    value: 470,
    type: 'Mint',
  },
  {
    time: '2019-08',
    value: 350,
    type: 'Mint',
  },
  {
    time: '2019-09',
    value: 900,
    type: 'Mint',
  },
  {
    time: '2019-10',
    value: 300,
    type: 'Mint',
  },
  {
    time: '2019-11',
    value: 450,
    type: 'Mint',
  },
  {
    time: '2019-12',
    value: 470,
    type: 'Mint',
  },
  {
    time: '2019-13',
    value: 350,
    type: 'Mint',
  },
  {
    time: '2019-14',
    value: 900,
    type: 'Mint',
  },
  {
    time: '2019-15',
    value: 300,
    type: 'Mint',
  },
  {
    time: '2019-16',
    value: 450,
    type: 'Mint',
  },
  {
    time: '2019-17',
    value: 470,
    type: 'Mint',
  },
  {
    time: '2019-18',
    value: 350,
    type: 'Mint',
  },
  {
    time: '2019-19',
    value: 900,
    type: 'Mint',
  },
  {
    time: '2019-20',
    value: 300,
    type: 'Mint',
  },
  {
    time: '2019-21',
    value: 450,
    type: 'Mint',
  },
  {
    time: '2019-22',
    value: 470,
    type: 'Mint',
  },


  {
    time: '2019-03',
    value: 350,
    type: 'Buy',
  },
  {
    time: '2019-04',
    value: 500,
    type: 'Buy',
  },
  {
    time: '2019-05',
    value: 300,
    type: 'Buy',
  },
  {
    time: '2019-06',
    value: 450,
    type: 'Buy',
  },
  {
    time: '2019-07',
    value: 470,
    type: 'Buy',
  },
  {
    time: '2019-08',
    value: 350,
    type: 'Buy',
  },
  {
    time: '2019-09',
    value: 480,
    type: 'Buy',
  },
  {
    time: '2019-10',
    value: 300,
    type: 'Buy',
  },
  {
    time: '2019-11',
    value: 450,
    type: 'Buy',
  },
  {
    time: '2019-12',
    value: 470,
    type: 'Buy',
  },
  {
    time: '2019-13',
    value: 350,
    type: 'Buy',
  },
  {
    time: '2019-14',
    value: 900,
    type: 'Buy',
  },
  {
    time: '2019-15',
    value: 300,
    type: 'Buy',
  },
  {
    time: '2019-16',
    value: 450,
    type: 'Buy',
  },
  {
    time: '2019-17',
    value: 470,
    type: 'Buy',
  },
  {
    time: '2019-18',
    value: 350,
    type: 'Buy',
  },
  {
    time: '2019-19',
    value: 900,
    type: 'Buy',
  },
  {
    time: '2019-20',
    value: 300,
    type: 'Buy',
  },
  {
    time: '2019-21',
    value: 450,
    type: 'Buy',
  },
  {
    time: '2019-22',
    value: 470,
    type: 'Buy',
  },

  {
    time: '2019-03',
    value: 350,
    type: 'Sell',
  },
  {
    time: '2019-04',
    value: 500,
    type: 'Sell',
  },
  {
    time: '2019-05',
    value: 300,
    type: 'Sell',
  },
  {
    time: '2019-06',
    value: 450,
    type: 'Sell',
  },
  {
    time: '2019-07',
    value: 470,
    type: 'Sell',
  },
  {
    time: '2019-08',
    value: 350,
    type: 'Sell',
  },
  {
    time: '2019-09',
    value: 400,
    type: 'Sell',
  },
  {
    time: '2019-10',
    value: 300,
    type: 'Sell',
  },
  {
    time: '2019-11',
    value: 450,
    type: 'Sell',
  },
  {
    time: '2019-12',
    value: 470,
    type: 'Sell',
  },
  {
    time: '2019-13',
    value: 350,
    type: 'Sell',
  },
  {
    time: '2019-14',
    value: 500,
    type: 'Sell',
  },
  {
    time: '2019-15',
    value: 300,
    type: 'Sell',
  },
  {
    time: '2019-16',
    value: 450,
    type: 'Sell',
  },
  {
    time: '2019-17',
    value: 470,
    type: 'Sell',
  },
  {
    time: '2019-18',
    value: 350,
    type: 'Sell',
  },
  {
    time: '2019-19',
    value: 900,
    type: 'Sell',
  },
  {
    time: '2019-20',
    value: 300,
    type: 'Sell',
  },
  {
    time: '2019-21',
    value: 450,
    type: 'Sell',
  },
  {
    time: '2019-22',
    value: 470,
    type: 'Sell',
  },

  {
    time: '2019-03',
    value: 350,
    type: 'Burn',
  },
  {
    time: '2019-04',
    value: 400,
    type: 'Burn',
  },
  {
    time: '2019-05',
    value: 300,
    type: 'Burn',
  },
  {
    time: '2019-06',
    value: 450,
    type: 'Burn',
  },
  {
    time: '2019-07',
    value: 470,
    type: 'Burn',
  },
  {
    time: '2019-08',
    value: 350,
    type: 'Burn',
  },
  {
    time: '2019-09',
    value: 600,
    type: 'Burn',
  },
  {
    time: '2019-10',
    value: 300,
    type: 'Burn',
  },
  {
    time: '2019-11',
    value: 450,
    type: 'Burn',
  },
  {
    time: '2019-12',
    value: 470,
    type: 'Burn',
  },
  {
    time: '2019-13',
    value: 350,
    type: 'Burn',
  },
  {
    time: '2019-14',
    value: 400,
    type: 'Burn',
  },
  {
    time: '2019-15',
    value: 300,
    type: 'Burn',
  },
  {
    time: '2019-16',
    value: 450,
    type: 'Burn',
  },
  {
    time: '2019-17',
    value: 470,
    type: 'Burn',
  },
  {
    time: '2019-18',
    value: 350,
    type: 'Burn',
  },
  {
    time: '2019-19',
    value: 700,
    type: 'Burn',
  },
  {
    time: '2019-20',
    value: 300,
    type: 'Burn',
  },
  {
    time: '2019-21',
    value: 450,
    type: 'Burn',
  },
  {
    time: '2019-22',
    value: 470,
    type: 'Burn',
  },

]

export const whaleTrendTempData2 = [
  {
    time: '2019-03',
    whaleActive: 500,
  },
  {
    time: '2019-04',
    whaleActive: 500,
  },
  {
    time: '2019-05',
    whaleActive: 400,
  },
  {
    time: '2019-06',
    whaleActive: 380,
  },
  {
    time: '2019-07',
    whaleActive: 350,
  },
  {
    time: '2019-08',
    whaleActive: 500,
  },
  {
    time: '2019-09',
    whaleActive: 530,
  },
  {
    time: '2019-10',
    whaleActive: 400,
  },
  {
    time: '2019-11',
    whaleActive: 380,
  },
  {
    time: '2019-12',
    whaleActive: 450,
  },
  {
    time: '2019-13',
    whaleActive: 500,
  },
  {
    time: '2019-14',
    whaleActive: 620,
  },
  {
    time: '2019-15',
    whaleActive: 400,
  },
  {
    time: '2019-16',
    whaleActive: 380,
  },
  {
    time: '2019-17',
    whaleActive: 420,
  },
  {
    time: '2019-18',
    whaleActive: 500,
  },
  {
    time: '2019-19',
    whaleActive: 480,
  },
  {
    time: '2019-20',
    whaleActive: 400,
  },
  {
    time: '2019-21',
    whaleActive: 380,
  },
  {
    time: '2019-22',
    whaleActive: 350,
  },
]

export const legendColumnChartSubData = [
  {
    amount: '4.36K',
    percent: 40.10,
  },
  {
    amount: '3.54K',
    percent: 4.45,
  },
  {
    amount: '2.78K',
    percent: 28.24,
  },
  {
    amount: '4.58K',
    percent: -85.11,
  },
]

const topBuyerRecord = {
  address: {
    // imageSrc: '/asset/images/character-bird.png',
    address: '🐋 dadykabslabye',
  },
  buyvolume: {
    price: '$1,976.77',
    percentage: '25%',
  },
  bought: {
    count: 1,
  },
  collections: {
    count: 1,
  },
}

export const topBuyerData = generateData(100, topBuyerRecord)

const topSellerRecord = {
  address: {
    // imageSrc: '/asset/images/character-bird.png',
    address: '🐋 dadykabslabye',
  },
  sellvolume: {
    price: '$1,976.77',
    percentage: '25%',
  },
  sold: {
    count: 2,
  },
  collections: {
    count: 1,
  },
}

export const topSellerData = generateData(100, topSellerRecord)

export const whaleTableRecord = {
  whale: {
    address: '🐋 0x668E96757',
  },
  mostHolding: {
    id: 'bc1ed928-3169-4caf-a0cb-df1a5ef6f290',
    slug: 'osashimichan',
    name: 'sashimi_lab_next',
    description: 'Yay~~~ Saikou desune',
    logoImage: {
      id: '27cb73e6-5c67-4fc1-be64-a7fc245cad4b',
      url: 'https://mynftdisplay.s3.ap-northeast-1.amazonaws.com/data/uploads/27cb/73e6/27cb73e6-5c67-4fc1-be64-a7fc245cad4b.jpg',
      storageName: 's3',
      mimeType: 'image/jpeg',
      format: {
        id: 1,
        mime: 'image/jpeg',
        name: 'jpeg',
        label: 'JPEG',
        extension: 'jpg',
      },
      dataSize: 6734,
      height: 120,
      width: 120,
      hash: 'd44864dc6c5164724d8cfc3742f44f277ef0264b2eb99cff8783f6c88dd8eaa4',
      externalId: null,
      createdAt: '2022-11-18T03:46:16.000+00:00',
      updatedAt: '2022-11-18T03:46:16.000+00:00',
    },
    featuredImage: {
      id: '27cb73e6-5c67-4fc1-be64-a7fc245cad4b',
      url: 'https://mynftdisplay.s3.ap-northeast-1.amazonaws.com/data/uploads/27cb/73e6/27cb73e6-5c67-4fc1-be64-a7fc245cad4b.jpg',
      storageName: 's3',
      mimeType: 'image/jpeg',
      format: {
        id: 1,
        mime: 'image/jpeg',
        name: 'jpeg',
        label: 'JPEG',
        extension: 'jpg',
      },
      dataSize: 6734,
      height: 120,
      width: 120,
      hash: 'd44864dc6c5164724d8cfc3742f44f277ef0264b2eb99cff8783f6c88dd8eaa4',
      externalId: null,
      createdAt: '2022-11-18T03:46:16.000+00:00',
      updatedAt: '2022-11-18T03:46:16.000+00:00',
    },
    bannerImage: {
      id: 'dbf23d24-172e-4725-8da1-aaa2e1485655',
      url: 'https://mynftdisplay.s3.ap-northeast-1.amazonaws.com/data/uploads/dbf2/3d24/dbf23d24-172e-4725-8da1-aaa2e1485655.png',
      storageName: 's3',
      mimeType: 'image/png',
      format: {
        id: 2,
        mime: 'image/png',
        name: 'png',
        label: 'PNG',
        extension: 'png',
      },
      dataSize: 151693,
      height: 400,
      width: 1400,
      hash: '93fa54a2b16bcecfbe8d56a0947d0ecba7e3e271b2e625dae4e20928cf039c33',
      externalId: null,
      createdAt: '2022-11-18T03:45:49.000+00:00',
      updatedAt: '2022-11-18T03:45:49.000+00:00',
    },
    category: {
      id: 'a952f3f6-1fb4-4e4f-904b-69112659ef91',
      slug: 'digitalArt',
      name: 'Digital Art',
      thumbnail: null,
      background: null,
      description: 'Digital Art NFTs',
      order: 1,
      createdAt: '2022-11-18T04:18:11.000+00:00',
      updatedAt: '2022-11-18T04:18:11.000+00:00',
    },
    webUrl: 'https://opensea.io/collection/osashimichan',
    discordUrl: null,
    telegramUrl: null,
    mediumUsername: null,
    twitterUsername: 'ssm_a_u',
    instagramUsername: null,
    isExplicit: 0,
    owner: {
      id: '1859d57c-5e89-4df3-b7a0-c20bf59c0812',
      username: '0x43D65b69949D10061b23708aC6Be268897F6A16f',
      email: null,
      thumbnail: null,
      background: null,
      description: null,
      twitterUsername: null,
      instagramUsername: null,
      webUrl: null,
      lastAccessedAt: null,
      language: {
        id: 1,
        name: 'en',
        label: 'English',
      },
      publicAddresses: [],
      createdAt: '2022-11-18T04:16:34.000+00:00',
      updatedAt: '2022-11-18T04:16:34.000+00:00',
    },
    numAssets: 0,
    numOwners: 0,
    totalVolume: null,
    floorPrice: null,
    paymentTokens: [],
    payoutAddresses: [],
    collaborators: [],
    createdAt: '2022-11-18T04:18:21.000+00:00',
    updatedAt: '2022-11-18T04:18:21.000+00:00',
  },
  holdingValue: {
    price: '$593,240,439.08',
    percentage: '80%',
  },
  estHv: {
    price: '$593,240,439.08',
    percentage: '80%',
  },
  pnl: {
    price: '$593,240,439.08',
    percentage: '80%',
  },
  buyVolume: {
    price: '$593,240,439.08',
    percentage: '80%',
  },
  sellVolume: {
    price: '$593,240,439.08',
    percentage: '80%',
  },
  collections: {
    amount: 523,
  },
  nfts: {
    amount: 19161,
  },
  activities: {
    amount: 19161,
  },
  lastDeal: {
    datetime: 'Jan 26, 2022',
  },
}

export const whaleTempData = generateData(100, whaleTableRecord)


