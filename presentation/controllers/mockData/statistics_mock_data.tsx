export const statisticsTrends = [
  {
    Date: '2010-01',
    scales: 1998,
  },
  {
    Date: '2010-02',
    scales: 1850,
  },
  {
    Date: '2010-03',
    scales: 1720,
  },
  {
    Date: '2010-04',
    scales: 1818,
  },
  {
    Date: '2010-05',
    scales: 1920,
  },
  {
    Date: '2010-06',
    scales: 1802,
  },
  {
    Date: '2010-07',
    scales: 1945,
  },
  {
    Date: '2010-08',
    scales: 1856,
  },
  {
    Date: '2010-09',
    scales: 2107,
  },
  {
    Date: '2010-10',
    scales: 2140,
  },
  {
    Date: '2010-11',
    scales: 2311,
  },
  {
    Date: '2010-12',
    scales: 1972,
  },
  {
    Date: '2011-01',
    scales: 1760,
  },
  {
    Date: '2011-02',
    scales: 1824,
  },
  {
    Date: '2011-03',
    scales: 1801,
  },
  {
    Date: '2011-04',
    scales: 2001,
  },
  {
    Date: '2011-05',
    scales: 1640,
  },
  {
    Date: '2011-06',
    scales: 1502,
  },
  {
    Date: '2011-07',
    scales: 1621,
  },
  {
    Date: '2011-08',
    scales: 1480,
  },
  {
    Date: '2011-09',
    scales: 1549,
  },
  {
    Date: '2011-10',
    scales: 1390,
  },
  {
    Date: '2011-11',
    scales: 1325,
  },
  {
    Date: '2011-12',
    scales: 1250,
  },
  {
    Date: '2012-01',
    scales: 1394,
  },
  {
    Date: '2012-02',
    scales: 1406,
  },
  {
    Date: '2012-03',
    scales: 1578,
  },
  {
    Date: '2012-04',
    scales: 1465,
  },
  {
    Date: '2012-05',
    scales: 1689,
  },
  {
    Date: '2012-06',
    scales: 1755,
  },
  {
    Date: '2012-07',
    scales: 1495,
  },
  {
    Date: '2012-08',
    scales: 1508,
  },
  {
    Date: '2012-09',
    scales: 1433,
  },
  {
    Date: '2012-10',
    scales: 1344,
  },
  {
    Date: '2012-11',
    scales: 1201,
  },
  {
    Date: '2012-12',
    scales: 1065,
  },
  {
    Date: '2013-01',
    scales: 1255,
  },
  {
    Date: '2013-02',
    scales: 1429,
  },
  {
    Date: '2013-03',
    scales: 1398,
  },
  {
    Date: '2013-04',
    scales: 1678,
  },
  {
    Date: '2013-05',
    scales: 1524,
  },
  {
    Date: '2013-06',
    scales: 1688,
  },
  {
    Date: '2013-07',
    scales: 1500,
  },
  {
    Date: '2013-08',
    scales: 1670,
  },
  {
    Date: '2013-09',
    scales: 1734,
  },
  {
    Date: '2013-10',
    scales: 1699,
  },
  {
    Date: '2013-11',
    scales: 1508,
  },
  {
    Date: '2013-12',
    scales: 1680,
  },
  {
    Date: '2014-01',
    scales: 1750,
  },
  {
    Date: '2014-02',
    scales: 1602,
  },
  {
    Date: '2014-03',
    scales: 1834,
  },
  {
    Date: '2014-04',
    scales: 1722,
  },
  {
    Date: '2014-05',
    scales: 1430,
  },
  {
    Date: '2014-06',
    scales: 1280,
  },
  {
    Date: '2014-07',
    scales: 1367,
  },
  {
    Date: '2014-08',
    scales: 1155,
  },
  {
    Date: '2014-09',
    scales: 1289,
  },
  {
    Date: '2014-10',
    scales: 1104,
  },
  {
    Date: '2014-11',
    scales: 1246,
  },
  {
    Date: '2014-12',
    scales: 1098,
  },
  {
    Date: '2015-01',
    scales: 1189,
  },
  {
    Date: '2015-02',
    scales: 1276,
  },
  {
    Date: '2015-03',
    scales: 1033,
  },
  {
    Date: '2015-04',
    scales: 956,
  },
  {
    Date: '2015-05',
    scales: 845,
  },
  {
    Date: '2015-06',
    scales: 1089,
  },
  {
    Date: '2015-07',
    scales: 944,
  },
  {
    Date: '2015-08',
    scales: 1043,
  },
  {
    Date: '2015-09',
    scales: 893,
  },
  {
    Date: '2015-10',
    scales: 840,
  },
  {
    Date: '2015-11',
    scales: 934,
  },
  {
    Date: '2015-12',
    scales: 810,
  },
  {
    Date: '2016-01',
    scales: 782,
  },
  {
    Date: '2016-02',
    scales: 1089,
  },
  {
    Date: '2016-03',
    scales: 745,
  },
  {
    Date: '2016-04',
    scales: 680,
  },
  {
    Date: '2016-05',
    scales: 802,
  },
  {
    Date: '2016-06',
    scales: 697,
  },
  {
    Date: '2016-07',
    scales: 583,
  },
  {
    Date: '2016-08',
    scales: 456,
  },
  {
    Date: '2016-09',
    scales: 524,
  },
  {
    Date: '2016-10',
    scales: 398,
  },
  {
    Date: '2016-11',
    scales: 278,
  },
  {
    Date: '2016-12',
    scales: 195,
  },
  {
    Date: '2017-01',
    scales: 145,
  },
  {
    Date: '2017-02',
    scales: 207,
  },
]

export const filterDays = [
  {
    name: '24H',
    value: '24h',
  },
  {
    name: '7D',
    value: '7d',
  },
  {
    name: '30D',
    value: '30d',
  },
  {
    name: 'All',
    value: 'all',
  },
]

export const filterTrends = [
  {
    name: 'Market Cap',
    value: 'marketCap',
  },
  {
    name: 'Volume',
    value: 'volume',
  },
  {
    name: 'Holders',
    value: 'holders',
  },
]

export const statisticsTraders = [
  {
    type: 'All',
    date: 1965,
    value: 1390.5,
  },
  {
    type: 'All',
    date: 1966,
    value: 1469.5,
  },
  {
    type: 'All',
    date: 1967,
    value: 1521.7,
  },
  {
    type: 'All',
    date: 1968,
    value: 1615.9,
  },
  {
    type: 'All',
    date: 1969,
    value: 1703.7,
  },
  {
    type: 'All',
    date: 1970,
    value: 1767.8,
  },
  {
    type: 'All',
    date: 1971,
    value: 1806.2,
  },
  {
    type: 'All',
    date: 1972,
    value: 1903.5,
  },
  {
    type: 'All',
    date: 1973,
    value: 1986.6,
  },
  {
    type: 'All',
    date: 1974,
    value: 1952,
  },
  {
    type: 'All',
    date: 1975,
    value: 1910.4,
  },
  {
    type: 'All',
    date: 1976,
    value: 2015.8,
  },
  {
    type: 'All',
    date: 1977,
    value: 2074.7,
  },
  {
    type: 'All',
    date: 1978,
    value: 2092.7,
  },
  {
    type: 'All',
    date: 1979,
    value: 2123.8,
  },
  {
    type: 'All',
    date: 1980,
    value: 2068.3,
  },
  {
    type: 'All',
    date: 1981,
    value: 2018,
  },
  {
    type: 'All',
    date: 1982,
    value: 1951.5,
  },
  {
    type: 'All',
    date: 1983,
    value: 1941.1,
  },
  {
    type: 'All',
    date: 1984,
    value: 2046.2,
  },
  {
    type: 'All',
    date: 1985,
    value: 2053.1,
  },
  {
    type: 'All',
    date: 1986,
    value: 2060.7,
  },
  {
    type: 'All',
    date: 1987,
    value: 2130.8,
  },
  {
    type: 'All',
    date: 1988,
    value: 2223.5,
  },
  {
    type: 'All',
    date: 1989,
    value: 2275.9,
  },
  {
    type: 'All',
    date: 1990,
    value: 2280.7,
  },
  {
    type: 'All',
    date: 1991,
    value: 2282,
  },
  {
    type: 'All',
    date: 1992,
    value: 2319.7,
  },
  {
    type: 'All',
    date: 1993,
    value: 2366.6,
  },
  {
    type: 'All',
    date: 1994,
    value: 2420.2,
  },
  {
    type: 'All',
    date: 1995,
    value: 2466.9,
  },
  {
    type: 'All',
    date: 1996,
    value: 2547.4,
  },
  {
    type: 'All',
    date: 1997,
    value: 2569,
  },
  {
    type: 'All',
    date: 1998,
    value: 2585.2,
  },
  {
    type: 'All',
    date: 1999,
    value: 2633.8,
  },
  {
    type: 'All',
    date: 2000,
    value: 2699.4,
  },
  {
    type: 'All',
    date: 2001,
    value: 2640.1,
  },
  {
    type: 'All',
    date: 2002,
    value: 2687.7,
  },
  {
    type: 'All',
    date: 2003,
    value: 2700.7,
  },
  {
    type: 'All',
    date: 2004,
    value: 2759.4,
  },
  {
    type: 'All',
    date: 2005,
    value: 2775.6,
  },
  {
    type: 'All',
    date: 2006,
    value: 2761.9,
  },
  {
    type: 'All',
    date: 2007,
    value: 2809.5,
  },
  {
    type: 'All',
    date: 2008,
    value: 2759.4,
  },
  {
    type: 'All',
    date: 2009,
    value: 2632.5,
  },
  {
    type: 'All',
    date: 2010,
    value: 2720.7,
  },
  {
    type: 'All',
    date: 2011,
    value: 2722.9,
  },
  {
    type: 'All',
    date: 2012,
    value: 2665.1,
  },
  {
    type: 'All',
    date: 2013,
    value: 2738.3,
  },
  {
    type: 'All',
    date: 2014,
    value: 2766.8,
  },
  {
    type: 'All',
    date: 2015,
    value: 2739.7,
  },
  {
    type: 'All',
    date: 2016,
    value: 2761.9,
  },
  {
    type: 'All',
    date: 2017,
    value: 2772.8,
  },
  {
    type: 'Seller',
    date: 1965,
    value: 109.2,
  },
  {
    type: 'Seller',
    date: 1966,
    value: 115.7,
  },
  {
    type: 'Seller',
    date: 1967,
    value: 120.5,
  },
  {
    type: 'Seller',
    date: 1968,
    value: 128,
  },
  {
    type: 'Seller',
    date: 1969,
    value: 134.4,
  },
  {
    type: 'Seller',
    date: 1970,
    value: 142.2,
  },
  {
    type: 'Seller',
    date: 1971,
    value: 157.5,
  },
  {
    type: 'Seller',
    date: 1972,
    value: 169.5,
  },
  {
    type: 'Seller',
    date: 1973,
    value: 186.3,
  },
  {
    type: 'Seller',
    date: 1974,
    value: 195.5,
  },
  {
    type: 'Seller',
    date: 1975,
    value: 198,
  },
  {
    type: 'Seller',
    date: 1976,
    value: 211.7,
  },
  {
    type: 'Seller',
    date: 1977,
    value: 223.8,
  },
  {
    type: 'Seller',
    date: 1978,
    value: 236.5,
  },
  {
    type: 'Seller',
    date: 1979,
    value: 251.8,
  },
  {
    type: 'Seller',
    date: 1980,
    value: 262.9,
  },
  {
    type: 'Seller',
    date: 1981,
    value: 262.7,
  },
  {
    type: 'Seller',
    date: 1982,
    value: 265.9,
  },
  {
    type: 'Seller',
    date: 1983,
    value: 268.3,
  },
  {
    type: 'Seller',
    date: 1984,
    value: 278.3,
  },
  {
    type: 'Seller',
    date: 1985,
    value: 285.2,
  },
  {
    type: 'Seller',
    date: 1986,
    value: 304.2,
  },
  {
    type: 'Seller',
    date: 1987,
    value: 315.4,
  },
  {
    type: 'Seller',
    date: 1988,
    value: 324.6,
  },
  {
    type: 'Seller',
    date: 1989,
    value: 329.9,
  },
  {
    type: 'Seller',
    date: 1990,
    value: 331.1,
  },
  {
    type: 'Seller',
    date: 1991,
    value: 339.7,
  },
  {
    type: 'Seller',
    date: 1992,
    value: 355.8,
  },
  {
    type: 'Seller',
    date: 1993,
    value: 368.8,
  },
  {
    type: 'Seller',
    date: 1994,
    value: 390.9,
  },
  {
    type: 'Seller',
    date: 1995,
    value: 408.3,
  },
  {
    type: 'Seller',
    date: 1996,
    value: 425.8,
  },
  {
    type: 'Seller',
    date: 1997,
    value: 448.2,
  },
  {
    type: 'Seller',
    date: 1998,
    value: 465.5,
  },
  {
    type: 'Seller',
    date: 1999,
    value: 463.7,
  },
  {
    type: 'Seller',
    date: 2000,
    value: 476.1,
  },
  {
    type: 'Seller',
    date: 2001,
    value: 477.7,
  },
  {
    type: 'Seller',
    date: 2002,
    value: 483.5,
  },
  {
    type: 'Seller',
    date: 2003,
    value: 489.3,
  },
  {
    type: 'Seller',
    date: 2004,
    value: 515.5,
  },
  {
    type: 'Seller',
    date: 2005,
    value: 533.6,
  },
  {
    type: 'Seller',
    date: 2006,
    value: 564,
  },
  {
    type: 'Seller',
    date: 2007,
    value: 587,
  },
  {
    type: 'Seller',
    date: 2008,
    value: 605.8,
  },
  {
    type: 'Seller',
    date: 2009,
    value: 596.8,
  },
  {
    type: 'Seller',
    date: 2010,
    value: 632.5,
  },
  {
    type: 'Seller',
    date: 2011,
    value: 658.9,
  },
  {
    type: 'Seller',
    date: 2012,
    value: 676.5,
  },
  {
    type: 'Seller',
    date: 2013,
    value: 692,
  },
  {
    type: 'Seller',
    date: 2014,
    value: 697.7,
  },
  {
    type: 'Seller',
    date: 2015,
    value: 701.1,
  },
  {
    type: 'Seller',
    date: 2016,
    value: 696.8,
  },
  {
    type: 'Seller',
    date: 2017,
    value: 700.6,
  },
  {
    type: 'Buyer',
    date: 1965,
    value: 1058.1,
  },
  {
    type: 'Buyer',
    date: 1966,
    value: 1089.7,
  },
  {
    type: 'Buyer',
    date: 1967,
    value: 1121.7,
  },
  {
    type: 'Buyer',
    date: 1968,
    value: 1196.6,
  },
  {
    type: 'Buyer',
    date: 1969,
    value: 1285.5,
  },
  {
    type: 'Buyer',
    date: 1970,
    value: 1369,
  },
  {
    type: 'Buyer',
    date: 1971,
    value: 1406.2,
  },
  {
    type: 'Buyer',
    date: 1972,
    value: 1472.7,
  },
  {
    type: 'Buyer',
    date: 1973,
    value: 1558,
  },
  {
    type: 'Buyer',
    date: 1974,
    value: 1535.5,
  },
  {
    type: 'Buyer',
    date: 1975,
    value: 1519.3,
  },
  {
    type: 'Buyer',
    date: 1976,
    value: 1606.9,
  },
  {
    type: 'Buyer',
    date: 1977,
    value: 1632.4,
  },
  {
    type: 'Buyer',
    date: 1978,
    value: 1687.5,
  },
  {
    type: 'Buyer',
    date: 1979,
    value: 1749.6,
  },
  {
    type: 'Buyer',
    date: 1980,
    value: 1706.4,
  },
  {
    type: 'Buyer',
    date: 1981,
    value: 1661.4,
  },
  {
    type: 'Buyer',
    date: 1982,
    value: 1630.2,
  },
  {
    type: 'Buyer',
    date: 1983,
    value: 1645.2,
  },
  {
    type: 'Buyer',
    date: 1984,
    value: 1686.9,
  },
  {
    type: 'Buyer',
    date: 1985,
    value: 1779.4,
  },
  {
    type: 'Buyer',
    date: 1986,
    value: 1811.3,
  },
  {
    type: 'Buyer',
    date: 1987,
    value: 1849.7,
  },
  {
    type: 'Buyer',
    date: 1988,
    value: 1870,
  },
  {
    type: 'Buyer',
    date: 1989,
    value: 1875,
  },
  {
    type: 'Buyer',
    date: 1990,
    value: 1853.3,
  },
  {
    type: 'Buyer',
    date: 1991,
    value: 1844.6,
  },
  {
    type: 'Buyer',
    date: 1992,
    value: 1814.1,
  },
  {
    type: 'Buyer',
    date: 1993,
    value: 1805.3,
  },
  {
    type: 'Buyer',
    date: 1994,
    value: 1791.3,
  },
  {
    type: 'Buyer',
    date: 1995,
    value: 1836.2,
  },
  {
    type: 'Buyer',
    date: 1996,
    value: 1896.1,
  },
  {
    type: 'Buyer',
    date: 1997,
    value: 1896.4,
  },
  {
    type: 'Buyer',
    date: 1998,
    value: 1918.8,
  },
  {
    type: 'Buyer',
    date: 1999,
    value: 1907.7,
  },
  {
    type: 'Buyer',
    date: 2000,
    value: 1932.1,
  },
  {
    type: 'Buyer',
    date: 2001,
    value: 1959.2,
  },
  {
    type: 'Buyer',
    date: 2002,
    value: 1954.8,
  },
  {
    type: 'Buyer',
    date: 2003,
    value: 1991.6,
  },
  {
    type: 'Buyer',
    date: 2004,
    value: 2025.4,
  },
  {
    type: 'Buyer',
    date: 2005,
    value: 2037.4,
  },
  {
    type: 'Buyer',
    date: 2006,
    value: 2056.4,
  },
  {
    type: 'Buyer',
    date: 2007,
    value: 2041.7,
  },
  {
    type: 'Buyer',
    date: 2008,
    value: 2038.5,
  },
  {
    type: 'Buyer',
    date: 2009,
    value: 1932.1,
  },
  {
    type: 'Buyer',
    date: 2010,
    value: 2001.1,
  },
  {
    type: 'Buyer',
    date: 2011,
    value: 1949.1,
  },
  {
    type: 'Buyer',
    date: 2012,
    value: 1944.3,
  },
  {
    type: 'Buyer',
    date: 2013,
    value: 1934,
  },
  {
    type: 'Buyer',
    date: 2014,
    value: 1871.2,
  },
  {
    type: 'Buyer',
    date: 2015,
    value: 1908.7,
  },
  {
    type: 'Buyer',
    date: 2016,
    value: 1934.6,
  },
  {
    type: 'Buyer',
    date: 2017,
    value: 1969.5,
  },
]

export const statisticsCategoryLiquidity = [
  {
    type: 'Art',
    sales: 38,
  },
  {
    type: 'Ultility',
    sales: 52,
  },
  {
    type: 'Collectibles',
    sales: 61,
  },
  {
    type: 'Social',
    sales: 145,
  },
  {
    type: 'Sports',
    sales: 48,
  },
  {
    type: 'Land',
    sales: 38,
  },
  {
    type: 'Defi',
    sales: 26,
  },
  {
    type: 'Game',
    sales: 22,
  },
  {
    type: 'Pfp',
    sales: 70,
  },
  {
    type: 'Metaverse',
    sales: 67,
  },
  {
    type: 'IP',
    sales: 40,
  },
  {
    type: 'Photography',
    sales: 10,
  },
  {
    type: 'Music',
    sales: 30,
  },
  {
    type: 'Domain',
    sales: 1,
  },
]

export const statisticsCategoryShare = [
  {
    type: 'PFP',
    value: 54.2,
  },
  {
    type: 'Collectibles',
    value: 14.6,
  },
  {
    type: 'Art',
    value: 8.92,
  },
  {
    type: 'Game',
    value: 8.35,
  },
  {
    type: 'Utility',
    value: 5.01,
  },
  {
    type: 'Metaverse',
    value: 2.86,
  },
  {
    type: 'Land',
    value: 2.19,
  },
  {
    type: 'IP',
    value: 1.39,
  },
  {
    type: 'Social',
    value: 1.38,
  },
  {
    type: 'Music',
    value: 0.44,
  },
]

export const filterCollectionCategory = [
  {
    name: 'Collection',
    value: 'collection',
  },
  {
    name: 'Category',
    value: 'category',
  },
]

export const filterValueVolume = [
  {
    name: 'Value',
    value: 'value',
  },
  {
    name: 'Volume',
    value: 'volume',
  },
]

export const statisticCards = [
  {
    totalValue: '1,908,334',
    percentage: '+0.2%',
    type: 'increase',
    field: 'Holders',
    description: 'Description',
  },
  {
    totalValue: '1,099',
    percentage: '',
    type: '',
    field: '🐋 Whales',
    description: 'Description',
    textColor: 'text-primary-2',
  },
  {
    totalValue: '1,873',
    percentage: '',
    type: '',
    field: 'Collections',
    description: 'Description',
  },
  {
    totalValue: '22,524,305',
    percentage: '',
    type: '',
    field: 'NFTs',
    description: 'Description',
  },
]

export const statisticCardsRight = [
  {
    totalValue: '46,953',
    percentage: '-3.40%',
    type: 'decrease',
    field: 'Traders(24H)',
    description: 'Description',
  },
  {
    totalValue: '27,935',
    percentage: '-3.03%',
    type: 'decrease',
    field: 'Buyers(24H)',
    description: 'Description',
  },
  {
    totalValue: '27,017',
    percentage: '-1.65%',
    type: 'decrease',
    field: 'Sellers(24H)',
    description: 'Description',
  },
  {
    totalValue: '86,306',
    percentage: '-11.31%',
    type: 'decrease',
    field: 'Transfers(24H)',
    description: 'Description',
  },
  {
    totalValue: '48,475',
    percentage: '-0.01%',
    type: 'decrease',
    field: 'Sales(24H)',
    description: 'Description',
  },
]
