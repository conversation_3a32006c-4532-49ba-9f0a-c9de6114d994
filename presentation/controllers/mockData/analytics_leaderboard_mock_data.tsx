import { generateData } from 'lib/utils'

const leaderBoardTableRecord = {
  address: {
    // imageSrc: '/asset/images/character-bird.png',
    address: '🐋 dadykabslabye',
  },
  pnl: {
    price: '$8,574,264.63',
    percentage: '80%',
  },
  buyvolume: {
    price: '$1,336,284.21',
    percentage: '30%',
  },
  sellvolume: {
    price: '$28,104,926.50',
    percentage: '20%',
  },
  sent: {
    count: 38.791,
  },
  received: {
    count: 38.791,
  },
  minted: {
    count: 38.791,
  },
}

export const leaderBoardTableData = generateData(120, leaderBoardTableRecord)

const topBuyerRecord = {
  address: {
    // imageSrc: '/asset/images/character-bird.png',
    address: '🐋 dadykabslabye',
  },
  buyvolume: {
    price: '$1,976.77',
    percentage: '25%',
  },
  bought: {
    count: 1,
  },
}

export const topBuyerData = generateData(100, topBuyerRecord)

const topSellerRecord = {
  address: {
    // imageSrc: '/asset/images/character-bird.png',
    address: '🐋 dadykabslabye',
  },
  sellvolume: {
    price: '$1,976.77',
    percentage: '25%',
  },
  sold: {
    count: 2,
  },
}

export const topSellerData = generateData(100, topSellerRecord)
