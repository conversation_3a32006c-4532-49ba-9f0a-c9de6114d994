import { generateData } from 'lib/utils'

export const mintTableRecord = {
  collection: {
    id: 'bc1ed928-3169-4caf-a0cb-df1a5ef6f290',
    slug: 'o<PERSON><PERSON><PERSON><PERSON>',
    name: 'sa<PERSON><PERSON>_lab_next',
    description: 'Ya<PERSON>~~~ <PERSON><PERSON><PERSON> desune',
    logoImage: {
      id: '27cb73e6-5c67-4fc1-be64-a7fc245cad4b',
      url: 'https://mynftdisplay.s3.ap-northeast-1.amazonaws.com/data/uploads/27cb/73e6/27cb73e6-5c67-4fc1-be64-a7fc245cad4b.jpg',
      storageName: 's3',
      mimeType: 'image/jpeg',
      format: {
        id: 1,
        mime: 'image/jpeg',
        name: 'jpeg',
        label: 'JPEG',
        extension: 'jpg',
      },
      dataSize: 6734,
      height: 120,
      width: 120,
      hash: 'd44864dc6c5164724d8cfc3742f44f277ef0264b2eb99cff8783f6c88dd8eaa4',
      externalId: null,
      createdAt: '2022-11-18T03:46:16.000+00:00',
      updatedAt: '2022-11-18T03:46:16.000+00:00',
    },
    featuredImage: {
      id: '27cb73e6-5c67-4fc1-be64-a7fc245cad4b',
      url: 'https://mynftdisplay.s3.ap-northeast-1.amazonaws.com/data/uploads/27cb/73e6/27cb73e6-5c67-4fc1-be64-a7fc245cad4b.jpg',
      storageName: 's3',
      mimeType: 'image/jpeg',
      format: {
        id: 1,
        mime: 'image/jpeg',
        name: 'jpeg',
        label: 'JPEG',
        extension: 'jpg',
      },
      dataSize: 6734,
      height: 120,
      width: 120,
      hash: 'd44864dc6c5164724d8cfc3742f44f277ef0264b2eb99cff8783f6c88dd8eaa4',
      externalId: null,
      createdAt: '2022-11-18T03:46:16.000+00:00',
      updatedAt: '2022-11-18T03:46:16.000+00:00',
    },
    bannerImage: {
      id: 'dbf23d24-172e-4725-8da1-aaa2e1485655',
      url: 'https://mynftdisplay.s3.ap-northeast-1.amazonaws.com/data/uploads/dbf2/3d24/dbf23d24-172e-4725-8da1-aaa2e1485655.png',
      storageName: 's3',
      mimeType: 'image/png',
      format: {
        id: 2,
        mime: 'image/png',
        name: 'png',
        label: 'PNG',
        extension: 'png',
      },
      dataSize: 151693,
      height: 400,
      width: 1400,
      hash: '93fa54a2b16bcecfbe8d56a0947d0ecba7e3e271b2e625dae4e20928cf039c33',
      externalId: null,
      createdAt: '2022-11-18T03:45:49.000+00:00',
      updatedAt: '2022-11-18T03:45:49.000+00:00',
    },
    category: {
      id: 'a952f3f6-1fb4-4e4f-904b-69112659ef91',
      slug: 'digitalArt',
      name: 'Digital Art',
      thumbnail: null,
      background: null,
      description: 'Digital Art NFTs',
      order: 1,
      createdAt: '2022-11-18T04:18:11.000+00:00',
      updatedAt: '2022-11-18T04:18:11.000+00:00',
    },
    webUrl: 'https://opensea.io/collection/osashimichan',
    discordUrl: null,
    telegramUrl: null,
    mediumUsername: null,
    twitterUsername: 'ssm_a_u',
    instagramUsername: null,
    isExplicit: 0,
    owner: {
      id: '1859d57c-5e89-4df3-b7a0-c20bf59c0812',
      username: '0x43D65b69949D10061b23708aC6Be268897F6A16f',
      email: null,
      thumbnail: null,
      background: null,
      description: null,
      twitterUsername: null,
      instagramUsername: null,
      webUrl: null,
      lastAccessedAt: null,
      language: {
        id: 1,
        name: 'en',
        label: 'English',
      },
      publicAddresses: [],
      createdAt: '2022-11-18T04:16:34.000+00:00',
      updatedAt: '2022-11-18T04:16:34.000+00:00',
    },
    numAssets: 0,
    numOwners: 0,
    totalVolume: null,
    floorPrice: null,
    paymentTokens: [],
    payoutAddresses: [],
    collaborators: [],
    createdAt: '2022-11-18T04:18:21.000+00:00',
    updatedAt: '2022-11-18T04:18:21.000+00:00',
  },
  minted: {
    count: 218,
  },
  mintVolume: {
    price: '0.357 ETH',
    percentage: '25%',
  },
  minters: {
    count: 26,
  },
  whales: {
    count: 0,
  },
  totalGas: {
    price: '8.5193 ETH',
    percentage: '25%',
  },
  firstMint: {
    datetime: '5 hours ago',
  },
  fomo: {
    level: 'high',
  },
}

export const mintTableData = generateData(100, mintTableRecord)
