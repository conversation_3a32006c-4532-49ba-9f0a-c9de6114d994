import Chain from 'domain/value_objects/chain'
import { generateData } from 'lib/utils'

const accountPnlRecord = {
  lastDate: {
    datetime: '2022’ 4.25',
  },
  nft: {
    id: '180beb11-0383-4987-9715-229efc80b578',
    collection: {
      name: 'sashi<PERSON>_lab_next',
    },
    contract: {
      chain: new Chain({name: 'ethereum'}),
    },
    name: 'KEMOMIMI GIRL#20987',
    thumbnail: {
      url: 'https://mynftdisplay.s3.ap-northeast-1.amazonaws.com/data/uploads/bd39/7faa/bd397faa-9d2e-4ddf-ae40-22be5f140666.jpg',
      mimeType: 'image/jpeg',
    },
  },
  tokenId: {
    tokenId: 3343,
  },
  buy: {
    price: '80 ETH',
    usdPrice: '$197,779.31',
  },
  sell: {
    price: '80 ETH',
    usdPrice: '$197,779.31',
  },
  difference: {
    price: '80 ETH',
    usdPrice: '$197,779.31',
  },
  floorPrice: {
    price: '80 ETH',
    usdPrice: '$197,779.31',
  },
  symbol: {
    symbol: 'WETH',
  },
}

export const accountPnlTempData = generateData(100, accountPnlRecord)

export const accountPnlPositions = {
  closed: {
    sent: 128.66,
    received: 272.73,
    gasFee: 0.13,
    realized: 288.00,
  },
  open: {
    sent: 33.00,
    valueFloor: 72.01,
    gasFee: 0.01,
    unrealized: 39.00,
  },
}

export const activityTrendTempData = [
  {
    time: '2019-03',
    value: 350,
    type: 'Mint',
  },
  {
    time: '2019-04',
    value: 900,
    type: 'Mint',
  },
  {
    time: '2019-05',
    value: 300,
    type: 'Mint',
  },
  {
    time: '2019-06',
    value: 450,
    type: 'Mint',
  },
  {
    time: '2019-07',
    value: 470,
    type: 'Mint',
  },
  {
    time: '2019-08',
    value: 350,
    type: 'Mint',
  },
  {
    time: '2019-09',
    value: 900,
    type: 'Mint',
  },
  {
    time: '2019-10',
    value: 300,
    type: 'Mint',
  },
  {
    time: '2019-11',
    value: 450,
    type: 'Mint',
  },
  {
    time: '2019-12',
    value: 470,
    type: 'Mint',
  },
  {
    time: '2019-13',
    value: 350,
    type: 'Mint',
  },
  {
    time: '2019-14',
    value: 900,
    type: 'Mint',
  },
  {
    time: '2019-15',
    value: 300,
    type: 'Mint',
  },
  {
    time: '2019-16',
    value: 450,
    type: 'Mint',
  },
  {
    time: '2019-17',
    value: 470,
    type: 'Mint',
  },
  {
    time: '2019-18',
    value: 350,
    type: 'Mint',
  },
  {
    time: '2019-19',
    value: 900,
    type: 'Mint',
  },
  {
    time: '2019-20',
    value: 300,
    type: 'Mint',
  },
  {
    time: '2019-21',
    value: 450,
    type: 'Mint',
  },
  {
    time: '2019-22',
    value: 470,
    type: 'Mint',
  },


  {
    time: '2019-03',
    value: 350,
    type: 'Buy',
  },
  {
    time: '2019-04',
    value: 500,
    type: 'Buy',
  },
  {
    time: '2019-05',
    value: 300,
    type: 'Buy',
  },
  {
    time: '2019-06',
    value: 450,
    type: 'Buy',
  },
  {
    time: '2019-07',
    value: 470,
    type: 'Buy',
  },
  {
    time: '2019-08',
    value: 350,
    type: 'Buy',
  },
  {
    time: '2019-09',
    value: 480,
    type: 'Buy',
  },
  {
    time: '2019-10',
    value: 300,
    type: 'Buy',
  },
  {
    time: '2019-11',
    value: 450,
    type: 'Buy',
  },
  {
    time: '2019-12',
    value: 470,
    type: 'Buy',
  },
  {
    time: '2019-13',
    value: 350,
    type: 'Buy',
  },
  {
    time: '2019-14',
    value: 900,
    type: 'Buy',
  },
  {
    time: '2019-15',
    value: 300,
    type: 'Buy',
  },
  {
    time: '2019-16',
    value: 450,
    type: 'Buy',
  },
  {
    time: '2019-17',
    value: 470,
    type: 'Buy',
  },
  {
    time: '2019-18',
    value: 350,
    type: 'Buy',
  },
  {
    time: '2019-19',
    value: 900,
    type: 'Buy',
  },
  {
    time: '2019-20',
    value: 300,
    type: 'Buy',
  },
  {
    time: '2019-21',
    value: 450,
    type: 'Buy',
  },
  {
    time: '2019-22',
    value: 470,
    type: 'Buy',
  },

  {
    time: '2019-03',
    value: 350,
    type: 'Sell',
  },
  {
    time: '2019-04',
    value: 500,
    type: 'Sell',
  },
  {
    time: '2019-05',
    value: 300,
    type: 'Sell',
  },
  {
    time: '2019-06',
    value: 450,
    type: 'Sell',
  },
  {
    time: '2019-07',
    value: 470,
    type: 'Sell',
  },
  {
    time: '2019-08',
    value: 350,
    type: 'Sell',
  },
  {
    time: '2019-09',
    value: 400,
    type: 'Sell',
  },
  {
    time: '2019-10',
    value: 300,
    type: 'Sell',
  },
  {
    time: '2019-11',
    value: 450,
    type: 'Sell',
  },
  {
    time: '2019-12',
    value: 470,
    type: 'Sell',
  },
  {
    time: '2019-13',
    value: 350,
    type: 'Sell',
  },
  {
    time: '2019-14',
    value: 500,
    type: 'Sell',
  },
  {
    time: '2019-15',
    value: 300,
    type: 'Sell',
  },
  {
    time: '2019-16',
    value: 450,
    type: 'Sell',
  },
  {
    time: '2019-17',
    value: 470,
    type: 'Sell',
  },
  {
    time: '2019-18',
    value: 350,
    type: 'Sell',
  },
  {
    time: '2019-19',
    value: 900,
    type: 'Sell',
  },
  {
    time: '2019-20',
    value: 300,
    type: 'Sell',
  },
  {
    time: '2019-21',
    value: 450,
    type: 'Sell',
  },
  {
    time: '2019-22',
    value: 470,
    type: 'Sell',
  },

  {
    time: '2019-03',
    value: 350,
    type: 'Burn',
  },
  {
    time: '2019-04',
    value: 400,
    type: 'Burn',
  },
  {
    time: '2019-05',
    value: 300,
    type: 'Burn',
  },
  {
    time: '2019-06',
    value: 450,
    type: 'Burn',
  },
  {
    time: '2019-07',
    value: 470,
    type: 'Burn',
  },
  {
    time: '2019-08',
    value: 350,
    type: 'Burn',
  },
  {
    time: '2019-09',
    value: 600,
    type: 'Burn',
  },
  {
    time: '2019-10',
    value: 300,
    type: 'Burn',
  },
  {
    time: '2019-11',
    value: 450,
    type: 'Burn',
  },
  {
    time: '2019-12',
    value: 470,
    type: 'Burn',
  },
  {
    time: '2019-13',
    value: 350,
    type: 'Burn',
  },
  {
    time: '2019-14',
    value: 400,
    type: 'Burn',
  },
  {
    time: '2019-15',
    value: 300,
    type: 'Burn',
  },
  {
    time: '2019-16',
    value: 450,
    type: 'Burn',
  },
  {
    time: '2019-17',
    value: 470,
    type: 'Burn',
  },
  {
    time: '2019-18',
    value: 350,
    type: 'Burn',
  },
  {
    time: '2019-19',
    value: 700,
    type: 'Burn',
  },
  {
    time: '2019-20',
    value: 300,
    type: 'Burn',
  },
  {
    time: '2019-21',
    value: 450,
    type: 'Burn',
  },
  {
    time: '2019-22',
    value: 470,
    type: 'Burn',
  },

  {
    time: '2019-03',
    value: 350,
    type: 'Send',
  },
  {
    time: '2019-04',
    value: 400,
    type: 'Send',
  },
  {
    time: '2019-05',
    value: 300,
    type: 'Send',
  },
  {
    time: '2019-06',
    value: 450,
    type: 'Send',
  },
  {
    time: '2019-07',
    value: 470,
    type: 'Send',
  },
  {
    time: '2019-08',
    value: 350,
    type: 'Send',
  },
  {
    time: '2019-09',
    value: 600,
    type: 'Send',
  },
  {
    time: '2019-10',
    value: 300,
    type: 'Send',
  },
  {
    time: '2019-11',
    value: 450,
    type: 'Send',
  },
  {
    time: '2019-12',
    value: 470,
    type: 'Send',
  },
  {
    time: '2019-13',
    value: 350,
    type: 'Send',
  },
  {
    time: '2019-14',
    value: 400,
    type: 'Send',
  },
  {
    time: '2019-15',
    value: 300,
    type: 'Send',
  },
  {
    time: '2019-16',
    value: 450,
    type: 'Send',
  },
  {
    time: '2019-17',
    value: 470,
    type: 'Send',
  },
  {
    time: '2019-18',
    value: 350,
    type: 'Send',
  },
  {
    time: '2019-19',
    value: 700,
    type: 'Send',
  },
  {
    time: '2019-20',
    value: 300,
    type: 'Send',
  },
  {
    time: '2019-21',
    value: 450,
    type: 'Send',
  },
  {
    time: '2019-22',
    value: 470,
    type: 'Send',
  },

  //
  {
    time: '2019-03',
    value: 350,
    type: 'Receive',
  },
  {
    time: '2019-04',
    value: 400,
    type: 'Receive',
  },
  {
    time: '2019-05',
    value: 300,
    type: 'Receive',
  },
  {
    time: '2019-06',
    value: 450,
    type: 'Receive',
  },
  {
    time: '2019-07',
    value: 470,
    type: 'Receive',
  },
  {
    time: '2019-08',
    value: 350,
    type: 'Receive',
  },
  {
    time: '2019-09',
    value: 600,
    type: 'Receive',
  },
  {
    time: '2019-10',
    value: 300,
    type: 'Receive',
  },
  {
    time: '2019-11',
    value: 450,
    type: 'Receive',
  },
  {
    time: '2019-12',
    value: 470,
    type: 'Receive',
  },
  {
    time: '2019-13',
    value: 350,
    type: 'Receive',
  },
  {
    time: '2019-14',
    value: 400,
    type: 'Receive',
  },
  {
    time: '2019-15',
    value: 300,
    type: 'Receive',
  },
  {
    time: '2019-16',
    value: 450,
    type: 'Receive',
  },
  {
    time: '2019-17',
    value: 470,
    type: 'Receive',
  },
  {
    time: '2019-18',
    value: 350,
    type: 'Receive',
  },
  {
    time: '2019-19',
    value: 700,
    type: 'Receive',
  },
  {
    time: '2019-20',
    value: 300,
    type: 'Receive',
  },
  {
    time: '2019-21',
    value: 450,
    type: 'Receive',
  },
  {
    time: '2019-22',
    value: 470,
    type: 'Receive',
  },
]

export const activityTrendLegendTempData: any = [
  {
    amount: 9,
    percent: -66.67,
  },
  {
    amount: 11,
    percent: -8.33,
  },
  {
    amount: 4,
    percent: -20,
  },
  {
    amount: 3,
    percent: 0,
  },
  {
    amount: 49,
    percent: -20.97,
  },
  {
    amount: 35,
    percent: 59.09,
  },
]

export const rarityDistributionTempData = [
  {
    year: '1951',
    value: 30,
  },
  {
    year: '1952',
    value: 15,
  },
  {
    year: '1956',
    value: 25,
  },
  {
    year: '1957',
    value: 100,
  },
  {
    year: '1958',
    value: 135,
  },
  {
    year: '1960',
    value: 140,
  },
  {
    year: '1962',
    value: 90,
  },
  {
    year: '1966',
    value: 61,
  },
  {
    year: '1967',
    value: 45,
  },
  {
    year: '1968',
    value: 8,
  },
]

const accountCollectionRecord = {
  collection: {
    id: 'bc1ed928-3169-4caf-a0cb-df1a5ef6f290',
    slug: 'osashimichan',
    name: 'sashimi_lab_next',
    description: 'Yay~~~ Saikou desune',
    logoImage: {
      id: '27cb73e6-5c67-4fc1-be64-a7fc245cad4b',
      url: 'https://mynftdisplay.s3.ap-northeast-1.amazonaws.com/data/uploads/27cb/73e6/27cb73e6-5c67-4fc1-be64-a7fc245cad4b.jpg',
      storageName: 's3',
      mimeType: 'image/jpeg',
      format: {
        id: 1,
        mime: 'image/jpeg',
        name: 'jpeg',
        label: 'JPEG',
        extension: 'jpg',
      },
      dataSize: 6734,
      height: 120,
      width: 120,
      hash: 'd44864dc6c5164724d8cfc3742f44f277ef0264b2eb99cff8783f6c88dd8eaa4',
      externalId: null,
      createdAt: '2022-11-18T03:46:16.000+00:00',
      updatedAt: '2022-11-18T03:46:16.000+00:00',
    },
    featuredImage: {
      id: '27cb73e6-5c67-4fc1-be64-a7fc245cad4b',
      url: 'https://mynftdisplay.s3.ap-northeast-1.amazonaws.com/data/uploads/27cb/73e6/27cb73e6-5c67-4fc1-be64-a7fc245cad4b.jpg',
      storageName: 's3',
      mimeType: 'image/jpeg',
      format: {
        id: 1,
        mime: 'image/jpeg',
        name: 'jpeg',
        label: 'JPEG',
        extension: 'jpg',
      },
      dataSize: 6734,
      height: 120,
      width: 120,
      hash: 'd44864dc6c5164724d8cfc3742f44f277ef0264b2eb99cff8783f6c88dd8eaa4',
      externalId: null,
      createdAt: '2022-11-18T03:46:16.000+00:00',
      updatedAt: '2022-11-18T03:46:16.000+00:00',
    },
    bannerImage: {
      id: 'dbf23d24-172e-4725-8da1-aaa2e1485655',
      url: 'https://mynftdisplay.s3.ap-northeast-1.amazonaws.com/data/uploads/dbf2/3d24/dbf23d24-172e-4725-8da1-aaa2e1485655.png',
      storageName: 's3',
      mimeType: 'image/png',
      format: {
        id: 2,
        mime: 'image/png',
        name: 'png',
        label: 'PNG',
        extension: 'png',
      },
      dataSize: 151693,
      height: 400,
      width: 1400,
      hash: '93fa54a2b16bcecfbe8d56a0947d0ecba7e3e271b2e625dae4e20928cf039c33',
      externalId: null,
      createdAt: '2022-11-18T03:45:49.000+00:00',
      updatedAt: '2022-11-18T03:45:49.000+00:00',
    },
    category: {
      id: 'a952f3f6-1fb4-4e4f-904b-69112659ef91',
      slug: 'digitalArt',
      name: 'Digital Art',
      thumbnail: null,
      background: null,
      description: 'Digital Art NFTs',
      order: 1,
      createdAt: '2022-11-18T04:18:11.000+00:00',
      updatedAt: '2022-11-18T04:18:11.000+00:00',
    },
    webUrl: 'https://opensea.io/collection/osashimichan',
    discordUrl: null,
    telegramUrl: null,
    mediumUsername: null,
    twitterUsername: 'ssm_a_u',
    instagramUsername: null,
    isExplicit: 0,
    owner: {
      id: '1859d57c-5e89-4df3-b7a0-c20bf59c0812',
      username: '0x43D65b69949D10061b23708aC6Be268897F6A16f',
      email: null,
      thumbnail: null,
      background: null,
      description: null,
      twitterUsername: null,
      instagramUsername: null,
      webUrl: null,
      lastAccessedAt: null,
      language: {
        id: 1,
        name: 'en',
        label: 'English',
      },
      publicAddresses: [],
      createdAt: '2022-11-18T04:16:34.000+00:00',
      updatedAt: '2022-11-18T04:16:34.000+00:00',
    },
    numAssets: 0,
    numOwners: 0,
    totalVolume: null,
    floorPrice: null,
    paymentTokens: [],
    payoutAddresses: [],
    collaborators: [],
    createdAt: '2022-11-18T04:18:21.000+00:00',
    updatedAt: '2022-11-18T04:18:21.000+00:00',
  },
  nfts: {
    amount: 187,
  },
  holdingValue: {
    price: '$593,240,439.08',
    percentage: '80%',
  },
  estHv: {
    price: '$593,240,439.08',
    percentage: '80%',
  },
  pnl: {
    price: '$593,240,439.08',
    percentage: '80%',
  },
  buyVolume: {
    price: '$593,240,439.08',
    percentage: '80%',
  },
  sellVolume: {
    price: '$593,240,439.08',
    percentage: '80%',
  },
  lastDeal: {
    datetime: 'Jan 26, 2022',
  },
}

export const accountCollectionTempData = generateData(120, accountCollectionRecord)

const relatedAddressRecord = {
  address: '📃 0xAbEffb',
  send: {
    count: 23,
  },
  receive: {
    count: 1,
  },
  lastTransfer: {
    datetime: 'Mar 19, 2022',
  },
}

export const relatedAddressTempData = generateData(15, relatedAddressRecord)

export const holdingTempData = [
  {
    type: 'dotdotdots',
    value: 20.02,
  },
  {
    type: 'Art Blocks',
    value: 11.95,
  },
  {
    type: 'JOYWORLD',
    value: 11.81,
  },
  {
    type: 'Fluf World',
    value: 8.73,
  },
  {
    type: 'BEEPLE',
    value: 6.43,
  },
  {
    type: 'RaidParty RaidParty',
    value: 5.14,
  },
  {
    type: 'More Loot',
    value: 31.04,
  },
  {
    type: 'others',
    value: 31.40,
  },
]

export const cumulativeTempData = [
  {
    year: '1991',
    value: 3,
    type: 'PnL',
  },
  {
    year: '1992',
    value: 7,
    type: 'PnL',
  },
  {
    year: '1993',
    value: 3.5,
    type: 'PnL',
  },
  {
    year: '1994',
    value: 5,
    type: 'PnL',
  },
  {
    year: '1995',
    value: 1.9,
    type: 'PnL',
  },
  {
    year: '1996',
    value: 11,
    type: 'PnL',
  },
  {
    year: '1997',
    value: 7,
    type: 'PnL',
  },
  {
    year: '1998',
    value: 9,
    type: 'PnL',
  },
  {
    year: '1999',
    value: 11,
    type: 'PnL',
  },
  {
    year: '1991',
    value: 4,
    type: 'Est Holding Value',
  },
  {
    year: '1992',
    value: 2,
    type: 'Est Holding Value',
  },
  {
    year: '1993',
    value: 3,
    type: 'Est Holding Value',
  },
  {
    year: '1994',
    value: 3.5,
    type: 'Est Holding Value',
  },
  {
    year: '1995',
    value: 4.2,
    type: 'Est Holding Value',
  },
  {
    year: '1996',
    value: 5,
    type: 'Est Holding Value',
  },
  {
    year: '1997',
    value: 5.5,
    type: 'Est Holding Value',
  },
  {
    year: '1998',
    value: 7,
    type: 'Est Holding Value',
  },
  {
    year: '1999',
    value: 9,
    type: 'Est Holding Value',
  },
  {
    year: '1991',
    value: 4,
    type: 'Holding Value',
  },
  {
    year: '1992',
    value: 10,
    type: 'Holding Value',
  },
  {
    year: '1993',
    value: 5,
    type: 'Holding Value',
  },
  {
    year: '1994',
    value: 5.5,
    type: 'Holding Value',
  },
  {
    year: '1995',
    value: 4.2,
    type: 'Holding Value',
  },
  {
    year: '1996',
    value: 7,
    type: 'Holding Value',
  },
  {
    year: '1997',
    value: 9.5,
    type: 'Holding Value',
  },
  {
    year: '1998',
    value: 9,
    type: 'Holding Value',
  },
  {
    year: '1999',
    value: 11,
    type: 'Holding Value',
  },
]

export const traderTempData = [
  {
    date: '03-14',
    value: 1,
    type: 'Buy',
  },
  {
    date: '03-15',
    value: 3,
    type: 'Buy',
  },
  {
    date: '03-16',
    value: 2.5,
    type: 'Buy',
  },
  {
    date: '03-17',
    value: 1.4,
    type: 'Buy',
  },
  {
    date: '03-18',
    value: 4,
    type: 'Buy',
  },
  {
    date: '03-19',
    value: 2.5,
    type: 'Buy',
  },
  {
    date: '03-20',
    value: 3,
    type: 'Buy',
  },
  {
    date: '03-21',
    value: 1.8,
    type: 'Buy',
  },
  {
    date: '03-14',
    value: 1.5,
    type: 'Sell',
  },
  {
    date: '03-15',
    value: 2.5,
    type: 'Sell',
  },
  {
    date: '03-16',
    value: 5,
    type: 'Sell',
  },
  {
    date: '03-17',
    value: 4,
    type: 'Sell',
  },
  {
    date: '03-18',
    value: 2.6,
    type: 'Sell',
  },
  {
    date: '03-19',
    value: 1.5,
    type: 'Sell',
  },
  {
    date: '03-20',
    value: 2,
    type: 'Sell',
  },
  {
    date: '03-21',
    value: 1.8,
    type: 'Sell',
  },
]

export const traderLegendTempData = [
  {
    amount: 11,
    percent: -8.33,
  },
  {
    amount: 4,
    percent: -20,
  },
]

export const relationalGraphLegendTempData = [
  {
    img: '/asset/images/whale-icon.png',
    label: 'Whale',
  },
  {
    img: '/asset/images/document-icon.png',
    label: 'Contract',
  },
  {
    img: '/asset/images/normal-contract-icon.png',
    label: 'Normal Address',
    tooltip: 'Normal Address',
  },
]

export const relationalGraphTempData = {
  id: 'Modeling Methods',
  children: [
    {
      id: 'Classification',
      children: [
        { id: 'Logistic regression', value: '', img: '/asset/images/document-icon.png' },
        { id: 'Linear discriminant analysis', value: '', img: '/asset/images/document-icon.png' },
        { id: 'Rules', value: '', img: '/asset/images/document-icon.png' },
        { id: 'Decision trees', value: '', img: '/asset/images/document-icon.png' },
        { id: 'Naive Bayes', value: '', img: '/asset/images/document-icon.png' },
        { id: 'K nearest neighbor', value: '', img: '/asset/images/document-icon.png' },
        { id: 'Probabilistic neural network', value: '', img: '/asset/images/document-icon.png' },
        { id: 'Support vector machine', value: '', img: '/asset/images/document-icon.png' },
      ],
      value: '',
      img: '/asset/images/whale-icon.png',
    },
  ],
  value: '',
  img: '/asset/images/normal-contract-icon.png',
}
