import { generateData } from 'lib/utils'

export const askDataTable = generateData(10, {
  size: 9,
  properties: {
    link: '',
    properties: '5074,4845,9716,5577,7575,8186,2563,7733,3745,1682,9282,3998,2712,3402,5213,9704,0250',
  },
  price: {
    price: 214,
  },
  token: {
    token: 'ETH',
  },
  action: {
    icon: 'chevronDownSelect',
    viewBox: '0 0 12.962 7.411',
    className: 'w-[13px] h-[8px] fill-primary-2 dark:fill-white stroke-none',
  },
})

export const forSaleDataTable = [
  {
    price: {
      price: 0.0897,
    },
    properties: {
      link: '',
      properties: 1234,
    },
    buy: {
      text: 'Buy Now',
    },
  },
  {
    price: {
      price: 0.1243,
    },
    properties: {
      link: '',
      properties: 1124,
    },
    buy: {
      text: 'Buy Now',
    },
  },
  {
    price: {
      price: 0.0897,
    },
    properties: {
      link: '',
      properties: 5734,
    },
    buy: {
      text: 'Buy Now',
    },
  },
  {
    price: {
      price: 2.0827,
    },
    properties: {
      link: '',
      properties: 4231,
    },
    buy: {
      text: 'Buy Now',
    },
  },
  {
    price: {
      price: 3.0897,
    },
    properties: {
      link: '',
      properties: 2334,
    },
    buy: {
      text: 'Buy Now',
    },
  },
  {
    price: {
      price: 1.1243,
    },
    properties: {
      link: '',
      properties: 1124,
    },
    buy: {
      text: 'Buy Now',
    },
  },
  {
    price: {
      price: 0.0297,
    },
    properties: {
      link: '',
      properties: 2234,
    },
    buy: {
      text: 'Buy Now',
    },
  },
  {
    price: {
      price: 0.0827,
    },
    properties: {
      link: '',
      properties: 4231,
    },
    buy: {
      text: 'Buy Now',
    },
  },
]
