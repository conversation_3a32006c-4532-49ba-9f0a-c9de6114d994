export const filterGacha = [
  {
    id: 1,
    label: 'Mystery Box',
    name: 'mystery<PERSON><PERSON>',
  },
]

export const filterRarityScore = [
  {
    name: 'Legendary',
    value: 'legendary',
    color: 'yellow',
  },
  {
    name: 'Rare',
    value: 'rare',
    color: 'purple',
  },
  {
    name: 'Classic',
    value: 'classic',
    color: 'blue',
  },
  {
    name: 'Normal',
    value: 'normal',
    color: 'gray',
  },
]

export const selectSearchOptions = [
  {
    title: 'Gender',
    name: 'Alien',
    percentage: '0.9%',
    value: 'alien',
  },
  {
    title: 'Gender 1',
    name: 'Alien',
    percentage: '0.9%',
    value: 'alien1',
  },
  {
    title: 'Gender 2',
    name: 'Alien',
    percentage: '0.9%',
    value: 'alien2',
  },
]

export const selectSearchAccount = [
  {
    title: '',
    name: 'NFT Namel',
    percentage: '',
    value: 'nftName',
  },
  {
    title: '',
    name: 'Collection Name',
    percentage: '',
    value: 'collectionName',
  },
]
export const mockTraitData = [
  {
    name: 'Background',
    values: [
      { value: 'M1 Yellow', count: 1921 },
      { value: 'M1 Purple', count: 1883 },
      { value: 'M1 Aquamarine', count: 1880 },
      { value: 'M1 Blue', count: 1543 },
      { value: 'M1 Red', count: 1201 },
      { value: 'M1 Green', count: 987 },
    ],
  },
  {
    name: 'Clothes',
    values: [
      { value: 'Hoodie', count: 2145 },
      { value: 'T-Shirt', count: 1832 },
      { value: 'Suit', count: 1654 },
      { value: 'Leather Jacket', count: 1231 },
      { value: 'Tank Top', count: 876 },
    ],
  },
  {
    name: 'Earring',
    values: [
      { value: 'M1 Silver Hoop', count: 1262 },
      { value: 'M1 Silver Stud', count: 1201 },
      { value: 'M1 Gold Cross', count: 986 },
      { value: 'M1 Diamond', count: 622 },
      { value: 'M1 None', count: 4329 },
    ],
  },
  {
    name: 'Eyes',
    values: [
      { value: 'Blue', count: 2567 },
      { value: 'Green', count: 2134 },
      { value: 'Brown', count: 1987 },
      { value: 'Black', count: 1654 },
      { value: 'Red', count: 876 },
    ],
  },
  {
    name: 'Fur',
    values: [
      { value: 'Brown', count: 3567 },
      { value: 'White', count: 2987 },
      { value: 'Black', count: 2543 },
      { value: 'Gray', count: 1876 },
      { value: 'Gold', count: 432 },
    ],
  },
  {
    name: 'Hat',
    values: [
      { value: 'Beanie', count: 1872 },
      { value: 'Cap', count: 1654 },
      { value: 'Cowboy', count: 1201 },
      { value: 'Crown', count: 876 },
      { value: 'None', count: 3421 },
    ],
  },
  {
    name: 'Mouth',
    values: [
      { value: 'Smile', count: 2543 },
      { value: 'Grin', count: 1876 },
      { value: 'Cigarette', count: 1332 },
      { value: 'Pipe', count: 987 },
      { value: 'Neutral', count: 1832 },
    ],
  },
] 