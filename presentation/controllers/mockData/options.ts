export const dateOptions = [
  {
    name: 'Last 7 days',
    value: 'last_7_days',
  },
  {
    name: 'Last 3 days',
    value: 'last_3_days',
  },
  {
    name: 'Last 2 days',
    value: 'last_2_days',
  },
  {
    name: 'Last 1 days',
    value: 'last_1_days',
  },
]

export const dayOptions = [
  {
    name: '7 days',
    value: '7days',
  },
  {
    name: '14 days',
    value: '14days',
  },
  {
    name: '21 days',
    value: '21days',
  },
  {
    name: '30 days',
    value: '30days',
  },
]

export const rowOptions = [
  {
    name: '20 rows',
    value: '20',
  },
  {
    name: '50 rows',
    value: '50',
  },
  {
    name: '100 rows',
    value: '100',
  },
]

export const blockChainOptions = [
  {
    name: 'Ethereum',
    value: 'eth',
    icon: '/asset/icons/ETH.svg',
  },
  {
    name: 'Wethereum',
    value: 'weth',
    icon: '/asset/icons/weth.png',
  },
  {
    name: 'Bitcoin',
    value: 'btc',
    icon: '/asset/icons/bitcoin.png',
  },
]

export const tokenOptions = [
  {
    name: 'ETH',
    value: 'eth',
    icon:'/asset/icons/ETH.svg',
  },
  {
    name: 'WETH',
    value: 'weth',
    icon:'/asset/icons/weth.png',
  },
  {
    name: 'DAI',
    value: 'dai',
    icon:'/asset/icons/dai.png',
  },
]

export const paymentFixedOptions = [
  {
    name: 'ETH',
    value: 'eth',
    icon:'/asset/icons/ETH.svg',
  },
  {
    name: 'WETH',
    value: 'weth',
    icon:'/asset/icons/weth.png',
  },
]

export const extendPaymentOptions = [
  {
    name: 'DAI',
    value: 'dai',
    icon:'/asset/icons/dai.png',
  },
]

export const allOptions = [
  {
    name: 'All',
    value: 'all',
  },
  {
    name: 'Buy',
    value: 'buy',
  },
  {
    name: 'Sell',
    value: 'sell',
  },
  {
    name: 'Last price',
    value: 'lastprice',
  },
]

export const hourOptions = [
  {
    name: '24H',
    value: '24h',
  },
  {
    name: '7D',
    value: '7d',
  },
  {
    name: '1D',
    value: '1d',
  },
]

export const chainOptions = [
  {
    name: 'All chains',
    value: 'all',
  },
  {
    name: 'Ethereum',
    value: 'ethereum',
  },
  {
    name: 'Polygon',
    value: 'polygon',
  },
  {
    name: 'Klaytn',
    value: 'klaytn',
  },
]

export const filterTimeOptions = [
  {
    name: '1H',
    value: '1h',
  },
  {
    name: '6H',
    value: '6h',
  },
  {
    name: '12H',
    value: '12h',
  },
  {
    name: '24H',
    value: '24h',
  },
  {
    name: '7D',
    value: '7d',
  },
  {
    name: '30D',
    value: '30d',
  },
]

export const filterMinHoursDay = [
  {
    name: '15Min',
    value: '15min',
  },
  {
    name: '30Min',
    value: '30min',
  },
  {
    name: '1H',
    value: '1h',
  },
  {
    name: '6H',
    value: '6h',
  },
  {
    name: '12H',
    value: '12h',
  },
  {
    name: '24H',
    value: '24h',
  },
  {
    name: '7D',
    value: '7d',
  },
  {
    name: '30D',
    value: '30d',
  },
]

export const filterMinHours = [
  {
    name: '15Min',
    value: '15min',
  },
  {
    name: '30Min',
    value: '30min',
  },
  {
    name: '1H',
    value: '1h',
  },
  {
    name: '6H',
    value: '6h',
  },
  {
    name: '12H',
    value: '12h',
  },
  {
    name: '24H',
    value: '24h',
  },
]

export const dateChartOptions = [
  {
    name: 'Last 90 days',
    value: 'last_90_days',
  },
  {
    name: 'Last 70 days',
    value: 'last_70_days',
  },
  {
    name: 'Last 45 days',
    value: 'last_45_days',
  },
  {
    name: 'Last 30 days',
    value: 'last_30_days',
  },
]

export const durationDateOptions = [
  {
    name: '7 days',
    value: 7,
  },
  {
    name: '14 days',
    value: 14,
  },
  {
    name: '21 days',
    value: 21,
  },
  {
    name: '30 days',
    value: 30,
  },
  {
    name: 'Custom date',
    value: 0,
  },
]

export const sortOptions = [
  {
    name: 'Sort By',
    value: '',
  },
  {
    name: 'Time',
    value: 'time',
  },
  {
    name: 'Price',
    value: 'price',
  },
]

export const filterWhaleHolders = [
  {
    name: 'Whales',
    value: 'whales',
  },
  {
    name: 'Top Holders',
    value: 'topholder',
  },
]

export const filterConcentration = [
  {
    name: 'Value',
    value: 'value',
  },
  {
    name: 'NFTs',
    value: 'nfts',
  },
]

export const filterDays = [
  {
    name: '24H',
    value: '24h',
  },
  {
    name: '7D',
    value: '7d',
  },
  {
    name: '30D',
    value: '30d',
  },
  {
    name: 'All',
    value: 'all',
  },
]

export const filterTrends = [
  {
    name: 'Market Cap',
    value: 'marketCap',
  },
  {
    name: 'Volume',
    value: 'volume',
  },
  {
    name: 'Holders',
    value: 'holders',
  },
]

export const filterDay = [
  {
    name: '24H',
    value: '24h',
  },
  {
    name: '7D',
    value: '7d',
  },
  {
    name: '30D',
    value: '30d',
  },
]

export const filterWeek = [
  {
    name: '24H',
    value: '24h',
  },
  {
    name: '7D',
    value: '7d',
  },
  {
    name: '30D',
    value: '30d',
  },
  {
    name: '30M',
    value: '30m',
  },
]


export const filterCollectionCat = [
  {
    name: 'Collection',
    value: 'collection',
  },
  {
    name: 'Category',
    value: 'category',
  },
]

export const filterCollectionCat2 = [
  {
    image: '/asset/images/<EMAIL>',
    name: 'Alex Smith',
    value: '1',
  },
  {
    image: '/asset/images/<EMAIL>',
    name: 'Alex Smith 2',
    value: '2',
  },
]

export const filterValueVolume = [
  {
    name: 'Value',
    value: 'value',
  },
  {
    name: 'Volume',
    value: 'volume',
  },
]

export const filterBuySell = [
  {
    name: 'Buy',
    value: 'buy',
  },
  {
    name: 'Sell',
    value: 'sell',
  },
]

export const filterHoldingPie = [
  {
    name: 'Est HV',
    value: 'estHv',
  },
  {
    name: 'Value',
    value: 'value',
  },
  {
    name: 'NFTs',
    value: 'nfts',
  },
]

export const adminFilterTime = [
  {
    name: '24H',
    value: '24d',
  },
  {
    name: '24H Ago',
    value: '24hago',
  },
  {
    name: '7D',
    value: '7d',
  },
  {
    name: '30D',
    value: '30d',
  },
]

export const filterGraph = [
  {
    name: 'Send',
    value: 'send',
  },
  {
    name: 'Received',
    value: 'received',
  },
]

export const dimensionOptions = [
  {
    name: 'Dimension 1',
    value: 'dimension1',
  },
  {
    name: 'Dimension 2',
    value: 'dimension2',
  },
  {
    name: 'Dimension 3',
    value: 'dimension3',
  },
]
