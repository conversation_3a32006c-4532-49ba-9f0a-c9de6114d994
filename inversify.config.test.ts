import { Container } from 'inversify'
import TYPES from 'types'

import StorageDatasource from 'data/datasources/interfaces/storage_datasource'

import ApiClient from 'data/datasources/remote/api_client'

import LocalStorageDatasource from 'data/datasources/local/local_storage_datasource'

const container = new Container()

// StorageDatasource
container.bind<StorageDatasource>(TYPES.StorageDatasource).to(LocalStorageDatasource)

// ApiClient
container.bind<ApiClient>(TYPES.ApiClient).toConstantValue(
  new ApiClient('https://internal-api-dev.nft-land.me')
)

export default container
