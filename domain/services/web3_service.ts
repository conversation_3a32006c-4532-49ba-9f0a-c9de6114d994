import detectEther<PERSON><PERSON>rovider from '@metamask/detect-provider'
import { MessageToSignService } from './utils/message_to_sign_service'
import { ethers } from 'ethers'
import Web3 from 'web3'

export default class Web3Service {
  private readonly ethereum: any = null
  private readonly provider: any = null

  constructor (ethereum: any) {
    this.ethereum = ethereum
    this.provider = new ethers.providers.Web3Provider(ethereum)
  }

  connect (): Promise<string> {
    return new Promise((resolve, reject) => {
      if (!this.ethereum) {
        reject(new Error('Failed to connect'))
      }
      let accounts: string[] = []
      try {
        this.ethereum
          .request({
            method: 'eth_requestAccounts',
          })
          .then((res: string[]) => {
            accounts = res
            resolve(accounts[0])
          })
      } catch (err) {
        this.ethereum
          .request({
            method: 'wallet_requestPermissions',
            // eslint-disable-next-line camelcase
            params: [{ eth_accounts: {} }],
          })
          .then(() => {
            this.ethereum
              .request({
                method: 'eth_requestAccounts',
              })
              .then((res: string[]) => {
                accounts = res
                resolve(accounts[0])
              })
          })
          .catch(() => {
            reject(new Error('Failed to connect'))
          })
      }
    })
  }

  sign (
    nonce: number,
    publicAddress: string,
    password: string
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      if (!this.ethereum) {
        reject(new Error('Failed to connect'))
      }
      try {
        const messageToSign =
          MessageToSignService.agreePrivacyPolicyAndTermsOfService(`${nonce}`)
        detectEthereumProvider().then((ethProvider) => {
          const web3 = new Web3(<any>ethProvider)
          web3.eth.personal
            .sign(messageToSign, publicAddress, password)
            .then((res: string) => {
              resolve(res)
            }).catch(() => {
              reject(new Error('User denied message signature.'))
            })
        })
      } catch (err) {
        reject(new Error('Failed to sign'))
      }
    })
  }

  logout (): Promise<boolean> {
    return new Promise((resolve, reject) => {
      if (!this.ethereum) {
        reject(new Error('Failed to connect'))
      }
      try {
        this.ethereum.removeListener('accountsChanged', () => {})
        resolve(true)
      } catch (err) {
        reject(new Error('Failed to logout'))
      }
    })
  }

  checkChainId (): Promise<number> {
    return new Promise((resolve, reject) => {
      if (!this.ethereum) {
        reject(new Error('Failed to connect'))
      }
      try {
        this.ethereum.request({ method: 'eth_chainId' }).then((res: string) => {
          resolve(parseInt(res, 16))
        })
      } catch (err) {
        reject(new Error('Failed to check chainId'))
      }
    })
  }
}
