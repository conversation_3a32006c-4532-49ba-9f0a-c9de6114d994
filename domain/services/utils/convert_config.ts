export default class ConvertConfig {
  
  static convertContractConfig (contracts: any) {
    return {
      domainRegistryAddress: contracts?.domainRegistryAddress,
      contractAddress: contracts?.marketplaceAddress,
      erc1155SeaDropConfigurerAddress: contracts?.erc1155SeaDropConfigurer,
      erc1155CloneFactoryAddress: contracts?.erc1155SeaDropCloneFactory,
      erc721CloneFactoryAddress: contracts?.erc721CloneFactoryAddress,
      nftPurchaseProxyAddress: contracts?.nftPurchaseProxy,
    }
  }
}