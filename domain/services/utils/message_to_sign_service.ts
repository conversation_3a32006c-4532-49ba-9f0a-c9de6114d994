import {envConfig} from 'presentation/constant/env'
export class MessageToSignService {

  static agreePrivacyPolicyAndTermsOfService (nonce: string) {
    return `Welcome to Quill!

Click to sign in and accept Quill's privacy policy and terms of service.
${envConfig.privacyPolicyUrl}
${envConfig.termsAndConditionsUrl}

This request will not trigger a blockchain transaction or cost any gas fees.

Nonce:
${nonce}`

  }
}