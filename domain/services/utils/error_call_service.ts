import { UserRejectedRequestError, UnknownRpcError } from 'viem'
import { ContractError } from 'errors/custom_datasource_errors'

export default class ErrorCallService {
  
  public isUserRejected (err: any): Boolean  {
    if (err instanceof UserRejectedRequestError) {
      return true
    }
    if (err instanceof UnknownRpcError) {
      // fallback for some wallets that don't follow EIP 1193, trust, safe
      if (err.details?.includes('cancel') || err.details?.includes('Transaction was rejected')) {
        return true
      }
    }

    // fallback for raw rpc error code
    if (err && typeof err === 'object') {
      if (
        ('code' in err && (err.code === 4001 || err.code === 'ACTION_REJECTED')) ||
      ('cause' in err && 'code' in err.cause && err.cause.code === 4001)
      ) {
        return true
      }

      if ('cause' in err) {
        return this.isUserRejected(err.cause)
      }
    }
    return false
  }

  public transactionError (error: any): any {
    if(this.isUserRejected(error)) {
      return new ContractError('Transaction was rejected', [])
    }
    if(error?.reason) {
      return new ContractError(error?.reason, [])
    }
    if(`${error}`?.indexOf('cannot estimate gas') > -1) {
      return new ContractError('Invalid params, cannot estimate gas.', [])
    }
    return new ContractError(error?.message || error, [])
  }
}