import { ethers } from 'ethers'
import { VENDING_MACHINE_CONFIG } from 'lib/vendingMachineConfig'

export interface VendingMachineContractParams {
  requestData: any
  signature: string
  chainId: number
  signer: ethers.Signer
}

export interface BuybackContractParams {
  tokenId: string
  contractAddress: string
  chainId: number
  signer: ethers.Signer
}

export class VendingMachineContractService {
  private getContractConfig (chainId: number) {
    const config = VENDING_MACHINE_CONFIG[chainId as keyof typeof VENDING_MACHINE_CONFIG]
    if (!config) {
      throw new Error(`Unsupported chain ID: ${chainId}`)
    }
    return config
  }

  async erc20Checkout (params: VendingMachineContractParams): Promise<ethers.ContractTransaction> {
    const { requestData, signature, chainId, signer } = params
    
    try {
      const config = this.getContractConfig(chainId)
      
      // Create contract instance
      const contract = new ethers.Contract(
        config.instantMintCheckoutAddress,
        config.instantMintCheckout<PERSON><PERSON>,
        signer
      )

      // Call the erc20Checkout function
      const tx = await contract.erc20Checkout(requestData, signature)
      
      return tx
    } catch (error) {
      console.error('Error calling erc20Checkout:', error)
      throw error
    }
  }

  async waitForTransaction (tx: ethers.ContractTransaction): Promise<ethers.ContractReceipt> {
    try {
      const receipt = await tx.wait()
      return receipt
    } catch (error) {
      console.error('Error waiting for transaction:', error)
      throw error
    }
  }

  getContractAddress (chainId: number): string {
    const config = this.getContractConfig(chainId)
    return config.instantMintCheckoutAddress
  }

  async checkAllowance (params: {
    tokenAddress: string
    ownerAddress: string
    spenderAddress: string
    signer: ethers.Signer
  }): Promise<ethers.BigNumber> {
    const { tokenAddress, ownerAddress, spenderAddress, signer } = params
    
    try {
      // ERC20 ABI for allowance function
      const erc20Abi = [
        'function allowance(address owner, address spender) view returns (uint256)',
      ]
      
      const tokenContract = new ethers.Contract(tokenAddress, erc20Abi, signer)
      const allowance = await tokenContract.allowance(ownerAddress, spenderAddress)
      
      return allowance
    } catch (error) {
      console.error('Error checking allowance:', error)
      throw error
    }
  }

  async approve (params: {
    tokenAddress: string
    spenderAddress: string
    amount: ethers.BigNumberish
    signer: ethers.Signer
  }): Promise<ethers.ContractTransaction> {
    const { tokenAddress, spenderAddress, amount, signer } = params
    
    try {
      // ERC20 ABI for approve function
      const erc20Abi = [
        'function approve(address spender, uint256 amount) returns (bool)',
      ]
      
      const tokenContract = new ethers.Contract(tokenAddress, erc20Abi, signer)
      const tx = await tokenContract.approve(spenderAddress, amount)
      
      return tx
    } catch (error) {
      console.error('Error approving token:', error)
      throw error
    }
  }

  async buybackNFT (params: BuybackContractParams): Promise<ethers.ContractTransaction> {
    const { tokenId, contractAddress, chainId, signer } = params
    
    try {
      const config = this.getContractConfig(chainId)
      
      // Create contract instance for buyback
      const contract = new ethers.Contract(
        config.instantMintCheckoutAddress,
        config.instantMintCheckoutAbi,
        signer
      )

      // Call the buyback function on smart contract
      // Assuming the contract has a buyback function that takes tokenId and contract address
      const tx = await contract.buyback(contractAddress, tokenId)
      
      return tx
    } catch (error) {
      console.error('Error calling buyback:', error)
      throw error
    }
  }
}

export default VendingMachineContractService