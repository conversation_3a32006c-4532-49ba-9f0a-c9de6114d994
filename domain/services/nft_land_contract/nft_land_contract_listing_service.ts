import { OrderWithCounter, 
  type ClaimNFTInput, 
  type RefuncForClaimNFTInput,
} from '@t2-web/nft_land_js/lib/types'
import { ContractTransaction, ethers, BigNumberish } from 'ethers'

import Listing from 'domain/models/listing'
import Error<PERSON>allService from 'domain/services/utils/error_call_service'
import NftLandContractAdaptor from '../../external_services/nft_land_contract_adaptor'
import CreateOrderInputConverter from './converters/create_order_input_converter'
import OrderComponentsConverter from './converters/order_component_converter'
import OrderWithCounterConverter from './converters/order_with_counter_converter'
import ContractAdaptorDatasource from 'data/datasources/interfaces/contract_adaptor_datasource'

export default class NftLandContractListingService {
  private nftLandAdaptor: NftLandContractAdaptor
  private errorCallService = new ErrorCallService()

  private createOrderInputConverter: CreateOrderInputConverter =
    new CreateOrderInputConverter()

  private orderComponentsConverter: OrderComponentsConverter =
    new OrderComponentsConverter()

  private orderWithCounterConverter: OrderWithCounterConverter =
    new OrderWithCounterConverter()

  constructor (contract : ContractAdaptorDatasource) {
    this.nftLandAdaptor = new NftLandContractAdaptor(contract)
  }

  public async create (
    listing: Listing,
    accountAddress: string,
    exactApproval?: boolean,
    salt?: string // this is a random number
  ): Promise<any> {
    try {
      const createOrderInput =
        this.createOrderInputConverter.listingToCreateOrderInput(
          listing,
          accountAddress,
          salt
        )
      const orderUseCase = await this.nftLandAdaptor.createOrder(
        createOrderInput,
        accountAddress,
        exactApproval
      )
      const excuActions = await orderUseCase.executeAllActions()
      const orderHash = await this.nftLandAdaptor.getOrderHash(excuActions?.parameters)
      return {
        orderHash: orderHash,
        ...excuActions,
      }
      
    } catch (error) {
      throw this.errorCallService.transactionError(error) 
    }
  }

  public async cancel (
    listing: Listing,
    accountAddress?: string,
    domain?: string
  ): Promise<ContractTransaction> {
    try {
      const orderParams =
        this.orderComponentsConverter.listingToOrderComponents(listing)
      const transactionMethods = this.nftLandAdaptor.cancelOrders(
        [orderParams],
        accountAddress,
        domain
      )
      return await transactionMethods.transact()
      
    } catch (error) {
      throw this.errorCallService.transactionError(error)
    }
  }

  public async fulfill (
    listing: Listing,
    unitsToFill: BigNumberish,
    accountAddress?: string,
    domain?: string,
  ): Promise<ContractTransaction> {
    try {
      const order =
        this.orderWithCounterConverter.listingToOrderWithCounter(listing)
      const orderUseCase = await this.nftLandAdaptor.fulfillOrder({
        order: order,
        accountAddress: accountAddress,
        domain: domain,
        unitsToFill: unitsToFill,
      })
      return await orderUseCase.executeAllActions()
    } catch (error) {
      throw this.errorCallService.transactionError(error)
    }
  }

  public async update (
    listing: Listing,
    accountAddress: string,
    exactApproval?: boolean,
    salt?: string,
    domain?: string
  ): Promise<OrderWithCounter> {
    try {
      await this.cancel(listing, accountAddress, domain)
      return await this.create(listing, accountAddress, exactApproval, salt)
    } catch (error) {
      throw this.errorCallService.transactionError(error) 
    }
  }

  public async claimNFT (
    input: ClaimNFTInput,
    accountAddress: string,
  ): Promise<ContractTransaction> {
    try {
      const transactionMethods = this.nftLandAdaptor.claimNFT(
        input,
        accountAddress,
      )
      const res = await transactionMethods.transact()
      if(!res || !res?.hash) {
        throw 'Submit transaction failed.'
      }
      return res
    } catch (error) {
      throw this.errorCallService.transactionError(error)
    }
  
  }
  
  public async refundForClaimNFT (
    input: RefuncForClaimNFTInput,
    accountAddress: string,
  ): Promise<ContractTransaction> {
    try {
      const transactionMethods = this.nftLandAdaptor.refundForClaimNFT(
        input,
        accountAddress,
      )
      const res = await transactionMethods.transact()
      if(!res || !res?.hash) {
        throw 'Submit transaction failed.'
      }
      return res
    } catch (error) {
      throw this.errorCallService.transactionError(error)
    }
  
  }
}
