import { ethers } from 'ethers'

import Collection from 'domain/models/collection'
import NftLandContractAdaptor from '../../external_services/nft_land_contract_adaptor'
import CreateCollectionInputConverter from './converters/create_collection_input_converter'
import ContractAdaptorDatasource from 'data/datasources/interfaces/contract_adaptor_datasource'

export default class NftLandContractCollectionService {
  private nftLandAdaptor: NftLandContractAdaptor

  private CreateCollectionInputConverter: CreateCollectionInputConverter =
    new CreateCollectionInputConverter()

  constructor (contract : ContractAdaptorDatasource) {
    this.nftLandAdaptor = new NftLandContractAdaptor(contract)
  }


  public async create (
    collection: Collection,
    accountAddress: string,
    tokenContractType: number,
    domain?: boolean,
    salt?: string // this is a random number
  ): Promise<ethers.ContractReceipt> {
    const createCollectionInput =
      this.CreateCollectionInputConverter.createCollectionInput(
        collection,
        accountAddress,
        tokenContractType
      )
    console.log('🚀 ~ NftLandContractCollectionService ~ createCollectionInput:', createCollectionInput)
    const {transact} = this.nftLandAdaptor.createCollection(
      createCollectionInput,
      accountAddress,
    )
    const tx = await transact()
    console.log('🚀 ~ NftLandContractCollectionService ~ tx:', tx)
    const receipt = await tx.wait()
    console.log('result: ', receipt)
    return receipt
  }


  // public async update (
  
  //   listing: Listing,
  //   accountAddress: string,
  //   exactApproval?: boolean,
  //   salt?: string,
  //   domain?: string
  // ): Promise<OrderWithCounter> {
  //   await this.cancel(listing, accountAddress, domain)
  //   return await this.create(listing, accountAddress, exactApproval, salt)
  // }
}
