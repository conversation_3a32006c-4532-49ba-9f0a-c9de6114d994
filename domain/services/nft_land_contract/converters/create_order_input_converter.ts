import { CreateOrderInput } from '@t2-web/nft_land_js/lib/types'

import Listing from 'domain/models/listing'
import CreateInputItemConverter from './create_input_item'
import Offer from 'domain/models/offer'

import { getDecimalAmount } from 'lib/number'

export default class CreateOrderInputConverter {
  private createInputItemConverter: CreateInputItemConverter =
    new CreateInputItemConverter()

  public offerToCreateOrderInput (
    offer: Offer,
    accountAddress: string,
    salt?: string | undefined
  ): CreateOrderInput {
    let amount = getDecimalAmount((offer?.price || 0) * offer.quantity, offer?.paymentToken?.decimals)
    const offerItems = [{
      token: offer?.paymentToken?.address || '',
      amount: amount.toFixed(0),
      endAmount: amount.toFixed(0),

    }]
    const fees = offer.fees?.map((i:any) => {
      let basisPoints = getDecimalAmount(Number(i.percent), 2).toFixed(0)
      return {
        recipient: i.address, 
        basisPoints: Number(basisPoints),
      }
    })

    const considerationsItems = [{
      ...this.createInputItemConverter.assetToCreateInputItem(
        offer.asset!,
        offer.quantity.toString()
      ),
      recipient: accountAddress,
    }]

    return {
      startTime: offer.createdAt?.toUnixInteger().toString(),
      endTime: offer.expiresAt?.toUnixInteger().toString(),
      offer: offerItems,
      consideration: considerationsItems,
      fees: fees,
      salt: salt,
      allowPartialFills: true,
    }
  }

  public listingToCreateOrderInput (
    listing: Listing,
    accountAddress: string,
    salt?: string | undefined
  ): CreateOrderInput {  
    const offer = listing.assets.map((asset, index) =>
      this.createInputItemConverter.assetToCreateInputItem(
        asset,
        listing.quantities[index].toString()
      )
    )
    let fees:any[] = []
    const quantities:number = listing.quantities?.reduce((acc, cur) => acc + Number(cur), 0)
    let amount = getDecimalAmount((listing?.price || 0) * quantities, listing?.paymentToken?.decimals)
    
    if(listing.listingType.isFixedPrice()) {
      // FixedPrice
      fees = listing.fees?.map((i:any) => {
        let basisPoints = getDecimalAmount(Number(i.percent), 2).toFixed(0)
        return {
          recipient: i.address, 
          basisPoints: Number(basisPoints),
        }
      })
    } else {
      // timedAuction
      if(listing.hasReservePrice && Number(listing?.reservePrice) > 0) {
        amount = getDecimalAmount((listing?.reservePrice || 0) * quantities, listing?.paymentToken?.decimals)
      }
      const feeTotal:number = listing.fees?.reduce((acc, cur) => acc + Number(cur.percent), 0)
      if(feeTotal > 0) {
        const feeAmount = amount.times(feeTotal).dividedBy(100)
        amount = amount.minus(feeAmount)
      }
    }
    
    let consideration:any[] = [{
      token: listing?.paymentToken?.address || '',
      amount: amount.toFixed(0, 1),
      endAmount: amount.toFixed(0, 1),
      recipient: accountAddress,
    }]

    if(listing.listingType.isFixedPrice() && listing.isReservedForBuyer && listing.reservedBuyerAddress) {
      const considerationBuyer = listing.assets.map((asset, index) => {
        return {
          ...this.createInputItemConverter.assetToCreateInputItem(
            asset,
            listing.quantities[index].toString()
          ),
          recipient: listing.reservedBuyerAddress,
        }
      })
      if(considerationBuyer?.length) {
        consideration.push(...considerationBuyer)
      }
    }

    return {
      startTime: listing.startAt?.toUnixInteger().toString(),
      endTime: listing.endAt?.toUnixInteger().toString(),
      offer: offer,
      consideration: consideration,
      fees: fees,
      salt: salt,
      allowPartialFills: true,
    }
  }
}
