import { OrderComponents } from '@t2-web/nft_land_js/lib/types'

import Listing from 'domain/models/listing'
import Offer from 'domain/models/offer'

export default class OrderComponentsConverter {
  public listingToOrderComponents (listing: Listing): OrderComponents {
    return listing.orderParams as OrderComponents
  }

  public offerToOrderComponents (offer: Offer): OrderComponents {
    return offer.orderParams as OrderComponents
  }
}
