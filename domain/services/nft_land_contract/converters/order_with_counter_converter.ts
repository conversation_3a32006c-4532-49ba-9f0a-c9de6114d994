import { OrderWithCounter } from '@t2-web/nft_land_js/lib/types'

import Listing from 'domain/models/listing'
import Offer from 'domain/models/offer'

export default class OrderWithCounterConverter {
  public listingToOrderWithCounter (listing: Listing): OrderWithCounter {
    return {
      parameters: listing.orderParams || {},
      signature: listing.signature || '',
    } as OrderWithCounter
  }

  public offerToOrderWithCounter (offer: Offer): OrderWithCounter {
    return {
      parameters: offer.orderParams || {},
      signature: offer.signature || '',
    } as OrderWithCounter
  }
}
