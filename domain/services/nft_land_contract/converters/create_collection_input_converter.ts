import { CreateCollectionInput } from '@t2-web/nft_land_js/lib/types'
import Collection from 'domain/models/collection'
export default class CreateCollectionInputConverter {
  public createCollectionInput ( collection : Collection, accountAddress: string, tokenContractType: number, salt?: string | undefined): CreateCollectionInput {
    return {
      name: collection.name || '',
      symbol: collection.symbol || '',
      type: tokenContractType || 0, 
    }
  }
}
