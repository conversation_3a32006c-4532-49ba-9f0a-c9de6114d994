import { ItemType } from '@t2-web/nft_land_js/lib/constants'
import { CreateInputItem, Item } from '@t2-web/nft_land_js/lib/types'

import Asset from 'domain/models/asset'

export default class CreateInputItemConverter {
  public assetToCreateInputItem (asset: Asset, amount:string): CreateInputItem {
    const isAssetErc1155 = asset.contract?.schema.isErc1155()
    return  {
      itemType: isAssetErc1155 ? ItemType.ERC1155 : ItemType.ERC721,
      token: asset.contract?.address!,
      identifier: String(asset.tokenId),
      amount: amount,
      endAmount: amount,
    }
  }
  
  public ItemType = ItemType

  public tokenBalanceItem (itemType: number, tokenAddress?: string, tokenId?: number|string ): Item {
    return  {
      itemType: itemType,
      token: tokenAddress || '',
      identifierOrCriteria: tokenId ? tokenId.toString() : '',
      startAmount: '',
      endAmount: '',
    }
  }
}
