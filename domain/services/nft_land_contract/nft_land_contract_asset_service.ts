import { ethers } from 'ethers'
import NftLandContractAdaptor from '../../external_services/nft_land_contract_adaptor'
import ErrorCallService from 'domain/services/utils/error_call_service'
import Asset from 'domain/models/asset'
import { ItemType } from '@t2-web/nft_land_js/lib/constants'
import ContractAdaptorDatasource from 'data/datasources/interfaces/contract_adaptor_datasource'

export default class NftLandContractAssetService {
  private nftLandAdaptor: NftLandContractAdaptor
  private errorCallService = new ErrorCallService()

  constructor (contract : ContractAdaptorDatasource) {
    this.nftLandAdaptor = new NftLandContractAdaptor(contract)
  }

  private getConfig1155 (asset: Asset, accountAddress: string): any {
    return {
      maxSupplyTokenIds: [asset?.tokenId || 1],
      maxSupplyAmounts: [asset?.totalSupply || 1],
      mintTokenIds: [asset?.tokenId || 1],
      mintAmounts: [asset?.totalSupply || 1],
      tokenURIs: [asset?.tokenUri || ''],
      mintRecipient: accountAddress,
      baseURI: '',
      contractURI: '',
      publicDrops: [],
      publicDropsIndexes: [],
      dropURI: '',
      allowListData: {
        merkleRoot: '******************************************000000000000000000000000',
        publicKeyURIs: [],
        allowListURI: '******************************************',
      },
      creatorPayouts: [],
      provenanceHash: '******************************************000000000000000000000000',
      allowedFeeRecipients: [],
      disallowedFeeRecipients: [],
      allowedPayers: [],
      disallowedPayers: [],
      allowedSigners: [],
      disallowedSigners: [],
      royaltyReceiver: '******************************************',
      royaltyBps: 0,
    }  
  }
  private getConfig721 (asset: Asset):any {
    return {
      maxSupply: 0,
      baseURI: '',
      contractURI: '',
      seaDropImpl: '******************************************',
      publicDrop: {
        mintPrice: 0,
        startTime: 0,
        endTime: 0,
        maxTotalMintableByWallet: 0,
        feeBps: 0,
        restrictFeeRecipients: false,
      },
      dropURI: '',
      allowListData: {
        merkleRoot:
          '******************************************000000000000000000000000',
        publicKeyURIs: [],
        allowListURI: '******************************************',
      },
      creatorPayoutAddress: '******************************************',
      provenanceHash:
        '******************************************000000000000000000000000',
      allowedFeeRecipients: [],
      disallowedFeeRecipients: [],
      allowedPayers: [],
      disallowedPayers: [],
      tokenGatedAllowedNftTokens: [],
      tokenGatedDropStages: [],
      disallowedTokenGatedAllowedNftTokens: [],
      signers: [],
      signedMintValidationParams: [],
      disallowedSigners: [],
      tokenURI: asset?.tokenUri,
    }     
  }

  public async create (
    asset: Asset,
    accountAddress: string,
  ): Promise<ethers.ContractReceipt> {
    try {
      let config = null,
        typeToken = ItemType.ERC1155
      
      if(asset?.contract?.schema.isErc1155()) {
        config = this.getConfig1155(asset, accountAddress)
        typeToken = ItemType.ERC1155
      } else if(asset?.contract?.schema.isErc721()) {
        console.log('-----isErc721')
        config = this.getConfig721(asset)
        typeToken = ItemType.ERC721
      }
      console.log('===============config', config)
      console.log('===============typeToken', typeToken)
      console.log('===============asset?.address', asset?.address)
      const {transact} = this.nftLandAdaptor.createAsset(
        {
          token: asset?.address,
          config: config,
          // domain: '',
          type: typeToken,
        },
        accountAddress,
      )
      const tx = await transact()
      const receipt = await tx.wait()
      console.log('---------create nft', receipt)
      return receipt
    } catch (error) {
      throw this.errorCallService.transactionError(error)
    }

  }
}
