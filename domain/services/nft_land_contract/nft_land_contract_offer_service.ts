import { OrderWithCounter, TipInputItem } from '@t2-web/nft_land_js/lib/types'

import Offer from 'domain/models/offer'
import ErrorCallService from 'domain/services/utils/error_call_service'
import NftLandContractAdaptor from '../../external_services/nft_land_contract_adaptor'
import { ContractTransaction, ethers } from 'ethers'
import CreateOrderInputConverter from './converters/create_order_input_converter'
import OrderComponentsConverter from './converters/order_component_converter'
import OrderWithCounterConverter from './converters/order_with_counter_converter'
import ContractAdaptorDatasource from 'data/datasources/interfaces/contract_adaptor_datasource'

export default class NftLandContractOfferService {
  private nftLandAdaptor: NftLandContractAdaptor
  private errorCallService = new ErrorCallService()
  private createOrderInputConverter: CreateOrderInputConverter =
    new CreateOrderInputConverter()

  private orderComponentsConverter: OrderComponentsConverter =
    new OrderComponentsConverter()

  private orderWithCounterConverter: OrderWithCounterConverter =
    new OrderWithCounterConverter()

  constructor (contract : ContractAdaptorDatasource) {
    this.nftLandAdaptor = new NftLandContractAdaptor(contract)
  }

  public async create (
    offer: Offer,
    accountAddress: string,
    exactApproval?: boolean,
    salt?: string
  ): Promise<any> {
    try {
      const createOrderInput =
        this.createOrderInputConverter.offerToCreateOrderInput(
          offer,
          accountAddress,
          salt
        )
      const orderUseCase = await this.nftLandAdaptor.createOrder(
        createOrderInput,
        accountAddress,
        exactApproval
      )
      const excuActions = await orderUseCase.executeAllActions()
      const orderHash = await this.nftLandAdaptor.getOrderHash(excuActions?.parameters)
      return {
        orderHash: orderHash,
        ...excuActions,
      }
    } catch (error) {
      throw this.errorCallService.transactionError(error)   
    }
  }

  public async cancel (
    offer: Offer,
    accountAddress?: string,
    domain?: string
  ): Promise<ContractTransaction> {
    try {
      const orderComponents =
        this.orderComponentsConverter.offerToOrderComponents(offer)
      const transactionMethods = this.nftLandAdaptor.cancelOrders(
        [orderComponents],
        accountAddress,
        domain
      )
      return await transactionMethods.transact()
    } catch (error) {
      throw this.errorCallService.transactionError(error)
    }
  }

  public async fulfill (
    offer: Offer,
    accountAddress?: string,
    domain?: string,
    tips?: TipInputItem[]
  ): Promise<ContractTransaction> {
    try {
      const orderWithCounter =
        this.orderWithCounterConverter.offerToOrderWithCounter(offer)
      const orderUseCase = await this.nftLandAdaptor.fulfillOrder({
        order: orderWithCounter,
        tips: tips,
        accountAddress: accountAddress,
        domain: domain,
        unitsToFill: offer.quantity || 1,
      })
      return await orderUseCase.executeAllActions()
    } catch (error) {
      throw this.errorCallService.transactionError(error)
    }
  }

  public async update (
    offer: Offer,
    accountAddress: string,
    exactApproval?: boolean,
    domain?: string,
    salt?: string
  ): Promise<OrderWithCounter> {
    try {
      await this.cancel(offer, accountAddress, domain)
      return await this.create(offer, accountAddress, exactApproval, salt)
    } catch (error) {
      throw this.errorCallService.transactionError(error)
    }
  }
}
