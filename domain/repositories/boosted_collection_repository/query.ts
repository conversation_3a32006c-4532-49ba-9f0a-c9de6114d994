export class Query {

  public limit:number = 60
  public page:number = 1

  public static fromOptions (options?:{[key:string]:any}) {
    return new this(options)
  }

  constructor (options?:{[key:string]:any}) {
    if (options === undefined) {
      return
    }

    if (options['limit'] != undefined) {
      this.limit = options['limit']
    }
    if (options['page'] != undefined) {
      this.page = options['page']
    }
  }

  public toJson (): {[key:string]:any} {
    return {
      limit: this.limit,
      page: this.page,
    }
  }
}
