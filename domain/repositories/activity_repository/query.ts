import Chain from 'domain/value_objects/chain'
import FilterEventType from './filter_event_type'
import SortBy from './sort_by'

export class Query {

  public assetId: string|null = null
  public assetIds: string[]|null = null
  public fromUserId: string|null = null
  public toUserId: string|null = null
  //public collectionId: string|null = null
  public chains: Chain[]|null = null
  public filterEventTypes: FilterEventType[]|null = null

  public sortBy: SortBy|null = null
  public limit:number = 60
  public page: number = 1
  public collectionIds: string[] | null = null
  public userId: string | null = null
  public chainId: number | null = null
  public paymentTokens: string[] | null = null
  public traits: Array<{name: string, values: string[]}> | null = null
  public unitPriceFrom: number | '' = ''
  public unitPriceTo: number | '' = ''
  public currencyId: string | '' = ''
  public static fromOptions (options?:{[key:string]:any}) {
    return new this(options)
  }

  constructor (options?:{[key:string]:any}) {

    if (options === undefined) {
      return
    }
    
    if (options['assetIds'] !== undefined) {

      this.assetIds = options['assetIds']
    }
    if (options['fromUserId'] !== undefined) {
      this.fromUserId = options['fromUserId']
    }
    if (options['toUserId'] !== undefined) {
      this.toUserId = options['toUserId']
    }
    // if (options['collectionId'] !== undefined) {
    //   this.collectionId = options['collectionId']
    // }
    if (options['chains'] !== undefined) {
      this.chains = options['chains']
    }
    if (options['filterEventTypes'] !== undefined) {
      this.filterEventTypes = options['filterEventTypes'] as FilterEventType[]
    }

    if (options['sortBy'] !== undefined) {
      this.sortBy = options['sortBy'] as SortBy
    }
    if (options['limit'] != undefined) {
      this.limit = options['limit']
    }
    if (options['page'] != undefined) {
      this.page = options['page']
    }
    if (options['collectionIds'] != undefined) {
      this.collectionIds = options['collectionIds']
    }
    if (options['userId'] !== undefined) {
      this.userId = options['userId']
    }
    if (options['chainId']!== undefined) {
      this.chainId = options['chainId']
    }
    if (options['unitPriceFrom'] !== undefined) {
      this.unitPriceFrom = options['unitPriceFrom']
    }
    if (options['unitPriceTo'] !== undefined) {
      this.unitPriceTo = options['unitPriceTo']
    }
    if (options['currencyId'] !== undefined) {
      this.currencyId = options['currencyId']
    }
    if (options['traits']!== undefined) {
      this.traits = options['traits']
    }
    
  }

  public toJson (): {[key:string]:any} {
    return {
      assetIds: this.assetIds,
      fromUserId: this.fromUserId,
      toUserId: this.toUserId,
      //collectionId: this.collectionId,
      chains: (this.chains !== null) ? this.chains.map((chain) => { return chain.getName() }) : null,
      filterEventTypes: this.filterEventTypes || [],
      sortBy: this.sortBy || null,
      //filterEventTypes: (this.filterEventTypes !== null) ? this.filterEventTypes.map((filterEventType) => { return filterEventType.getName() }) : null,
      limit: this.limit,
      page: this.page,
      collectionIds: this.collectionIds,
      userId: this.userId,
      chainId: this.chainId,
      paymentTokens: this.paymentTokens,
      traits: this.traits,
      unitPriceFrom: this.unitPriceFrom,
      unitPriceTo: this.unitPriceTo,
      currencyId: this.currencyId,
    }
  }
}
