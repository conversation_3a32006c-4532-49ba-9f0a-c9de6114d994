import Base from 'domain/value_objects/base'

export default class FilterEventType extends Base {

  public static getResourceArray () {
    return [
      {
        id: 1,
        name: 'listings',
        label: 'Listed',
      },
      {
        id: 2,
        name: 'sales',
        label: 'Sales',
      },
      {
        id: 3,
        name: 'bids',
        label: 'Bids',
      },
      {
        id: 4,
        name: 'transfers',
        label: 'Transfers',
      },
    ]
  }

  public static listings (): FilterEventType {
    return this.fromName<FilterEventType>('listings')
  }

  public static sales (): FilterEventType {
    return this.fromName<FilterEventType>('sales')
  }

  public static bids (): FilterEventType {
    return this.fromName<FilterEventType>('bids')
  }

  public static transfers (): FilterEventType {
    return this.fromName<FilterEventType>('transfers')
  }

  public isListings (): boolean {
    return (this.getName() === 'listings')
  }

  public isSales (): boolean {
    return (this.getName() === 'sales')
  }

  public isBids (): boolean {
    return (this.getName() === 'bids')
  }

  public isTransfers (): boolean {
    return (this.getName() === 'transfers')
  }

}