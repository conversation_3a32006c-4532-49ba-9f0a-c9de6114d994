import Base from 'domain/value_objects/base'

export default class SortBy extends Base {

  public static getResourceArray () {
    return [
      {
        id: 1,
        name: 'recentlyListed',
        label: 'Recently Listed',
      },
      {
        id: 2,
        name: 'recentlyCreated',
        label: 'Recently Created',
      },
      {
        id: 3,
        name: 'recentlySold',
        label: 'Recently Sold',
      },
      {
        id: 4,
        name: 'endingSoon',
        label: 'Ending Soon',
      },
      {
        id: 5,
        name: 'priceLowToHigh',
        label: 'Price: Low to High',
      },
      {
        id: 6,
        name: 'priceHighToLow',
        label: 'Price: High to Low',
      },
      {
        id: 7,
        name: 'highestLastSale',
        label: 'Highest Last Sale',
      },
      {
        id: 8,
        name: 'oldest',
        label: 'Oldest',
      },
    ]
  }

  public static recentlyListed (): SortBy {
    return this.fromName<SortBy>('recentlyListed')
  }

  public static recentlyCreated (): SortBy {
    return this.fromName<SortBy>('recentlyCreated')
  }

  public static recentlySold (): SortBy {
    return this.fromName<SortBy>('recentlySold')
  }

  public static recentlyReceived (): SortBy {
    return this.fromName<SortBy>('recentlyReceived')
  }

  public static endingSoon (): SortBy {
    return this.fromName<SortBy>('endingSoon')
  }

  public static priceLowToHigh (): SortBy {
    return this.fromName<SortBy>('priceLowToHigh')
  }

  public static priceHighToLow (): SortBy {
    return this.fromName<SortBy>('priceHighToLow')
  }

  public static highestLastSale (): SortBy {
    return this.fromName<SortBy>('highestLastSale')
  }

  public static oldest (): SortBy {
    return this.fromName<SortBy>('oldest')
  }

  public isRecentlyListed (): boolean {
    return (this.getName() === 'recentlyListed')
  }

  public isRecentlyCreated (): boolean {
    return (this.getName() === 'recentlyCreated')
  }

  public isRecentlySold (): boolean {
    return (this.getName() === 'recentlySold')
  }

  public isEndingSoon (): boolean {
    return (this.getName() === 'endingSoon')
  }

  public isPriceLowToHigh (): boolean {
    return (this.getName() === 'priceLowToHigh')
  }

  public isPriceHighToLow (): boolean {
    return (this.getName() === 'priceHighToLow')
  }

  public isHighestLastSale (): boolean {
    return (this.getName() === 'highestLastSale')
  }

  public isOldest (): boolean {
    return (this.getName() === 'oldest')
  }
   public static totalVolume (): SortBy {
    return new SortBy({
      id: 9,
      name: 'totalVolume',
      label: 'Total Volume',
    });
  }

  public isTotalVolume (): boolean {
    return this.getName() === 'totalVolume';
  }

}