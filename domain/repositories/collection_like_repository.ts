import 'reflect-metadata'

import { Query } from 'domain/repositories/collection_like_repository/query'
import CollectionLike from 'domain/models/collection_like'

import type CollectionLikeDatasource from 'data/datasources/interfaces/collection_like_datasource'
import { Query as DatasourceQuery } from 'data/datasources/interfaces/collection_like_datasource/query'
import { CollectionLikeJson } from 'data/jsons/collection_like_json'
import CollectionLikeRemoteDatasource from 'data/datasources/remote/collection_like_remote_datasource'

export default class CollectionLikeRepository {

  private datasource:CollectionLikeDatasource

  constructor (){
    this.datasource = new CollectionLikeRemoteDatasource()
  }

  search (query: Query): Promise<CollectionLike[]> {
    return new Promise((resolve, reject) => {
      this.datasource.search(DatasourceQuery.fromJson(query.toJson()))
        .then((jsons:CollectionLikeJson[]) => {
          resolve(jsons.map((json) => { return CollectionLike.fromJson(json) }))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number>{
    return new Promise((resolve, reject) => {
      this.datasource.totalCount(DatasourceQuery.fromJson(query.toJson()))
        .then((totalCount) => {
          resolve(totalCount)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  collectionIds (query: Query): Promise<string[]> {
    return new Promise((resolve, reject) => {
      this.datasource.collectionIds(DatasourceQuery.fromJson(query.toJson()))
        .then((collectionIds) => {
          resolve(collectionIds)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
