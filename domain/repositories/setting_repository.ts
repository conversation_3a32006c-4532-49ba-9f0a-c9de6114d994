import 'reflect-metadata'
import type SettingDatasource from 'data/datasources/interfaces/setting_datasource'
import SettingRemoteDatasource from 'data/datasources/remote/setting_remote_datasource'
import { SettingJson } from 'data/jsons/setting_json'

/* eslint-disable space-before-function-paren */
export default class SettingRepository {
  private datasource: SettingDatasource

  constructor() {
    this.datasource = new SettingRemoteDatasource()
  }

  getSetting(key: string): Promise<SettingJson> {
    return new Promise((resolve, reject) => {
      this.datasource
        .getSetting(key)
        .then((data: SettingJson) => {
          resolve(data)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  updateSetting(params: SettingJson): Promise<SettingJson> {
    return new Promise((resolve, reject) => {
      this.datasource
        .updateSetting(params)
        .then((data: SettingJson) => {
          resolve(data)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
}
