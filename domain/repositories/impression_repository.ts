import 'reflect-metadata'
import TargetEntityType from 'domain/value_objects/target_entity_type'

import Impression from 'domain/models/impression'

import type ImpressionDatasource from 'data/datasources/interfaces/impression_datasource'
import { ImpressionJson } from 'data/jsons/impression_json'
import ImpressionRemoteDatasource from 'data/datasources/remote/impression_remote_datasource'

export default class ImpressionRepository {

  private datasource:ImpressionDatasource

  constructor (){
    this.datasource = new ImpressionRemoteDatasource()
  }

  create (targetEntityType:TargetEntityType, targetEntityId:string): Promise<Impression> {
    return new Promise((resolve, reject) => {
      this.datasource.create(targetEntityType.getName(),targetEntityId)
        .then((json:ImpressionJson) => {
          resolve(Impression.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
