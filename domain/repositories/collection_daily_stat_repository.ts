import 'reflect-metadata'

import { Query } from 'domain/repositories/collection_daily_stat_repository/query'
import CollectionDailyStat from 'domain/models/collection_daily_stat'

import type CollectionDailyStatDatasource from 'data/datasources/interfaces/collection_daily_stat_datasource'
import { Query as DatasourceQuery } from 'data/datasources/interfaces/collection_daily_stat_datasource/query'
import { CollectionDailyStatJson } from 'data/jsons/collection_daily_stat_json'
import CollectionDailyStatRemoteDatasource from 'data/datasources/remote/collection_daily_stat_remote_datasource'

export default class CollectionDailyStatRepository {

  private datasource:CollectionDailyStatDatasource

  constructor (){
    this.datasource = new CollectionDailyStatRemoteDatasource()
  }

  findById (id: string): Promise<CollectionDailyStat> {
    return new Promise((resolve, reject) => {
      this.datasource.findById(id)
        .then((json:CollectionDailyStatJson) => {
          resolve(CollectionDailyStat.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  search (query: Query): Promise<CollectionDailyStat[]> {
    return new Promise((resolve, reject) => {
      this.datasource.search(DatasourceQuery.fromJson(query.toJson()))
        .then((jsons:any) => {
          console.log("🚀 ~ CollectionDailyStatRepository ~ .then ~ jsons:", jsons)
          resolve(jsons)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    return new Promise((resolve, reject) => {
      this.datasource.totalCount(DatasourceQuery.fromJson(query.toJson()))
        .then((totalCount) => {
          resolve(totalCount)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
