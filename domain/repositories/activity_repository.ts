import 'reflect-metadata'

import Activity from 'domain/models/activity'
import { Query } from 'domain/repositories/activity_repository/query'

import type ActivityDatasource from 'data/datasources/interfaces/activity_datasource'
import { Query as DatasourceQuery } from 'data/datasources/interfaces/activity_datasource/query'
import ActivityRemoteDatasource from 'data/datasources/remote/activity_remote_datasource'
import { ActivityJson } from 'data/jsons/activity_json'

export default class ActivityRepository {

  private datasource:ActivityDatasource

  constructor (){
    this.datasource = new ActivityRemoteDatasource()
  }

  findById (id: string): Promise<Activity> {
    return new Promise((resolve, reject) => {
      this.datasource.findById(id)
        .then((json:ActivityJson) => {
          resolve(Activity.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  search (query: Query): Promise<Activity[]> {
    console.log('🚀 ~ ActivityRepository ~ search ~ query:',query, query.toJson())
    return new Promise((resolve, reject) => {
      this.datasource.search(DatasourceQuery.fromJson(query.toJson()))
        .then((jsons:ActivityJson[]) => {
          console.log('🚀 ~ ActivityRepository ~ .then ~ jsons:', jsons)
          resolve(jsons.map((json) => { return Activity.fromJson(json) }))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    return new Promise((resolve, reject) => {
      this.datasource.totalCount(DatasourceQuery.fromJson(query.toJson()))
        .then((totalCount) => {
          resolve(totalCount)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
