import 'reflect-metadata'

import EmailVerification from 'domain/models/email_verification'

import type EmailVerificationDatasource from 'data/datasources/interfaces/email_verification_datasource'
import { EmailVerificationJson } from 'data/jsons/email_verification_json'
import EmailVerificationRemoteDatasource from 'data/datasources/remote/email_verification_remote_datasource'

export default class EmailVerificationRepository {

  private datasource:EmailVerificationDatasource

  constructor (){
    this.datasource = new EmailVerificationRemoteDatasource()
  }


  issue (email: string): Promise<EmailVerification> {
    return new Promise((resolve, reject) => {
      this.datasource.issue(email)
        .then((json:EmailVerificationJson) => {
          resolve(EmailVerification.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  verify (token: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.datasource.verify(token)
        .then((verify) => {
          resolve(verify)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  hasActiveRequest (): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.datasource.hasActiveRequest()
        .then((hasActiveRequest) => {
          resolve(hasActiveRequest)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  resendEmail (): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.datasource.resendEmail()
        .then((resendEmail) => {
          resolve(resendEmail)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
