import { DateTime } from 'luxon'

export class Query {

  public collectionId: string|null = null
  public userId: string|null = null
  public minCreatedAt: DateTime|null = null
  public maxCreatedAt: DateTime|null = null

  public limit:number = 60
  public page:number = 1

  public static fromOptions (options?:{[key:string]:any}) {
    return new this(options)
  }

  constructor (options?:{[key:string]:any}) {
    if (options === undefined) {
      return
    }
    if (options['collectionId'] !== undefined) {
      this.collectionId = options['collectionId']
    }
    if (options['userId'] !== undefined) {
      this.userId = options['userId']
    }
    if (options['minCreatedAt'] !== undefined) {
      this.minCreatedAt = options['minCreatedAt'] as DateTime
    }
    if (options['maxCreatedAt'] !== undefined) {
      this.maxCreatedAt = options['maxCreatedAt'] as DateTime
    }

    if (options['limit'] != undefined) {
      this.limit = options['limit']
    }
    if (options['page'] != undefined) {
      this.page = options['page']
    }
  }

  public toJson ():{[key:string]:any} {
    return {
      collectionId: this.collectionId,
      userId: this.userId,
      minCreatedAt: (this.minCreatedAt !== null) ? this.minCreatedAt.toISO() : null,
      maxCreatedAt: (this.maxCreatedAt !== null) ? this.maxCreatedAt.toISO() : null,
      limit: this.limit,
      page: this.page,
    }
  }
}
