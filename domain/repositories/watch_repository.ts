import 'reflect-metadata'

import Watch from 'domain/models/watch'
import TargetEntityType from 'domain/value_objects/target_entity_type'

import type WatchDatasource from 'data/datasources/interfaces/watch_datasource'
import { WatchJson } from 'data/jsons/watch_json'
import WatchRemoteDatasource from 'data/datasources/remote/watch_remote_datasource'

export default class WatchRepository {

  private datasource:WatchDatasource

  constructor (){
    this.datasource = new WatchRemoteDatasource()
  }

  create (targetEntityType:TargetEntityType, targetEntityId:string): Promise<Watch> {
    return new Promise((resolve, reject) => {
      this.datasource.create(
        targetEntityType.getName(),
        targetEntityId
      )
        .then((json:WatchJson) => {
          resolve(Watch.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  delete (targetEntityType:TargetEntityType, targetEntityId:string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.datasource.delete(
        targetEntityType.getName(),
        targetEntityId
      )
        .then((deleteBoolean) => {
          resolve(deleteBoolean)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
}
