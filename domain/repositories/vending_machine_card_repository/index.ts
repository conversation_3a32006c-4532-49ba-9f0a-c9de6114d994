import 'reflect-metadata'

import VendingMachineImport<PERSON>ard from 'domain/models/vending_machine_import_card'
import VendingMachineCard from 'domain/models/vending_machine_card'
import VendingMachineCheckout from 'domain/models/vending_machine_checkout'
import { Query } from 'domain/repositories/vending_machine_card_repository/query'
import { QueryCheckout } from 'domain/repositories/vending_machine_card_repository/query_checkout'
import {
  VendingMachineListImportCardType,
  VendingMachineListCardType,
  VendingMachineListCheckoutType,
} from 'domain/repositories/vending_machine_card_repository/list_card_type'

import type VendingMachineCardDatasource from 'data/datasources/interfaces/vending_machine_card_datasource'
import VendingMachineCardRemoteDatasource from 'data/datasources/remote/vending_machine_card_remote_datasource'
import {
  VendingMachineImport<PERSON>ard<PERSON>son,
  VendingMachineCard<PERSON>son,
  PayloadUpdateVendingMachineCard<PERSON>son,
  VendingMachineImport<PERSON>ard<PERSON>ist<PERSON>son,
  VendingMachineCard<PERSON><PERSON><PERSON><PERSON>,
  Checkout<PERSON>son,
  ListCheckout<PERSON>son,
} from 'data/jsons/vending-machine/vending_machine_card_json'

/* eslint-disable space-before-function-paren */
export default class VendingMachineCardRepository {
  private datasource: VendingMachineCardDatasource

  constructor() {
    this.datasource = new VendingMachineCardRemoteDatasource()
  }

  listImportCard(query: Query): Promise<VendingMachineListImportCardType> {
    return new Promise((resolve, reject) => {
      this.datasource
        .listImportCard(query)
        .then((jsons: VendingMachineImportCardListJson) => {
          const data = {
            ...jsons,
            items: jsons.items.map((json: VendingMachineImportCardJson) => {
              return VendingMachineImportCard.fromJson(json)
            }),
          }
          resolve(data)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  listCard(query: Query): Promise<VendingMachineListCardType> {
    return new Promise((resolve, reject) => {
      this.datasource
        .listCard(query)
        .then((jsons: VendingMachineCardListJson) => {
          const data = {
            ...jsons,
            items: jsons.items.map((json: VendingMachineCardJson) => {
              return VendingMachineCard.fromJson(json)
            }),
          }
          resolve(data)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  listCardIds(query: Query): Promise<VendingMachineListCardType> {
    return new Promise((resolve, reject) => {
      this.datasource
        .listCardIds(query)
        .then((jsons: VendingMachineCardListJson) => {
          const data = {
            ...jsons,
            items: jsons.items.map((json: VendingMachineCardJson) => {
              return VendingMachineCard.fromJson(json)
            }),
          }
          resolve(data)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  importCard(params: any): Promise<VendingMachineImportCard> {
    return new Promise((resolve, reject) => {
      this.datasource
        .importCard(params)
        .then((json) => {
          resolve(VendingMachineImportCard.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  batchImportCard(file: File): Promise<any> {
    return new Promise((resolve, reject) => {
      this.datasource
        .batchImportCard(file)
        .then((json) => {
          resolve(json)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  updateCard(
    id: string,
    payload: PayloadUpdateVendingMachineCardJson
  ): Promise<VendingMachineCardJson> {
    return new Promise((resolve, reject) => {
      this.datasource
        .updateCard(id, payload)
        .then((json) => {
          resolve(json)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  mapCategory(cardId: string, categoryId: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.datasource
        .mapCategory(cardId, categoryId)
        .then((json) => {
          resolve(json)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  importCardTemplate(): Promise<any> {
    return new Promise((resolve, reject) => {
      this.datasource
        .importCardTemplate()
        .then((json) => {
          resolve(json)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  currencyRate(baseCurrency: string, currency: string): Promise<number> {
    return new Promise((resolve, reject) => {
      this.datasource
        .currencyRate(baseCurrency, currency)
        .then((data) => {
          resolve(data)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  listCheckout(query: QueryCheckout): Promise<VendingMachineListCheckoutType> {
    return new Promise((resolve, reject) => {
      this.datasource
        .listCheckout(query)
        .then((jsons: ListCheckoutJson) => {
          const data = {
            limit: jsons?.pagination?.perPage || 0,
            page: jsons?.pagination?.currentPage || 0,
            total: jsons?.pagination?.total || 0,
            items: jsons?.checkouts?.map((json: CheckoutJson) => {
              return VendingMachineCheckout.fromJson(json)
            }),
          }
          resolve(data)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  exportCheckout(query: QueryCheckout): Promise<any> {
    return new Promise((resolve, reject) => {
      this.datasource
        .exportCheckout(query)
        .then((json) => {
          resolve(json)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
}
