import VendingMachineImportCard from 'domain/models/vending_machine_import_card'
import VendingMachineCard from 'domain/models/vending_machine_card'
import VendingMachineCheckout from 'domain/models/vending_machine_checkout'

export type VendingMachineListImportCardType = {
  limit: number
  page: number
  total: number
  items: VendingMachineImportCard[]
}

export type VendingMachineListCardType = {
  limit: number
  page: number
  total: number
  items: VendingMachineCard[]
}

export type VendingMachineListCheckoutType = {
  limit: number
  page: number
  total: number
  items: VendingMachineCheckout[]
}
