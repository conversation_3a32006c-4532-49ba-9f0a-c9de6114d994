import { DateTime } from 'luxon'

export class QueryCheckout {
  public limit: number | undefined = undefined
  public page: number | undefined = undefined
  public chainId: number = 0
  public sortBy: string = ''
  public sortOrder: string = ''
  public userId: string = ''
  public categoryId: string = ''
  public status: string = ''
  public cardId: string = ''
  public startDate: DateTime | undefined = undefined
  public endDate: DateTime | undefined = undefined

  /* eslint-disable space-before-function-paren */
  public static fromJson(json?: { [key: string]: any }) {
    return new this(json)
  }

  constructor(options?: { [key: string]: any }) {
    if (options === undefined) {
      return
    }

    if (options['limit'] != undefined) {
      this.limit = options['limit']
    }
    if (options['page'] != undefined) {
      this.page = options['page']
    }
    if (options['page'] != undefined) {
      this.page = options['page']
    }
    if (options['chainId'] != undefined) {
      this.chainId = options['chainId']
    }
    if (options['sortBy'] != undefined) {
      this.sortBy = options['sortBy']
    }
    if (options['sortOrder'] != undefined) {
      this.sortOrder = options['sortOrder']
    }
    if (options['userId'] != undefined) {
      this.userId = options['userId']
    }
    if (options['categoryId'] != undefined) {
      this.categoryId = options['categoryId']
    }
    if (options['status'] != undefined) {
      this.status = options['status']
    }
    if (options['cardId'] != undefined) {
      this.cardId = options['cardId']
    }
    if (options['startDate'] != undefined) {
      this.startDate = options['startDate']
    }
    if (options['endDate'] != undefined) {
      this.endDate = options['endDate']
    }
  }

  public toJson(): { [key: string]: any } {
    return {
      limit: this.limit,
      page: this.page,
      chainId: this.chainId || undefined,
      sortBy: this.sortBy || undefined,
      sortOrder: this.sortOrder || undefined,
      userId: this.userId || undefined,
      categoryId: this.categoryId || undefined,
      status: this.status || undefined,
      cardId: this.cardId || undefined,
      startDate: this?.startDate?.toMillis() || undefined,
      endDate: this.endDate?.endOf('day')?.toMillis() || undefined,
    }
  }

  public toQueryString(): string {
    const json = this.toJson()
    const searchParams = new URLSearchParams()

    Object.entries(json).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value))
      }
    })
    return searchParams.toString()
  }
}
