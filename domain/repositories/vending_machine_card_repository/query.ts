export class Query {
  public limit: number = 20
  public page: number = 1
  public search: string = ''
  public cardIds: string[] = []
  public cardId: string = ''
  public categoryId: string = ''
  public statuses: string[] = []

  /* eslint-disable space-before-function-paren */
  public static fromJson(json?: { [key: string]: any }) {
    return new this(json)
  }

  constructor(options?: { [key: string]: any }) {
    if (options === undefined) {
      return
    }

    if (options['limit'] != undefined) {
      this.limit = options['limit']
    }
    if (options['page'] != undefined) {
      this.page = options['page']
    }
    if (options['search'] != undefined) {
      this.search = options['search']
    }
    if (options['cardIds']) {
      this.cardIds = options['cardIds']
    }
    if (options['cardId']) {
      this.cardId = options['cardId']
    }
    if (options['categoryId']) {
      this.categoryId = options['categoryId']
    }
    if (options['statuses']) {
      this.statuses = options['statuses']
    }
  }

  public toJson(): { [key: string]: any } {
    return {
      limit: this.limit,
      page: this.page,
      search: this.search || undefined,
      // eslint-disable-next-line camelcase
      card_ids: this.cardIds || undefined,
      cardId: this.cardId || undefined,
      categoryId:
        this.categoryId && this.categoryId != 'n/a'
          ? this.categoryId
          : undefined,
      hasCategory: this.categoryId === 'n/a' ? false : undefined,
      statuses: this.statuses || undefined,
    }
  }
}
