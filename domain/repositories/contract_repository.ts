import 'reflect-metadata'
import Chain from 'domain/value_objects/chain'

import { Query } from 'domain/repositories/contract_repository/query'
import Contract from 'domain/models/contract'

import type ContractDatasource from 'data/datasources/interfaces/contract_datasource'
import { Query as DatasourceQuery } from 'data/datasources/interfaces/contract_datasource/query'
import { ContractJson } from 'data/jsons/contract_json'
import ContractRemoteDatasource from 'data/datasources/remote/contract_remote_datasource'

export default class ContractRepository {

  private datasource:ContractDatasource

  constructor (){
    this.datasource = new ContractRemoteDatasource()
  }

  findById (id: string): Promise<Contract> {
    return new Promise((resolve, reject) => {
      this.datasource.findById(id)
        .then((json:ContractJson) => {
          resolve(Contract.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  findByChainAndAddress (chain:Chain, address:string): Promise<Contract> {
    return new Promise((resolve, reject) => {
      this.datasource.findByChainAndAddress(chain.getName(), address)
        .then((json:ContractJson) => {
          resolve(Contract.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  search (query: Query): Promise<Contract[]> {
    return new Promise((resolve, reject) => {
      this.datasource.search(DatasourceQuery.fromJson(query.toJson()))
        .then((jsons:ContractJson[]) => {
          resolve(jsons.map((json) => { return Contract.fromJson(json) }))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    return new Promise((resolve, reject) => {
      this.datasource.totalCount(DatasourceQuery.fromJson(query.toJson()))
        .then((totalCount) => {
          resolve(totalCount)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
