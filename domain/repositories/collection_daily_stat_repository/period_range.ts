import Base from 'domain/value_objects/base'

export default class PeriodRange extends Base {

  public static getResourceArray () {
    return [
      {
        id: 1,
        name: 'lastSevenDays',
        label: 'last 7 days',
      },
      {
        id: 2,
        name: 'lastFourteenDays',
        label: 'last 14 days',
      },
      {
        id: 3,
        name: 'lastThirtyDays',
        label: 'last 30 days',
      },
      {
        id: 4,
        name: 'lastSixtyDays',
        label: 'last 60 days',
      },
      {
        id: 5,
        name: 'lastNinetyDays',
        label: 'last 90 days',
      },
      {
        id: 6,
        name: 'lastYear',
        label: 'Last year',
      },
      {
        id: 9,
        name: 'allTime',
        label: 'All Time',
      },
    ]
  }

  public static lastSevenDays (): PeriodRange {
    return this.fromName<PeriodRange>('lastSevenDays')
  }

  public static lastFourteenDays (): PeriodRange {
    return this.fromName<PeriodRange>('lastFourteenDays')
  }

  public static lastThirtyDays (): PeriodRange {
    return this.fromName<PeriodRange>('lastThirtyDays')
  }

  public static lastSixtyDays (): PeriodRange {
    return this.fromName<PeriodRange>('lastSixtyDays')
  }

  public static lastNinetyDays (): PeriodRange {
    return this.fromName<PeriodRange>('lastNinetyDays')
  }

  public static allTime (): PeriodRange {
    return this.fromName<PeriodRange>('allTime')
  }

  public isLastSevenDays (): boolean {
    return (this.getName() === 'lastSevenDays')
  }

  public isLastFourteenDays (): boolean {
    return (this.getName() === 'lastFourteenDays')
  }

  public isLastThirtyDays (): boolean {
    return (this.getName() === 'lastThirtyDays')
  }

  public isLastSixtyDays (): boolean {
    return (this.getName() === 'lastSixtyDays')
  }

  public isLastNinetyDays (): boolean {
    return (this.getName() === 'lastNinetyDays')
  }

  public isAllTime (): boolean {
    return (this.getName() === 'allTime')
  }

}