import PeriodRange from './period_range'

export class Query {

  public periodRange: PeriodRange|null = null
  public collectionId: string|null = null
  public limit:number = 60
  public page:number = 1
  public assetIds: string[] | null = null
  public collectionIds: string[] | null = null
  public static fromOptions (options?:{[key:string]:any}) {
    return new this(options)
  }

  constructor (options?:{[key:string]:any}) {
    if (options === undefined) {
      return
    }
    if (options['periodRange'] !== undefined) {
      this.periodRange = options['periodRange'] as PeriodRange
    }
    if (options['collectionId'] !== undefined) {
      this.collectionId = options['collectionId'] as string
    }

    if (options['limit'] != undefined) {
      this.limit = options['limit']
    }
    if (options['page'] != undefined) {
      this.page = options['page']
    }
    if (options['assetIds'] !== undefined) {
      this.assetIds = options['assetIds']
    }
    if (options['collectionIds'] !== undefined) {
      this.collectionIds = options['collectionIds']
    }
  }

  public toJson ():{[key:string]:any} {
    return {
      periodRange: (this.periodRange !== null) ? this.periodRange.getName() : null,
      collectionId: this.collectionId,
      limit: this.limit,
      page: this.page,
      assetIds: this.assetIds,
      collectionIds: this.collectionIds
    }
  }
}
