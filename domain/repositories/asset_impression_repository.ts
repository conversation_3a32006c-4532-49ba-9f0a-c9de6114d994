import 'reflect-metadata'

import { Query } from 'domain/repositories/asset_impression_repository/query'
import AssetImpression from 'domain/models/asset_impression'

import type AssetImpressionDatasource from 'data/datasources/interfaces/asset_impression_datasource'
import { Query as DatasourceQuery } from 'data/datasources/interfaces/asset_impression_datasource/query'
import { AssetImpressionJson } from 'data/jsons/asset_impression_json'
import AssetImpressionRemoteDatasource from 'data/datasources/remote/asset_impression_remote_datasource'

export default class AssetImpressionRepository {

  private datasource:AssetImpressionDatasource

  constructor () {
    this.datasource = new AssetImpressionRemoteDatasource()
  }

  search (query: Query): Promise<AssetImpression[]> {
    return new Promise((resolve, reject) => {
      this.datasource.search(DatasourceQuery.fromJson(query.toJson()))
        .then((jsons:AssetImpressionJson[]) => {
          resolve(jsons.map((json) => {
            return AssetImpression.fromJson(json)
          }))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }


  totalCount (query: Query): Promise<number> {
    return new Promise((resolve, reject) => {
      this.datasource.totalCount(DatasourceQuery.fromJson(query.toJson()))
        .then((totalCount) => {
          resolve(totalCount)
        })
        .catch( (e)=> {
          reject(e)
        })
    })
  }


  assetIds (query: Query): Promise<string[]> {
    return new Promise((resolve, reject) => {
      this.datasource.assetIds(DatasourceQuery.fromJson(query.toJson()))
        .then((assetIds) => {
          resolve(assetIds)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
