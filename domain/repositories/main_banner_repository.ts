import 'reflect-metadata'

import MainBanner from 'domain/models/main_banner'
import { Query } from 'domain/repositories/main_banner_repository/query'

import type MainBannerDatasource from 'data/datasources/interfaces/main_banner_datasource'
import { Query as DatasourceQuery } from 'data/datasources/interfaces/main_banner_datasource/query'
import MainBannerRemoteDatasource from 'data/datasources/remote/main_banner_remote_datasource'
import { MainBannerJson } from 'data/jsons/main_banner_json'

export default class MainBannerRepository {

  private datasource:MainBannerDatasource

  constructor() {
    //this.datasource = container.get<MainBannerDatasource>(TYPES.MainBannerDatasource)
    this.datasource = new MainBannerRemoteDatasource()
  }

  findById (id: string): Promise<MainBanner> {
    return new Promise((resolve, reject) => {
      this.datasource.findById(id)
        .then((json:MainBannerJson) => {
          resolve(MainBanner.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  search (query: Query): Promise<MainBanner[]> {
    return new Promise((resolve, reject) => {
      this.datasource.search(DatasourceQuery.fromJson(query.toJson()))
        .then((jsons:MainBannerJson[]) => {
          resolve(jsons.map((json) => { return MainBanner.fromJson(json) }))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    return new Promise((resolve, reject) => {
      this.datasource.totalCount(DatasourceQuery.fromJson(query.toJson()))
        .then((totalCount) => {
          resolve(totalCount)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
