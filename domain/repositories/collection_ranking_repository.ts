import 'reflect-metadata'

import CollectionRanking from 'domain/models/collection_ranking'
import { Query } from 'domain/repositories/collection_ranking/query'

import type CollectionRakingDatasource from 'data/datasources/interfaces/collection_ranking_datasource'
import { Query as DatasourceQuery } from 'data/datasources/interfaces/collection_ranking_datasource/query'
//import CollectionRemoteDatasource from 'data/datasources/remote/collection_remote_datasource'
import CollectionRankingRemoteDatasource from 'data/datasources/remote/collection_ranking_remote_datasource'
import { CollectionJson } from 'data/jsons/collection_json'

export default class CollectionRankingRepository {

  private datasource:CollectionRakingDatasource

  constructor (){
    this.datasource = new CollectionRankingRemoteDatasource()
  }

  findById (id: string): Promise<CollectionRanking> {
    return new Promise((resolve, reject) => {
      this.datasource.findById(id)
        .then((json:CollectionJson) => {
          resolve(CollectionRanking.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  findBySlug (slug: string): Promise<CollectionRanking> {
    return new Promise((resolve, reject) => {
      this.datasource.findBySlug(slug)
        .then((json:CollectionJson) => {
          resolve(CollectionRanking.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  search (query: Query): Promise<CollectionRanking[]> {
    return new Promise((resolve, reject) => {
      this.datasource.search(DatasourceQuery.fromJson(query.toJson()))
        .then((jsons:CollectionJson[]) => {
          resolve(jsons.map((json) => { return CollectionRanking.fromJson(json) }))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    return new Promise((resolve, reject) => {
      this.datasource.totalCount(DatasourceQuery.fromJson(query.toJson()))
        .then((totalCount) => {
          resolve(totalCount)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }


}
