import 'reflect-metadata'

import Collection from 'domain/models/collection'
import { Query } from 'domain/repositories/collection_repository/query'

import type CollectionDatasource from 'data/datasources/interfaces/collection_datasource'
import { Query as DatasourceQuery } from 'data/datasources/interfaces/collection_datasource/query'
import CollectionRemoteDatasource from 'data/datasources/remote/collection_remote_datasource'
import { CollectionJson } from 'data/jsons/collection_json'

export default class CollectionRepository {

  private datasource: CollectionDatasource

  constructor (){
    this.datasource = new CollectionRemoteDatasource()
  }

  findById (id: string): Promise<Collection> {
    return new Promise((resolve, reject) => {
      this.datasource.findById(id)
        .then((json:CollectionJson) => {
          resolve(Collection.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  findBySlug (slug: string, isExact : boolean, chainId?: number): Promise<any> {
    return new Promise((resolve, reject) => {
      this.datasource.findBySlug(slug, isExact, chainId)
        .then((json: any) => {
          if (isExact === false) {
            let result = []
            for (let i = 0; i < json.length; i++) {
              const element = json[i]
              const collection = Collection.fromJson(element)
              result.push(collection)
            }
            resolve(result)
          } else {
            const collection = Collection.fromJson(json)
            resolve(collection)
          }
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  search (query: Query): Promise<Collection[]> {
    return new Promise((resolve, reject) => {
      this.datasource.search(DatasourceQuery.fromJson(query.toJson()))
        .then((jsons:CollectionJson[]) => {
          resolve(jsons.map((json) => { return Collection.fromJson(json) }))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    return new Promise((resolve, reject) => {
      this.datasource.totalCount(DatasourceQuery.fromJson(query.toJson()))
        .then((totalCount) => {
          resolve(totalCount)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  save (collection:Collection): Promise<Collection> {
    if (collection.id === undefined) {
      let covertCreateInput = collection.toCreateJson()
      console.log('🚀 ~ CollectionRepository ~ save ~ covertCreateInput:', covertCreateInput)
      return new Promise((resolve, reject) => {
        this.datasource.create(covertCreateInput)
          .then((json) => {
            resolve(Collection.fromJson(json))
          })
          .catch((e) => {
            reject(e)
          })
      })
    }

    return new Promise((resolve, reject) => {
      try {
        let covertUpdateInput = collection.toUpdateJson()
        console.log('🚀 ~ CollectionRepository ~ returnnewPromise ~ covertUpdateInput:', covertUpdateInput)
        this.datasource.update(covertUpdateInput)
          .then((json) => {
            resolve(Collection.fromJson(json))
          })
          .catch((e) => {
            reject(e)
          })
      }catch (e) {
        console.log('🚀 ~ CollectionRepository ~ returnnewPromise ~ e:', e)
      }
    })
  }

  import (collection: Collection & { txHash: string } & { contractAddress: string }): Promise<Collection> {
    return new Promise((resolve, reject) => {
      let covertCreateInput = collection.toCreateJson()
      const importJson: any = {
        ...covertCreateInput,
        txHash: collection.txHash,
        contractAddress: collection.contractAddress,
      }
      
      this.datasource.import(importJson)
        .then((json: CollectionJson) => {
          resolve(Collection.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  importFromContractAddress (collection: Collection & { contractAddress: string } ): Promise<Collection> {
    return new Promise((resolve, reject) => {
      let covertCreateInput = collection.toCreateJsonForImportContractAddress()
      const importJson: any = {
        ...covertCreateInput,
        contractAddress: collection.contractAddress,
      }
      
      this.datasource.importFromContractAddress(importJson)
        .then((json: CollectionJson) => {
          resolve(Collection.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
    
  }
}
