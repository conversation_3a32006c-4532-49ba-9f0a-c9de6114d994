import Chain from 'domain/value_objects/chain'
import Marketplace from 'domain/value_objects/marketplace'
import PaymentToken from 'domain/value_objects/payment_token'
import FilterStatus from './filter_status'
import SortBy from './sort_by'

export class Query {
  public keywords: string[]|null = null
  public filterStatuses: FilterStatus[]|null = null
  public paymentToken: PaymentToken|null = null
  public minPrice: number|null = null
  public maxPrice: number|null = null
  public collectionIds: string[] |null = null
  public chains: Chain[]|null = null
  public categoryIds: string[]|null = null
  public paymentTokens: any[]|null = null
  public marketplaces: Marketplace[]|null = null
  public filterListedAt: string|null = null
  public ownerId: string|null = null
  public creatorId: string|null = null

  public sortBy: SortBy|null = null
  public limit:number = 60
  public page: number = 1
  public priceTokenUnit: string|null = null // eth or usd
  public type: string | null = null
  public chainId: number | null = null
  public traits: Array<{name: string, values: string[]}> | null = null
  public periodRange: string | null = null
  public static fromOptions (options?:{[key:string]:any}) {
    return new this(options)
  }

  constructor (options?:{[key:string]:any}) {
    if (options === undefined) {
      return
    }
    if (options['keywords'] !== undefined) {
      this.keywords = options['keywords']
    }
    if (options['filterStatuses'] !== undefined) {
      this.filterStatuses = options['filterStatuses'] as FilterStatus[]
    }
    if (options['paymentToken'] !== undefined) {
      this.paymentToken = options['paymentToken'] as PaymentToken
    }
    if (options['minPrice'] !== undefined) {
      this.minPrice = options['minPrice']
    }
    if (options['maxPrice'] !== undefined) {
      this.maxPrice = options['maxPrice']
    }
    if (options['collectionIds'] !== undefined) {
      this.collectionIds = options['collectionIds']
    }
    if (options['chains'] !== undefined) {
      this.chains = options['chains'] as Chain[]
    }
    if (options['categoryIds'] !== undefined) {
      this.categoryIds = options['categoryIds']
    }
    if (options['paymentTokens'] !== undefined) {
      this.paymentTokens = options['paymentTokens'] as any[]
    }
    if (options['marketplaces'] !== undefined) {
      this.marketplaces = options['marketplaces'] as Marketplace[]
    }
    if (options['filterListedAt'] !== undefined) {
      this.filterListedAt = options['filterListedAt']
    }
    if (options['ownerId'] !== undefined) {
      this.ownerId = options['ownerId']
    }
    if (options['creatorId'] !== undefined) {
      this.creatorId = options['creatorId']
    }

    if (options['sortBy'] !== undefined) {
      this.sortBy = options['sortBy'] as SortBy
    }
    if (options['limit'] != undefined) {
      this.limit = options['limit']
    }
    if (options['page'] != undefined) {
      this.page = options['page']
    }
    if (options['priceTokenUnit'] != undefined) {
      this.priceTokenUnit = options['priceTokenUnit']
    }
    if (options['type'] != undefined) {
      this.type = options['type']
    }
    if (options['periodRange']!= undefined) {
      this.periodRange = options['periodRange']
    }
    if (options['chainId']!= undefined) {
      this.chainId = options['chainId']
    }
    if(options['traits']!== undefined) {
      this.traits = options['traits']
    }
  }

  public toJson (): { [key: string]: any } {
    return {
      keywords: this.keywords,
      //filterStatuses: (this.filterStatuses !== null) ? this.filterStatuses.map((filterStatus) => { return filterStatus?.getName() }) : null,
      filterStatuses: this.filterStatuses || [],
      //paymentToken: (this.paymentToken !== null) ? this.paymentToken.getName() : null,
      minPrice: this.minPrice,
      maxPrice: this.maxPrice,
      collectionIds: this.collectionIds,
      chainIds: (this.chains !== null) ? this.chains.map((chain) => { return chain.getChainId() }) : null,
      categoryIds: this.categoryIds,
      paymentTokens: (this.paymentTokens !== null) ? this.paymentTokens.map((paymentToken) => { return paymentToken.name || paymentToken?.getName() }) : null,
      marketplaces: (this.marketplaces !== null) ? this.marketplaces.map((marketplace) => { return marketplace?.getName() }) : null,
      // marketplaces: this.marketplaces || [],
      filterListedAt: this.filterListedAt,
      ownerId: this.ownerId,
      creatorId: this.creatorId,
      //sortBy: (this.sortBy !== null) ? this.sortBy.getName() : null,
      sortBy: this.sortBy || null,
      limit: this.limit,
      page: this.page,
      priceTokenUnit: this.priceTokenUnit,
      type: this.type,
      chainId: this.chainId,
      traits: this.traits,
      periodRange: this.periodRange,
    }
  }
}
