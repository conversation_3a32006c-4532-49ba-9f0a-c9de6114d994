import Base from 'domain/value_objects/base'

export default class SortBy extends Base {

  public static getResourceArray () {
    return [
      {
        id: 1,
        name: 'recentlyListed',
        value: 'recentlyListed',
        label: 'Recently Listed',
      },
      {
        id: 2,
        name: 'recentlyCreated',
        value: 'recentlyCreated',
        label: 'Recently Created',
      },
      {
        id: 3,
        name: 'recentlySold',
        value: 'recentlySold',
        label: 'Recently Sold',
      },
      {
        id: 4,
        name: 'recentlyReceived',
        value: 'recentlyReceived',
        label: 'Recently Received',
      },
      {
        id: 5,
        name: 'endingSoon',
        value: 'endingSoon',
        label: 'Ending Soon',
      },
      {
        id: 6,
        name: 'priceLowToHigh',
        value: 'priceLowToHigh',
        label: 'Price: Low to High',
      },
      {
        id: 7,
        name: 'priceHighToLow',
        value: 'priceHighToLow',
        label: 'Price: High to Low',
      },
      {
        id: 8,
        name: 'highestLastSale',
        value: 'highestLastSale',
        label: 'Highest Last Sale',
      },
      {
        id: 9,
        name: 'oldest',
        value: 'oldest',
        label: 'Oldest',
      },
    
    ]
  }

  public static recentlyListed (): SortBy {
    return this.fromName<SortBy>('recentlyListed')
  }

  public static recentlyCreated (): SortBy {
    return this.fromName<SortBy>('recentlyCreated')
  }

  public static recentlySold (): SortBy {
    return this.fromName<SortBy>('recentlySold')
  }

  public static endingSoon (): SortBy {
    return this.fromName<SortBy>('endingSoon')
  }

  public static priceLowToHigh (): SortBy {
    return this.fromName<SortBy>('priceLowToHigh')
  }

  public static priceHighToLow (): SortBy {
    return this.fromName<SortBy>('priceHighToLow')
  }

  public static highestLastSale (): SortBy {
    return this.fromName<SortBy>('highestLastSale')
  }

  public static oldest (): SortBy {
    return this.fromName<SortBy>('oldest')
  }

  public isRecentlyListed (): boolean {
    return (this.getName() === 'recentlyListed')
  }

  public isRecentlyCreated (): boolean {
    return (this.getName() === 'recentlyCreated')
  }

  public isRecentlySold (): boolean {
    return (this.getName() === 'recentlySold')
  }

  public isEndingSoon (): boolean {
    return (this.getName() === 'endingSoon')
  }

  public isPriceLowToHigh (): boolean {
    return (this.getName() === 'priceLowToHigh')
  }

  public isPriceHighToLow (): boolean {
    return (this.getName() === 'priceHighToLow')
  }

  public isHighestLastSale (): boolean {
    return (this.getName() === 'highestLastSale')
  }

  public isOldest (): boolean {
    return (this.getName() === 'oldest')
  }

}