import Base from 'domain/value_objects/base'

export default class FilterStatus extends Base {

  public static getResourceArray () {
    return [
      {
        id: 1,
        name: 'buyNow',
        label: 'Buy Now',
      },
      {
        id: 2,
        name: 'onAuction',
        label: 'On Auction',
      },
      {
        id: 3,
        name: 'new',
        label: 'New',
      },
      {
        id: 4,
        name: 'hasOffer',
        label: 'Has Offer',
      },
    ]
  }

  public static buyNow (): FilterStatus {
    return this.fromName<FilterStatus>('buyNow')
  }

  public static onAuction (): FilterStatus {
    return this.fromName<FilterStatus>('onAuction')
  }

  public static new (): FilterStatus {
    return this.fromName<FilterStatus>('new')
  }

  public static hasOffer (): FilterStatus {
    return this.fromName<FilterStatus>('hasOffer')
  }

  public isBuyNow (): boolean {
    return (this.getName() === 'buyNow')
  }

  public isOnAuction (): boolean {
    return (this.getName() === 'onAuction')
  }

  public isNew (): boolean {
    return (this.getName() === 'new')
  }

  public isHasOffer (): boolean {
    return (this.getName() === 'hasOffer')
  }

}