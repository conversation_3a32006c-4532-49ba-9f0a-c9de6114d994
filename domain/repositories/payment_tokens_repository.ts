import 'reflect-metadata'

import PaymentToken from 'domain/models/payment_tokens'

import type PaymentTokensDatasource from 'data/datasources/interfaces/payment_tokens_datasource'
import PaymentTokensRemoteDatasource from 'data/datasources/remote/payment_tokens_remote_datasource'
import { PaymentTokensJson } from 'data/jsons/payment_tokens_json'

export default class ActivityRepository {

  private datasource:PaymentTokensDatasource

  constructor (){
    this.datasource = new PaymentTokensRemoteDatasource()
  }

  getPaymentTokens (chainId?: number): Promise<PaymentToken[]> {
    return new Promise((resolve, reject) => {
      this.datasource.getPaymentTokens(chainId)
        .then((jsons:PaymentTokensJson[]) => {
          console.log('🚀 ~ ActivityRepository ~ .then ~ json:', jsons)
          resolve(jsons.map((json) => { return PaymentToken.fromJson(json) }))
        })
        .catch((e:any) => {
          console.log('🚀 ~ ActivityRepository ~ returnnewPromise ~ e:', e)
          reject(e)
        })
    })
  }


}
