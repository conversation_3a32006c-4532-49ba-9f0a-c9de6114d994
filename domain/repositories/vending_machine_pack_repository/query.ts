export class Query {
  
  public limit: number = 10
  public page: number = 1
  public sort: string = 'created_at:desc'
  public keywords?: string
  public categoryId?: string
  public minPrice?: number
  public maxPrice?: number
  public isEnabled?: boolean
  public minCreatedAt?: string
  public maxCreatedAt?: string
  public minUpdatedAt?: string
  public maxUpdatedAt?: string

  public static fromJson (json?: {[key:string]:any}) {
    return new this(json)
  }

  constructor (options?: {[key:string]:any}) {
    if (options === undefined) {
      return
    }

    if (options['limit'] != undefined) {
      this.limit = Math.min(options['limit'], 60) // Max 60 as per API spec
    }
    if (options['page'] != undefined) {
      this.page = options['page']
    }
    if (options['sort'] != undefined) {
      this.sort = options['sort']
    }
    if (options['keywords'] != undefined) {
      this.keywords = options['keywords']
    }
    if (options['categoryId'] != undefined) {
      this.categoryId = options['categoryId']
    }
    if (options['minPrice'] != undefined) {
      this.minPrice = options['minPrice']
    }
    if (options['maxPrice'] != undefined) {
      this.maxPrice = options['maxPrice']
    }
    if (options['isEnabled'] != undefined) {
      this.isEnabled = options['isEnabled']
    }
    if (options['minCreatedAt'] != undefined) {
      this.minCreatedAt = options['minCreatedAt']
    }
    if (options['maxCreatedAt'] != undefined) {
      this.maxCreatedAt = options['maxCreatedAt']
    }
    if (options['minUpdatedAt'] != undefined) {
      this.minUpdatedAt = options['minUpdatedAt']
    }
    if (options['maxUpdatedAt'] != undefined) {
      this.maxUpdatedAt = options['maxUpdatedAt']
    }
  }

  public toJson (): {[key:string]:any} {
    const params: {[key:string]:any} = {
      limit: this.limit,
      page: this.page,
      sort: this.sort,
    }

    if (this.keywords !== undefined) {
      params.keywords = this.keywords
    }
    if (this.categoryId !== undefined) {
      params.categoryId = this.categoryId
    }
    if (this.minPrice !== undefined) {
      params.minPrice = this.minPrice
    }
    if (this.maxPrice !== undefined) {
      params.maxPrice = this.maxPrice
    }
    if (this.isEnabled !== undefined) {
      params.isEnabled = this.isEnabled
    }
    if (this.minCreatedAt !== undefined) {
      params.minCreatedAt = this.minCreatedAt
    }
    if (this.maxCreatedAt !== undefined) {
      params.maxCreatedAt = this.maxCreatedAt
    }
    if (this.minUpdatedAt !== undefined) {
      params.minUpdatedAt = this.minUpdatedAt
    }
    if (this.maxUpdatedAt !== undefined) {
      params.maxUpdatedAt = this.maxUpdatedAt
    }

    return params
  }
}
