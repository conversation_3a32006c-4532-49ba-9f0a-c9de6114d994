import 'reflect-metadata'

import VendingMachinePack from 'domain/models/vending_machine_pack'
import { Query } from 'domain/repositories/vending_machine_pack_repository/query'
import { VendingMachinePackListType } from 'domain/repositories/vending_machine_pack_repository/list_pack_type'

import  VendingMachinePackDatasource, { type CreatePackParams,type UpdatePackParams } from 'data/datasources/interfaces/vending_machine_pack_datasource'
import VendingMachinePackRemoteDatasource from 'data/datasources/remote/vending_machine_pack_remote_datasource'
import { VendingMachinePackJson, VendingMachinePackListJson } from 'data/jsons/vending-machine/vending_machine_pack_json'

export default class VendingMachinePackRepository {

  private datasource: VendingMachinePackDatasource

  constructor () {
    this.datasource = new VendingMachinePackRemoteDatasource()
  }

  list (query: Query): Promise<VendingMachinePackListType> {
    return new Promise((resolve, reject) => {
      this.datasource.list(query)
        .then((jsons: VendingMachinePackListJson) => {
          const data = {
            ...jsons,
            items: jsons.items.map((json: VendingMachinePackJson) => { 
              return VendingMachinePack.fromJson(json) 
            }),
          }
          resolve(data)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  getById (id: string): Promise<VendingMachinePack> {
    return new Promise((resolve, reject) => {
      this.datasource.getById(id)
        .then((json: VendingMachinePackJson) => {
          resolve(VendingMachinePack.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  create (params: CreatePackParams): Promise<VendingMachinePack> {
    return new Promise((resolve, reject) => {
      this.datasource.create(params)
        .then((json: VendingMachinePackJson) => {
          resolve(VendingMachinePack.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  update (id: string, params: UpdatePackParams): Promise<VendingMachinePack> {
    return new Promise((resolve, reject) => {
      this.datasource.update(id, params)
        .then((json: VendingMachinePackJson) => {
          resolve(VendingMachinePack.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  delete (id: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.datasource.delete(id)
        .then(() => {
          resolve()
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
}
