import 'reflect-metadata'

import { Query } from 'domain/repositories/collection_impression_repository/query'
import CollectionImpression from 'domain/models/collection_impression'

import type CollectionImpressionDatasource from 'data/datasources/interfaces/collection_impression_datasource'
import { Query as DatasourceQuery } from 'data/datasources/interfaces/collection_impression_datasource/query'
import { CollectionImpressionJson } from 'data/jsons/collection_impression_json'
import CollectionImpressionRemoteDatasource from 'data/datasources/remote/collection_impression_remote_datasource'

export default class CollectionImpressionRepository {

  private datasource:CollectionImpressionDatasource

  constructor (){
    this.datasource = new CollectionImpressionRemoteDatasource()
  }

  search (query: Query): Promise<CollectionImpression[]> {
    return new Promise((resolve, reject) => {
      this.datasource.search(DatasourceQuery.fromJson(query.toJson()))
        .then((jsons:CollectionImpressionJson[]) => {
          resolve(jsons.map((json) => { return CollectionImpression.fromJson(json) }))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number>{
    return new Promise((resolve, reject) => {
      this.datasource.totalCount(DatasourceQuery.fromJson(query.toJson()))
        .then((totalCount) => {
          resolve(totalCount)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  collectionIds (query: Query): Promise<string[]> {
    return new Promise((resolve, reject) => {
      this.datasource.collectionIds(DatasourceQuery.fromJson(query.toJson()))
        .then((collectionIds) => {
          resolve(collectionIds)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
