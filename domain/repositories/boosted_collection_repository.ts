import 'reflect-metadata'

import { Query } from 'domain/repositories/boosted_collection_repository/query'
import BoostedCollection from 'domain/models/boosted_collection'

import type BoostedCollectionDatasource from 'data/datasources/interfaces/boosted_collection_datasource'
import { Query as DatasourceQuery } from 'data/datasources/interfaces/boosted_collection_datasource/query'
import { BoostedCollectionJson } from 'data/jsons/boosted_collection_json'
import BoostedCollectionRemoteDatasource from 'data/datasources/remote/boosted_collection_remote_datasource'

export default class BoostedCollectionRepository {

  private datasource:BoostedCollectionDatasource

  constructor () {
    this.datasource = new BoostedCollectionRemoteDatasource()
  }

  findById (id: string): Promise<BoostedCollection> {
    return new Promise((resolve, reject) => {
      this.datasource.findById(id)
        .then((json:BoostedCollectionJson) => {
          resolve(BoostedCollection.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  search (query: Query): Promise<BoostedCollection[]> {
    return new Promise((resolve, reject) => {
      this.datasource.search(DatasourceQuery.fromJson(query.toJson()))
        .then((jsons:BoostedCollectionJson[]) => {
          resolve(jsons.map((json) => {
            return BoostedCollection.fromJson(json)
          }))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }


  totalCount (query: Query): Promise<number> {
    return new Promise((resolve, reject) => {
      this.datasource.totalCount(DatasourceQuery.fromJson(query.toJson()))
        .then((totalCount) => {
          resolve(totalCount)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
}
