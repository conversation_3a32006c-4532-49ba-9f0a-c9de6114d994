import 'reflect-metadata'

import LoginStatus from 'domain/models/login_status'
import LoginSuccess from 'domain/models/login_success'
import User from 'domain/models/user'

import type UserDatasource from 'data/datasources/interfaces/user_datasource'
import { LoginStatusJson } from 'data/jsons/login_status_json'
import { LoginSuccessJson } from 'data/jsons/login_success_json'
import { UserJson } from 'data/jsons/user_json'
import UserRemoteDatasource from 'data/datasources/remote/user_remote_datasource'

export default class UserRepository {

  private datasource:UserDatasource

  constructor (){
    this.datasource = new UserRemoteDatasource()
  }

  me (): Promise<User> {
    return new Promise((resolve, reject) => {
      this.datasource.me()
        .then((json:UserJson) => {
          resolve(User.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  findByPublicAddress (publicAddress: string): Promise<User> {
    return new Promise((resolve, reject) => {
      this.datasource.findByPublicAddress(publicAddress)
        .then((json:UserJson) => {
          resolve(User.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  findByUsername (username: string, isExact : boolean): Promise<any> {
    return new Promise((resolve, reject) => {
      this.datasource.findByUsername(username, isExact)
        .then((json: any) => {
          console.log('🚀 ~ UserRepository ~ .then ~ json:', json)
          if (isExact) {
            resolve(User.fromJson(json))
          } else { 
            let result = []
            if(json?.length > 0) {
              for (let i = 0; i < json?.length; i++) {
                const element = json[i]
                const user = User.fromJson(element)
                result.push(user)
              }
            }
            resolve(result)
          }
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  findById (id: string): Promise<User> {
    return new Promise((resolve, reject) => {
      this.datasource.findById(id)
        .then((json:UserJson) => {
          resolve(User.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  loginStatus (): Promise<LoginStatus> {
    return new Promise((resolve, reject) => {
      this.datasource.loginStatus()
        .then((json:LoginStatusJson) => {
          resolve(LoginStatus.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  nonce (publicAddress: string): Promise<number> {
    return new Promise((resolve, reject) => {
      this.datasource.nonce(publicAddress)
        .then((nonce) => {
          resolve(nonce)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  create (publicAddress: string): Promise<User> {
    return new Promise((resolve, reject) => {
      this.datasource.create(publicAddress)
        .then((json:UserJson) => {
          resolve(User.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  login (publicAddress: string, signature:string): Promise<LoginSuccess> {
    return new Promise((resolve, reject) => {
      this.datasource.login(publicAddress, signature)
        .then((json:LoginSuccessJson) => {
          resolve(LoginSuccess.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  logout (): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.datasource.logout()
        .then((logout) => {
          resolve(logout)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  isUsernameAvailable (username: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.datasource.isUsernameAvailable(username)
        .then((isUsernameAvailable) => {
          resolve(isUsernameAvailable)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  isEmailAvailable (email: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.datasource.isEmailAvailable(email)
        .then((isEmailAvailable) => {
          resolve(isEmailAvailable)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  update (user: User): Promise<User> {
    return new Promise((resolve, reject) => {
      this.datasource.update(user.toUpdateJson())
        .then((json) => {
          resolve(User.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  updateEmail (email: string): Promise<User> {
    return new Promise((resolve, reject) => {
      this.datasource.updateEmail(email)
        .then((json:UserJson) => {
          resolve(User.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  updateLanguage (languageName: string): Promise<User> {
    return new Promise((resolve, reject) => {
      this.datasource.updateLanguage(languageName)
        .then((json:UserJson) => {
          resolve(User.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
  
  loginWithGoogle (): Promise<string> {
    return new Promise((resolve, reject) => {
      this.datasource.loginWithGoogle()
        .then((data) => {
          resolve(data)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
  
  setTokenUser (token: string): void {
    return this.datasource.setTokenUser(token)
  }

  linkWallet (publicAddress: string, signature:string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.datasource.linkWallet(publicAddress, signature)
        .then((data) => {
          resolve(data)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
