import 'reflect-metadata'

import UserOfferSetting from 'domain/models/user_offer_setting'

import type UserOfferSettingDatasource from 'data/datasources/interfaces/user_offer_setting_datasource'
import { UserOfferSettingJson } from 'data/jsons/user_offer_setting_json'
import UserOfferSettingRemoteDatasource from 'data/datasources/remote/user_offer_setting_remote_datasource'

export default class UserOfferSettingRepository {

  private datasource:UserOfferSettingDatasource

  constructor (){
    this.datasource = new UserOfferSettingRemoteDatasource()
  }

  search (): Promise<UserOfferSetting[]> {
    return new Promise((resolve, reject) => {
      this.datasource.search()
        .then((jsons:UserOfferSettingJson[]) => {
          resolve(jsons.map((json) => { return UserOfferSetting.fromJson(json) }))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  update (collectionId:string, minimumPrice:number): Promise<UserOfferSetting> {
    return new Promise((resolve, reject) => {
      this.datasource.update(collectionId, minimumPrice)
        .then((json:UserOfferSettingJson) => {
          resolve(UserOfferSetting.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
