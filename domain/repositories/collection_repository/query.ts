
import Chain from 'domain/value_objects/chain'
import SortBy from './sort_by'

export class Query {
  public categoryIds: string[] | null = null
  public ownerId: string | null = null

  public sortBy: SortBy | null = null
  public chains: Chain[] | null = null
  public limit: number = 60
  public page: number = 1

  
  public filterStatuses: string[] | null = null
  public priceTokenUnit: string | null = null // USD or ETH
  public minPrice: number | null = null
  public maxPrice: number | null = null
  public collectionIds: string[] | null = null
  public type: string | null = null
  public typeId: number | null = null
  public periodRange: string | null = null

  public chainId : number | null = null
  public static fromOptions (options?: { [key: string]: any }) {
    return new this(options)
  }

  constructor (options?: { [key: string]: any }) {
    if (options === undefined) {
      return
    }
    if (options['categoryIds'] !== undefined) {
      this.categoryIds = options['categoryIds']
    }
    if (options['ownerId'] !== undefined) {
      this.ownerId = options['ownerId']
    }
    if (options['chains'] !== undefined) {
      this.chains = options['chains']
    }

    if (options['sortBy'] !== undefined) {
      this.sortBy = options['sortBy'] as SortBy
    }
    if (options['limit'] != undefined) {
      this.limit = options['limit']
    }
    if (options['page'] != undefined) {
      this.page = options['page']
    }

    if (options['filterStatuses'] != undefined) {
      this.filterStatuses = options['filterStatuses']
    }
    if (options['priceTokenUnit'] != undefined) {
      this.priceTokenUnit = options['priceTokenUnit']
    }
    if (options['minPrice'] != undefined) {
      this.minPrice = options['minPrice']
    }
    if (options['maxPrice'] != undefined) {
      this.maxPrice = options['maxPrice']
    }
    if (options['collectionIds'] != undefined) {
      this.collectionIds = options['collectionIds']
    }
    if (options['type'] != undefined) {
      this.type = options['type']
    }
    if (options['typeId'] != undefined) {
      this.typeId = options['typeId']
    }
    if (options['periodRange']!= undefined) {
      this.periodRange = options['periodRange']
    }
    if (options['chainId']!= undefined) {
      this.chainId = options['chainId']
    }
  }

  public toJson (): { [key: string]: any } {
    return {
      categoryIds: this.categoryIds,
      ownerId: this.ownerId,
      chains:this.chains,
      // this.chains !== null
      //   ? this.chains.map((chain) => {
      //     return chain.getName()
      //   })
      //   : null,
      sortBy: this.sortBy !== null ? this.sortBy.getName() : null,
      limit: this.limit,
      page: this.page,

      filterStatuses: this.filterStatuses,
      priceTokenUnit: this.priceTokenUnit,
      minPrice: this.minPrice,
      maxPrice: this.maxPrice,
      collectionIds: this.collectionIds,
      type: this.type,
      typeId: this.typeId,
      periodRange: this.periodRange,
      chainId: this.chainId,
    }
  }
}
