import 'reflect-metadata'

import type MultimediaDatasource from 'data/datasources/interfaces/multimedia_datasource'
import Multimedia from 'domain/models/multimedia'
import MultimediaRemoteDatasource from 'data/datasources/remote/multimedia_datasource'

export default class MultimediaRepository {

  private datasource:MultimediaDatasource

  constructor (){
    this.datasource = new MultimediaRemoteDatasource()
  }

  upload (file: File): Promise<Multimedia> {
    return new Promise((resolve, reject) => {
      this.datasource.upload(file)
        .then((json) => {
          resolve(Multimedia.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
