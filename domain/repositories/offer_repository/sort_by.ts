import Base from 'domain/value_objects/base'

export default class SortBy extends Base {

  public static getResourceArray () {
    return [
      {
        id: 1,
        name: 'recentlyOffered',
        label: 'Recently Offered',
      },
      {
        id: 2,
        name: 'recentlyCreated',
        label: 'Recently Created',
      },
      {
        id: 3,
        name: 'priceHighToLow',
        label: 'Price: High to Low',
      },
      {
        id: 4,
        name: 'priceLowToHigh',
        label: 'Price: Low to High',
      },
      {
        id: 5,
        name: 'endingSoon',
        label: 'Ending Soon',
      },
      {
        id: 9,
        name: 'oldest',
        label: 'Oldest',
      },
    ]
  }

  public static recentlyOffered (): SortBy {
    return this.fromName<SortBy>('recentlyOffered')
  }

  public static recentlyCreated (): SortBy {
    return this.fromName<SortBy>('recentlyCreated')
  }

  public static priceHighToLow (): SortBy {
    return this.fromName<SortBy>('priceHighToLow')
  }

  public static priceLowToHigh (): SortBy {
    return this.fromName<SortBy>('priceLowToHigh')
  }

  public static endingSoon (): SortBy {
    return this.fromName<SortBy>('endingSoon')
  }

  public static oldest (): SortBy {
    return this.fromName<SortBy>('oldest')
  }

  public isRecentlyOffered (): boolean {
    return (this.getName() === 'recentlyOffered')
  }

  public isRecentlyCreated (): boolean {
    return (this.getName() === 'recentlyCreated')
  }

  public isPriceHighToLow (): boolean {
    return (this.getName() === 'priceHighToLow')
  }

  public isPriceLowToHigh (): boolean {
    return (this.getName() === 'priceLowToHigh')
  }

  public isEndingSoon (): boolean {
    return (this.getName() === 'endingSoon')
  }

  public isOldest (): boolean {
    return (this.getName() === 'oldest')
  }

}