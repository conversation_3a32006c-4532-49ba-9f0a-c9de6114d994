import PaymentToken from 'domain/models/payment_tokens'
import SortBy from './sort_by'

export class Query {

  public assetId: string|null = null
  public userId: string|null = null
  public paymentToken: PaymentToken|null = null
  public minPrice: number|null = null
  public maxPrice: number|null = null

  public sortBy: SortBy|null = null
  public limit:number = 60
  public page: number = 1
  public collectionIds : string[] | null = null
  public statusId: number | null = null
  public ownerId: string | null = null
  public chainId: number | null = null
  public static fromOptions (options?:{[key:string]:any}) {
    return new this(options)
  }

  constructor (options?:{[key:string]:any}) {
    if (options === undefined) {
      return
    }
    if (options['assetId'] !== undefined) {
      this.assetId = options['assetId']
    }
    if (options['userId'] !== undefined) {
      this.userId = options['userId']
    }
    if (options['paymentToken'] !== undefined) {
      this.paymentToken = options['paymentToken'] as PaymentToken
    }
    if (options['minPrice'] !== undefined) {
      this.minPrice = options['minPrice']
    }
    if (options['maxPrice'] !== undefined) {
      this.maxPrice = options['maxPrice']
    }

    if (options['sortBy'] !== undefined) {
      this.sortBy = options['sortBy'] as SortBy
    }
    if (options['limit'] != undefined) {
      this.limit = options['limit']
    }
    if (options['page'] != undefined) {
      this.page = options['page']
    }
    if (options['collectionIds'] != undefined) {
      this.collectionIds = options['collectionIds']
    }
    if (options['statusId'] != undefined) {
      this.statusId = options['statusId']
    }
    if (options['ownerId'] !== undefined) {
      this.ownerId = options['ownerId']
    }
    if (options['chainId']!== undefined) {
      this.chainId = options['chainId']
    }
  }

  public toJson ():{[key:string]:any} {
    return {
      assetId: this.assetId,
      userId: this.userId,
      paymentTokenId: (this.paymentToken !== null) ? this.paymentToken.id : null,
      minPrice: this.minPrice,
      maxPrice: this.maxPrice,
      statusId: this.statusId || null,
      //sortBy: (this.sortBy !== null) ? this.sortBy.getName() : null,
      sortBy: this.sortBy || null,
      limit: this.limit,
      page: this.page,
      collectionIds: this.collectionIds,
      ownerId: this.ownerId,
      chainId: this.chainId
    }
  }
}
