import 'reflect-metadata'
import NewsModel from 'domain/models/news'
import type NewsDatasource from 'data/datasources/interfaces/news_datasource'
import NewsRemoteDatasource from 'data/datasources/remote/news_remote_datasource'

export default class NewsRepository {
  private datasource: NewsDatasource

  constructor () {
    this.datasource = new NewsRemoteDatasource()
  }

  getNews (): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this.datasource.getNews()
        .then((jsons : any) => {
          console.log('🚀 ~ NewsRepository ~ .then ~ jsons:', jsons)
          const newsList = jsons.news || []
          resolve(newsList)
        })
        .catch((e) => {
          console.log('🚀 ~ NewsRepository ~ returnnewPromise ~ e:', e)
          reject(e)
        })
    })
  }
  editNews (news: any[]): Promise<any[]> {
    return new Promise((resolve, reject) => {
      console.log('🚀 ~ NewsRepository ~ editNews ~ news:', news)
      this.datasource.editNews(news)
        .then((jsons : any) => {
          const newsList = jsons.news || []
          resolve(newsList)
        })
        .catch((e) => {
          console.log('🚀 ~ NewsRepository ~ returnnewPromise ~ e:', e)
          reject(e)
        })
    })
  }
}