import 'reflect-metadata'

import { Query } from 'domain/repositories/collection_watch_repository/query'
import CollectionWatch from 'domain/models/collection_watch'

import type CollectionWatchDatasource from 'data/datasources/interfaces/collection_watch_datasource'
import { Query as DatasourceQuery } from 'data/datasources/interfaces/collection_watch_datasource/query'
import { CollectionWatchJson } from 'data/jsons/collection_watch_json'
import CollectionWatchRemoteDatasource from 'data/datasources/remote/collection_watch_remote_datasource'

export default class CollectionWatchRepository {

  private datasource:CollectionWatchDatasource

  constructor (){
    this.datasource = new CollectionWatchRemoteDatasource()
  }

  search (query: Query): Promise<CollectionWatch[]> {
    return new Promise((resolve, reject) => {
      this.datasource.search(DatasourceQuery.fromJson(query.toJson()))
        .then((jsons:CollectionWatchJson[]) => {
          resolve(jsons.map((json) => { return CollectionWatch.fromJson(json) }))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number>{
    return new Promise((resolve, reject) => {
      this.datasource.totalCount(DatasourceQuery.fromJson(query.toJson()))
        .then((totalCount) => {
          resolve(totalCount)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  collectionIds (query: Query): Promise<string[]>  {
    return new Promise((resolve, reject) => {
      this.datasource.collectionIds(DatasourceQuery.fromJson(query.toJson()))
        .then((collectionIds) => {
          resolve(collectionIds)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
