import 'reflect-metadata'
import TargetEntityType from 'domain/value_objects/target_entity_type'

import Like from 'domain/models/like'

import type LikeDatasource from 'data/datasources/interfaces/like_datasource'
import { LikeJson } from 'data/jsons/like_json'
import LikeRemoteDatasource from 'data/datasources/remote/like_remote_datasource'

export default class LikeRepository {

  private datasource:LikeDatasource

  constructor (){
    this.datasource = new LikeRemoteDatasource()
  }

  create (targetEntityType:TargetEntityType, targetEntityId:string): Promise<Like> {
    return new Promise((resolve, reject) => {
      this.datasource.create(targetEntityType.getName(),targetEntityId)
        .then((json:LikeJson) => {
          resolve(Like.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  delete (targetEntityType:TargetEntityType, targetEntityId:string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.datasource.delete(targetEntityType.getName(),targetEntityId)
        .then((deleteBoolean) => {
          resolve(deleteBoolean)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
}
