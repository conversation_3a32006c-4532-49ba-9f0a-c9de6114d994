import 'reflect-metadata'

import type UserPublicAddressDatasource from 'data/datasources/interfaces/user_public_address_datasource'
import UserPublicAddressRemoteDatasource from 'data/datasources/remote/user_public_address_remote_datasource'

export default class UserPublicAddressRepository {

  private datasource:UserPublicAddressDatasource

  constructor (){
    this.datasource = new UserPublicAddressRemoteDatasource()
  }

  search (): Promise<string[]> {
    return new Promise((resolve, reject) => {
      this.datasource.search()
        .then((publicAddresses) => {
          resolve(publicAddresses)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  create (publicAddress:string, signature:string): Promise<string> {
    return new Promise((resolve, reject) => {
      this.datasource.create(publicAddress, signature)
        .then((createdPublicAddress) => {
          resolve(createdPublicAddress)
        }
        )
        .catch((e) => {
          reject(e)
        })
    })
  }

  delete (publicAddress:string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.datasource.delete(publicAddress)
        .then((deletePublicAddress) => {
          resolve(deletePublicAddress)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
}
