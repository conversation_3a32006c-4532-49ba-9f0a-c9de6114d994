import 'reflect-metadata'

import { Query } from 'domain/repositories/listing_repository/query'
import Listing from 'domain/models/listing'
import ListingOrder from '../../domain/models/listing_order'

import type ListingDatasource from 'data/datasources/interfaces/listing_datasource'
import { Query as DatasourceQuery } from 'data/datasources/interfaces/listing_datasource/query'
import { QueryOrder } from 'data/datasources/interfaces/listing_datasource/query_order'
import { ListingJson } from 'data/jsons/listing_json'
import { ListingOrderJson } from 'data/jsons/listing/listing_order_json'
import { ListingCreateOrderSlashJson } from 'data/jsons/listing/listing_create_order_slash_json'
import { ListingOrderUpdateJson } from 'data/jsons/listing/listing_update_order_json'
import ListingRemoteDatasource from 'data/datasources/remote/listing_remote_datasource'

export default class ListingRepository {

  private datasource:ListingDatasource

  constructor (){
    this.datasource = new ListingRemoteDatasource()
  }

  findById (id: string): Promise<Listing> {
    return new Promise((resolve, reject) => {
      this.datasource.findById(id)
        .then((json:ListingJson) => {
          resolve(Listing.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  search (query: Query): Promise<Listing[]> {
    return new Promise((resolve, reject) => {
      this.datasource.search(DatasourceQuery.fromJson(query.toJson()))
        .then((jsons:ListingJson[]) => {
          resolve(jsons.map((json) => { return Listing.fromJson(json) }))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    return new Promise((resolve, reject) => {
      this.datasource.totalCount(DatasourceQuery.fromJson(query.toJson()))
        .then((totalCount) => {
          resolve(totalCount)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  save (listing:Listing): Promise<Listing> {

    if (listing.id === undefined) {
      return new Promise((resolve, reject) => {
        this.datasource.create(listing.toCreateJson())
          .then((json) => {
            resolve(Listing.fromJson(json))
          })
          .catch((e) => {
            reject(e)
          })
      })
    }

    return new Promise((resolve, reject) => {
      this.datasource.update(listing.toUpdateJson())
        .then((json) => {
          resolve(Listing.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  createOrderSlash (params: ListingCreateOrderSlashJson): Promise<string> {
    return new Promise((resolve, reject) => {
      this.datasource.createOrderSlash(params)
        .then((orderPaymentUrl:string) => {
          resolve(orderPaymentUrl)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  findAssetIdByPaymentToken (id: string): Promise<ListingOrder> {
    return new Promise((resolve, reject) => {
      this.datasource.findAssetIdByPaymentToken(id)
        .then((json: ListingOrderJson) => {
          resolve(ListingOrder.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  searchOrder (query: QueryOrder): Promise<ListingOrder[]> {
    return new Promise((resolve, reject) => {
      this.datasource.searchOrder(query)
        .then((jsons:ListingOrderJson[]) => {
          resolve(jsons.map((json) => { return ListingOrder.fromJson(json) }))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCountOrder (query: QueryOrder): Promise<number> {
    return new Promise((resolve, reject) => {
      this.datasource.totalCountOrder(query)
        .then((totalCount) => {
          resolve(totalCount)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  getSignatureClaimNft (id: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.datasource.getSignatureClaimNft(id)
        .then((data: any) => {
          resolve(data)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
  
  getSignatureRefundToken (id: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.datasource.getSignatureRefundToken(id)
        .then((data: any) => {
          resolve(data)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  updateOrder (params: ListingOrderUpdateJson): Promise<ListingOrder> {
    return new Promise((resolve, reject) => {
      this.datasource.updateOrder(params)
        .then((json: ListingOrderJson) => {
          resolve(ListingOrder.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
}
