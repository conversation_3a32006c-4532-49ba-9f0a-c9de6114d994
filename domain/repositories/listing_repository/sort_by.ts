import Base from 'domain/value_objects/base'

export default class SortBy extends Base {

  public static getResourceArray () {
    return [
      {
        id: 1,
        name: 'recentlyListed',
        label: 'Recently Listed',
      },
      {
        id: 2,
        name: 'recentlyCreated',
        label: 'Recently Created',
      },
      {
        id: 3,
        name: 'priceLowToHigh',
        label: 'Price: Low to High',
      },
      {
        id: 4,
        name: 'priceHighToLow',
        label: 'Price: High to Low',
      },
      {
        id: 5,
        name: 'endingSoon',
        label: 'Ending Soon',
      },
      {
        id: 9,
        name: 'oldest',
        label: 'Oldest',
      },
    ]
  }

  public static recentlyListed (): SortBy {
    return this.fromName<SortBy>('recentlyListed')
  }

  public static recentlyCreated (): SortBy {
    return this.fromName<SortBy>('recentlyCreated')
  }

  public static priceLowToHigh (): SortBy {
    return this.fromName<SortBy>('priceLowToHigh')
  }

  public static priceHighToLow (): SortBy {
    return this.fromName<SortBy>('priceHighToLow')
  }

  public static endingSoon (): SortBy {
    return this.fromName<SortBy>('endingSoon')
  }

  public static oldest (): SortBy {
    return this.fromName<SortBy>('oldest')
  }

  public isRecentlyListed (): boolean {
    return (this.getName() === 'recentlyListed')
  }

  public isRecentlyCreated (): boolean {
    return (this.getName() === 'recentlyCreated')
  }

  public isPriceLowToHigh (): boolean {
    return (this.getName() === 'priceLowToHigh')
  }

  public isPriceHighToLow (): boolean {
    return (this.getName() === 'priceHighToLow')
  }

  public isEndingSoon (): boolean {
    return (this.getName() === 'endingSoon')
  }

  public isOldest (): boolean {
    return (this.getName() === 'oldest')
  }

}