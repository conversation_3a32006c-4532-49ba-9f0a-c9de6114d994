export class Query {
  
  public limit: number = 10
  public page: number = 1
  public sort: string = 'created_at:desc'
  public keywords?: string
  public enable?: boolean
  public minCreatedAt?: string
  public maxCreatedAt?: string
  public minUpdatedAt?: string
  public maxUpdatedAt?: string

  public static fromJson (json?: {[key:string]:any}) {
    return new this(json)
  }

  constructor (options?: {[key:string]:any}) {
    if (options === undefined) {
      return
    }

    if (options['limit'] != undefined) {
      this.limit = Math.min(options['limit'], 60) // Max 60 as per API spec
    }
    if (options['page'] != undefined) {
      this.page = options['page']
    }
    if (options['sort'] != undefined) {
      this.sort = options['sort']
    }
    if (options['keywords'] != undefined) {
      this.keywords = options['keywords']
    }
    if (options['enable'] != undefined) {
      this.enable = options['enable']
    }
    if (options['minCreatedAt'] != undefined) {
      this.minCreatedAt = options['minCreatedAt']
    }
    if (options['maxCreatedAt'] != undefined) {
      this.maxCreatedAt = options['maxCreatedAt']
    }
    if (options['minUpdatedAt'] != undefined) {
      this.minUpdatedAt = options['minUpdatedAt']
    }
    if (options['maxUpdatedAt'] != undefined) {
      this.maxUpdatedAt = options['maxUpdatedAt']
    }
  }

  public toJson (): {[key:string]:any} {
    const params: {[key:string]:any} = {
      limit: this.limit,
      page: this.page,
      sort: this.sort,
    }

    if (this.keywords !== undefined) {
      params.keywords = this.keywords
    }
    if (this.enable !== undefined) {
      params.enable = this.enable
    }
    if (this.minCreatedAt !== undefined) {
      params.minCreatedAt = this.minCreatedAt
    }
    if (this.maxCreatedAt !== undefined) {
      params.maxCreatedAt = this.maxCreatedAt
    }
    if (this.minUpdatedAt !== undefined) {
      params.minUpdatedAt = this.minUpdatedAt
    }
    if (this.maxUpdatedAt !== undefined) {
      params.maxUpdatedAt = this.maxUpdatedAt
    }

    return params
  }
}
