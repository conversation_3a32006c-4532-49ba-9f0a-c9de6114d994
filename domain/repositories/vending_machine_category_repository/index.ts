import 'reflect-metadata'

import VendingMachineCategory from 'domain/models/vending_machine_category'
import { Query } from 'domain/repositories/vending_machine_category_repository/query'
import { VendingMachineCategoryListType } from 'domain/repositories/vending_machine_category_repository/list_category_type'

import VendingMachineCategoryDatasource, { type CreateCategoryParams, type UpdateCategoryParams } from 'data/datasources/interfaces/vending_machine_category_datasource'
import VendingMachineCategoryRemoteDatasource from 'data/datasources/remote/vending_machine_category_remote_datasource'
import { VendingMachineCategoryJson, VendingMachineCategoryListJson } from 'data/jsons/vending-machine/vending_machine_category_json'

export default class VendingMachineCategoryRepository {

  private datasource: VendingMachineCategoryDatasource

  constructor () {
    this.datasource = new VendingMachineCategoryRemoteDatasource()
  }

  list (query: Query): Promise<VendingMachineCategoryListType> {
    return new Promise((resolve, reject) => {
      this.datasource.list(query)
        .then((jsons: VendingMachineCategoryListJson) => {
          const data = {
            ...jsons,
            items: jsons.items.map((json: VendingMachineCategoryJson) => { 
              return VendingMachineCategory.fromJson(json) 
            }),
          }
          resolve(data)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  getById (id: string): Promise<VendingMachineCategory> {
    return new Promise((resolve, reject) => {
      this.datasource.getById(id)
        .then((json: VendingMachineCategoryJson) => {
          resolve(VendingMachineCategory.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  create (params: CreateCategoryParams): Promise<VendingMachineCategory> {
    return new Promise((resolve, reject) => {
      this.datasource.create(params)
        .then((json: VendingMachineCategoryJson) => {
          resolve(VendingMachineCategory.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  update (id: number, params: UpdateCategoryParams): Promise<VendingMachineCategory> {
    return new Promise((resolve, reject) => {
      this.datasource.update(id, params)
        .then((json: VendingMachineCategoryJson) => {
          resolve(VendingMachineCategory.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  delete (id: number): Promise<void> {
    return new Promise((resolve, reject) => {
      this.datasource.delete(id)
        .then(() => {
          resolve()
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
}
