import 'reflect-metadata'

import Asset from 'domain/models/asset'
import { Query } from 'domain/repositories/asset_ranking/query'

import type AssetRankingDatasource from 'data/datasources/interfaces/asset_ranking_datasource'
import { Query as DatasourceQuery } from 'data/datasources/interfaces/asset_ranking_datasource/query'
import AssetRankingRemoteDatasource from 'data/datasources/remote/asset_ranking_remote_datasource'
import { AssetJson } from 'data/jsons/asset_json'

export default class AssetRankingRepository {
  private datasource:AssetRankingDatasource

  constructor (){
    this.datasource = new AssetRankingRemoteDatasource()
  }

  search (query: Query): Promise<Asset[]> {
    return new Promise((resolve, reject) => {
      this.datasource.search(DatasourceQuery.fromJson(query.toJson()))
        .then((jsons:AssetJson[]) => {
          resolve(jsons.map((json) => { return Asset.fromJson(json) }))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    return new Promise((resolve, reject) => {
      this.datasource.totalCount(DatasourceQuery.fromJson(query.toJson()))
        .then((totalCount) => {
          resolve(totalCount)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
} 