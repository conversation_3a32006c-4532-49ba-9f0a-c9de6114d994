import 'reflect-metadata'

import { Query } from 'domain/repositories/asset_daily_stat_repository/query'
import AssetDailyStat from 'domain/models/asset_daily_stat'

import type AssetDailyStatDatasource from 'data/datasources/interfaces/asset_daily_stat_datasource'
import { Query as DatasourceQuery } from 'data/datasources/interfaces/asset_daily_stat_datasource/query'
import { AssetDailyStatJson } from 'data/jsons/asset_daily_stat_json'
import AssetDailyStatRemoteDatasource from 'data/datasources/remote/asset_daily_stat_remote_datasource'

export default class AssetDailyStatRepository {

  private datasource:AssetDailyStatDatasource

  constructor () {
    this.datasource = new AssetDailyStatRemoteDatasource()
  }

  findById (id: string): Promise<AssetDailyStat> {
    return new Promise((resolve, reject) => {
      this.datasource.findById(id)
        .then((json:AssetDailyStatJson) => {
          resolve(AssetDailyStat.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  search (query: Query): Promise<AssetDailyStat[]> {
    console.log('🚀 ~ AssetDailyStatRepository ~ search ~ query:', query)
    return new Promise((resolve, reject) => {
      this.datasource.search(DatasourceQuery.fromJson(query.toJson()))
        .then((jsons:any) => {
          console.log('🚀 ~ AssetDailyStatRepository ~ .then ~ jsons:', jsons)
          resolve(jsons)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    return new Promise((resolve, reject) => {
      this.datasource.totalCount(DatasourceQuery.fromJson(query.toJson()))
        .then((totalCount) => {
          resolve(totalCount)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
