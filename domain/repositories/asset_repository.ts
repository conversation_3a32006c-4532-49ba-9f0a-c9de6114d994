import Chain from 'domain/value_objects/chain'
import 'reflect-metadata'

import Asset from 'domain/models/asset'
import { Query } from 'domain/repositories/asset_repository/query'

import type AssetDatasource from 'data/datasources/interfaces/asset_datasource'
import { Query as DatasourceQuery } from 'data/datasources/interfaces/asset_datasource/query'
import AssetRemoteDatasource from 'data/datasources/remote/asset_remote_datasource'
import { AssetJson } from 'data/jsons/asset_json'
import { AssetsRefreshMetadata } from 'data/jsons/assets/assets_refresh_metadata_json'

export default class AssetRepository {

  private datasource:AssetDatasource

  constructor (){
    this.datasource = new AssetRemoteDatasource()
  }

  findById (id:string): Promise<Asset> {
    return new Promise((resolve, reject) => {
      this.datasource.findById(id)
        .then((json:AssetJson) => {
          resolve(Asset.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  findByChainAndAddressAndTokenId (chain:Chain, address:string, tokenId:string): Promise<Asset> {
    return new Promise((resolve, reject) => {
      this.datasource.findByChainAndAddressAndTokenId(chain.getName(),address,tokenId)
        .then((json:AssetJson) => {
          resolve(Asset.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  search (query: Query): Promise<Asset[]> {
    return new Promise((resolve, reject) => {
      this.datasource.search(DatasourceQuery.fromJson(query.toJson()))
        .then((jsons:AssetJson[]) => {
          resolve(jsons.map((json) => {
            return Asset.fromJson(json)
          }))
        })
        .catch((e) => {
          console.log('🚀 ~ AssetRepository ~ returnnewPromise ~ e:', e)
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    return new Promise((resolve, reject) => {
      this.datasource.totalCount(DatasourceQuery.fromJson(query.toJson()))
        .then( (totalCount) => {
          resolve(totalCount)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
  
  createMetadata (assets: Asset): Promise<any> {
    return new Promise((resolve, reject) => {
      let covertMetadataInput = Asset.toCreateMetadataJson(assets)
      this.datasource.createMetadata(covertMetadataInput)
        .then( (res:any) => {
          resolve(res)
        })
        .catch((e:any) => {
          reject(e)
        })
    })
  }
  
  create (assets: Asset): Promise<any> {
    return new Promise((resolve, reject) => {
      let covertInput = Asset.toCreateJson(assets)
      this.datasource.create(covertInput)
        .then( (json:AssetJson) => {
          resolve(Asset.fromJson(json))
        })
        .catch((e:any) => {
          reject(e)
        })
    })
  }
  
  checkOwnership (id: string, userAddress: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.datasource.checkOwnership(id, userAddress)
        .then( (data: any) => {
          resolve(data)
        })
        .catch((e:any) => {
          resolve(false)
        })
    })
  }
  
  refreshMetadata (params: AssetsRefreshMetadata): Promise<any> {
    return new Promise((resolve, reject) => {
      this.datasource.refreshMetadata(params)
        .then( (data: any) => {
          resolve(data)
        })
        .catch((e:any) => {
          reject(false)
        })
    })
  }
}
