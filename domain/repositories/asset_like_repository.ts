import 'reflect-metadata'

import { Query } from 'domain/repositories/asset_like_repository/query'
import AssetLike from 'domain/models/asset_like'

import type AssetLikeDatasource from 'data/datasources/interfaces/asset_like_datasource'
import { Query as DatasourceQuery } from 'data/datasources/interfaces/asset_like_datasource/query'
import { AssetLikeJson } from 'data/jsons/asset_like_json'
import AssetLikeRemoteDatasource from 'data/datasources/remote/asset_like_remote_datasource'

export default class AssetLikeRepository {

  private datasource:AssetLikeDatasource

  constructor () {
    this.datasource = new AssetLikeRemoteDatasource()
  }

  search (query: Query): Promise<AssetLike[]> {
    return new Promise((resolve, reject) => {
      this.datasource.search(DatasourceQuery.fromJson(query.toJson()))
        .then((jsons:AssetLikeJson[]) => {
          resolve(jsons.map((json) => {
            return AssetLike.fromJson(json)
          }))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }


  totalCount (query: Query): Promise<number> {
    return new Promise((resolve, reject) => {
      this.datasource.totalCount(DatasourceQuery.fromJson(query.toJson()))
        .then((totalCount) => {
          resolve(totalCount)
        })
        .catch( (e)=> {
          reject(e)
        })
    })
  }


  assetIds (query: Query): Promise<string[]> {
    return new Promise((resolve, reject) => {
      this.datasource.assetIds(DatasourceQuery.fromJson(query.toJson()))
        .then((assetIds) => {
          resolve(assetIds)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
