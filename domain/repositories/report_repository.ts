import 'reflect-metadata'

import Asset from 'domain/models/asset'
import Collection from 'domain/models/collection'
import Report from 'domain/models/report'
import AssetReason from 'domain/models/report/asset_reason'
import CollectionReason from 'domain/models/report/collection_reason'

import type ReportDatasource from 'data/datasources/interfaces/report_datasource'
import TargetEntityType from 'domain/value_objects/target_entity_type'
import ReportRemoteDatasource from 'data/datasources/remote/report_remote_datasource'

export default class ReportRepository {

  private datasource:ReportDatasource

  constructor (){
    this.datasource = new ReportRemoteDatasource()
  }

  createAssetReport (asset:Asset, reason:AssetReason): Promise<Report> {
    if (asset.id === undefined) {
      throw new Error('asset.id must be specified to create report')
    }

    return new Promise((resolve, reject) => {
      this.datasource.create(
        TargetEntityType.asset().getName(),
                asset.id!,
                reason.getId()
      )
        .then((json) => {
          resolve(Report.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  createCollectionReport (collection:Collection, reason:CollectionReason): Promise<Report> {
    if (collection.id === undefined) {
      throw new Error('collection.id must be specified to create report')
    }

    return new Promise((resolve, reject) => {
      this.datasource.create(
        TargetEntityType.collection().getName(),
                collection.id!,
                reason.getId()
      )
        .then((json) => {
          resolve(Report.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
