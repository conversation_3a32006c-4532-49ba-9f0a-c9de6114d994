import 'reflect-metadata'

import { Query } from 'domain/repositories/category_repository/query'
import Category from 'domain/models/category'

import type CategoryDatasource from 'data/datasources/interfaces/category_datasource'
import { Query as DatasourceQuery } from 'data/datasources/interfaces/category_datasource/query'
import { CategoryJson } from 'data/jsons/category_json'
import CategoryRemoteDatasource from 'data/datasources/remote/category_remote_datasource'

export default class CategoryRepository {

  private datasource:CategoryDatasource

  constructor () {
    this.datasource = new CategoryRemoteDatasource()
  }

  findById (id: string): Promise<Category> {
    return new Promise((resolve, reject) => {
      this.datasource.findById(id)
        .then((json:CategoryJson) => {
          resolve(Category.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  findBySlug (slug: string): Promise<Category> {
    return new Promise((resolve, reject) => {
      this.datasource.findBySlug(slug)
        .then((json:CategoryJson) => {
          resolve(Category.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  search (query: Query): Promise<Category[]> {
    return new Promise((resolve, reject) => {
      this.datasource.search(DatasourceQuery.fromJson(query.toJson()))
        .then((jsons:CategoryJson[]) => {
          resolve(jsons.map((json) => { return Category.fromJson(json) }))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    return new Promise((resolve, reject) => {
      this.datasource.totalCount(DatasourceQuery.fromJson(query.toJson()))
        .then((totalCount) => {
          resolve(totalCount)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
