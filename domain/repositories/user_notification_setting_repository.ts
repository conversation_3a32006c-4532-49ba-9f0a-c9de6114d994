import 'reflect-metadata'

import UserNotificationSetting from 'domain/models/user_notification_setting'

import type UserNotificationSettingDatasource from 'data/datasources/interfaces/user_notification_setting_datasource'
import { UserNotificationSettingJson } from 'data/jsons/user_notification_setting_json'
import UserNotificationSettingRemoteDatasource from 'data/datasources/remote/user_notification_setting_remote_datasource'

export default class UserNotificationSettingRepository {

  private datasource:UserNotificationSettingDatasource

  constructor (){
    this.datasource = new UserNotificationSettingRemoteDatasource()
  }

  mine (): Promise<UserNotificationSetting> {
    return new Promise((resolve, reject) => {
      this.datasource.mine()
        .then((json:UserNotificationSettingJson) => {
          resolve(UserNotificationSetting.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  update (setting:UserNotificationSetting): Promise<UserNotificationSetting> {
    return new Promise((resolve, reject) => {
      this.datasource.update(setting.toUpdateJson())
        .then((json:UserNotificationSettingJson) => {
          resolve(UserNotificationSetting.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
}
