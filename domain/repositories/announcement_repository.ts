import 'reflect-metadata'

import { Query } from 'domain/repositories/announcement_repository/query'
import Announcement from 'domain/models/announcement'

import type AnnouncementDatasource from 'data/datasources/interfaces/announcement_datasource'
import { Query as DatasourceQuery } from 'data/datasources/interfaces/announcement_datasource/query'
import { AnnouncementJson } from 'data/jsons/announcement_json'
import AnnouncementRemoteDatasource from 'data/datasources/remote/announcement_remote_datasource'

export default class AnnouncementRepository {

  private datasource:AnnouncementDatasource

  constructor (){
    this.datasource = new AnnouncementRemoteDatasource()
  }

  findById (id: string): Promise<Announcement> {
    return new Promise((resolve, reject) => {
      this.datasource.findById(id)
        .then((json:AnnouncementJson) => {
          resolve(Announcement.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  search (query: Query): Promise<Announcement[]> {
    return new Promise((resolve, reject) => {
      this.datasource.search(DatasourceQuery.fromJson(query.toJson()))
        .then((jsons:AnnouncementJson[]) => {
          resolve(jsons.map((json) => { return Announcement.fromJson(json) }))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    return new Promise((resolve, reject) => {
      this.datasource.totalCount(DatasourceQuery.fromJson(query.toJson()))
        .then((totalCount) => {
          resolve(totalCount)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

}
