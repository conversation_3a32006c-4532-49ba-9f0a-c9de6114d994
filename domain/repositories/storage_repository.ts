import 'reflect-metadata'
import LocalStorageDatasource from 'data/datasources/local/local_storage_datasource'

export default class StorageRepository {
  private localStorageDatasource = new LocalStorageDatasource()
  
  constructor (private keyPrefix: string = 'store') {}

  setKeyPrefix (prefix: string): void {
    this.keyPrefix = prefix
  }

  _getKey (key: string): string {
    return `${this.keyPrefix}_${key}`
  }

  getValue (key: string): string | null {
    return this.localStorageDatasource.read(this._getKey(key))
  }
  setValue (key: string, value: string): void {
    return this.localStorageDatasource.write(this._getKey(key), value)
  }
  clearValue (key: string): void {
    return this.localStorageDatasource.delete(this._getKey(key))
  }
  clearAll (): void {
    return this.localStorageDatasource.deleteAll()
  }
}
