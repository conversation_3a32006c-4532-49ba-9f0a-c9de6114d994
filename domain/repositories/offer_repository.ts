import 'reflect-metadata'

import { Query } from 'domain/repositories/offer_repository/query'
import Offer from 'domain/models/offer'

import type OfferDatasource from 'data/datasources/interfaces/offer_datasource'
import { Query as DatasourceQuery } from 'data/datasources/interfaces/offer_datasource/query'
import { OfferJson } from 'data/jsons/offer_json'
import OfferRemoteDatasource from 'data/datasources/remote/offer_remote_datasource'

export default class OfferRepository {

  private datasource:OfferDatasource

  constructor (){
    this.datasource = new OfferRemoteDatasource()
  }

  findById (id: string): Promise<Offer> {
    return new Promise((resolve, reject) => {
      this.datasource.findById(id)
        .then((json:OfferJson) => {
          resolve(Offer.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  search (query: Query): Promise<Offer[]> {
    return new Promise((resolve, reject) => {
      this.datasource.search(DatasourceQuery.fromJson(query.toJson()))
        .then((jsons:OfferJson[]) => {
          resolve(jsons.map((json) => { return Offer.fromJson(json) }))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  totalCount (query: Query): Promise<number> {
    return new Promise((resolve, reject) => {
      this.datasource.totalCount(DatasourceQuery.fromJson(query.toJson()))
        .then((totalCount) => {
          resolve(totalCount)
        })
        .catch((e) => {
          reject(e)
        })
    })
  }

  save (offer: Offer): Promise<Offer> {
    if (offer.id === undefined) {
      return new Promise((resolve, reject) => {
        this.datasource.create(offer.toCreateJson())
          .then((json) => {
            resolve(Offer.fromJson(json))
          })
          .catch((e) => {
            reject(e)
          })
      })
    }

    return new Promise((resolve, reject) => {
      this.datasource.update(offer.toUpdateJson())
        .then((json) => {
          resolve(Offer.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
  
  cancel (offer: Offer): Promise<Offer> {
    return new Promise((resolve, reject) => {
      this.datasource.cancel(offer.toUpdateJson())
        .then((json) => {
          resolve(Offer.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
  
  accept (offer: Offer): Promise<Offer> {
    return new Promise((resolve, reject) => {
      this.datasource.accept(offer.toAccept())
        .then((json) => {
          resolve(Offer.fromJson(json))
        })
        .catch((e) => {
          reject(e)
        })
    })
  }
}
