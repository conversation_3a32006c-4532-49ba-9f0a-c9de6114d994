import { Schema } from 'domain/models/contract/schema'
import Chain from 'domain/value_objects/chain'

export class Query {

  public chain: Chain|null = null
  public schema: Schema|null = null
  public address: string|null = null
  public collectionId: string|null = null

  public limit:number = 60
  public page:number = 1

  public static fromOptions (options?:{[key:string]:any}) {
    return new this(options)
  }

  constructor (options?:{[key:string]:any}) {
    if (options === undefined) {
      return
    }
    if (options['chain'] !== undefined) {
      this.chain = options['chain'] as Chain
    }
    if (options['schema'] !== undefined) {
      this.schema = options['schema'] as Schema
    }
    if (options['address'] !== undefined) {
      this.address = options['address']
    }
    if (options['collectionId'] !== undefined) {
      this.collectionId = options['collectionId']
    }

    if (options['limit'] != undefined) {
      this.limit = options['limit']
    }
    if (options['page'] != undefined) {
      this.page = options['page']
    }
  }

  public toJson ():{[key:string]:any} {
    return {
      chain: (this.chain !== null) ? this.chain.getName() : null,
      schema: (this.schema !== null) ? this.schema.getName() : null,
      address: this.address,
      collectionId: this.collectionId,
      limit: this.limit,
      page: this.page,
    }
  }
}
