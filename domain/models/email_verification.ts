import { EmailVerificationJson } from 'data/jsons/email_verification_json'
import { DateTime } from 'luxon'
import { BaseModelInterface } from './interfaces/base_model'

export default class EmailVerification implements BaseModelInterface {

  public static fromJson (json:EmailVerificationJson): EmailVerification {

    return new this(
      json.id,
      json.userId,
      json.token,
      json.verificationUrl,
      json.email,
      (json.expiresAt !== null) ? DateTime.fromISO(json.expiresAt) : null,
      DateTime.fromISO(json.createdAt),
      DateTime.fromISO(json.updatedAt)
    )
  }

  constructor (
    public id: string|undefined,
    public userId: string,
    public token: string,
    public verificationUrl: string,
    public email: string,
    public expiresAt: DateTime|null,
    public createdAt: DateTime,
    public updatedAt: DateTime
  ){}

}
