import { UserUpdateJson } from 'data/jsons/user/user_update_json'
import { UserJson } from 'data/jsons/user_json'
import { Language } from 'domain/value_objects/language'
import { DateTime } from 'luxon'
import { BaseModelInterface } from './interfaces/base_model'
import Multimedia from './multimedia'
import { envConfig } from 'presentation/constant/env'

export default class User implements BaseModelInterface {

  public static fromJson (json:UserJson): User {
    if (json.language === undefined) {
      throw new Error('language is undefined')
    }
    const language = json.language.id ? Language.fromId<Language>(json.language.id) : json.language
    if (language.isUnknown()) {
      throw new Error(`LanguageId is invalid: ${json.language.id}`)
    }
    let isLinkWallet = true
    if(json?.internalWallet && json?.publicAddresses?.length) {
      isLinkWallet = json.internalWallet.toLocaleLowerCase() !== json?.publicAddresses[0].toLocaleLowerCase()
    }

    return new this(
      json.id,
      json.username,
      json.email,
      (json.thumbnail !== null) ? Multimedia.fromJson(json.thumbnail) : null,
      (json.background !== null) ? Multimedia.fromJson(json.background) : null,
      json.description,
      json.twitterUsername,
      json.instagramUsername,
      json.webUrl,
      language,
      (json.lastAccessedAt !== null) ? DateTime.fromISO(json.lastAccessedAt) : null,
      json.publicAddresses,
      DateTime.fromISO(json.createdAt),
      DateTime.fromISO(json.updatedAt),
      json.receivePercent, // TODO
      json.roleId,
      json.internalWallet,
      isLinkWallet,
      json?.nonce ? Number(json?.nonce) : 0,
      json.roleId === envConfig.userRole.operator
    )
  }

  constructor (
    public id: string|undefined,
    public username: string,
    public email: string|null,
    public thumbnail: Multimedia|null,
    public background: Multimedia|null,
    public description: string|null,
    public twitterUsername: string|null,
    public instagramUsername: string|null,
    public webUrl: string|null,
    public language: Language,
    public lastAccessedAt: DateTime|null,
    public publicAddresses: string[],
    public createdAt: DateTime,
    public updatedAt: DateTime,
    public receivePercent?: number, // TODO
    public roleId?: number,
    public internalWallet?: string,
    public isLinkWallet?: boolean,
    public nonce?: number,
    public isOperator?: boolean,
  ){}

  public toUpdateJson (): UserUpdateJson {
    if (this.id === undefined) {
      throw new Error('id must be specified to update')
    }

    return {
      id: this.id,
      username: this.username,
      description: this.description,
      thumbnailId: this.thumbnail?.id ?? null,
      backgroundId: this.background?.id ?? null,
      twitterUsername: this.twitterUsername,
      instagramUsername: this.instagramUsername,
      webUrl: this.webUrl,
    }
  }
}
