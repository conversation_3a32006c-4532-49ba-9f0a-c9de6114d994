import { CollectionCreateJson } from 'data/jsons/collection/collection_create_json'
import { CollectionUpdateJson } from 'data/jsons/collection/collection_update_json'
import { CollectionJson } from 'data/jsons/collection_json'
import PaymentToken from 'domain/models/payment_tokens'
import { DateTime } from 'luxon'
import Category from './category'
import { BaseEditableModelInterface } from './interfaces/base_model'
import Multimedia from './multimedia'
import User from './user'

export default class Collection implements BaseEditableModelInterface {
  public static fromJson (json: CollectionJson): Collection {
    try { 
      const paymentTokens: PaymentToken[] = []
    
      for (const paymentTokenJson of json.paymentTokens) {
        if (paymentTokenJson.id === undefined) {
          throw new Error('paymentToken.id is undefined')
        }
        // paymentTokenJson.label = paymentTokenJson.name
        paymentTokens.push(PaymentToken.fromJson(paymentTokenJson))
      }
      return new this(
        json.id,
        json.slug,
        json.name,
        json.symbol,
        json.description,
        json.webUrl,
        json.discordUrl,
        json.telegramUrl,
        json.twitterUrl,
        json.mediumUsername,
        json.twitterUsername,
        json.instagramUsername,
        json.isExplicit,
        json.numAssets,
        json.numOwners,
        json.totalVolumeUsd,
        json.totalVolumeJpy,
        json.floorPriceUsd,
        json.floorPriceJpy,
        DateTime.fromISO(json.createdAt),
        DateTime.fromISO(json.updatedAt),
        json.logoImage ? Multimedia.fromJson(json.logoImage) : null,
        json.featuredImage 
          ? Multimedia.fromJson(json.featuredImage)
          : null,
        json.bannerImage ? Multimedia.fromJson(json.bannerImage) : null,
        json.category ? Category.fromJson(json.category) : null,
        User.fromJson(json.owner),
        paymentTokens,
        json.payoutAddresses,
        json.collaborators.map((collaborator) => {
          return User.fromJson(collaborator)
        }),
        json.contractAddress,
        json.payouts,
        json.chainId,
        json.schemaId,
        json.contract,
        json.recommendationImage ? Multimedia.fromJson(json.recommendationImage) : null,
        json.traitTypes
      )
    } catch (error) {
      console.log('🚀 ~ Collection ~ fromJson ~ error:', error)
      throw new Error('Error while creating collection')
    }
     
  }

  constructor (
    public id: string | undefined = undefined,
    public slug: string,
    public name: string | null,
    public symbol: string | null,
    public description: string | null,
    public webUrl: string | null,
    public discordUrl: string | null,
    public telegramUrl: string | null,
    public twitterUrl: string | null,
    public mediumUsername: string | null,
    public twitterUsername: string | null,
    public instagramUsername: string | null,
    public isExplicit: boolean,
    public numAssets: number,
    public numOwners: number,
    public totalVolumeUsd: number | null,
    public totalVolumeJpy: number | null,
    public floorPriceUsd: number | null,
    public floorPriceJpy: number | null,
    public createdAt: DateTime,
    public updatedAt: DateTime,
    public logoImage: Multimedia | null,
    public featuredImage: Multimedia | null,
    public bannerImage: Multimedia | null,
    public category: Category | null | any,
    public owner: User,
    public paymentTokens: any,
    public payoutAddresses: string[],
    public collaborators: User[],
    public collectionAddress: string | null = null,
    public owners: User[] | any[],//payouts
    public chainId: number,
    public schemaId: number,
    public contract: any,
    public recommendationImage: Multimedia | null,
    public traitTypes: any[]
  ) {}

  public toCreateJson (): CollectionCreateJson {
    if (this.name === null) {
      throw new Error('name must be specified to create')
    }
    if(this.slug === null) {
      throw new Error('slug must be specified to create')
    }
    if(this.logoImage === null) {
      throw new Error('logoImageId must be specified to create')
    }
    let categoryId = ''
    if(typeof this.category === 'object') {
      categoryId = this.category.id
    }
    if(Array.isArray(this.category)) {
      categoryId = this.category?.map((category : any) => {
        return category.id
      })[0] || null
    }
    
    return {
      slug: this.slug,
      name: this.name,
      symbol: this.symbol,
      chainId: this.chainId,
      description: this.description,
      logoImageId:
        this.logoImage !== null && this.logoImage.id !== undefined
          ? this.logoImage.id
          : null,
      featuredImageId:
        this.featuredImage !== null && this.featuredImage.id !== undefined
          ? this.featuredImage.id
          : null,
      bannerImageId:
        this.bannerImage !== null && this.bannerImage.id !== undefined
          ? this.bannerImage.id
          : null,
      categoryId:categoryId,
      webUrl: this.webUrl,
      discordUrl: this.discordUrl,
      telegramUrl: this.telegramUrl,
      twitterUrl: this.twitterUrl,
      mediumUsername: this.mediumUsername,
      twitterUsername: this.twitterUrl, // because user input url
      instagramUsername: this.instagramUsername,
      isExplicit: this.isExplicit,
      collaboratorIds: this.collaborators.map((collaborator) => {
        return collaborator.id!
      }),
      paymentTokenIds: this.paymentTokens,
      payouts: this.owners.map((owner) => {
        return {
          address: owner.publicAddresses && owner.publicAddresses[0],
          percent: owner.receivePercent,
        }
      }),
      contractAddress: this.collectionAddress,
      schemaId: this.schemaId,
    }
  }
  public toCreateJsonForImportContractAddress (): any {

    let categoryId = ''
    if(typeof this.category === 'object') {
      categoryId = this.category.id
    }
    if(Array.isArray(this.category)) {
      categoryId = this.category?.map((category : any) => {
        return category.id
      })[0] || null
    }
    const logoImageUrl: any = this.logoImage || null
    const bannerImageUrl: any = this.bannerImage || null
    return {
      slug: null, // not transfer to server 
      name: this.name || '',
      symbol: this.symbol,
      chainId: this.chainId,
      description: this.description,
      logoImageId: null, // not transfer to server 
      featuredImageId: null, // not transfer to server 
      bannerImageId: null, // not transfer to server 
      categoryId:categoryId,
      webUrl: this.webUrl,
      discordUrl: this.discordUrl,
      telegramUrl: this.telegramUrl,
      mediumUsername: this.mediumUsername,
      twitterUsername: this.twitterUrl,
      instagramUsername: this.instagramUsername,
      isExplicit: this.isExplicit,
      collaboratorIds: this.collaborators?.length > 0 ? this.collaborators.map((collaborator) => {
        return collaborator.id!
      }) : [],
      paymentTokenIds: this.paymentTokens,
      payouts: this.owners?.length > 0 ? this.owners.map((owner) => {
        return {
          address: owner.publicAddresses && owner.publicAddresses[0],
          percent: owner.receivePercent,
        }
      }) : [],
      contractAddress: this.collectionAddress,
      schemaId: this.schemaId,
      logoImageUrl: logoImageUrl,
      bannerImageUrl: bannerImageUrl,
    }
  }
  public toUpdateJson (): CollectionUpdateJson {
    if (this.id === undefined) {
      throw new Error('id must be specified to update')
    }
    if (this.name === null) {
      throw new Error('name must be specified to update')
    }
    let categoryId = ''
    if(typeof this.category === 'object') {
      categoryId = this.category.id
    }
    if(Array.isArray(this.category)) {
      categoryId = this.category?.map((category : any) => {
        return category.id
      })[0] || null
    }
    return {
      id: this.id,
      slug: this.slug,
      name: this.name,
      symbol: this.symbol,
      chainId: this.chainId,
      description: this.description,
      logoImageId:
        this.logoImage !== null && this.logoImage.id !== undefined
          ? this.logoImage.id
          : null,
      featuredImageId:
        this.featuredImage !== null && this.featuredImage.id !== undefined
          ? this.featuredImage.id
          : null,
      bannerImageId:
        this.bannerImage !== null && this.bannerImage.id !== undefined
          ? this.bannerImage.id
          : null,
      categoryId:categoryId,
      webUrl: this.webUrl,
      discordUrl: this.discordUrl,
      telegramUrl: this.telegramUrl,
      twitterUrl: this.twitterUrl,
      mediumUsername: this.mediumUsername,
      twitterUsername: this.twitterUrl,
      instagramUsername: this.instagramUsername,
      isExplicit: this.isExplicit,
      collaboratorIds: this.collaborators.map((collaborator) => {
        return collaborator.id!
      }),
      paymentTokenIds: this.paymentTokens,
      payouts: this.owners.map((owner) => {
        return {
          address: owner.publicAddresses && owner.publicAddresses[0],
          percent: owner.receivePercent,
        }
      }),
      contractAddress: this.collectionAddress,
      schemaId: this.schemaId,
    }
  }
}
