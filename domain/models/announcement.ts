import { AnnouncementJson } from 'data/jsons/announcement_json'
import { DateTime } from 'luxon'
import { Status } from './announcement/status'
import { BaseModelInterface } from './interfaces/base_model'
import Multimedia from './multimedia'

export default class Announcement implements BaseModelInterface {

  public static fromJson (json:AnnouncementJson): Announcement {

    if (json.status.id === undefined) {
      throw new Error('status.id is undefined')
    }

    const status:Status = Status.fromId<Status>(json.status.id)
    if (status.isUnknown()) {
      throw new Error(`status.id is invalid: ${json.status.id}`)
    }

    return new this(
      json.id,
      json.title,
      (json.thumbnail !== null) ? Multimedia.fromJson(json.thumbnail) : null,
      json.body,
      status,
      (json.publishedAt !== null) ? DateTime.fromISO(json.publishedAt) : null,
      DateTime.fromISO(json.createdAt),
      DateTime.fromISO(json.updatedAt)
    )
  }

  constructor (
    public id: string|undefined,
    public title: string|null,
    public thumbnail: Multimedia|null,
    public body: string|null,
    public status: Status,
    public publishedAt: DateTime|null,
    public createdAt: DateTime,
    public updatedAt: DateTime
  ){}

}
