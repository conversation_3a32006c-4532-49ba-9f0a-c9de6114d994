import { CategoryJson } from 'data/jsons/category_json'
import { DateTime } from 'luxon'
import { BaseModelInterface } from './interfaces/base_model'
import Multimedia from './multimedia'

export default class Category implements BaseModelInterface {

  public static fromJson (json:CategoryJson): Category {

    return new this(
      json.id,
      json.slug,
      json.name,
      (json.thumbnail !== null) ? Multimedia.fromJson(json.thumbnail) : null,
      (json.background !== null) ? Multimedia.fromJson(json.background) : null,
      json.description,
      json.order,
      DateTime.fromISO(json.createdAt),
      DateTime.fromISO(json.updatedAt)
    )
  }

  constructor (
    public id: string|undefined,
    public slug: string,
    public name: string,
    public thumbnail: Multimedia|null,
    public background: Multimedia|null,
    public description: string|null,
    public order: number,
    public createdAt: DateTime,
    public updatedAt: DateTime
  ){}

}
