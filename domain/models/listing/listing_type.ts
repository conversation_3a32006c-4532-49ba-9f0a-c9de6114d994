import Base from 'domain/value_objects/base'

export default class ListingType extends Base {

  public static getResourceArray () {
    return [
      {
        id: 1,
        name: 'fixedPrice',
        label: 'Fixed Price',
      },
      {
        id: 2,
        name: 'timedAuction',
        label: 'Timed Auction',
      },
    ]
  }

  public static fixedPrice (): ListingType {
    return this.fromName<ListingType>('fixedPrice')
  }

  public static timedAuction (): ListingType {
    return this.fromName<ListingType>('timedAuction')
  }

  public isFixedPrice (): boolean {
    return (this.getName() === 'fixedPrice')
  }

  public isTimedAuction (): boolean {
    return (this.getName() === 'timedAuction')
  }
}