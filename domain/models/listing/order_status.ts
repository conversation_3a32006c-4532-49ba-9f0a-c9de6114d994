import Base from 'domain/value_objects/base'

export class OrderStatus extends Base {

  public static getResourceArray () {
    return [
      {
        id: 1,
        name: 'initiated',
        label: 'initiated',
      },
      {
        id: 2,
        name: 'success',
        label: 'Success',
      },
      {
        id: 3,
        name: 'failed',
        label: 'Failed',
      },
    ]
  }

  public isActive (): boolean {
    return this.getName() === 'active'
  }
  public isSuccess (): boolean {
    return this.getName() === 'success'
  } 
}

export class Status extends Base {

  public static getResourceArray () {
    return [
      {
        id: 1,
        name: 'new',
        label: 'New',
      },
      {
        id: 2,
        name: 'active',
        label: 'Active',
      },
      {
        id: 3,
        name: 'submitted',
        label: 'Submitted',
      },
      {
        id: 4,
        name: 'finished',
        label: 'Finished',
      },
      {
        id: 5,
        name: 'failed',
        label: 'Failed',
      },
      {
        id: 6,
        name: 'signing',
        label: 'signing',
      },
      {
        id: 7,
        name: 'refunded',
        label: 'Refunded',
      },
      
    ]
  }

  public static finished (): Status {
    return this.fromName<Status>('finished')
  }

  public static refunded (): Status {
    return this.fromName<Status>('refunded')
  }

  public isNew (): boolean {
    return this.getName() === 'new'
  }
  
  public isActive (): boolean {
    return this.getName() === 'active'
  }
  
  public isFinished (): boolean {
    return this.getName() === 'finished'
  }
  
}
