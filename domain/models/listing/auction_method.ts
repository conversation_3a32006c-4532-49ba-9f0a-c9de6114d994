import Base from 'domain/value_objects/base'

export default class AuctionMethod extends Base {

  public static getResourceArray () {
    return [
      {
        id: 1,
        name: 'sellToHighestBidder',
        label: 'Sell to highest bidder',
      },
      {
        id: 2,
        name: 'sellWithDecliningPrice',
        label: 'Sell with declining price',
      },
    ]
  }

  public static sellToHighestBidder (): AuctionMethod {
    return this.fromName<AuctionMethod>('sellToHighestBidder')
  }

  public static sellWithDecliningPrice (): AuctionMethod {
    return this.fromName<AuctionMethod>('sellWithDecliningPrice')
  }

  public isSellToHighestBidder (): boolean {
    return (this.getName() === 'sellToHighestBidder')
  }

  public isSellWithDecliningPrice (): boolean {
    return (this.getName() === 'sellWithDecliningPrice')
  }
}