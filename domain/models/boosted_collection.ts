import { BoostedCollectionJson } from 'data/jsons/boosted_collection_json'
import { DateTime } from 'luxon'
import { BaseModelInterface } from './interfaces/base_model'
import { Status } from './boosted_collection/status'
import Collection from './collection'

export default class BoostedCollection implements BaseModelInterface {

  public static fromJson (json:BoostedCollectionJson): BoostedCollection {

    if (json.status.id === undefined) {
      throw new Error('status.id is undefined')
    }

    const status = Status.fromId<Status>(json.status.id)
    if (status.isUnknown()) {
      throw new Error(`Status is invalid: ${json.status.id}`)
    }

    return new this(
      json.id,
      Collection.fromJson(json.collection),
      status,
      (json.expiresAt !== null) ? DateTime.fromISO(json.expiresAt) : null,
      DateTime.fromISO(json.createdAt),
      DateTime.fromISO(json.updatedAt)
    )
  }

  constructor (
    public id: string|undefined,
    public collection: Collection,
    public status: Status,
    public expiresAt: DateTime|null,
    public createdAt: DateTime,
    public updatedAt: DateTime
  ){}

}
