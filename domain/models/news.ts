export default class News {
  date: string
  description: string
  url?: string

  constructor (date: string, description: string, url?: string) {
    this.date = date
    this.description = description
    this.url = url
  }

  static fromJson (json: any): News {
    return new News(
      json.date,
      json.description,
      json.url
    )
  }

  toJson () {
    return {
      date: this.date,
      description: this.description,
      url: this.url,
    }
  }
}