import { DateTime } from 'luxon'
import { BaseModelInterface } from './interfaces/base_model'
import { CheckoutJson } from 'data/jsons/vending-machine/vending_machine_card_json'
import Chain from '../value_objects/chain'
import { StatusCheckout } from './card_vending_machine/status_checkout'

/* eslint-disable space-before-function-paren */
export default class VendingMachineCheckout implements BaseModelInterface {
  constructor(
    public id: string,
    public date: DateTime,
    public category: string,
    public price: string,
    public cardId: string,
    public quantity: number,
    public mintTxHash: string,
    public toAddress: string,
    public status: StatusCheckout | null,
    public checkoutTxHash: string,
    public checkoutBlockchainTimestamp: DateTime,
    public mintBlockchainTimestamp: DateTime,
    public chain: Chain,
    public lastError: string
  ) {}

  public static fromJson(json: CheckoutJson): VendingMachineCheckout {
    return new VendingMachineCheckout(
      json.id,
      DateTime.fromISO(json.date),
      json.category,
      json.price,
      json.cardId,
      json.quantity,
      json.mintTxHash,
      json.toAddress,
      json.status ? StatusCheckout.fromName<StatusCheckout>(json.status) : null,
      json.checkoutTxHash,
      DateTime.fromISO(json.checkoutBlockchainTimestamp),
      DateTime.fromISO(json.mintBlockchainTimestamp),
      Chain.fromId<Chain>(json.chainId),
      json.lastError
    )
  }
}
