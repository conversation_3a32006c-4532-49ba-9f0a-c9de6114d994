import { BaseModelInterface } from './interfaces/base_model'
import User from './user'
import { LoginStatusJson } from 'data/jsons/login_status_json'

export default class LoginStatus implements BaseModelInterface {

  public static fromJson (json:LoginStatusJson): LoginStatus {

    return new this(
      json.isLoggedIn,
      (json.user !== null) ? User.fromJson(json.user) : null
    )
  }

  constructor (
    public isLoggedIn: boolean,
    public user: User|null
  ){}

}
