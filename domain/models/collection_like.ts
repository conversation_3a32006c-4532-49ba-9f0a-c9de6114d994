import { DateTime } from 'luxon'
import { BaseModelInterface } from './interfaces/base_model'
import { CollectionLikeJson } from 'data/jsons/collection_like_json'
import Collection from './collection'
import User from './user'

export default class CollectionLike implements BaseModelInterface {

  public static fromJson (json:CollectionLikeJson): CollectionLike {

    return new this(
      json.id,
      (json.collection !== null) ? Collection.fromJson(json.collection) : null,
      (json.user !== null) ? User.fromJson(json.user) : null,
      DateTime.fromISO(json.createdAt),
      DateTime.fromISO(json.updatedAt)
    )
  }

  constructor (
    public id: string|undefined,
    public collection: Collection|null,
    public user: User|null,
    public createdAt: DateTime,
    public updatedAt: DateTime
  ){}

}
