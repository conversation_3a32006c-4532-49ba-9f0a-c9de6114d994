import Base from 'domain/value_objects/base'

export class Status extends Base {
  public static getResourceArray () {
    return [
      {
        id: 1,
        name: 'active',
        label: 'Active',
      },
      {
        id: 2,
        name: 'canceled',
        label: 'Canceled',
      },
      {
        id: 3,
        name: 'finished',
        label: 'Finished',
      },
      {
        id: 4,
        name: 'expired',
        label: 'Expired',
      },
    ]
  }

  public static active (): Status {
    return this.fromName<Status>('active')
  }

  public static canceled (): Status {
    return this.fromName<Status>('canceled')
  }

  public static finished (): Status {
    return this.fromName<Status>('finished')
  }

  public static expired (): Status {
    return this.fromName<Status>('expired')
  }

  public isActive (): boolean {
    return this.getName() === 'active'
  }

  public isCanceled (): boolean {
    return this.getName() === 'canceled'
  }

  public isFinished (): boolean {
    return this.getName() === 'finished'
  }

  public isExpired (): boolean {
    return this.getName() === 'expired'
  }
}
