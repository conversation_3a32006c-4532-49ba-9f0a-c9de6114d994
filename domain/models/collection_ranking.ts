import { CollectionJson } from 'data/jsons/collection_json'
import PaymentToken from 'domain/value_objects/payment_token'
import { DateTime } from 'luxon'
import Category from './category'
import { BaseModelInterface } from './interfaces/base_model'
import Multimedia from './multimedia'
import User from './user'


export default class CollectionRanking implements BaseModelInterface {
  public static fromJson (json: CollectionJson): CollectionRanking {
    const paymentTokens: PaymentToken[] = []
    if (json.paymentTokens?.length > 0) {
      for (const paymentTokenJson of json.paymentTokens) {
        if (paymentTokenJson.id === undefined) {
          throw new Error('paymentToken.id is undefined')
        }
     
        paymentTokens.push(paymentTokenJson)
      }
    }
    const owner: any = json?.owner ? User.fromJson(json?.owner) : null
    const collaborators = json.collaborators ?  json.collaborators.map((collaborator) => {
      return User.fromJson(collaborator)
    }) : []
    return new this(
      json.id,
      json.slug,
      json.name,
      json.description,
      json.webUrl,
      json.discordUrl,
      json.telegramUrl,
      json.mediumUsername,
      json.twitterUsername,
      json.instagramUsername,
      json.isExplicit,
      json.numAssets,
      json.numOwners,
      json.totalVolumeUsd,
      json.totalVolumeJpy,
      json.floorPriceUsd,
      json.floorPriceJpy,
      DateTime.fromISO(json.createdAt),
      DateTime.fromISO(json.updatedAt),
      json.logoImage ? Multimedia.fromJson(json.logoImage) : null,
      json.featuredImage
        ? Multimedia.fromJson(json.featuredImage)
        : null,
      json.bannerImage ? Multimedia.fromJson(json.bannerImage) : null,
      json.category ? Category.fromJson(json.category) : null,
      owner,
      paymentTokens,
      json.payoutAddresses,
      collaborators,
      json.rankingVolume,
      json.chainId
    )
  }

  constructor (
    public id: string | undefined = undefined,
    public slug: string,
    public name: string | null,
    public description: string | null,
    public webUrl: string | null,
    public discordUrl: string | null,
    public telegramUrl: string | null,
    public mediumUsername: string | null,
    public twitterUsername: string | null,
    public instagramUsername: string | null,
    public isExplicit: boolean,
    public numAssets: number,
    public numOwners: number,
    public totalVolumeUsd: number | null,
    public totalVolumeJpy: number | null,
    public floorPriceUsd: number | null,
    public floorPriceJpy: number | null,
    public createdAt: DateTime,
    public updatedAt: DateTime,
    public logoImage: Multimedia | null,
    public featuredImage: Multimedia | null,
    public bannerImage: Multimedia | null,
    public category: Category | null,
    public owner: User,
    public paymentTokens: PaymentToken[],
    public payoutAddresses: string[],
    public collaborators: User[],
    public rankingVolume: string | number,
    public chainId: number | null
  ) {}
}
