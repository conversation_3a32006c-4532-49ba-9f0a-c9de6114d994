import { DateTime } from 'luxon'

import { BaseModelInterface } from './interfaces/base_model'
import VendingMachineCategory from './vending_machine_category'
import { VendingMachineCardJson } from 'data/jsons/vending-machine/vending_machine_card_json'
/* eslint-disable space-before-function-paren */
export default class VendingMachineCard implements BaseModelInterface {
  // Note: When editing the constructor, always edit function 'newEmptyVendingMachineCard'.
  constructor(
    public id: string,
    public categoryId: string | null,
    public category: VendingMachineCategory | null,
    public cardId: string,
    public currency: string,
    public source: string,
    public status: string,
    public price: string,
    public shop: string,
    public stock: number,
    public mintedQty: number,
    public buyBackQty: number,
    public convertedQty: number,
    public metadata: any,
    public name: string,
    public image: string,
    public isEnabled: boolean,
    public scrapedAt: DateTime | null,
    public createdAt: DateTime,
    public updatedAt: DateTime,
  ) {}

  public static newEmptyVendingMachineCard(): VendingMachineCard {
    return new this(
      '', // id
      null, // categoryId
      null, // category
      '', // cardId
      '$', //currency
      '', //source
      '', //status
      '0', //price
      '', //shop
      0, //stock
      0, //mintedQty
      0, //buyBackQty
      0, //convertedQty
      null, //metadata
      '', //name
      '', //image
      false, //enable
      DateTime.now(), // createdAt
      DateTime.now(), // updatedAt
      DateTime.now(), // scrapeAt
    )
  }

  public static fromJson(json: VendingMachineCardJson): VendingMachineCard {
    let _card = this.newEmptyVendingMachineCard()
    _card.id = json.id
    _card.cardId = json.cardId
    _card.categoryId = json.categoryId
    _card.category = json.category
      ? VendingMachineCategory.fromJson(json.category)
      : null
    _card.cardId = json.cardId
    _card.currency = json.currency
    _card.source = json.source
    _card.status = json.status
    _card.price = json.price
    _card.shop = json.shop
    _card.stock = json.stock
    _card.mintedQty = json.mintedQty
    _card.buyBackQty = json.buyBackQty
    _card.convertedQty = json.convertedQty
    _card.metadata = json.metadata
    _card.name = json?.metadata?.name || ''
    _card.image = json?.metadata?.image || ''
    _card.isEnabled = json.isEnabled
    _card.createdAt = DateTime.fromISO(json.createdAt)
    _card.updatedAt = DateTime.fromISO(json.updatedAt)
    _card.scrapedAt = json.scrapedAt ? DateTime.fromISO(json.scrapedAt) : null
    return _card
  }
}
