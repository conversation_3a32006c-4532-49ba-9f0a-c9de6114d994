import { DateTime } from 'luxon'
import Multimedia from './multimedia'
import { BaseModelInterface } from './interfaces/base_model'
import { MainBannerJson } from 'data/jsons/main_banner_json'
import {Status} from './main_banner/status'
import Collection from './collection'
export default class MainBanner implements BaseModelInterface {

  public static fromJson (json: MainBannerJson): MainBanner {

    return new this(
      json.id,
      json.title,
      (json.banner !== null) ? Multimedia.fromJson(json.banner) : null,
      json.url,
      json.priority,
      Status.fromId<Status>(json.statusId),
      (json.expiresAt !== null) ? DateTime.fromISO(json.expiresAt) : null,
      DateTime.fromISO(json.createdAt),
      DateTime.fromISO(json.updatedAt),
      (json.collection !== null) ? Collection.fromJson(json.collection) : null
    )
  }

  constructor (
    public id: string|undefined,
    public title: string|null,
    public banner: Multimedia|null,
    public url: string|null,
    public priority: number,
    public status: Status,
    public expiresAt: DateTime|null,
    public createdAt: DateTime,
    public updatedAt: DateTime,
    public collection: Collection|null
  ){}

}
