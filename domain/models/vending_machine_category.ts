import { DateTime } from 'luxon'
import { BaseModelInterface } from './interfaces/base_model'
import { VendingMachineCategoryJson } from 'data/jsons/vending-machine/vending_machine_category_json'

export default class VendingMachineCategory implements BaseModelInterface {
  constructor (
    public id: number | undefined,
    public name: string,
    public enable: boolean,
    public createdAt: DateTime,
    public updatedAt: DateTime,
  ){}

  public static newEmptyVendingMachineCategory (): VendingMachineCategory {
    return new this(
      undefined, // id
      '', // name
      true, // enable
      DateTime.now(), // createdAt
      DateTime.now(), // updatedAt
    )
  }

  public static fromJson (json: VendingMachineCategoryJson): VendingMachineCategory {
    let category = this.newEmptyVendingMachineCategory()
    category.id = json.id
    category.name = json.name
    category.enable = json.enable
    category.createdAt = DateTime.fromISO(json.createdAt)
    category.updatedAt = DateTime.fromISO(json.updatedAt)
    return category
  }

  public toJson (): VendingMachineCategoryJson {
    return {
      id: this.id!,
      name: this.name,
      enable: this.enable,
      createdAt: this.createdAt.toISO()!,
      updatedAt: this.updatedAt.toISO()!,
    }
  }

  public getFormattedCreatedAt (): string {
    return this.createdAt.toFormat('yyyy-MM-dd HH:mm:ss')
  }

  public getFormattedUpdatedAt (): string {
    return this.updatedAt.toFormat('yyyy-MM-dd HH:mm:ss')
  }
}
