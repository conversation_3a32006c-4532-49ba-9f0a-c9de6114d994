import Base from 'domain/value_objects/base'

export class TypeId extends Base {

  public static getResourceArray () {
    return [
      {
        id: 1,
        name: 'createNft',
        label: 'Create NFT',
      },
      {
        id: 2,
        name: 'importNft',
        label: 'import NFT',
      },
    ]
  }

  public static createNft (): TypeId {
    return this.fromId<TypeId>(1)
  }
  
  public static importNft (): TypeId {
    return this.fromId<TypeId>(2)
  }

  public isCreateNft (): boolean {
    return (this.getId() === 1)
  }

  public isImportNft (): boolean {
    return (this.getId() === 2)
  }
    
}