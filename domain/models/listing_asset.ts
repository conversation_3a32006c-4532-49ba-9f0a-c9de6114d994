import { ListingAssetJson } from 'data/jsons/listing_asset_json'
import { BaseModelInterface } from './interfaces/base_model'
import Asset from './asset'
import { DateTime } from 'luxon'

export default class ListingAsset implements BaseModelInterface {
  public static fromJson (json: ListingAssetJson): ListingAsset {
    if (json.id === undefined) {
      throw new Error('id is undefined')
    }
    if (json.listingId === undefined) {
      throw new Error('listingId is undefined')
    }
    if (json.assetId === undefined) {
      throw new Error('assetId is undefined')
    }
    if (json.quantity === undefined) {
      throw new Error('quantity is undefined')
    }
    if (json.createdAt === undefined) {
      throw new Error('createdAt is undefined')
    }
    if (json.updatedAt === undefined) {
      throw new Error('updatedAt is undefined')
    }
    if (json.asset === undefined || json.asset === null) {
      throw new Error('asset is undefined')
    }

    return new this(
      json.id,
      json.listingId,
      Asset.fromJson(json.asset),
      json.quantity,
      DateTime.fromISO(json.createdAt),
      DateTime.fromISO(json.updatedAt)
    )
  }

  constructor (
    public id: string,
    public listingId: string,
    public asset: Asset,
    public quantity: number,
    public createdAt: DateTime,
    public updatedAt: DateTime
  ) {}
}
