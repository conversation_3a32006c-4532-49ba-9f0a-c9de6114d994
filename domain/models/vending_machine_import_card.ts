import { DateTime } from 'luxon'

import { BaseModelInterface } from './interfaces/base_model'
import { VendingMachineImportCardJson } from 'data/jsons/vending-machine/vending_machine_card_json'
import { Status } from './card_vending_machine/status'

/* eslint-disable space-before-function-paren */
export default class VendingMachineImportCard implements BaseModelInterface {
  // Note: When editing the constructor, always edit function 'newEmptyVendingMachineImportCard'.
  constructor(
    public id: string | undefined,
    public cardId: string | undefined,
    public status: Status | null,
    public createdAt: DateTime,
    public updatedAt: DateTime,
    public scrapeAt: DateTime | null,
    public message: string,
  ) {}

  public static newEmptyVendingMachineImportCard(): VendingMachineImportCard {
    return new this(
      undefined, // id
      undefined, // cardId
      null,
      DateTime.now(), // createdAt
      DateTime.now(), // updatedAt
      DateTime.now(), // scrapeAt
      '',
    )
  }

  public static fromJson(
    json: VendingMachineImportCardJson,
  ): VendingMachineImportCard {
    let _card = this.newEmptyVendingMachineImportCard()
    _card.id = json.id
    _card.cardId = json.cardId
    _card.status = Status.fromName<Status>(json.status)
    _card.createdAt = DateTime.fromISO(json.createdAt)
    _card.updatedAt = DateTime.fromISO(json.updatedAt)
    _card.scrapeAt = json.scrapedAt ? DateTime.fromISO(json.scrapedAt) : null
    _card.message = json.message || ''
    return _card
  }
}
