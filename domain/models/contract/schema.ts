import Base from 'domain/value_objects/base'

export class Schema extends Base {

  public static getResourceArray () {
    return [
      {
        id: 1,
        name: 'erc721',
        label: 'ERC721',
        selectable: true,
      },
      {
        id: 2,
        name: 'erc1155',
        label: 'ERC1155',
        selectable: false,
      },
      {
        id: 3,
        name: 'erc4907',
        label: 'ERC4907',
        selectable: false,
      },
    ]
  }

  public isSelectable (): boolean {
    return this.resource['selectable'] as boolean
  }

  public toJson (): any {
    return {
      id: this.getId(),
      name: this.getName(),
      label: this.getLabel(),
      selectable: this.isSelectable(),
    }
  }
    
  public static erc721 (): Schema {
    return this.fromName<Schema>('erc721')
  }

  public static erc1155 (): Schema {
    return this.fromName<Schema>('erc1155')
  }

  public static erc4907 (): Schema {
    return this.fromName<Schema>('erc4907')
  }

  public isErc721 (): boolean {
    return (this.getName() === 'erc721')
  }

  public isErc1155 (): boolean {
    return (this.getName() === 'erc1155')
  }

  public isErc4907 (): boolean {
    return (this.getName() === 'erc4907')
  }
}