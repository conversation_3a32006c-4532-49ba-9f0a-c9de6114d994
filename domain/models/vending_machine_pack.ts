import { DateTime } from 'luxon'
import { BaseModelInterface } from './interfaces/base_model'
import { VendingMachinePackJson, Odds<PERSON>son } from 'data/jsons/vending-machine/vending_machine_pack_json'

export interface Odds {
  id?: number;
  fromPrice: number;
  toPrice: number;
  rate: number;
}

export default class VendingMachinePack implements BaseModelInterface {
  constructor (
    public id: string | undefined,
    public name: string,
    public description: string,
    public price: number,
    public categoryId: string,
    public imageUrl: string,
    public setting: Record<string, any>,
    public isEnabled: boolean,
    public createdAt: DateTime,
    public updatedAt: DateTime,
  ){}

  public static newEmptyVendingMachinePack (): VendingMachinePack {
    return new this(
      undefined, // id
      '', // name
      '', // description
      0, // price
      '', // categoryId
      '', // imageUrl
      {}, // setting
      true, // isEnabled
      DateTime.now(), // createdAt
      DateTime.now(), // updatedAt
    )
  }

  public static fromJson (json: VendingMachinePackJson): VendingMachinePack {
    let pack = this.newEmptyVendingMachinePack()
    pack.id = json.id
    pack.name = json.name
    pack.description = json.description
    pack.price = json.price
    pack.categoryId = json.categoryId
    pack.imageUrl = json.imageUrl
    pack.setting = json.setting
    pack.isEnabled = json.isEnabled
    pack.createdAt = DateTime.fromISO(json.createdAt)
    pack.updatedAt = DateTime.fromISO(json.updatedAt)
    return pack
  }

  public toJson (): VendingMachinePackJson {
    return {
      id: this.id!,
      name: this.name,
      description: this.description,
      price: this.price,
      categoryId: this.categoryId,
      imageUrl: this.imageUrl,
      setting: this.setting,
      isEnabled: this.isEnabled,
      createdAt: this.createdAt.toISO()!,
      updatedAt: this.updatedAt.toISO()!,
    }
  }

  public getFormattedCreatedAt (): string {
    return this.createdAt.toFormat('yyyy-MM-dd HH:mm:ss')
  }

  public getFormattedUpdatedAt (): string {
    return this.updatedAt.toFormat('yyyy-MM-dd HH:mm:ss')
  }

  public getFormattedPrice (): string {
    return this.price.toString()
  }
}
