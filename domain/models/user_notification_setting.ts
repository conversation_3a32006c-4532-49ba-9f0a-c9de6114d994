import { UserNotificationSettingUpdateJson } from 'data/jsons/user_notification_setting/user_notification_setting_update_json'
import { UserNotificationSettingJson } from 'data/jsons/user_notification_setting_json'
import { DateTime } from 'luxon'
import { BaseModelInterface } from './interfaces/base_model'

export default class UserNotificationSetting implements BaseModelInterface {

  public static fromJson (json:UserNotificationSettingJson): UserNotificationSetting {

    return new this(
      json.id,
      json.userId,
      json.onItemSold,
      json.onBidActivity,
      json.onPriceChange,
      json.onAuctionExpiration,
      json.onOutbid,
      json.onReferralSuccess,
      json.onOwnedAssetUpdates,
      json.onSuccessfullyPurchase,
      json.onNewsLetter,
      json.minimumBidThresholdUsd,
      json.minimumBidThresholdJpy,
      DateTime.fromISO(json.createdAt),
      DateTime.fromISO(json.updatedAt)
    )
  }

  constructor (
    public id: string|undefined,
    public userId: string,
    public onItemSold: boolean,
    public onBidActivity: boolean,
    public onPriceChange: boolean,
    public onAuctionExpiration: boolean,
    public onOutbid: boolean,
    public onReferralSuccess: boolean,
    public onOwnedAssetUpdates: boolean,
    public onSuccessfullyPurchase: boolean,
    public onNewsLetter: boolean,
    public minimumBidThresholdUsd: number,
    public minimumBidThresholdJpy: number,
    public createdAt: DateTime,
    public updatedAt: DateTime
  ){}

  public toUpdateJson (): UserNotificationSettingUpdateJson {
    return {
      onItemSold: this.onItemSold,
      onBidActivity: this.onBidActivity,
      onPriceChange: this.onPriceChange,
      onAuctionExpiration: this.onAuctionExpiration,
      onOutbid: this.onOutbid,
      onReferralSuccess: this.onReferralSuccess,
      onOwnedAssetUpdates: this.onOwnedAssetUpdates,
      onSuccessfullyPurchase: this.onSuccessfullyPurchase,
      onNewsLetter: this.onNewsLetter,
      minimumBidThresholdUsd: this.minimumBidThresholdUsd,
      minimumBidThresholdJpy: this.minimumBidThresholdJpy,
    }
  }
}
