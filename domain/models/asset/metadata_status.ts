import Base from 'domain/value_objects/base'

export class MetadataStatus extends Base {

  public static getResourceArray () {
    return [
      {
        id: 1,
        name: 'centralized',
        label: 'Centralized',
      },
      {
        id: 2,
        name: 'frozen',
        label: 'Frozen',
      },
    ]
  }

  public static centralized (): MetadataStatus {
    return this.fromName<MetadataStatus>('centralized')
  }

  public static frozen (): MetadataStatus {
    return this.fromName<MetadataStatus>('frozen')
  }

  public isCentralized (): boolean {
    return (this.getName() === 'centralized')
  }

  public isFrozen (): boolean {
    return (this.getName() === 'frozen')
  }
    
}