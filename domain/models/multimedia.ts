import { MultimediaJson } from 'data/jsons/multimedia_json'
import { DateTime } from 'luxon'
import { BaseModelInterface } from './interfaces/base_model'
import { Format } from './multimedia/format'

export default class Multimedia implements BaseModelInterface {

  public static fromJson (json:MultimediaJson): Multimedia {

    return new this(
      json.id,
      json.url,
      json.storageName,
      json.mimeType,
      json.dataSize,
      json.height,
      json.width,
      json.hash,
      json.externalId,
      DateTime.fromISO(json.createdAt),
      DateTime.fromISO(json.updatedAt)
    )
  }

  constructor (
    public id: string|undefined,
    public url: string,
    public storageName: string,
    public mimeType: string,
    public dataSize: number,
    public height: number|null,
    public width: number|null,
    public hash: string,
    public externalId: string|null,
    public createdAt: DateTime,
    public updatedAt: DateTime
  ){}

  public getFormat (): Format {
    const format = Format.fromMime(this.mimeType)
    if (format.isUnknown()) {
      throw new Error(`unknown mime: ${this.mimeType}`)
    }
    return format
  }

}
