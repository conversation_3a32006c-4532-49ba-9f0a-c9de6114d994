import { DateTime } from 'luxon'
import { BaseModelInterface } from './interfaces/base_model'
import { ContractJson } from 'data/jsons/contract_json'
import Collection from './collection'
import Chain from 'domain/value_objects/chain'
import { Schema } from './contract/schema'
import Marketplace from 'domain/value_objects/marketplace'

export default class Contract implements BaseModelInterface {

  public static fromJson (json:ContractJson): Contract {

    if (json.chain.id === undefined) {
      throw new Error('chain.id is undefined')
    }
    let chain = Chain.fromId<Chain>(json.chain.id)
    if (chain.isUnknown()) {
      // throw new Error(`chain.id is invalid: ${json.chain.id}`)
      chain = new Chain(json.chain)
    }

    if (json.schema.id === undefined) {
      throw new Error('schema.id is undefined')
    }
    const schema = Schema.fromId<Schema>(json.schema.id)
    if (schema.isUnknown()) {
      throw new Error(`schema.id is invalid: ${json.schema.id}`)
    }

    const marketplaces:Marketplace[] = []
    for (const marketplaceJson of json.marketplaces) {
      if (marketplaceJson.id === undefined) {
        throw new Error('marketplace.id is undefined')
      }
      const marketplace = Marketplace.fromId<Marketplace>(marketplaceJson.id)
      if (marketplace.isUnknown()) {
        throw new Error(`marketplace.id is invalid: ${marketplaceJson.id}`)
      }
    }

    return new this(
      json.id,
      chain,
      schema,
      json.address,
      json.name,
      json.symbol,
      (json.collection !== null) ? Collection.fromJson(json.collection) : null,
      DateTime.fromISO(json.createdAt),
      DateTime.fromISO(json.updatedAt),
      marketplaces,
    )
  }

  constructor (
    public id: string|undefined,
    public chain: Chain,
    public schema: Schema,
    public address: string,
    public name: string|null,
    public symbol: string|null,
    public collection: Collection|null,
    public createdAt: DateTime,
    public updatedAt: DateTime,
    public marketplaces: Marketplace[],
  ){}

}
