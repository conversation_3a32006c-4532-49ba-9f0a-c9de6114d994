import Base from 'domain/value_objects/base'

export class Status extends Base {

  public static getResourceArray () {
    return [
      {
        id: 1,
        name: 'draft',
        label: 'Draft',
      },
      {
        id: 2,
        name: 'closed',
        label: 'Closed',
      },
      {
        id: 9,
        name: 'opened',
        label: 'opened',
      },
    ]
  }

  public static draft (): Status {
    return this.fromName<Status>('draft')
  }

  public static closed (): Status {
    return this.fromName<Status>('closed')
  }

  public static opened (): Status {
    return this.fromName<Status>('opened')
  }

  public isDraft (): boolean {
    return (this.getName() === 'draft')
  }

  public isClosed (): boolean {
    return (this.getName() === 'closed')
  }

  public isOpened (): boolean {
    return (this.getName() === 'opened')
  }

}