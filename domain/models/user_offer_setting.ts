import { UserOfferSettingJson } from 'data/jsons/user_offer_setting_json'
import { DateTime } from 'luxon'
import { BaseModelInterface } from './interfaces/base_model'

export default class UserOfferSetting implements BaseModelInterface {

  public static fromJson (json:UserOfferSettingJson): UserOfferSetting {

    return new this(
      json.id,
      json.userId,
      json.collectionId,
      json.minimumPrice,
      DateTime.fromISO(json.createdAt),
      DateTime.fromISO(json.updatedAt)
    )
  }

  constructor (
    public id: string|undefined,
    public userId: string,
    public collectionId: string,
    public minimumPrice: number,
    public createdAt: DateTime,
    public updatedAt: DateTime
  ){}

}
