import Base from 'domain/value_objects/base'

export default class ActivityEvent extends Base {

  public static getResourceArray () {
    return [
      {
        id: 1,
        name: 'minted',
        label: 'Minted',
      },
      {
        id: 2,
        name: 'sale',
        label: 'Sale',
      },
      {
        id: 3,
        name: 'transfer',
        label: 'Transfer',
      },
      {
        id: 4,
        name: 'offer',
        label: 'Offer',
      },
      {
        id: 5,
        name: 'listed',
        label: 'Listed',
      },
    ]
  }

  public static minted (): ActivityEvent {
    return this.fromName<ActivityEvent>('minted')
  }

  public static sale (): ActivityEvent {
    return this.fromName<ActivityEvent>('sale')
  }

  public static transfer (): ActivityEvent {
    return this.fromName<ActivityEvent>('transfer')
  }

  public static offer (): ActivityEvent {
    return this.fromName<ActivityEvent>('offer')
  }

  public static listed (): ActivityEvent {
    return this.fromName<ActivityEvent>('listed')
  }

  public isMinted (): boolean {
    return (this.getName() === 'minted')
  }

  public isSale (): boolean {
    return (this.getName() === 'sale')
  }

  public isTransfer (): boolean {
    return (this.getName() === 'transfer')
  }

  public isOffer (): boolean {
    return (this.getName() === 'offer')
  }

  public isListed (): boolean {
    return (this.getName() === 'listed')
  }
}