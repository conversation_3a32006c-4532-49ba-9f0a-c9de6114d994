import { DateTime } from 'luxon'
import { BaseModelInterface } from './interfaces/base_model'
import { WatchJson } from 'data/jsons/watch_json'
import TargetEntityType from 'domain/value_objects/target_entity_type'

export default class Watch implements BaseModelInterface {

  public static fromJson (json:WatchJson): Watch {
    if (json.targetEntityType.id === undefined) {
      throw new Error('targetEntityType.id is undefined')
    }

    const targetEntityType = TargetEntityType.fromId<TargetEntityType>(json.targetEntityType.id)
    if (targetEntityType.isUnknown()) {
      throw new Error(`targetEntityType.id is invalid: ${json.targetEntityType.id}`)
    }

    return new this(
      json.id,
      json.uniqueKey,
      targetEntityType,
      json.targetEntityId,
      json.userId,
      DateTime.fromISO(json.createdAt),
      DateTime.fromISO(json.updatedAt)
    )
  }

  constructor (
    public id: string|undefined,
    public uniqueKey: string,
    public targetEntityType: TargetEntityType,
    public targetEntityId: string,
    public userId: string,
    public createdAt: DateTime,
    public updatedAt: DateTime
  ){}

}
