import { DateTime } from 'luxon'
import { BaseModelInterface } from './interfaces/base_model'
import { ListingOrderJson } from 'data/jsons/listing/listing_order_json'
import User from './user'
import Listing from './listing'
import Asset from './asset'
import { Status, OrderStatus } from './listing/order_status'

export default class ListingOrder implements BaseModelInterface {
  constructor (
    public id: string | undefined,
    public user: User | null,
    public listing: Listing | null,
    public asset: Asset | null,
    public quantity: Number | 0,
    public orderCode: string | null,
    public orderAmount: Number | 0,
    public orderAmountType: string | null,
    public orderChainId: Number | null,
    public orderPaymentToken: string | null,
    public orderPaymentUrl: string | null,
    public status: Status | null,
    public orderStatus: OrderStatus | null,
    
  ) {}

  public static newEmtyListingOrder (): ListingOrder {
    return new this(
      undefined,
      null,
      null,
      null,
      0,
      null,
      0,
      null,
      null,
      null,
      null,
      null,
      null,
    )
  }
  
  public static fromJson (json: ListingOrderJson): ListingOrder {
    let _order = this.newEmtyListingOrder()
    const listing = json?.listing !== null ? Listing.fromJson(json.listing) : null
    _order.id = json?.id || undefined
    _order.user = json?.user !== null ? User.fromJson(json.user) : null
    _order.listing = json?.listing !== null ? Listing.fromJson(json.listing) : null
    _order.asset = listing?.listingAssets?.length ? listing?.listingAssets[0].asset : null
    _order.quantity = json?.quantity 
    _order.orderCode = json?.orderCode 
    _order.orderAmount = json?.orderAmount 
    _order.orderAmountType = json?.orderAmountType 
    _order.orderChainId = Number(json?.orderChainId) || null 
    _order.orderPaymentToken = json?.orderPaymentToken 
    _order.orderPaymentUrl = json?.orderPaymentUrl 
    _order.status = Status.fromId<Status>(json.statusId)
    _order.orderStatus = OrderStatus.fromId<OrderStatus>(json.orderStatusId)
    
    return _order
  }  
}
