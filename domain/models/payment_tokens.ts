import { paymentTokenImages } from 'lib/valueObjects'
import { nativeTokenAddresses } from 'lib/token_addresses'
import { PaymentTokensJson } from 'data/jsons/payment_tokens_json'
import { BaseModelInterface } from './interfaces/base_model'
export default class PaymentTokens implements BaseModelInterface {
  constructor (
    // public id: number|undefined,
    // public name:string,
    // public label:string,
    // public chainName: string,
    public id: number|undefined,
    public name: string,
    public type: string,
    public label: string,
    public symbol: string,
    public value: string|number|undefined,
    public icon: string|undefined,
    public priceUsd: number,
    public decimals: number,
    public address: string,
    public isNative: boolean,
    public isWrappedEther : boolean,
 
  ) {}

  public static newEmtyPaymentTokens (): PaymentTokens {
    return new this(
      undefined,
      '',
      '',
      '',
      '',
      '',
      '',
      0,
      18,
      '0x0',
      false,
      false,
    )
  }

  public static fromJson (json:PaymentTokensJson): PaymentTokens {
    let newToken = this.newEmtyPaymentTokens()
    newToken.id = json?.id
    newToken.value = json?.id
    newToken.name = json?.symbol
    newToken.type = json?.name
    newToken.label = json?.symbol
    newToken.symbol = json?.symbol
    newToken.icon = paymentTokenImages[json?.symbol?.toLocaleLowerCase() || json?.name?.toLocaleLowerCase()]
    newToken.priceUsd = json?.price || 0,
    newToken.decimals = json?.decimals
    newToken.address = json?.address
    newToken.isNative = json?.address == nativeTokenAddresses
    newToken.isWrappedEther = ['weth', 'woas', 'wpol'].includes(json?.symbol?.toLocaleLowerCase())
    return newToken 
  }
  
}
