import { DateTime } from 'luxon'
import Collection from './collection'
import Contract from './contract'
import Multimedia from './multimedia'
import { BaseModelInterface } from './interfaces/base_model'
import { AssetJson } from 'data/jsons/asset_json'
import { AssetAttributeLevelJson } from 'data/jsons/asset_attribute_level_json'
import { AssetAttributePropertyJson } from 'data/jsons/asset_attribute_property_json'
import { AssetAttributeStatJson } from 'data/jsons/asset_attribute_stat_json'
import { AssetsCreateMetadata } from 'data/jsons/assets/assets_create_metadata_json'
import { AssetsCreate } from 'data/jsons/assets/assets_create_json'
import { MetadataStatus } from './asset/metadata_status'
import Marketplace from 'domain/value_objects/marketplace'
import User from './user'
import Chain from '../value_objects/chain'
import { Schema } from 'domain/models/contract/schema'
import PaymentToken from 'domain/models/payment_tokens'
import ListingType from './listing/listing_type'

export default class Asset implements BaseModelInterface {
  // Note: When editing the constructor, always edit function 'newEmtyAsset'.
  constructor (
    public id: string|undefined,
    public collection: Collection|null,
    public contract: Contract|null,
    public chain: Chain|null,
    public address: string,
    public chainId: number|null,//new
    public metadataId: string|null,//new
    public imageId: string|null,//new
    public txHash: string|null,//new
    public tokenId: string|number,
    public tokenUri: string|null,
    public name: string|null,
    public externalUrl: string|null,
    public description: string|null,
    public thumbnail: Multimedia|null,//re-check
    public multimedia: Multimedia|null,
    public hasUnlockableContent: boolean,
    public unlockableContent: string|null,
    public isExplicit: boolean,
    public metadataStatus: MetadataStatus,
    public metadataJson: string|null,
    public numOwners: number|null,
    public totalSupply: number|null,
    public numTotalImpressions: number|null,
    public numTotalLikes: number|null,
    public currentPaymentToken: PaymentToken|null,
    public currentPrice: number|null,
    public minListingId: number|null,
    public minListingType: ListingType|null,
    public minListingPaymentToken: PaymentToken|null,
    public minListingPrice: number|null,
    public minListingExpiresAt: DateTime|null,
    public minListingCreatedAt: DateTime|null,
    public lastSoldListingId: number|null,
    public lastSoldPaymentToken: PaymentToken|null,
    public lastSoldPrice: number|null,
    public lastSoldAt: DateTime|null,
    public hasActiveOffers: boolean|null,
    public attributeLevels: AssetAttributeLevelJson[],
    public attributeProperties: AssetAttributePropertyJson[],
    public attributeStats: AssetAttributeStatJson[],
    public marketplaces: Marketplace[],
    public creators: User[],
    public owners: User[],
    public createdAt: DateTime,
    public updatedAt: DateTime,
  ){}

  public static newEmtyAsset (): Asset {
    return new this(
      undefined, // id
      null, // collection
      null, // contract
      Chain.ethereum(), //chain
      '', // address
      1,
      '',
      '',
      '',
      '', // tokenId
      null, // tokenUri
      '', // name
      null, // externalUrl
      null, // description
      null, // thumbnail
      null, // multimedia
      false, // hasUnlockableContent
      null, // unlockableContent
      false, // isExplicit
      MetadataStatus.centralized(), // metadataStatus
      null, // metadataJson
      null, // numOwners
      null, // totalSupply
      null, // numTotalImpressions
      null, // numTotalLikes
      null, // currentPaymentToken
      null, // currentPrice
      null, // minListingId
      null, // minListingType
      null, // minListingPaymentToken
      null, // minListingPrice
      null, // minListingExpiresAt
      null, // minListingCreatedAt
      null, // lastSoldListingId
      null, // lastSoldPaymentToken
      null, // lastSoldAt
      null, // json.hasActiveOffers,
      null, // numDistinctOwners
      [], // attributeLevels
      [], // attributeProperties
      [], // attributeStats
      [], // marketplaces
      [], // creators
      [], // owners
      DateTime.now(), // createdAt
      DateTime.now(), // updatedAt
    )
  }

  public static fromJson (json:AssetJson): Asset {

    if (json.metadataStatus.id === undefined) {
      throw new Error('metadataStatus.id is undefined')
    }

    const metadataStatus = MetadataStatus.fromId<MetadataStatus>(json.metadataStatus.id)
    if (metadataStatus.isUnknown()) {
      throw new Error(`metadataStatus is invalid: ${json.metadataStatus.id}`)
    }

    const marketplaces:Marketplace[] = []
    for (const marketplaceJson of json.marketplaces) {
      if (marketplaceJson.id === undefined) {
        throw new Error('marketplace.id is undefined')
      }
      const marketplace = Marketplace.fromId<Marketplace>(marketplaceJson.id)
      if (marketplace.isUnknown()) {
        throw new Error(`marketplace.id is invalid: ${marketplaceJson.id}`)
      }
      marketplaces.push(marketplace)
    }
    let  contract = new Contract(
      undefined,
      new Chain(json.chain),
      Schema.erc1155(),
      json.address, // address
      null, // name
      null, // symbol
      null, // collection
      DateTime.now(),// createdAt
      DateTime.now(),// updatedAt
      []// marketplaces
    )
    if(json?.contract?.id){
      contract = Contract.fromJson(json.contract)
    } else if(json?.collection?.contract?.id) {
      contract = Contract.fromJson(json?.collection?.contract)
    }

    let _asset = this.newEmtyAsset()
    _asset.id = json.id
    _asset.collection = (json.collection !== null) ? Collection.fromJson(json.collection) : null
    _asset.contract = contract
    _asset.chain = new Chain(json.chain)
    _asset.address = json.address
    _asset.chainId = json.chainId
    _asset.metadataId = json.metadataId
    _asset.imageId = json.imageId
    _asset.txHash = json.txHash
    _asset.tokenId = json.tokenId
    _asset.tokenUri = json.tokenUri
    _asset.name = json.name
    _asset.externalUrl = json.externalUrl
    _asset.description = json.description
    _asset.thumbnail = (json.thumbnail !== null) ? Multimedia.fromJson(json.thumbnail) : null
    _asset.multimedia = (json.multimedia !== null) ? Multimedia.fromJson(json.multimedia) : null
    _asset.hasUnlockableContent = json.hasUnlockableContent
    _asset.unlockableContent = json.unlockableContent
    _asset.isExplicit = json.isExplicit
    _asset.metadataStatus = metadataStatus
    _asset.metadataJson = json.metadataJson
    _asset.numOwners = json.numOwners
    _asset.totalSupply = json.totalSupply
    _asset.numTotalImpressions = json.numTotalImpressions
    _asset.numTotalLikes = json.numTotalLikes
    _asset.currentPaymentToken = json.currentPaymentToken ? PaymentToken.fromJson(json.currentPaymentToken) : null
    _asset.currentPrice = json.currentPrice
    _asset.minListingId = json.minListingId
    _asset.minListingType = json.minListingTypeId ? ListingType.fromId<ListingType>(json.minListingTypeId) : null
    _asset.minListingPaymentToken = json.minListingPaymentToken ? PaymentToken.fromJson(json.minListingPaymentToken) : null
    _asset.minListingPrice = json.minListingPrice
    _asset.minListingExpiresAt = (json.minListingExpiresAt !== null) ? DateTime.fromISO(json.minListingExpiresAt) : null
    _asset.minListingCreatedAt = (json.minListingCreatedAt !== null) ? DateTime.fromISO(json.minListingCreatedAt) : null
    _asset.lastSoldListingId = json.lastSoldListingId
    _asset.lastSoldPaymentToken = json.lastSoldPaymentToken ? PaymentToken.fromJson(json.lastSoldPaymentToken) : null
    _asset.lastSoldPrice = json.lastSoldPrice
    _asset.lastSoldAt = (json.lastSoldAt !== null) ? DateTime.fromISO(json.lastSoldAt) : null
    _asset.hasActiveOffers = json.hasActiveOffers
    _asset.attributeLevels = json.attributeLevels
    _asset.attributeProperties = json.attributeProperties
    _asset.attributeStats = json.attributeStats
    _asset.marketplaces = marketplaces
    _asset.creators = json.creators.map((creator) => { return User.fromJson(creator) })
    _asset.owners = json.owners.map((owner) => { return User.fromJson(owner) })
    _asset.createdAt = DateTime.fromISO(json.createdAt)
    _asset.updatedAt = DateTime.fromISO(json.updatedAt)
    return _asset
  }

  public static toCreateMetadataJson (asset: Asset): AssetsCreateMetadata {
    return {
      name: asset?.name || '',
      imageId: asset?.multimedia?.id || null,
      description: asset?.description || null,
      properties: asset?.attributeProperties || [],
      levels: asset?.attributeLevels || [],
      stats: asset?.attributeStats || [],
      collectionId: asset?.collection?.id || null,
    }
  }
  
  public static toCreateJson (asset: Asset): AssetsCreate {
    return {
      name: asset?.name || '',
      externalLink: asset?.externalUrl,
      description: asset?.description || null,
      properties: asset?.attributeProperties || [],
      levels: asset?.attributeLevels || [],
      stats: asset?.attributeStats || [],
      collectionId: asset?.collection?.id || null,
      unlockableContent: asset.unlockableContent,
      hasUnlockableContent: asset.hasUnlockableContent,
      isExplicit: asset.isExplicit,
      supply: Number(asset.totalSupply),
      tokenId: Number(asset.tokenId),
      tokenUri: asset.tokenUri,
      metadataId: asset.metadataId,
      imageId: asset.imageId,
      txHash: asset.txHash,
    }
  }

  public isOwnerAsset (addressUser: string|undefined) {
    if(!addressUser) return false
    if (this?.owners?.find((i: User) => i?.publicAddresses[0]?.toLowerCase() == addressUser?.toLowerCase()) ) {
      return true
    } 
    return false
  }

  public getCreatorEarnings (userAddress = '') {
    try {
      const _userAddress = userAddress?.toLowerCase()
      const list = this.collection?.owners?.
        filter((i:any) => i.address?.toLowerCase() != _userAddress && Number(i.percent) > 0)?.
        map((i:any) => {return { address: i.address, percent: Number(i.percent)}})
        
      const totalRateFee = list?.reduce((acc, cur) => acc + cur?.percent, 0)
      return {
        list: list,
        totalRateFee: totalRateFee,
      }
    } catch (error) {
      return null
    }
  }
}
