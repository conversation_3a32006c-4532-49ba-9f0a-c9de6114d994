import { CollectionDailyStatJson } from 'data/jsons/collection_daily_stat_json'
import { DateTime } from 'luxon'
import { BaseModelInterface } from './interfaces/base_model'

export default class CollectionDailyStat implements BaseModelInterface {

  public static fromJson (json:CollectionDailyStatJson): CollectionDailyStat {

    return new this(
      json.id,
      json.collectionId,
      DateTime.fromISO(json.date),
      json.totalVolumeUsd,
      json.totalVolumeJpy,
      json.totalSales,
      json.totalSupply,
      json.floorPriceUsd,
      json.floorPriceJpy,
      json.averagePriceUsd,
      json.averagePriceJpy,
      json.dailyVolumeUsd,
      json.dailyVolumeJpy,
      json.dailySales,
      DateTime.fromISO(json.createdAt),
      DateTime.fromISO(json.updatedAt)
    )
  }

  constructor (
    public id: string|undefined,
    public collectionId: string,
    public date: DateTime,
    public totalVolumeUsd: number|null,
    public totalVolumeJpy: number|null,
    public totalSales: number|null,
    public totalSupply: number|null,
    public floorPriceUsd: number|null,
    public floorPriceJpy: number|null,
    public averagePriceUsd: number|null,
    public averagePriceJpy: number|null,
    public dailyVolumeUsd: number|null,
    public dailyVolumeJpy: number|null,
    public dailySales: number|null,
    public createdAt: DateTime,
    public updatedAt: DateTime
  ){}

}
