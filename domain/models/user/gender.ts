import Base from 'domain/value_objects/base'

export class Gender extends Base {

  public static getResourceArray () {
    return [
      {
        id: 1,
        name: 'male',
        label: 'Male',
      },
      {
        id: 2,
        name: 'female',
        label: 'Female',
      },
    ]
  }

  public static male (): Gender {
    return this.fromName<Gender>('male')
  }

  public static female (): Gender {
    return this.fromName<Gender>('female')
  }

  public isMale (): boolean {
    return (this.getName() === 'male')
  }

  public isFemale (): boolean {
    return (this.getName() === 'female')
  }
}