import Base from 'domain/value_objects/base'
/* eslint-disable space-before-function-paren */
export class StatusCheckout extends Base {
  public static getResourceArray() {
    return [
      {
        id: 1,
        name: 'PENDING',
        label: 'Pending',
      },
      {
        id: 2,
        name: 'MINTING',
        label: 'Minting',
      },
      {
        id: 3,
        name: 'COMPLETED',
        label: 'Completed',
      },
      {
        id: 4,
        name: 'FAILED',
        label: 'Failed',
      },
    ]
  }

  public getStatusBadge(): string {
    const status = this.getId()
    switch (status) {
      case 1:
        return 'bg-blue-100 text-blue-800'
      case 2:
        return 'bg-cyan-100 text-cyan-800'
      case 3:
        return 'bg-green-100 text-green-800'
      case 4:
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }
}
