import Base from 'domain/value_objects/base'

export class Status extends Base {

  public static getResourceArray () {
    return [
      {
        id: 1,
        name: 'PENDING',
        label: 'Pending',
      },
      {
        id: 2,
        name: 'COMPLETED',
        label: 'Completed',
      },
      {
        id: 3,
        name: 'ERROR',
        label: 'Error',
      },
    ]
  }

  public static completed (): Status {
    return this.fromName<Status>('COMPLETED')
  }

  public getStatusBadge (): string   {
    const status = this.getId()
    switch (status) {
      case 1:
        return 'bg-blue-100 text-blue-800'
      case 2:
        return 'bg-green-100 text-green-800'
      case 3:
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }
}
