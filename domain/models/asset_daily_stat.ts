import { AssetDailyStatJson } from 'data/jsons/asset_daily_stat_json'
import { DateTime } from 'luxon'
import { BaseModelInterface } from './interfaces/base_model'

export default class AssetDailyStat implements BaseModelInterface {

  public static fromJson (json:AssetDailyStatJson): AssetDailyStat {
    return new this(
      json.id,
      json.assetId,
      DateTime.fromISO(json.date),
      json.totalVolumeUsd,
      json.totalVolumeJpy,
      json.totalSales,
      json.averagePriceUsd,
      json.averagePriceJpy,
      json.dailyVolumeUsd,
      json.dailyVolumeJpy,
      json.dailySales,
      DateTime.fromISO(json.createdAt),
      DateTime.fromISO(json.updatedAt)
    )
  }

  constructor (
    public id: string|undefined,
    public assetId: string,
    public date: DateTime,
    public totalVolumeUsd: number|null,
    public totalVolumeJpy: number|null,
    public totalSales: number|null,
    public averagePriceUsd: number|null,
    public averagePriceJpy: number|null,
    public dailyVolumeUsd: number|null,
    public dailyVolumeJpy: number|null,
    public dailySales: number|null,
    public createdAt: DateTime,
    public updatedAt: DateTime
  ){}

}
