import { DateTime } from 'luxon'
import { BaseModelInterface } from './interfaces/base_model'
import { OfferJson } from 'data/jsons/offer_json'
import Asset from './asset'
import User from './user'
import PaymentToken from 'domain/models/payment_tokens'
import { Offer<PERSON>reateJ<PERSON> } from 'data/jsons/offer/offer_create_json'
import { OfferUpdateJson } from 'data/jsons/offer/offer_update_json'
import { OfferCancelJson } from 'data/jsons/offer/offer_cancel_json'
import { OfferAcceptJson } from 'data/jsons/offer/offer_accept_json'
import { Status } from './offer/status'

export default class Offer implements BaseModelInterface {

  constructor (
    public id: string | undefined,
    public listingId: string | null,
    public asset: Asset | null,
    public quantity: number,
    public user: User | null,
    public paymentToken: PaymentToken | null,
    public price: number,
    public status: Status,
    public signature: string | null,
    public expiresAt: DateTime | null,
    public createdAt: DateTime,
    public updatedAt: DateTime,
    public orderParams: any,
    public orderHash: string,
    public fees: any[],
  ) {}

  public static newEmtyOffer (): Offer {
    return new this(
      undefined,
      null,
      null,
      1,
      null,
      null,
      0,
      Status.active(),
      null,
      DateTime.now(),
      DateTime.now(),
      DateTime.now(),
      '',
      '',
      []
    )
  }
  
  public static fromJson (json: OfferJson): Offer {
    if (json.paymentToken.id === undefined) {
      throw new Error('paymentToken.id is undefined')
    }
    let _offer = this.newEmtyOffer()
    _offer.id =  json.id
    _offer.listingId =  json.listingId
    _offer.asset =  json.asset !== null ? Asset.fromJson(json.asset) : null
    _offer.quantity =  json.quantity
    _offer.user =  json.user !== null ? User.fromJson(json.user) : null
    _offer.paymentToken = PaymentToken.fromJson(json.paymentToken)
    _offer.price =  json.price
    _offer.status =  Status.fromId<Status>(json.statusId)
    _offer.signature =  json.signature
    _offer.expiresAt =  json.expiresAt !== null ? DateTime.fromISO(json.expiresAt) : null
    _offer.createdAt =  DateTime.fromISO(json.createdAt)
    _offer.updatedAt =  DateTime.fromISO(json.updatedAt)
    _offer.orderParams =  json.orderParams
    _offer.orderHash =  json.orderHash

    return _offer
  }

  public toCreateJson (): OfferCreateJson {
    if (this.asset === null) {
      throw new Error('asset must be specified to create')
    }
    if (this.asset.id === undefined) {
      throw new Error('asset.id must be specified to create')
    }
    if (this.expiresAt === null) {
      throw new Error('expiresAt must be specified to create')
    }

    return {
      assetId: this.asset.id,
      listingId: this.listingId,
      paymentTokenId: this.paymentToken?.id || 0,
      price: this.price,
      signature: this.signature,
      expiresAt: this.expiresAt.toISO(),
      orderParams: this.orderParams,
      quantity: this.quantity,
      orderHash: this.orderHash,
    }
  }

  public toUpdateJson (): OfferUpdateJson {
    if (this.id === undefined) {
      throw new Error('id must be specified to update')
    }
    if (this.expiresAt === null) {
      throw new Error('expiresAt must be specified to update')
    }

    return {
      id: this.id,
      statusId: this.status.getId(),
      paymentTokenId: this.paymentToken?.id || 0,
      price: this.price,
      signature: this.signature,
      expiresAt: this.expiresAt?.toISO(),
      orderParams: this.orderParams,
    }
  }

  public toCancel (): OfferCancelJson {
    if (this.id === undefined) {
      throw new Error('id must be specified to update')
    }
    
    return {
      id: this.id,
    }
  }

  public toAccept (): OfferAcceptJson {
    if (this.id === undefined) {
      throw new Error('id must be specified to update')
    }
    
    if (this.quantity === undefined) {
      throw new Error('quantity must be specified to update')
    }
    
    return {
      id: this.id,
      quantity: Number(this.quantity),
    }
  }
}
