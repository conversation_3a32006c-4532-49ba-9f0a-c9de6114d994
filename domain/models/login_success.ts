import { LoginSuccessJson } from 'data/jsons/login_success_json'
import { DateTime } from 'luxon'
import { BaseModelInterface } from './interfaces/base_model'

export default class LoginSuccess implements BaseModelInterface {

  public static fromJson (json:LoginSuccessJson): LoginSuccess {

    return new this(
      json.type,
      json.token,
      json.tokenHash,
      DateTime.fromISO(json.expiresAt)
    )
  }

  constructor (
    public type: string,
    public token: string,
    public tokenHash: string,
    public expiresAt: DateTime
  ){}
}
