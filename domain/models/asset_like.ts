import { DateTime } from 'luxon'
import { BaseModelInterface } from './interfaces/base_model'
import { AssetLikeJson } from 'data/jsons/asset_like_json'
import Asset from './asset'
import User from './user'

export default class AssetLike implements BaseModelInterface {

  public static fromJson (json:AssetLikeJson): AssetLike {

    return new this(
      json.id,
      (json.asset !== null) ? Asset.fromJson(json.asset) : null,
      (json.user !== null) ? User.fromJson(json.user) : null,
      DateTime.fromISO(json.createdAt),
      DateTime.fromISO(json.updatedAt)
    )
  }

  constructor (
    public id: string|undefined,
    public asset: Asset|null,
    public user: User|null,
    public createdAt: DateTime,
    public updatedAt: DateTime
  ){}

}
