import { DateTime } from 'luxon'
import { BaseModelInterface } from './interfaces/base_model'
import User from './user'
import { ListingJson } from 'data/jsons/listing_json'
import ListingType from './listing/listing_type'
import AuctionMethod from './listing/auction_method'
import PaymentToken from 'domain/models/payment_tokens'
import { ListingCreateJson } from 'data/jsons/listing/listing_create_json'
import { ListingUpdateJson } from 'data/jsons/listing/listing_update_json'
import { Status } from './listing/status'
import ListingAsset from './listing_asset'
import Asset from './asset'

export default class Listing implements BaseModelInterface {
  constructor (
    public id: string | undefined,
    public user: User | null,
    public assets: Asset[],
    public listingAssets: ListingAsset[],
    public quantities: number[],
    public listingType: ListingType,
    public auctionMethod: AuctionMethod | null,
    public paymentToken: PaymentToken | null,
    public price: number | null,
    public startingPrice: number | null,
    public endingPrice: number | null,
    public startAt: DateTime | null,
    public endAt: DateTime | null,
    public isBundle: boolean,
    public bundleName: string | null,
    public bundleDescription: string | null,
    public isReservedForBuyer: boolean,
    public reservedBuyerAddress: string | null,
    public hasReservePrice: boolean,
    public reservePrice: number | null,
    public status: Status,
    public createdAt: DateTime,
    public updatedAt: DateTime,
    public signature: string,
    public orderParams: any,
    public orderHash: string | null,
    public fees: any[],
  ) {}

  public static newEmtyListing (): Listing {
    return new this(
      undefined,
      null,
      [],
      [],
      [],
      ListingType.fixedPrice(),
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      false,
      null,
      null,
      false,
      null,
      false,
      null,
      Status.active(),
      DateTime.now(),
      DateTime.now(),
      '',
      '',
      '',
      []
    )
  }
  
  public static fromJson (json: ListingJson): Listing {
    if (json.listingType.id === undefined) {
      throw new Error('listingType.id is undefined')
    }
    if (json.listingType.id === null) {
      throw new Error('listingType.id is null')
    }
    const listingType = ListingType.fromId<ListingType>(json.listingType.id)
    if (listingType.isUnknown()) {
      throw new Error(`listingType.id is invalid: ${json.listingType.id}`)
    }

    // let auctionMethod: AuctionMethod = json.auctionMethod
    // if (json.auctionMethod !== null) {
    //   if (json.auctionMethod.getId() === undefined) {
    //     throw new Error('auctionMethod.id is undefined')
    //   }
    //   auctionMethod = AuctionMethod.fromId<AuctionMethod>(
    //     json.auctionMethod.getId()
    //   )
    //   if (auctionMethod.isUnknown()) {
    //     throw new Error(
    //       `auctionMethod.id is invalid: ${json.auctionMethod.getId()}`
    //     )
    //   }
    // }

    if (json.paymentToken !== null) {
      if (json.paymentToken.id === undefined) {
        throw new Error('paymentToken.id is undefined')
      }
    }
    let _listing = this.newEmtyListing()
    _listing.id = json.id
    _listing.user =  json.user !== null ? User.fromJson(json.user) : null
    _listing.listingAssets = json.listingAssets.map((listingAsset) => {
      return ListingAsset.fromJson(listingAsset)
    })
    _listing.quantities = json.quantities,
    _listing.listingType = listingType,
    _listing.auctionMethod = json.auctionMethod,
    _listing.paymentToken = PaymentToken.fromJson(json.paymentToken),
    _listing.price = json.price,
    _listing.startingPrice = json.startingPrice,
    _listing.endingPrice = json.endingPrice,
    _listing.startAt = json.startAt !== null ? DateTime.fromISO(json.startAt) : null,
    _listing.endAt = json.endAt !== null ? DateTime.fromISO(json.endAt) : null,
    _listing.isBundle = json.isBundle,
    _listing.bundleName = json.bundleName,
    _listing.bundleDescription = json.bundleDescription,
    _listing.isReservedForBuyer = json.isReservedForBuyer,
    _listing.reservedBuyerAddress = json.reservedBuyerAddress,
    _listing.hasReservePrice = json.hasReservePrice,
    _listing.reservePrice = json.reservePrice,
    _listing.status = Status.fromId<Status>(json.statusId),
    _listing.createdAt = DateTime.fromISO(json.createdAt),
    _listing.updatedAt = DateTime.fromISO(json.updatedAt),
    _listing.signature = json.signature,
    _listing.orderParams = json.orderParams  
    _listing.orderHash = json.orderHash  
    return _listing
  }

  public toCreateJson (): ListingCreateJson {
    if (this.assets.length === 0) {
      throw new Error('assets must be specified to create')
    }
    if (this.quantities.length === 0) {
      throw new Error('quantities must be specified to create')
    }
    if (this.assets.length !== this.quantities.length) {
      throw new Error('assets and quantities must be same length')
    }
    if (this.listingType === null) {
      throw new Error('listingType must be specified to create')
    }
    if (this.paymentToken === null) {
      throw new Error('paymentToken must be specified to create')
    }
    if (this.price === null) {
      throw new Error('price must be specified to create')
    }
    if (this.startAt === null) {
      throw new Error('startAt must be specified to create')
    }
    if (this.endAt === null) {
      throw new Error('endAt must be specified to create')
    }

    const assetIds = this.assets.map((asset) => {
      return asset.id ?? ''
    })

    return {
      assetIds: assetIds,
      quantities: this.quantities,
      listingType: this.listingType.getName(),
      auctionMethod: this.auctionMethod?.getName() ?? null,
      paymentTokenId: this.paymentToken?.id || null,
      price: this.price,
      endingPrice: this.endingPrice,
      startAt: this.startAt.toISO()!,
      endAt: this.endAt.toISO()!,
      isBundle: this.isBundle,
      bundleName: this.bundleName,
      bundleDescription: this.bundleDescription,
      isReservedForBuyer: this.isReservedForBuyer,
      reservedBuyerAddress: this.reservedBuyerAddress,
      hasReservePrice: this.hasReservePrice,
      reservePrice: this.reservePrice,
      signature: this.signature,
      orderParams: this.orderParams,
      orderHash: this.orderHash,
    }
  }

  public toUpdateJson (): ListingUpdateJson {
    if (this.id === undefined) {
      throw new Error('id must be specified to update')
    }
    if (
      this.isReservedForBuyer === undefined &&
      this.reservedBuyerAddress === null
    ) {
      throw new Error('isReservedForBuyer must be specified to update')
    }
    if (this.hasReservePrice === undefined && this.reservePrice === null) {
      throw new Error('hasReservePrice must be specified to update')
    }

    return {
      id: this.id,
      endAt: this.endAt !== null ? this.endAt.toISO() : null,
      bundleName: this.bundleName,
      bundleDescription: this.bundleDescription,
      isReservedForBuyer: this.isReservedForBuyer,
      reservedBuyerAddress: this.reservedBuyerAddress,
      hasReservePrice: this.hasReservePrice,
      reservePrice: this.reservePrice,
      signature: this.signature,
      orderParams: this.orderParams,
      statusId: this.status.getId(),
    }
  }

  public isActiveFixedPrice (): boolean {
    return this?.listingType?.isFixedPrice() && 
        (this.endAt?.diffNow()?.valueOf() || 0) > 0 && 
        (this.startAt?.diffNow()?.valueOf() || 0) < 0 && 
        this?.status?.isActive()
  }
  
  public isActiveTimedAuction (): boolean {
    return this?.listingType?.isTimedAuction() && 
        (this.endAt?.diffNow()?.valueOf() || 0) > 0 && 
        (this.startAt?.diffNow()?.valueOf() || 0) < 0 && 
        this?.status?.isActive()
  }
}
