import { ReportJson } from 'data/jsons/report_json'
import TargetEntityType from 'domain/value_objects/target_entity_type'
import { DateTime } from 'luxon'
import { BaseModelInterface } from './interfaces/base_model'
import AssetReason from './report/asset_reason'
import CollectionReason from './report/collection_reason'

export default class Report implements BaseModelInterface {

  public static fromJson (json:ReportJson): Report {

    if (json.targetEntityType.id === undefined) {
      throw new Error('targetEntityType.id is undefined')
    }
    const targetEntityType = TargetEntityType.fromId<TargetEntityType>(json.targetEntityType.id)
    if (targetEntityType.isUnknown()) {
      throw new Error(`targetEntityType.id is invalid: ${json.targetEntityType.id}`)
    }

    return new this(
      json.id,
      json.uniqueKey,
      targetEntityType,
      json.targetEntityId,
      json.reasonId,
      json.userId,
      DateTime.fromISO(json.createdAt),
      DateTime.fromISO(json.updatedAt)
    )
  }

  constructor (
    public id: string|undefined,
    public uniqueKey: string,
    public targetEntityType: TargetEntityType,
    public targetEntityId: string,
    public reasonId: number,
    public userId: string|null,
    public createdAt: DateTime,
    public updatedAt: DateTime
  ){}

  public getReason (): AssetReason|CollectionReason {
    if (this.targetEntityType.isAsset()) {
      return AssetReason.fromId<AssetReason>(this.reasonId)
    } else if (this.targetEntityType.isCollection()) {
      return CollectionReason.fromId<CollectionReason>(this.reasonId)
    } else {
      throw new Error('targetEntityType is unknown')
    }
  }

}
