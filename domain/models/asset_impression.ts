import { DateTime } from 'luxon'
import { BaseModelInterface } from './interfaces/base_model'
import { AssetImpressionJson } from 'data/jsons/asset_impression_json'
import Asset from './asset'
import User from './user'

export default class AssetImpression implements BaseModelInterface {

  public static fromJson (json:AssetImpressionJson): AssetImpression {

    return new this(
      json.id,
      (json.asset !== null) ? Asset.fromJson(json.asset) : null,
      (json.user !== null) ? User.fromJson(json.user) : null,
      DateTime.fromISO(json.createdAt),
      DateTime.fromISO(json.updatedAt)
    )
  }

  constructor (
    public id: string|undefined,
    public asset: Asset|null,
    public user: User|null,
    public createdAt: DateTime,
    public updatedAt: DateTime
  ){}

}
