import Base from 'domain/value_objects/base'

export class Format extends Base {

  public static getResourceArray () {
    return [
      {
        id: 1,
        mime: 'image/jpeg',
        name: 'jpeg',
        extension: 'jpg',
        label: 'JPEG',
      },
      {
        id: 2,
        mime: 'image/png',
        name: 'png',
        extension: 'png',
        label: 'PNG',
      },
      {
        id: 3,
        mime: 'image/gif',
        name: 'gif',
        extension: 'gif',
        label: 'GIF',
      },
      {
        id: 4,
        mime: 'image/svg+xml',
        name: 'svg',
        extension: 'svg',
        label: 'SVG',
      },
      {
        id: 5,
        mime: 'image/webp',
        name: 'webp',
        extension: 'webp',
        label: 'WEBP',
      },
      {
        id: 6,
        mime: 'image/tiff',
        name: 'tiff',
        extension: 'tif',
        label: 'TIFF',
      },
      {
        id: 7,
        mime: 'image/heic',
        name: 'heic',
        extension: 'heic',
        label: 'HEIC',
      },
      {
        id: 8,
        mime: 'image/avif',
        name: 'avif',
        extension: 'avif',
        label: 'AVIF',
      },
      {
        id: 9,
        mime: 'audio/vnd.wave',
        name: 'wave',
        extension: 'wav',
        label: 'WAVE',
      },
      {
        id: 10,
        mime: 'audio/aiff',
        name: 'aiff',
        extension: 'aif',
        label: 'AIFF',
      },
      {
        id: 11,
        mime: 'audio/mpeg',
        name: 'mp3',
        extension: 'mp3',
        label: 'MP3',
      },
      {
        id: 12,
        mime: 'audio/x-m4a',
        name: 'm4a',
        extension: 'm4a',
        label: 'M4A',
      },
      {
        id: 13,
        mime: 'video/webm',
        name: 'webm',
        extension: 'webm',
        label: 'WEBM',
      },
      {
        id: 14,
        mime: 'video/x-flv',
        name: 'flv',
        extension: 'flv',
        label: 'FLV',
      },
      {
        id: 15,
        mime: 'video/ogg',
        name: 'ogg',
        extension: 'ogv',
        label: 'OGG',
      },
      {
        id: 16,
        mime: 'video/vnd.avi',
        name: 'avi',
        extension: 'avi',
        label: 'AVI',
      },
      {
        id: 17,
        mime: 'video/quicktime',
        name: 'mov',
        extension: 'mov',
        label: 'MOV',
      },
      {
        id: 18,
        mime: 'video/x-ms-asf',
        name: 'wmv',
        extension: 'asf',
        label: 'WMV',
      },
      {
        id: 19,
        mime: 'video/mp4',
        name: 'mp4',
        extension: 'mp4',
        label: 'MP4',
      },
      {
        id: 20,
        mime: 'video/x-m4v',
        name: 'mp4',
        extension: 'mp4',
        label: 'MP4',
      },
      {
        id: 21,
        mime: 'video/x-matroska',
        name: 'matroska',
        extension: 'mkv',
        label: 'Matroska',
      },
      {
        id: 22,
        mime: 'model/gltf-binary',
        name: 'glb',
        extension: 'glb',
        label: 'GLB',
      },
      {
        id: 23,
        mime: 'application/json',
        name: 'json',
        extension: 'json',
        label: 'JSON',
      },
    ]
  }

  public static getUnknown<Format> (): Format {
    return new this({
      id: 0,
      mime: '---',
      name: 'unknown',
      extension: '---',
      label: '---',
    }) as Format
  }

  public static fromMime (mime:string): Format {
    const resourceOf = Format._mimeToResource()
    if (resourceOf[ mime ] === undefined) {
      return Format.getUnknown()
    }
    const resource = resourceOf[ mime ]
    return new Format(resource) 
  }

  public getMime (): string {
    return this.resource['mime'] as string
  }

  public getExtension (): string {
    return this.resource['extension'] as string
  }

  public toJson (): any {
    return {
      id: this.getId(),
      mime: this.getMime(),
      name: this.getName(),
      label: this.getLabel(),
      extension: this.getExtension(),
    }
  }

  public static jpeg (): Format {
    return this.fromName<Format>('jpeg')
  }

  public static png (): Format {
    return this.fromName<Format>('png')
  }

  public static gif (): Format {
    return this.fromName<Format>('gif')
  }

  public static svg (): Format {
    return this.fromName<Format>('svg')
  }

  public static webp (): Format {
    return this.fromName<Format>('webp')
  }

  public static tiff (): Format {
    return this.fromName<Format>('tiff')
  }

  public static heic (): Format {
    return this.fromName<Format>('heic')
  }

  public static avif (): Format {
    return this.fromName<Format>('avif')
  }

  public static wave (): Format {
    return this.fromName<Format>('wave')
  }

  public static aiff (): Format {
    return this.fromName<Format>('aiff')
  }

  public static mp3 (): Format {
    return this.fromName<Format>('mp3')
  }

  public static m4a (): Format {
    return this.fromName<Format>('m4a')
  }

  public static webm (): Format {
    return this.fromName<Format>('webm')
  }

  public static flv (): Format {
    return this.fromName<Format>('flv')
  }

  public static ogg (): Format {
    return this.fromName<Format>('ogg')
  }

  public static avi (): Format {
    return this.fromName<Format>('avi')
  }

  public static mov (): Format {
    return this.fromName<Format>('mov')
  }

  public static wmv (): Format {
    return this.fromName<Format>('wmv')
  }

  public static mp4 (): Format {
    return this.fromName<Format>('mp4')
  }

  public static matroska (): Format {
    return this.fromName<Format>('matroska')
  }

  public static glb (): Format {
    return this.fromName<Format>('glb')
  }

  private static _mimeToResource () {
    const resources = this.getResourceArray()
    const mimeToResource: {[key:string]:{[key:string]:string|number|boolean}} = {}
    resources.forEach((resource) => {
      let mime = resource['mime']
      mimeToResource[ mime ] = resource
    })
    return mimeToResource
  }
}