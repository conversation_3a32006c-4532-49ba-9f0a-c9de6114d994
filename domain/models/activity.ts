import { ActivityJson } from 'data/jsons/activity_json'
import PaymentToken from 'domain/models/payment_tokens'
import { DateTime } from 'luxon'
import ActivityEvent from './activity/activity_event'
import Asset from './asset'
import { BaseModelInterface } from './interfaces/base_model'
import User from './user'
import Chain from '../value_objects/chain'

export default class Activity implements BaseModelInterface {

  public static fromJson (json: ActivityJson): Activity {
    try {
      if (json.activityEvent.id === undefined) {
        throw new Error('activityEvent.id is undefined')
      }

      const activityEvent:ActivityEvent = ActivityEvent.fromId<ActivityEvent>(json.activityEvent.id as number)
      if (activityEvent.isUnknown()) {
        throw new Error(`activityEvent.id is invalid: ${json.activityEvent.id}`)
      }

      let asset = json.asset ? Asset.fromJson(json.asset) : null
      return new this(
        json.id,
        asset,
        activityEvent,
        json.collectionId,
        Chain.fromId<Chain>(json.chainId),
        json?.paymentToken?.id ? PaymentToken.fromJson(json.paymentToken) : null,
        json.price,
        json.quantity,
        (json.fromUser !== null) ? User.fromJson(json.fromUser) : null,
        (json.toUser !== null) ? User.fromJson(json.toUser) : null,
        DateTime.fromISO(json.createdAt),
        DateTime.fromISO(json.updatedAt),
        DateTime.fromISO(json.minedAt),
        json.offerer,
        json.recipient
      )
    }catch (error) {
      console.log('🚀 ~ Activity ~ fromJson ~ error:', error)
      throw error
    }
  }

  constructor (
    public id: string|undefined,
    public asset:Asset | null,
    public activityEvent:ActivityEvent,
    public collectionId: string | null,
    public chain: Chain,
    public paymentToken:PaymentToken|null,
    public price:number|null,
    public quantity:number|null,
    public fromUser:User|null,
    public toUser:User|null,
    public createdAt:DateTime,
    public updatedAt: DateTime,
    public minedAt: DateTime | null = null,
    public offerer: string | null = null,
    public recipient: string | null = null
  ) {}

}
