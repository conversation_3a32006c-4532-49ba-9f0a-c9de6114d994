import Base from 'domain/value_objects/base'

export class Status extends Base {

  public static getResourceArray () {
    return [
      {
        id: 1,
        name: 'draft',
        label: 'Draft',
      },
      {
        id: 2,
        name: 'reserved',
        label: 'Reserved',
      },
      {
        id: 3,
        name: 'closed',
        label: 'Closed',
      },
      {
        id: 9,
        name: 'published',
        label: 'Published',
      },
    ]
  }

  public static draft (): Status {
    return this.fromName<Status>('draft')
  }

  public static reserved (): Status {
    return this.fromName<Status>('reserved')
  }

  public static closed (): Status {
    return this.fromName<Status>('closed')
  }

  public static published (): Status {
    return this.fromName<Status>('published')
  }

  public isDraft (): boolean {
    return (this.getName() === 'draft')
  }

  public isReserved (): boolean {
    return (this.getName() === 'reserved')
  }

  public isClosed (): boolean {
    return (this.getName() === 'closed')
  }

  public isPublished (): boolean {
    return (this.getName() === 'published')
  }

}