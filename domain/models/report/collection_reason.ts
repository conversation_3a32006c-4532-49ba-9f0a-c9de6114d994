import Base from 'domain/value_objects/base'

export default class CollectionReason extends Base {

  public static getResourceArray () {

    return [
      {
        id: 1,
        name: 'fake',
        label: 'Fake collection or possible scam',
      },
      {
        id: 2,
        name: 'explicit',
        label: 'Explicit and sensitive content',
      },
      {
        id: 3,
        name: 'spam',
        label: 'spam',
      },
      {
        id: 4,
        name: 'wrongCategory',
        label: 'In the wrong category',
      },
      {
        id: 9,
        name: 'other',
        label: 'Other',
      },
    ]
  }

  public static fake (): CollectionReason {
    return this.fromName<CollectionReason>('fake')
  }

  public static explicit (): CollectionReason {
    return this.fromName<CollectionReason>('explicit')
  }

  public static spam (): CollectionReason {
    return this.fromName<CollectionReason>('spam')
  }

  public static wrongCategory (): CollectionReason {
    return this.fromName<CollectionReason>('wrongCategory')
  }

  public static other (): CollectionReason {
    return this.fromName<CollectionReason>('other')
  }

  public isFake (): boolean {
    return this.getName() === 'fake'
  }

  public isExplicit (): boolean {
    return this.getName() === 'explicit'
  }

  public isSpam (): boolean {
    return this.getName() === 'spam'
  }

  public isWrongCategory (): boolean {
    return this.getName() === 'wrongCategory'
  }

  public isOther (): boolean {
    return this.getName() === 'other'
  }
}