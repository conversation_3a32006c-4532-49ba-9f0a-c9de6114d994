import Base from 'domain/value_objects/base'

export default class AssetReason extends Base {

  public static getResourceArray () {
        
    return [
      {
        id: 1,
        name: 'fake',
        label: 'Fake collection or possible scam',
      },
      {
        id: 2,
        name: 'explicit',
        label: 'Explicit and sensitive content',
      },
      {
        id: 3,
        name: 'spam',
        label: 'spam',
      },
      {
        id: 9,
        name: 'other',
        label: 'Other',
      },
    ]
  }

  public static fake (): AssetReason {
    return this.fromName<AssetReason>('fake')
  }

  public static explicit (): AssetReason {
    return this.fromName<AssetReason>('explicit')
  }

  public static spam (): AssetReason {
    return this.fromName<AssetReason>('spam')
  }

  public static other (): AssetReason {
    return this.fromName<AssetReason>('other')
  }

  public isFake (): boolean {
    return this.getName() === 'fake'
  }

  public isExplicit (): boolean {
    return this.getName() === 'explicit'
  }

  public isSpam (): boolean {
    return this.getName() === 'spam'
  }

  public isOther (): boolean {
    return this.getName() === 'other'
  }
}