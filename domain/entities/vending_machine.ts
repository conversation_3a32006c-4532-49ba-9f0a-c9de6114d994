// Defines a package type (category)
export interface PackageType {
  id: string;
  title: string;
}

// Card item inside a package
export interface CardItem {
  id: number | string;
  image: string;
  rarity?: string;
}

// Defines a package item
export interface PackageItem {
  id: number | string;
  title: string;
  price: number;
  image: string;
  typeId: string;
  slug: string;
  cardItems?: CardItem[]; // Items shown on hover
}

// Detailed package for the package detail page
export interface PackageDetail extends PackageItem {
  description: string;
  expectedValue: number;
  odds: PackageOdds[];
  relatedPackages?: PackageItem[];
}

// Package odds information
export interface PackageOdds {
  range: string;
  percentage: number;
  isActive?: boolean;
} 