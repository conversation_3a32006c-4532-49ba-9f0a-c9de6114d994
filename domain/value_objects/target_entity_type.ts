import Base from './base'

export default class TargetEntityType extends Base {

  public static getResourceArray () {
    return [
      {
        id: 1,
        name: 'asset',
        label: 'Asset',
      },
      {
        id: 2,
        name: 'collection',
        label: 'Collection',
      },
    ]
  }

  public static asset (): TargetEntityType {
    return this.fromName('asset')
  }

  public static collection (): TargetEntityType {
    return this.fromName('collection')
  }

  public isAsset (): boolean {
    return this.getName() === 'asset'
  }

  public isCollection (): boolean {
    return this.getName() === 'collection'
  }
}