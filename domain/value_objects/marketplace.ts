import Base from './base'

export default class Marketplace extends Base {

  public static getResourceArray () {
    return [
      {
        id: 1,
        name: 'nftLand',
        label: 'Quill',
      },
      {
        id: 2,
        name: 'opensea',
        label: 'OpenSea',
      },
      {
        id: 3,
        name: 'looksRare',
        label: 'LooksRare',
      },
      {
        id: 4,
        name: 'x2y2',
        label: 'X2Y2',
      },
    ]
  }

  public getIconPath (): string {
    return `/asset/icons/marketplace/${this.getName()}.png`
  }

  public static nftLand (): Marketplace {
    return this.fromName('nftLand')
  }

  public static opensea (): Marketplace {
    return this.fromName('opensea')
  }

  public static looksRare (): Marketplace {
    return this.fromName('looksRare')
  }

  public static x2y2 (): Marketplace {
    return this.fromName('x2y2')
  }

  public isNftLand (): boolean {
    return this.getName() === 'nftLand'
  }

  public isOpensea (): boolean {
    return this.getName() === 'opensea'
  }

  public isLooksRare (): boolean {
    return this.getName() === 'looksRare'
  }

  public isX2y2 (): boolean {
    return this.getName() === 'x2y2'
  }
}