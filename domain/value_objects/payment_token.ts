import Base from './base'
import Chain from './chain'
import { tokenAddresses, nativeTokenAddresses } from 'lib/token_addresses'
export default class PaymentToken extends Base {
  public static getResourceArray () {
    return [
      // Ethereum
      {
        id: 1,
        name: 'eth',
        label: 'ETH',
        chainName: 'ethereum',
      },
      {
        id: 2,
        name: 'weth',
        label: 'WETH',
        chainName: 'ethereum',
      },
      {
        id: 3,
        name: 'dai',
        label: 'DAI',
        chainName: 'ethereum',
      },
      {
        id: 4,
        name: 'usdc',
        label: 'USDC',
        chainName: 'ethereum',
      },
      {
        id: 5,
        name: 'ape',
        label: 'APE',
        chainName: 'ethereum',
      },
      {
        id: 6,
        name: 'ash',
        label: 'ASH',
        chainName: 'ethereum',
      },
      {
        id: 7,
        name: 'bat',
        label: 'BAT',
        chainName: 'ethereum',
      },
      {
        id: 8,
        name: 'cube',
        label: 'CUBE',
        chainName: 'ethereum',
      },
      {
        id: 9,
        name: 'gala',
        label: 'GALA',
        chainName: 'ethereum',
      },
      {
        id: 10,
        name: 'link',
        label: 'LINK',
        chainName: 'ethereum',
      },
      {
        id: 11,
        name: 'mana',
        label: 'MANA',
        chainName: 'ethereum',
      },
      {
        id: 12,
        name: 'nct',
        label: 'NCT',
        chainName: 'ethereum',
      },
      {
        id: 13,
        name: 'revv',
        label: 'REVV',
        chainName: 'ethereum',
      },
      {
        id: 14,
        name: 'sand',
        label: 'SAND',
        chainName: 'ethereum',
      },
      {
        id: 15,
        name: 'uni',
        label: 'UNI',
        chainName: 'ethereum',
      },
      {
        id: 16,
        name: 'volt',
        label: 'VOLT',
        chainName: 'ethereum',
      },
      // Polygon
      {
        id: 17,
        name: 'matic',
        label: 'MATIC',
        chainName: 'polygon',
      },
      {
        id: 18,
        name: 'weth_polygon',
        label: 'WETH',
        chainName: 'polygon',
      },
      {
        id: 19,
        name: 'dai_polygon',
        label: 'DAI',
        chainName: 'polygon',
      },
      {
        id: 20,
        name: 'usdc_polygon',
        label: 'USDC',
        chainName: 'polygon',
      },
      {
        id: 21,
        name: 'revv_polygon',
        label: 'REVV',
        chainName: 'polygon',
      },
      {
        id: 22,
        name: 'sand_polygon',
        label: 'SAND',
        chainName: 'polygon',
      },
      // Arbitrum
      {
        id: 23,
        name: 'weth_arbitrum',
        label: 'WETH',
        chainName: 'arbitrum',
      },
      // Avalanche
      {
        id: 24,
        name: 'avax',
        label: 'AVAX',
        chainName: 'avalanche',
      },
      // BNB Chain
      {
        id: 25,
        name: 'bnb',
        label: 'BNB',
        chainName: 'binanceSmartChain',
      },
      // Klaytn
      {
        id: 26,
        name: 'klay',
        label: 'KLAY',
        chainName: 'klaytn',
      },
      // Optimism
      {
        id: 27,
        name: 'op',
        label: 'OP',
        chainName: 'optimism',
      },
      {
        id: 28,
        name: 'weth_optimism',
        label: 'WETH',
        chainName: 'optimism',
      },
    ]
  }

  public getAddress (): string {
    return tokenAddresses[this.getChainName()][this.getName()]
  }
  public getChainId (): number {
    return this.resource['chainId'] as number
  }
  public isNative (): boolean {
    return this.getAddress() === nativeTokenAddresses
  }

  public getIconPath (): string {
    return `/asset/icons/payment_tokens/${this.getName()}_${this.getChainName()}.png`
  }

  public getChainName (): string {
    return this.resource['chainName'] as string
  }

  public getChain (): Chain {
    return Chain.fromName<Chain>(this.getChainName())
  }

  public static eth (): PaymentToken {
    return this.fromName('eth')
  }

  public static weth (): PaymentToken {
    return this.fromName('weth')
  }

  public static dai (): PaymentToken {
    return this.fromName('dai')
  }

  public static usdc (): PaymentToken {
    return this.fromName('usdc')
  }

  public static ape (): PaymentToken {
    return this.fromName('ape')
  }

  public static ash (): PaymentToken {
    return this.fromName('ash')
  }

  public static bat (): PaymentToken {
    return this.fromName('bat')
  }

  public static cube (): PaymentToken {
    return this.fromName('cube')
  }

  public static gala (): PaymentToken {
    return this.fromName('gala')
  }

  public static link (): PaymentToken {
    return this.fromName('link')
  }

  public static mana (): PaymentToken {
    return this.fromName('mana')
  }

  public static nct (): PaymentToken {
    return this.fromName('nct')
  }

  public static revv (): PaymentToken {
    return this.fromName('revv')
  }

  public static sand (): PaymentToken {
    return this.fromName('sand')
  }

  public static uni (): PaymentToken {
    return this.fromName('uni')
  }

  public static volt (): PaymentToken {
    return this.fromName('volt')
  }

  public static matic (): PaymentToken {
    return this.fromName('matic')
  }

  public static wethPolygon (): PaymentToken {
    return this.fromName('weth_polygon')
  }

  public static daiPolygon (): PaymentToken {
    return this.fromName('dai_polygon')
  }

  public static usdcPolygon (): PaymentToken {
    return this.fromName('usdc_polygon')
  }

  public static revvPolygon (): PaymentToken {
    return this.fromName('revv_polygon')
  }

  public static sandPolygon (): PaymentToken {
    return this.fromName('sand_polygon')
  }

  public static avax (): PaymentToken {
    return this.fromName('avax')
  }

  public isEth (): boolean {
    return this.getName() === 'eth'
  }

  public isWeth (): boolean {
    return this.getName() === 'weth'
  }

  public isDai (): boolean {
    return this.getName() === 'dai'
  }

  public isUsdc (): boolean {
    return this.getName() === 'usdc'
  }

  public isApe (): boolean {
    return this.getName() === 'ape'
  }

  public isAsh (): boolean {
    return this.getName() === 'ash'
  }

  public isBat (): boolean {
    return this.getName() === 'bat'
  }

  public isCube (): boolean {
    return this.getName() === 'cube'
  }

  public isGala (): boolean {
    return this.getName() === 'gala'
  }

  public isLink (): boolean {
    return this.getName() === 'link'
  }

  public isMana (): boolean {
    return this.getName() === 'mana'
  }

  public isNct (): boolean {
    return this.getName() === 'nct'
  }

  public isRevv (): boolean {
    return this.getName() === 'revv'
  }

  public isSand (): boolean {
    return this.getName() === 'sand'
  }

  public isUni (): boolean {
    return this.getName() === 'uni'
  }

  public isVolt (): boolean {
    return this.getName() === 'volt'
  }

  public isMatic (): boolean {
    return this.getName() === 'matic'
  }

  public isWethPolygon (): boolean {
    return this.getName() === 'weth_polygon'
  }

  public isDaiPolygon (): boolean {
    return this.getName() === 'dai_polygon'
  }

  public isUsdcPolygon (): boolean {
    return this.getName() === 'usdc_polygon'
  }

  public isRevvPolygon (): boolean {
    return this.getName() === 'revv_polygon'
  }

  public isSandPolygon (): boolean {
    return this.getName() === 'sand_polygon'
  }

  public isAvax (): boolean {
    return this.getName() === 'avax'
  }
}
