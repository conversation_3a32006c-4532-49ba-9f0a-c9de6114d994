import Base from './base'
import ResourcesForDev from './chain/resources_for_dev'
import ResourcesForProd from './chain/resources_for_prod'
import { chains } from 'presentation/constant/chains'
import * as chainsWagmi from '@wagmi/core/chains'


export default class Chain extends Base {
  public static getResourceArray () {
    return process.env.NEXT_PUBLIC_NODE_ENV === 'production'
      ? ResourcesForProd
      : ResourcesForDev
  }

  public getIconPath (): string {
    return `/asset/icons/${this.getName()}.png`
  }
  
  public getSymbol (): string {
    if(this.isEthereum()) {
      return 'ETH'
    }
    if(this.isPolygon()) {
      return 'POL'
    }
    return ''
  }
  
  public getExplorerUrl (): string {
    const chainId = Number(this.getChainId())
    const chainInfo = [...chains].find((chain) => chain.id === chainId)
    if(!chainInfo) {
      const arrChain = Object.values(chainsWagmi)
      const wagmiChainInfo = arrChain.find((i) => i.id === chainId)
      return wagmiChainInfo?.blockExplorers?.default?.url || ''
    }
    return chainInfo.blockExplorers?.default?.url || ''
  }

  public static ethereum (): Chain {
    return this.fromName('ethereum')
  }

  public static polygon (): Chain {
    return this.fromName('polygon')
  }

  public static binanceSmartChain (): Chain {
    return this.fromName('binanceSmartChain')
  }

  public static solana (): Chain {
    return this.fromName('solana')
  }

  public static avalanche (): Chain {
    return this.fromName('avalanche')
  }

  public static ethereumSepolia (): Chain {
    return this.fromName('ethereumSepolia')
  }

  public static ethereumAmoy (): Chain {
    return this.fromName('ethereumAmoy')
  }

  public isEthereum (): boolean {
    return this.getName() === 'ethereum'
  }

  public isPolygon (): boolean {
    return this.getName() === 'polygon'
  }

  public isBinanceSmartChain (): boolean {
    return this.getName() === 'binanceSmartChain'
  }

  public isSolana (): boolean {
    return this.getName() === 'solana'
  }

  public isAvalanche (): boolean {
    return this.getName() === 'avalanche'
  }

  public isEthereumSepolia (): boolean {
    return this.getName() === 'ethereumSepolia'
  }

  public isEthereumAmoy (): boolean {
    return this.getName() === 'ethereumAmoy'
  }

}
