const ResourcesForProd = [
  {
    id: 1,
    chainId: 1,
    name: 'ethereum',
    label: 'Ethereum',
    currency: 'ETH',
  },
  {
    id: 137,
    chainId: 137,
    name: 'polygon',
    label: 'Polygon',
    currency: 'POL',
  },
  {
    id: 29548,
    chainId: 29548,
    name: 'mchverse',
    label: 'MCH Verse',
    currency: 'OAS',
  },
  {
    id: 10143,
    chainId: 10143,
    name: 'monad',
    label: 'Monad Testnet',
    currency: 'M<PERSON>',
  },
  {
    id: 812397,
    chainId: 812397,
    name: 'sgverse',
    label: 'SG Verse',
    currency: 'OAS',
  },
  {
    id: 56,
    chainId: 56,
    name: 'bsc',
    label: 'BSC',
    currency: 'BNB',
  },
  {
    id: 19011,
    chainId: 19011,
    name: 'homeverse',
    label: 'Home Verse',
    currency: 'OAS',
  },
  {
    id: 248,
    chainId: 248,
    name: 'oasys',
    label: 'OASYS',
    currency: 'OAS',
  },
  {
    id: 42161,
    chainId: 42161,
    name: 'arbitrum',
    label: 'Arbitrum',
    currency: 'ETH',
  },
  {
    id: 43114,
    chainId: 43114,
    name: 'avalanche',
    label: 'Avalanche',
    currency: 'AVAX',
  },
  {
    id: 10,
    chainId: 10,
    name: 'optimism',
    label: 'Optimism',
    currency: 'ETH',
  },
  {
    id: 1088,
    chainId: 1088,
    name: 'metis',
    label: 'Metis',
    currency: 'METIS',
  },
  {
    id: 1101,
    chainId: 1101,
    name: 'polygonZkEvm',
    label: 'Polygon zkEVM',
    currency: 'ETH',
  },
  // {
  //   id: 2400,
  //   chainId: 2400,
  //   name: 'tcgverse',
  //   label: 'TCG Verse',
  //   currency: 'OAS',
  // },
  // {
  //   id: 999,
  //   chainId: 999,
  //   name: 'hyperliquid',
  //   label: 'Hyperliquid',
  //   currency: 'HYPE',
  // },
]

export default ResourcesForProd
