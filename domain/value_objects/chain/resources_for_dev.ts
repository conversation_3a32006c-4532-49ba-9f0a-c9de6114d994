const ResourcesForDev = [
  // TODO: demo assets' chain is ethereum. Remove this later
  {
    id: 17000,
    chainId: 17000,
    name: 'ethereum',
    label: 'Ether<PERSON> Holesky',
    currency: 'ETH',
  },
  {
    id: 11155111,
    chainId: 11155111,
    name: 'ethereumSepolia',
    label: 'Ethereum Sepolia',
    currency: 'ETH',
  },
  {
    id: 80002,
    chainId: 80002,
    name: 'polygon',
    label: 'Polygon Amoy',
    currency: 'POL',
  },
  {
    id: 420,
    chainId: 420,
    name: 'mchverse',
    label: 'MCH Testnet',
    currency: 'OAS',
  },
  {
    id: 40875,
    chainId: 40875,
    name: 'homeverse',
    label: 'Home Verse',
    currency: 'OAS',
  },
  {
    id: 247101234,
    chainId: 247101234,
    name:'sgverse',
    label: 'SG Verse',
    currency: 'OAS',
  },
  {
    id: 12401,
    chainId: 12401,
    name:'tcgverse',
    label: 'TCG Verse',
    currency: 'OAS',
  },
  {
    id: 10143,
    chainId: 10143,
    name: 'monad',
    label: 'Monad Testnet',
    currency: 'MON',
  },
  {
    id: 97,
    chainId: 97,
    name: 'bscTestnet',
    label: 'BSC Testnet',
    currency: 'BNB',
  },
  {
    id: 9372,
    chainId: 9372,
    name: 'oasys',
    label: 'OASYS',
    currency: 'OAS',
  },
  {
    id: 43113,
    chainId: 43113,
    name: 'avalancheFuji',
    label: 'Avalanche Fuji',
    currency: 'AVAX',
  },
  {
    id: 421614,
    chainId: 421614,
    name: 'arbitrumSepolia',
    label: 'Arbitrum Sepolia',
    currency: 'ETH',
  },
  {
    id: 11155420,
    chainId: 11155420,
    name: 'optimismSepolia',
    label: 'Optimism Sepolia',
    currency: 'ETH',
  },
  {
    id: 2442,
    chainId: 2442,
    name: 'polygonZkEvmCardona',
    label: 'Polygon zkEVM Cardona',
    currency: 'ETH',
  },  
  {
    id: 59902,
    chainId: 59902,
    name: 'metisSepolia',
    label: 'Metis Sepolia',
    currency: 'sMETIS',
  },
  {
    id: 999,
    chainId: 999,
    name: 'hyperliquid',
    label: 'Hyperliquid',
    currency: 'HYPE',
  },
]

export default ResourcesForDev
