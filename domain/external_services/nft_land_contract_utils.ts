import { balanceOf } from '@t2-web/nft_land_js/lib/utils/balance.js'
import {
  type InputCriteria,
  type Item,
} from '@t2-web/nft_land_js/lib/types'
import { providers as multicallProviders } from '@0xsequence/multicall'

export default class NftLandContractUtils {
  
  public getBalanceOf (owner: string, item: Item, multicallProvider: multicallProviders.MulticallProvider, criteria?: InputCriteria ){
    return balanceOf(owner, item, multicallProvider, criteria)
  }

}
