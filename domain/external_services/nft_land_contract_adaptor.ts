import { NftLand } from '@t2-web/nft_land_js'
import { ExecutionStructOutput } from '@t2-web/nft_land_js/lib/typechain/NftLand'
import { balanceOf } from '@t2-web/nft_land_js/lib/utils/balance.js'
import {
  type ContractMethodReturnType,
  CreateBulkOrdersAction,
  CreateCollectionInput,
  ConfigCollectionInput,
  CreateOrderAction,
  CreateOrderInput, type DomainRegistryContract,
  type ExchangeAction,
  type InputCriteria, type MatchOrdersFulfillment,
  type NftLandContract,
  type Order,
  type OrderComponents,
  type OrderStatus,
  OrderUseCase,
  type OrderWithCounter,
  type TipInputItem,
  type TransactionMethods,
  type Item,
  type ClaimNFTInput,
  type RefuncForClaimNFTInput,
} from '@t2-web/nft_land_js/lib/types'
import { BigNumber, BigNumberish, ethers, PayableOverrides } from 'ethers'
import { providers as multicallProviders } from '@0xsequence/multicall'
import ContractAdaptorDatasource from 'data/datasources/interfaces/contract_adaptor_datasource'

export default class NftLandContractAdaptor {
  private sdk: NftLand
  constructor (contract: ContractAdaptorDatasource) {
    console.log('🚀 ~ NftLandContractAdaptor ~ constructor ~ contract:', contract)
    this.sdk = new NftLand(contract)
  }

  /**
   * @param input
   * @param accountAddress
   * @param exactApproval
   */
  public createOrder (input: CreateOrderInput, accountAddress?: string, exactApproval?: boolean): Promise<OrderUseCase<CreateOrderAction>> {
    return this.sdk.createOrder(input, accountAddress, exactApproval)
  }

  /**
   * @param createOrderInput
   * @param accountAddress
   * @param exactApproval
   */
  public createBulkOrders (createOrderInput: CreateOrderInput[], accountAddress?: string, exactApproval?: boolean): Promise<OrderUseCase<CreateBulkOrdersAction>> {
    return this.sdk.createBulkOrders(createOrderInput, accountAddress, exactApproval)
  }

  /**
   * @param orderComponents
   * @param accountAddress
   */
  public signOrder (orderComponents: OrderComponents, accountAddress?: string): Promise<string> {
    return this.sdk.signOrder(orderComponents, accountAddress)
  }

  /**
   * @param orderComponents
   * @param accountAddress
   */
  public signBulkOrder (orderComponents: OrderComponents[], accountAddress?: string): Promise<OrderWithCounter[]> {
    return this.sdk.signBulkOrder(orderComponents, accountAddress)
  }

  /**
   * @param orders
   * @param accountAddress
   * @param domain
   */
  public cancelOrders (orders: OrderComponents[], accountAddress?: string, domain?: string): TransactionMethods<ContractMethodReturnType<NftLandContract, 'cancel'>> {
    return this.sdk.cancelOrders(orders, accountAddress, domain)
  }

  /**
   * @param offerer
   * @param domain
   */
  public bulkCancelOrders (offerer?: string, domain?: string): TransactionMethods<ContractMethodReturnType<NftLandContract, 'incrementCounter'>> {
    return this.sdk.bulkCancelOrders(offerer, domain)
  }

  /**
   * @param orders
   * @param accountAddress
   * @param domain
   */
  public validate (orders: Order[], accountAddress?: string, domain?: string): TransactionMethods<ContractMethodReturnType<NftLandContract, 'validate'>> {
    return this.sdk.validate(orders, accountAddress, domain)
  }

  /**
   * @param orderHash
   */
  public getOrderStatus (orderHash: string): Promise<OrderStatus> {
    return this.sdk.getOrderStatus(orderHash)
  }

  /**
   * @param offerer
   */
  public getCounter (offerer: string): Promise<BigNumber> {
    return this.sdk.getCounter(offerer)
  }

  /**
   * @param orderComponents
   */
  public getOrderHash (orderComponents: OrderComponents): string {
    return this.sdk.getOrderHash(orderComponents)
  }

  /**
   * @param order
   * @param unitsToFill
   * @param offerCriteria
   * @param considerationCriteria
   * @param tips
   * @param extraData
   * @param accountAddress
   * @param conduitKey
   * @param recipientAddress
   * @param domain
   * @param exactApproval
   */
  public fulfillOrder ({ order, unitsToFill, offerCriteria, considerationCriteria, tips, extraData, accountAddress, conduitKey, recipientAddress, domain, exactApproval }: {
    order: OrderWithCounter;
    unitsToFill?: BigNumberish;
    offerCriteria?: InputCriteria[];
    considerationCriteria?: InputCriteria[];
    tips?: TipInputItem[];
    extraData?: string;
    accountAddress?: string;
    conduitKey?: string;
    recipientAddress?: string;
    domain?: string;
    exactApproval?: boolean;
  }): Promise<OrderUseCase<ExchangeAction<ContractMethodReturnType<NftLandContract, 'fulfillBasicOrder' | 'fulfillOrder' | 'fulfillAdvancedOrder'>>>> {
    return this.sdk.fulfillOrder({
      order,
      unitsToFill,
      offerCriteria,
      considerationCriteria,
      tips,
      extraData,
      accountAddress,
      conduitKey,
      recipientAddress,
      domain,
      exactApproval,
    })
  }

  /**
   * @param fulfillOrderDetails
   * @param accountAddress
   * @param conduitKey
   * @param recipientAddress
   * @param domain
   * @param exactApproval
   */
  public fulfillOrders ({ fulfillOrderDetails, accountAddress, conduitKey, recipientAddress, domain, exactApproval }: {
    fulfillOrderDetails: {
      order: OrderWithCounter;
      unitsToFill?: BigNumberish;
      offerCriteria?: InputCriteria[];
      considerationCriteria?: InputCriteria[];
      tips?: TipInputItem[];
      extraData?: string;
    }[];
    accountAddress?: string;
    conduitKey?: string;
    recipientAddress?: string;
    domain?: string;
    exactApproval?: boolean;
  }): Promise<OrderUseCase<ExchangeAction<[boolean[], ExecutionStructOutput[]]>>> {
    return this.sdk.fulfillOrders({
      fulfillOrderDetails,
      accountAddress,
      conduitKey,
      recipientAddress,
      domain,
      exactApproval,
    })
  }

  /**
   * @param orders
   * @param fulfillments
   * @param overrides
   * @param accountAddress
   * @param domain
   */
  public matchOrders ({ orders, fulfillments, overrides, accountAddress, domain }: {
    orders: (OrderWithCounter | Order)[];
    fulfillments: MatchOrdersFulfillment[];
    overrides?: PayableOverrides;
    accountAddress?: string;
    domain?: string;
  }): TransactionMethods<ContractMethodReturnType<NftLandContract, 'matchOrders'>> {
    return this.sdk.matchOrders({
      orders,
      fulfillments,
      overrides,
      accountAddress,
      domain,
    })
  }

  /**
   * @param domain
   * @param accountAddress
   */
  public setDomain (domain: string, accountAddress?: string): TransactionMethods<ContractMethodReturnType<DomainRegistryContract, 'setDomain'>> {
    return this.sdk.setDomain(domain, accountAddress)
  }

  /**
   * @param tag
   */
  public getNumberOfDomains (tag: string): Promise<BigNumber> {
    return this.sdk.getNumberOfDomains(tag)
  }

  /**
   * @param tag
   * @param index
   */
  public getDomain (tag: string, index: number): Promise<string> {
    return this.sdk.getDomain(tag, index)
  }

  /**
   * @param tag
   * @param shouldThrow
   */
  public getDomains (tag: string, shouldThrow?: boolean): Promise<string[]> {
    return this.sdk.getDomains(tag, shouldThrow)
  }

  // collection sections - START
  public createCollection (input: CreateCollectionInput,accountAddress: string) {
    return this.sdk.createCollection(input, accountAddress)
  }
  // collection sections - END

  /**
   * 
   * @param input 
   * @param accountAddress 
   * @returns 
   */
  public createAsset (input: ConfigCollectionInput, accountAddress: string ){
    return this.sdk.multiConfigForCollection(input, accountAddress)
  } 
  
  /**
   * 
   * @param input 
   * @param accountAddress 
   * @returns 
   */
  public claimNFT (input: ClaimNFTInput, accountAddress: string ){
    return this.sdk.claimNFT(input, accountAddress)
  } 

  /**
   * 
   * @param input 
   * @param accountAddress 
   * @returns 
   */
  public refundForClaimNFT (input: RefuncForClaimNFTInput, accountAddress: string ){
    return this.sdk.refundForClaimNFT(input, accountAddress)
  } 
}
