export default abstract class BaseError extends <PERSON>rror {

  protected errors:string[] = []
  protected errorObjects:any[] = []
  constructor (message:string, errors:string[], errorObjects?:any[]) {
    super(message)
    this.errors = errors
    this.errorObjects = errorObjects || []
  }

  public getMessage (): string {
    return this.message
  }

  public getErrors (): string[] {
    return this.errors
  }
  public getErrorObjects (): any[] {
    return this.errorObjects
  }

  public showMsgErrors (): string {
    if(this.errors?.length) {
      return this.errors[0]
    }
    if(this.message) {
      return this.message
    }
    return 'An error occurreds'
  }
}