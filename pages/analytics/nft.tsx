import Head from 'next/head'
import { NextPage } from 'next'
import { useRouter } from 'next/router'
import { useState, useEffect } from 'react'
import ButtonTabs from 'presentation/components/button-tabs'
import Welcome from 'presentation/page_components/analytics/welcome'
import NftBoardGUI from 'presentation/page_components/analytics/nftBoard'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import { FilterParams } from 'types/type'
import { analyticsTabs } from 'presentation/page_components/analytics/_analyticsTabs'
import { pageSizes } from 'lib/hook/customHook'
import CategoryUseCase from 'presentation/use_cases/category_use_case'
import { Query } from 'domain/repositories/category_repository/query'
import Category from 'domain/models/category'
import { toast } from 'react-toastify'

const categoryUseCase = new CategoryUseCase()
const PageSize = pageSizes.pageSize20

//Todo remove mock data
import { nftTableData } from 'presentation/controllers/mockData/analytics_nft_mock_data'
//End todo remove mock data

const NftBoard: NextPage = () => {
  const initParams: FilterParams = {
    categoryId: '',
    page: 1,
    limit: PageSize,
  }

  const router = useRouter()
  const [activeTab, setActiveTab] = useState<string>('')
  const [total, setTotal] = useState<number>(0)
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [currentLimit, setCurrentLimit] = useState<number>(PageSize)
  const [categories, setCategories] = useState<Category[]>([])
  const [isFetching, setIsFetching] = useState<boolean>(true)
  const { filterParams, changeFilterParam, setFilterParams } =
    useChangeFilterParams(initParams)
  //TODO remove any type
  const [filterCategories, setFilterCategories] = useState<any>([])
  const [tableData, setTableData] = useState<any>([])
  //End Todo

  const changeRequestTab = (tab: string) => {
    router.push(`/analytics/${tab}`, undefined, { shallow: true })
  }

  const fetchCategories = async () => {
    const query = new Query()
    await categoryUseCase
      .search(query)
      .then((res) => {
        setCategories(res)
      })
      .catch((_error) => {
        toast.error('Failed to fetch categories')
      })
  }

  const fetchTableData = () => {
    setIsFetching(true)
    //TODO
    setTotal(nftTableData.length)
    let data = nftTableData.slice(
      (currentPage - 1) * currentLimit,
      currentPage * currentLimit
    )
    setTableData(data)
    setIsFetching(false)
  }

  const formatCategories = () => {
    const formattedCategories = categories.map((category: any) => {
      return {
        id: category.id,
        name: category.name,
        value: category.slug,
      }
    })
    setFilterCategories(formattedCategories)
  }

  useEffect(() => {
    fetchTableData()
  }, [filterParams])

  useEffect(() => {
    if (categories.length > 0) {
      formatCategories()
    }
  }, [categories])

  useEffect(() => {
    if (router.asPath.includes('nft')) {
      setActiveTab('nft')
    }
    fetchCategories()
  }, [])

  return (
    <>
      <Head>
        <title>Analytics - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>
      <main>
        <NftBoardGUI
          filterParams={filterParams}
          currentPage={currentPage}
          onChangePage={setCurrentPage}
          currentLimit={currentLimit}
          onChangeCurrentLimit={setCurrentLimit}
          tableData={tableData}
          total={total}
          onChangeFilterParam={changeFilterParam}
          onChangeFilterParams={setFilterParams}
          categories={filterCategories}
          isFetching={isFetching}
          analyticTabs={
            <ButtonTabs
              tabs={analyticsTabs}
              onChangeTab={changeRequestTab}
              activeTab={activeTab}
            />
          }
        />
        <Welcome />
      </main>
    </>
  )
}

export default NftBoard
