import Head from 'next/head'
import { NextPage } from 'next'
import { useRouter } from 'next/router'
import { useState, useEffect } from 'react'
import ButtonTabs from 'presentation/components/button-tabs'
import Welcome from 'presentation/page_components/analytics/welcome'
import MintBoardGUI from 'presentation/page_components/analytics/mintBoard'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import { FilterParams } from 'types/type'
import { analyticsTabs } from 'presentation/page_components/analytics/_analyticsTabs'

//Todo remove mock data
import { mintTableData } from 'presentation/controllers/mockData/analytics_mint_mock_data'
import { pageSizes } from 'lib/hook/customHook'
//End todo remove mock data

const PageSize = pageSizes.pageSize20

const MintBoard: NextPage = () => {
  const initParams: FilterParams = {
    page: 1,
    limit: PageSize,
  }

  const router = useRouter()
  const [activeTab, setActiveTab] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [currentLimit, setCurrentLimit] = useState(PageSize)
  const [tableData, setTableData] = useState<any>([])
  const [total, setTotal] = useState(0)
  const { filterParams, changeFilterParam, setFilterParams } = useChangeFilterParams(initParams)
  const [isFetching, setIsFetching] = useState(true)

  const fetchTableData = () => {
    setIsFetching(true)
    //TODO
    setTotal(mintTableData.length)
    let data = mintTableData.slice((currentPage - 1) * currentLimit, currentPage * currentLimit)
    setTableData(data)
    setIsFetching(false)
  }

  const changeRequestTab = (tab: string) => {
    router.push(`/analytics/${tab}`, undefined, { shallow: true })
  }

  useEffect(() => {
    fetchTableData()
  }, [filterParams])

  useEffect(() => {
    if (router.asPath.includes('mint')) {
      setActiveTab('mint')
    }
  }, [])

  return (
    <>
      <Head>
        <title>Analytics - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>
      <main>
        <MintBoardGUI
          filterParams={filterParams}
          currentPage={currentPage}
          onChangePage={setCurrentPage}
          currentLimit={currentLimit}
          onChangeCurrentLimit={setCurrentLimit}
          tableData={tableData}
          total={total}
          onChangeFilterParam={changeFilterParam}
          onChangeFilterParams={setFilterParams}
          isFetching={isFetching}
          analyticTabs={<ButtonTabs tabs={analyticsTabs} onChangeTab={changeRequestTab} activeTab={activeTab} />}
        />
        <Welcome />
      </main>
    </>
  )
}

export default MintBoard
