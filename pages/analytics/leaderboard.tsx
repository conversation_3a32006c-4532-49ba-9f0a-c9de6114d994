import Head from 'next/head'
import { NextPage } from 'next'
import { useRouter } from 'next/router'
import { useState, useEffect } from 'react'
import ButtonTabs from 'presentation/components/button-tabs'
import Welcome from 'presentation/page_components/analytics/welcome'
import LeaderboardTab from 'presentation/page_components/collection-analytics/leaderboardTab'
import { analyticsTabs } from 'presentation/page_components/analytics/_analyticsTabs'
const LeaderBoard: NextPage = () => {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('')

  const changeRequestTab = (tab: string) => {
    router.push(`/analytics/${tab}`, undefined, { shallow: true })
  }

  useEffect(() => {
    if (router.asPath.includes('leaderboard')) {
      setActiveTab('leaderboard')
    }
  }, [])

  return (
    <>
      <Head>
        <title>Analytics - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>
      <main>
        <LeaderboardTab
          analyticTabs={<ButtonTabs tabs={analyticsTabs} onChangeTab={changeRequestTab} activeTab={activeTab} />}
        />
        <Welcome/>
      </main>
    </>
  )
}

export default LeaderBoard
