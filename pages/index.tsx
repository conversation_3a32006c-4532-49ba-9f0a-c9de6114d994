import { NextPage } from 'next'
import { useState, useEffect } from 'react'
import { useScreenWidth, screen } from 'lib/hook/customHook'
import Hero from 'presentation/page_components/homepage/hero'
import ToggleCard from 'presentation/components/toggle-card'
import Recommend from 'presentation/page_components/homepage/recommend-slide'
import Services from 'presentation/page_components/homepage/services'
import TopSale from 'presentation/page_components/homepage/top-sale'
import NewCollections from 'presentation/page_components/homepage/new-collections'
import NewListings from 'presentation/page_components/homepage/new-listings'
import TopSaleMobile from 'presentation/page_components/homepage/top-sale-mobile'
import News from 'presentation/page_components/homepage/news'
import Head from 'next/head'
const Home: NextPage = () => {
  const [isMobile, setIsMobile] = useState(false)
  const [isReady, setIsReady] = useState(false)

  const screenWidth = useScreenWidth()

  useEffect(() => {
    setIsReady(false)
    if (screenWidth && (screenWidth < screen.md)) {
      setIsMobile(true)
    } else {
      setIsMobile(false)
    }
    setIsReady(true)
  }, [screenWidth])

  return (
    <>
      <Head>
        <title>NFT Marketplace - Quill</title>
        <meta name="description" content="Quill is a versatile NFT marketplace where you can buy, sell, and explore unique digital assets. Enjoy seamless transactions and a user-friendly platform for all your NFT needs." />
        <meta name="viewport" content="initial-scale=1, width=device-width" />

        <meta property="og:title" content="NFT Marketplace - Quill"/>
        <meta property="og:type" content="website"/>
        <meta property="og:url" content="https://apps.quillnft.net/"/>
        <meta property="og:image" content="https://apps.quillnft.net/ogp-img.png"/>
        <meta property="og:site_name" content="Quill"/>
        <meta property="og:description" content="Quill is a versatile NFT marketplace where you can buy, sell, and explore unique digital assets. Enjoy seamless transactions and a user-friendly platform for all your NFT needs."/>
        <meta property="og:locale" content="en_US"/>

        <link rel="icon" href="/favicon.ico" />
      </Head>
      <main>
        <Hero />
        <div className="container m-auto">
          {isReady && (
            <NewCollections />
          )} 
          {isReady && (
            <NewListings />
          )}
          {isReady && (
            <TopSale />
          )} 
          {/* {isReady && isMobile && (
            <TopSaleMobile />
          )}
          {isReady && !isMobile && (
            <TopSale />
          )} */}
          <div className="recommend-section m-auto bg-[url('/asset/icons/recommend-main-bg.png')] bg-cover">
            <Recommend />
          </div>
        </div>
        <div className="container m-auto mt-10">
          <News />
        </div>
        {/* <Services isMobile={false} /> */}
        {/* <ToggleCard /> */}
      </main>
    </>
  )
}

export default Home
