import { NextPage } from 'next'
import Link from 'next/link'
import Head from 'next/head'
import { useRouter } from 'next/router'
import { useState } from 'react'
import Container from 'presentation/components/container'
import Heading from 'presentation/components/heading'
import Tooltip from 'presentation/components/tooltip'
import ButtonTag from 'presentation/components/button/button-tag'
import Welcome from 'presentation/page_components/analytics/welcome'
import TradeTab from 'presentation/page_components/whale-tracking/tradeTab'
import MintTab from 'presentation/page_components/whale-tracking/mintTab'
import ActivityTab from 'presentation/page_components/whale-tracking/activityTab'
import WhaleTab from 'presentation/page_components/whale-tracking/whaleTab'

export const whaleTrackingTabs = [
  {
    name: 'Trade',
    slug: '',
  },
  {
    name: 'Mint',
    slug: 'mint',
  },
  {
    name: 'Activity',
    slug: 'activity',
  },
  {
    name: 'Whale',
    slug: 'whale',
  },
]

const WhaleTracking: NextPage = (props: any) => {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState(props.initTab)

  const changeRequestTab = (tab: string) => {
    router.push(
      {
        query: { ...router.query, tab },
      },
      undefined,
      { shallow: true }
    )

    setActiveTab(tab)
  }

  return (
    <>
      <Head>
        <title>Whale Tracking - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>
      <main>
        <Container className="pt-9">
          <div className="mb-30p text-center">
            <Heading className="text-dark-primary dark:text-white">
              Whale Tracking
            </Heading>
            <div className="mt-4 flex flex-wrap items-center justify-center">
              <p className="text-gray-1 dark:text-gray-3 text-stroke-thin">
                🐋 1,107 whales hold NFTs worth $4.57B, occupying 26.87% of the
                global market cap of $17B.
              </p>
              <div className="flex items-center shrink-0 space-x-1">
                <Tooltip className="-mt-0.5">Information</Tooltip>
                <Link
                  href=""
                  className="text-primary-2 text-stroke-thin pt-0.5"
                >
                  Whale List
                </Link>
              </div>
            </div>
          </div>

          <div className="mb-5 flex flex-wrap justify-center lg:justify-start lg:flex-nowrap gap-2 md:gap-4 xl:gap-5">
            {whaleTrackingTabs.map((tab, key) => (
              <ButtonTag
                key={key}
                className="shrink-0"
                active={activeTab === tab.slug}
                onClick={() => changeRequestTab(tab.slug)}
              >
                {tab.name}
              </ButtonTag>
            ))}
          </div>

          {activeTab === '' && <TradeTab />}
          {activeTab === 'mint' && <MintTab />}
          {activeTab === 'activity' && <ActivityTab />}
          {activeTab === 'whale' && <WhaleTab />}
        </Container>
        <Welcome />
      </main>
    </>
  )
}

export async function getServerSideProps (context: any) {
  let initTab = ''
  if (context.query.tab) {
    initTab = context.query.tab
  }

  return {
    props: {
      initTab,
    },
  }
}

export default WhaleTracking
