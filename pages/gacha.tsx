import { NextPage } from 'next'
import Head from 'next/head'
import Link from 'next/link'
import { useState, Fragment, useEffect } from 'react'
import { CountDown } from 'types/type'
import Container from 'presentation/components/container'
import Heading from 'presentation/components/heading'
import Image from 'next/image'
import But<PERSON> from 'presentation/components/button'
import SvgIcon from 'presentation/components/svg-icon'
import ButtonIcon from 'presentation/components/button/button-icon'
import UserCard from 'presentation/components/card/user-card'
import StatCard from 'presentation/components/card/stat-card'

const countDown: CountDown = {
  day: 0,
  hrs: 2,
  min: 54,
  sec: 16,
}

//Todo remove mock data
import { gachaRecord } from 'presentation/controllers/mockData/gacha_mock_data'
//End todo remove mock data

const Gacha: NextPage = () => {
  const [isFetching, setIsFetching] = useState<boolean>(true)
  //Todo remove any type
  const [gacha, setGacha] = useState<any>({})
  //End todo

  const fetchGacha = () => {
    setIsFetching(true)
    //TODO remove
    setGacha(gachaRecord)
    setTimeout(() => {
      setIsFetching(false)
    }, 500)
  }

  useEffect(() => {
    fetchGacha()
  }, [])

  return (
    <>
      <Head>
        <title>Mystery Box - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>
      <main>
        {!isFetching && (
          <Container className="pt-9">
            <div className="mb-11 md:w-3/4 mx-auto text-center">
              <Heading className="text-dark-primary dark:text-white italic">
                Mystery Box
              </Heading>
              <p className="mt-4 text-gray-1 dark:text-gray-3 text-stroke-thin">
                A wholesome farm owner in funbig. Upcoming gallery solo show in
                USA
              </p>
            </div>

            <div className="container-lg">
              <div className="flex flex-wrap sm:flex-nowrap gap-5 sm:gap-0">
                <div className="w-full sm:w-4/12 xl:w-[365px] h-fit shrink-0">
                  <div className="relative w-full pt-[100%]">
                    <Image
                      alt={gacha.name || ''}
                      fill
                      style={{ objectFit: 'cover' }}
                      src={
                        gacha.thumbnail?.url ||
                        '/asset/images/<EMAIL>'
                      }
                      priority
                    />
                  </div>
                  <p className="text-sm text-gray-2 text-right pr-2 mt-1">
                    <span className="icon-wrapper">
                      <SvgIcon
                        name="heart"
                        className="w-6 h-6 stroke-none fill-gray-2"
                      />
                    </span>
                    <span className="ml-0.5 align-middle font-medium text-base">
                      {gacha.numTotalLikes?.toLocaleString()}
                    </span>
                  </p>
                </div>
                <div className="w-full sm:flex-1 sm:pl-4 md:pl-8 lg:pl-14 xl:pl-[76px] xl:pr-[59px]">
                  <div className="flex gap-5 xl:gap-[27px]">
                    <Heading className="text-dark-primary dark:white italic text-left w-max md:max-w-[300px] lg:max-w-[430px] truncate">
                      Ftribe Fighters Mystery Box
                    </Heading>
                    <div className="flex lg:justify-start justify-center xl:w-auto w-full xl:mt-0 mt-3">
                      <ButtonIcon
                        className="mr-[10px] h-fit"
                        svgIcon="refresh"
                        iconClassName="w-5 h-5 stroke-none fill-primary-2"
                        onClick={() => fetchGacha()}
                      />
                      <ButtonIcon
                        className="mr-[10px] h-fit"
                        svgIcon="share"
                        iconClassName="w-5 h-5 stroke-none fill-primary-2"
                      />
                      <ButtonIcon
                        className="mr-[10px] h-fit"
                        svgIcon="moreVertical"
                        iconClassName="w-5 h-5 stroke-none fill-primary-2"
                      />
                    </div>
                  </div>
                  <div className="lg:mt-4 mt-8 flex flex-wrap lg:justify-start justify-center items-center">
                    <UserCard
                      className="mr-4 xl:mr-16 lg:mr-10"
                      name={gacha.collection?.name || ''}
                      nickname={gacha.collection?.name || ''}
                    >
                      <div className="relative shrink-0 w-[60px] h-[60px]">
                        <Image
                          alt=""
                          className="rounded-full"
                          src={
                            gacha.collection?.logoImage?.url ||
                            '/asset/images/character-ship.png'
                          }
                          fill
                        />
                      </div>
                    </UserCard>
                    <div className="inline-flex lg:justify-start justify-center items-center space-x-1 font-medium text-xl w-full lg:w-auto lg:mt-0 mt-5">
                      <span className="text-gray-2">Owned by </span>
                      <Link
                        href={`${gacha.collection?.owner?.username}`}
                        className="text-primary-2 inline-block align-text-bottom leading-none max-w-[8rem] truncate"
                      >
                        {gacha.collection?.owner?.username}
                      </Link>
                    </div>
                  </div>

                  <div className="mt-5 w-full flex gap-[7px]">
                    {Object.keys(countDown).map((item, i) => (
                      <Fragment key={i}>
                        <div key={i} className="shrink-0">
                          <p className="text-base lg:text-3xl font-bold text-blue-4 text-center">
                            {String(
                              countDown[item as keyof CountDown]
                            ).padStart(2, '0')}
                          </p>
                          <p className="text-sm lg:text-23 text-gray-1 capitalize">
                            {item}
                          </p>
                        </div>
                        {i < Object.keys(countDown).length - 1 && (
                          <div className="text-base lg:text-3xl font-bold text-blue-4">
                            :
                          </div>
                        )}
                      </Fragment>
                    ))}
                  </div>

                  {/* Statistic */}
                  <div className="flex flex-wrap lg:flex-nowrap lg:justify-start justify-center mt-8 gap-2 md:gap-4">
                    <StatCard
                      widthClasses="lg:w-32 w-32"
                      icon="userFluid"
                      title={gacha.numOwners?.toLocaleString() || 0}
                      subtitle="Owners"
                    />
                    <StatCard
                      widthClasses="lg:w-32 w-32"
                      icon="grid1"
                      title={gacha.totalSupply?.toLocaleString() || 0}
                      subtitle="Total"
                    />
                    {/* To do 1000 => K, 1000000 => M */}
                    <StatCard
                      widthClasses="lg:w-32 w-32"
                      icon="eye"
                      title={gacha.numTotalImpressions?.toLocaleString() || 0}
                      subtitle="View"
                    />
                    {/* To do 1000 => K, 1000000 => M */}
                    <StatCard
                      widthClasses="lg:w-32 w-32"
                      icon="heart"
                      title={gacha.numTotalLikes?.toLocaleString() || 0}
                      subtitle="Favorites"
                    />
                  </div>
                  <p className="inline-flex lg:justify-start justify-center mt-8 w-full">
                    <SvgIcon
                      name="adjustmentVertical"
                      viewBox="0 0 15.086 22.63"
                      stroke="#777E90"
                      strokeWidth="1"
                      className="w-3.5 dark:stroke-white"
                    />
                    <span className="ml-2 text-lg align-middle text-stroke-thin">
                      Sale ends 2022-1-16T15:48:10 UTC
                    </span>
                  </p>
                  <div className="mt-4 flex flex-wrap-reverse lg:justify-start justify-center w-full">
                    <div className="mr-4 flex items-center">
                      <span className="mr-1.5">
                        <Image
                          alt="coin"
                          src="/asset/icons/ETH.svg"
                          width={30}
                          height={30}
                        />
                      </span>
                      <span className="text-lg lg:text-3xl font-medium text-primary-2">
                        0.70 ETH
                      </span>
                    </div>
                    <div className="h-fit flex items-center pb-1.5">
                      <span className="mr-1.5">
                        <Image
                          alt=""
                          src="/asset/icons/dollar.png"
                          width={18}
                          height={18}
                        />
                      </span>
                      <span className="text-base align-middle font-medium">
                        467.11 ETH
                      </span>
                    </div>
                  </div>
                  <div className="flex flex-wrap lg:justify-start justify-center w-full mt-8">
                    <Button
                      variant="dark"
                      className="mr-3 mb-3 xl:mr-6 min-w-[112px]"
                    >
                      Buy Now
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </Container>
        )}
      </main>
    </>
  )
}

export default Gacha
