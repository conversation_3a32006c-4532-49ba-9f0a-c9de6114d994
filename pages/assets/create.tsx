import Head from 'next/head'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { NextPage } from 'next'
import { useEffect, useState } from 'react'
import { useForm } from 'lib/hook/useForm'
import { useAuthentication } from 'lib/hook/auth_hook'
import { useCustomQuery } from 'lib/hook/useCustomQuery'
import { toast } from 'react-toastify'
import Container from 'presentation/components/container'
import Heading from 'presentation/components/heading'
import Select from 'presentation/components/select'
import Button from 'presentation/components/button'
import SvgIcon from 'presentation/components/svg-icon'
import Input from 'presentation/components/input'
import Tooltip from 'presentation/components/tooltip'
import Switch from 'presentation/components/switch'
import Loader from 'presentation/components/loader'
import AddPropertiesModal from 'presentation/components/modal/add-properties'
import AddArrayValueModal from 'presentation/components/modal/add-array-value'
import RequireConnect from 'presentation/components/require-connect'
import AssetThumbnailFile from 'presentation/components/asset/thumbnail-file'
import CollectionUseCase from 'presentation/use_cases/collection_use_case'
import AssetUseCase from 'presentation/use_cases/asset_use_case'
import MultimediaUseCase from 'presentation/use_cases/multimedia_use_case'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'
import { envConfig } from 'presentation/constant/env'
import { Query as CollectionQuery } from 'domain/repositories/collection_repository/query'
import User from 'domain/models/user'
import Collection from 'domain/models/collection'
import {TypeId as TypeIdCollection} from 'domain/models/collection/type_id'
import Asset from 'domain/models/asset'
import Contract from 'domain/models/contract'
import Chain from 'domain/value_objects/chain'
import { isURL } from '../../lib/validate'

const collectionUseCase = new CollectionUseCase()
const multimediaUseCase = new MultimediaUseCase()
const assetUseCase = new AssetUseCase()

const CreateAsset: NextPage = () => {
  const initFormData = {
    name: '',
    externalUrl: '',
    description: '',
    collection: null,
    contract: null,
    collectionId: '',
    attributeProperties: [],
    attributeLevels: [],
    attributeStats: [],
    unlockableContent: false,
    unlockableContentData: '',
    isExplicit: false,
    totalSupply: 1,
    chainId: '',
    metadataInfo: '',
  }
  const maxTotalSupply = 20000
  /**
   * DATA
   */
  const router = useRouter()
  const [chains, setChains] = useState<Chain[]>([])
  const [chainsSelected, setChainsSelected] = useState<Chain>()
  const [isChainsReady, setIsChainsReady] = useState(false)
  const [properties, setProperties] = useState<any>([])
  const [levels, setLevels] = useState<any>([])
  const [stats, setStats] = useState<any>([])
  const [arePropertiesReady, setArePropertiesReady] = useState(false)
  const [areLevelsReady, setAreLevelsReady] = useState(false)
  const [areStatsReady, setAreStatsReady] = useState(false)
  const [showPropertiesModal, setShowPropertiesModal] = useState(false)
  const [showLevelsModal, setShowLevelsModal] = useState(false)
  const [showStatsModal, setShowStatsModal] = useState(false)
  const { formData, setFormValue } = useForm(initFormData)
  const [currentContracts, setCurrentContracts] = useState<any>({})
  const [isLoading, setIsLoading] = useState(false)
  const [externalUrlValid, setExtrnalUrlValid] = useState<Boolean>(true)
  const [fileItem, setFile] = useState<File>()
  const [errorFile, setErrorFile] = useState<String>()
  const [duplicatesAttribute, setDuplicatesAttribute] = useState<String[]>([])

  /**
   * HOOKS
   */
  const { getProviderEthers, switchNetwork, currentChainId, addressConnect} = useAuthentication()
  const authStateProvider = AuthStateProvider((state) => ({
    me: state.me,
  }))
  const { data: collections = [], isFetching: isFetchingCollections } = useCustomQuery(
    ['userCollections', authStateProvider?.me?.id],
    async () => {
      const queryCollection = new CollectionQuery({
        ownerId: authStateProvider?.me?.id || '',
        typeId: TypeIdCollection.createNft().getId(),
      })
      try {
        const res = await collectionUseCase.search(queryCollection)
        return res?.filter((i) => i?.collectionAddress && i?.chainId) 
      } catch (error) {
        toast.error('Fetch collections failed.')
      }
    },
    {
      enabled: !!authStateProvider?.me?.id,
      staleTime: 3000 * 60, // cache 3 min
    }
  )
  
  /**
   * FUNCTIONS
   */
  const formatChains = () => {
    const formattedChain = Chain.getResourceArray().map((chain) => {
      return Chain.fromName<Chain>(chain.name)
    })
    setChains(formattedChain)
  }

  const uploadFile = async (file: any) => {
    try {
      if (file) {
        const res = await multimediaUseCase.upload(file)
        return res
      }
      throw 'Upload file failed.'
    } catch (error) {
      throw 'Upload file failed.'
    }
  }
  
  const checkFile = async (file: any) => {
    try {
      setFile(undefined)
      if(!file) {
        setErrorFile('The field is required')
        return  
      }
      const type = file.type.split('/')[0]
      const maxSize:any = {
        image: 5,
        video: 5,
        audio: 5,
      }
      if(!maxSize[type]) {
        setErrorFile('Unsupported file type')
        return
      }
      const size =  file.size / 1024 / 1024 
      if(size > maxSize[type]) {
        setErrorFile(`File exceeds size limit of ${maxSize[type]}M`)
        return
      }
      setErrorFile('')
      setFile(file)
    } catch (error) {
      setErrorFile('')
    }
  }

  const onChangeCollection = (collection: Collection) => {
    if(collection) {
      setIsChainsReady(false)
      const chain = chains?.find((chain:Chain) => chain.getChainId() == (collection?.chainId))
      const contract = Contract.fromJson(collection.contract) 
      setChainsSelected(chain)
      setIsChainsReady(true)
      setFormValue('chainId', chain?.getChainId())
      setFormValue('collection', collection, true)
      setFormValue('contract', contract, true)
      if(contract?.schema?.isErc721()){
        setFormValue('totalSupply', 1)
      }
    }
  }

  const initEnvConfig = () => {
    const config: any = envConfig
    setCurrentContracts(config.contracts[formData?.chainId || 1])
  }

  const fillPercentage = (value: number, max: number) => {
    let p = (value / max) * 100
    return `${p}%`
  }

  const onSwitchNetwork = () => {
    if(currentChainId !== formData.chainId) {
      const networkSupported = chains?.find((chain:Chain) => chain.getChainId() == formData.chainId)
      switchNetwork(networkSupported?.getResource())
    }
  }

  const checkExternalUrlValid = async (externalUrl: string) => {
    setFormValue('externalUrl', externalUrl)
    if (!externalUrl) {
      setExtrnalUrlValid(true)
      return
    } 
    const isValid = isURL(externalUrl)
    setExtrnalUrlValid(isValid)
  }

  const checkDupAttribute = (): string[] => {
    const arr = [
      ...formData?.attributeProperties?.map((i:any) => i.type) || [],
      ...formData?.attributeLevels?.map((i:any) => i.name) || [],
      ...formData?.attributeStats?.map((i:any) => i.name) || [],
    ]
    const elementTracker:any = {}

    const duplicates:string[] = []
    arr.forEach((item) => {
      if (elementTracker[item]) {
        duplicates.push(item)
      } else {
        elementTracker[item] = true
      }
    })

    setDuplicatesAttribute(duplicates)
    return duplicates
  }
  
  const submitForm = async (e: any) => {
    try {
      e.preventDefault()
      if (!formData.name || !fileItem || !formData.collection || !formData.totalSupply) {
        throw 'Please fill all required fields.'
      }
      if(Number(formData.totalSupply) < 1) {
        throw 'Supply must be a number'
      }
      if(Number(formData.totalSupply) > maxTotalSupply) {
        throw 'Supply must be a number'
      }
      if(!externalUrlValid) {
        throw 'The External Link invalid'
      }
      const dups = checkDupAttribute()
      if(dups?.length) {
        throw 'Properties type, Levels type, Stats type can\'t be duplicated.'
      }
      setIsLoading(true)

      const multimedia = await uploadFile(fileItem)
      const newAsset = Asset.newEmtyAsset()
      newAsset.collection = formData.collection
      newAsset.contract = formData.contract
      newAsset.chain = formData.collection?.chainId
      newAsset.address = formData.collection?.collectionAddress
      newAsset.name = formData.name
      newAsset.externalUrl = formData.externalUrl
      newAsset.description = formData.description
      newAsset.multimedia = multimedia
      newAsset.hasUnlockableContent = formData.unlockableContent
      newAsset.unlockableContent = formData.unlockableContentData
      newAsset.isExplicit = formData.isExplicit
      newAsset.totalSupply = Number(formData.totalSupply)
      newAsset.attributeLevels = formData.attributeLevels
      newAsset.attributeProperties = formData.attributeProperties
      newAsset.attributeStats = formData.attributeStats
      
      const resMetadata = await assetUseCase.createMetadata(newAsset)
      console.log('scCreateAsset----------------resMetadata', resMetadata)
      newAsset.metadataId = resMetadata?.metadataId
      newAsset.imageId = resMetadata?.imageId
      newAsset.tokenId = resMetadata?.tokenId
      newAsset.tokenUri = resMetadata?.url
      
      const provider = await getProviderEthers()
      const accountAddress = addressConnect || ''
      const domainRegistryAddress = currentContracts?.domainRegistryAddress || ''
      const marketplaceAddress = currentContracts?.marketplaceAddress || ''
      
      if (!domainRegistryAddress || !marketplaceAddress) {
        throw new Error('Domain registry or marketplace address is not support in this network')
      }
      console.log('🚀 ~ submitForm ~ currentContracts:', currentContracts)
      const resContract = await assetUseCase.scCreateAsset(
        newAsset, 
        accountAddress, 
        provider, 
        currentContracts
      )
      newAsset.txHash = resContract?.transactionHash || ''
      const resAsset = await assetUseCase.create(newAsset)
      
      toast.success('Create item successfully.')
      router.push('/account')
      setIsLoading(false)
    } catch (error: any) {
      console.log('CreateAsset----------------error', error)
      toast.error(error?.showMsgErrors ? error?.showMsgErrors() : error?.message || error)
      setIsLoading(false)
    }
  }


  /**
   * LIFECYCLES
   */
  useEffect(() => {
    if (properties.length > 0) {
      setArePropertiesReady(true)
    }
  }, [properties])

  useEffect(() => {
    if (levels.length > 0) {
      setAreLevelsReady(true)
    }
  }, [levels])

  useEffect(() => {
    if (stats.length > 0) {
      setAreStatsReady(true)
    }
  }, [stats])

  
  useEffect(() => {
    formatChains()
  }, [])

  useEffect(() => {
    initEnvConfig()
  }, [formData.chainId])
  
  useEffect(() => {
    checkDupAttribute()
  }, [formData])
  
  useEffect(() => {
    if(collections?.length) {
      onChangeCollection(collections[0])
    }
  }, [collections])

  return (
    <>
      <Head>
        <title>Create New Item - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>
      <main>
        {authStateProvider.me?.id && addressConnect ? (
          <>
            <Container className="pt-9 md:pb-0 pb-9">
              <div className="text-dark-primary dark:text-white text-center">
                <Heading>Create New Item</Heading>
              </div>
              <div className="container-lg mt-5 pb-[6px]">
                <div className="create-item-form-cont">
                  <form className="mx-auto create-form" onSubmit={submitForm}>
                    <div className="flex flex-wrap">
                      <div className="md2:w-1/3 w-full max-w-[300px]">
                        {/* Image */}
                        <div className="w-full">
                          <p>
                            <span className="text-red-1">*</span>
                            <span className="text-gray-1 dark:text-gray-3 text-stroke-thin">
                              Required Fields
                            </span>
                          </p>
                          <div className="mt-5 flex items-center">
                            <p className="mr-5 font-medium text-dark-primary dark:text-white">
                              Image, Video, Audio, or 3D Model{' '}
                              <span className="text-red-1">*</span>
                            </p>
                            <Tooltip className="-mt-1">
                              Image, Video, Audio, or 3D Model
                            </Tooltip>
                          </div>
                          <div className="relative mt-5">
                            <div className="absolute top-3 right-3 z-20 cursor-pointer">
                              <input
                                className="w-[22px] h-[22px] opacity-0 absolute text-0 cursor-pointer"
                                type="file"
                                id="img"
                                name="logoImage"
                                accept="image/*,video/*,audio/*"
                                onChange={(e: any) =>
                                  checkFile(e.target.files[0])
                                }
                              />
                              <SvgIcon
                                name="plusCircle"
                                className="icon-22 stroke-none fill-primary-2 cursor-pointer"
                              />
                            </div>
                            {fileItem && (
                              <AssetThumbnailFile 
                                className='w-full pt-[100%] rounded-10 bg-gray-5 dark:bg-blue-1' 
                                file={fileItem} 
                              />
                            )}
                            {!fileItem && (
                              <div className="w-full pt-[100%] rounded-10 bg-gray-5 dark:bg-blue-1">
                              </div>
                            )}
                            {errorFile && (
                              <div className="text-sm text-red text-stroke-thin mt-1">
                                {errorFile}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Item Name */}
                      <div className="form-right">
                        <div className="w-full sm:w-1/2 mt-7">
                          <p className="font-medium text-dark-primary dark:text-white">
                            Item Name <span className="text-red-1">*</span>
                          </p>
                          <Input
                            type="text"
                            className="mt-10p"
                            value={formData.name}
                            maxLength={25}
                            onChange={(e) =>
                              setFormValue('name', e.target.value)
                            }
                          />
                        </div>

                        {/* External Link */}
                        <div className="mt-5">
                          <p className="font-medium text-dark-primary dark:text-white">
                            External Link
                          </p>
                          <p className="text-sm text-gray-1 dark:text-gray-3 text-stroke-thin">
                            You are welcome to link to your own webpage with
                            more details.
                          </p>
                          <Input
                            width="w-full sm:w-1/2"
                            type="text"
                            className="mt-10p"
                            value={formData.externalUrl}
                            maxLength={1000}
                            onChange={(e) =>
                              checkExternalUrlValid( e.target.value)
                            }
                          />
                          {!externalUrlValid && (
                            <div className="inline-flex items-center">
                              <span className="text-sm text-red text-stroke-thin">
                                The External Link invalid
                              </span>
                            </div>
                          )}
                        </div>

                        {/* Description */}
                        <div className="mt-5">
                          <p className="font-medium text-dark-primary dark:text-white">
                            Description
                          </p>
                          <p className="mt-1 text-sm text-gray-1 dark:text-gray-3 text-stroke-thin">
                            The description will be included on the item’s
                            detail page underneath its image.{' '}
                            {/* <span className="text-primary-2">Markdown</span>{' '}
                            syntax is supported. */}
                          </p>
                          <textarea
                            rows={3}
                            className="px-5 py-3 mt-2 rounded-3xl w-full outline-none bg-gray-5 dark:bg-blue-1"
                            value={formData.description}
                            maxLength={500}
                            onChange={(e) =>
                              setFormValue('description', e.target.value)
                            }
                          />
                        </div>

                        {/* Collection */}
                        <div className="mt-5">
                          <p className="font-medium text-dark-primary dark:text-white">
                            Collection <span className="text-red-1">*</span>
                          </p>
                          <div className="flex items-center">
                            <p className="text-sm text-gray-1 dark:text-gray-3 text-stroke-thin">
                              This is the collection where your item will appear.
                            </p>
                            <Link
                              href={'/account/collections'}
                              className="cursor-pointer"
                            >
                              <Tooltip>
                                You can manage your collections here
                              </Tooltip>
                            </Link>
                          </div>
                          {!isFetchingCollections && collections?.length > 0 && (
                            <Select
                              customWidth={true}
                              rounded
                              options={collections}
                              className="w-8/12 mt-2"
                              arrowIconClass="stroke-none fill-primary-2"
                              isCollectionList={true}
                              placeholder="Select Collection"
                              onSelect={(option) =>{ onChangeCollection(option) }}
                            />
                          )}
                          {!isFetchingCollections && collections?.length == 0 && (
                            <div className='text-dark-primary dark:text-white text-stroke-thin'>
                              <span className="text-red-1"> No collection yet.</span>
                              <Link href={'/collections/create'} className="text-primary-2 px-2 py-4 cursor-pointer">
                                Create a collection 
                              </Link>
                            </div>
                          )}
                          {!chainsSelected && collections?.length > 0 && (
                            <div className="text-red-1 mt-2">This Blockchain is not supported</div>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="w-full mt-11 form-bottom">
                      {/* Properties */}
                      <div className="flex items-baseline mt-5">
                        <div className="lg:w-64 w-44 mr-4 xl:mr-10 flex justify-between items-center">
                          <p className="mr-3 xl:mr-5 flex-none font-medium lg:w-[90px] w-32 lg:max-w-[90px] lg:whitespace-nowrap whitespace-pre-line text-dark-primary dark:text-white">
                            Properties
                          </p>
                          <div className="w-max">
                            <SvgIcon
                              name="plusCircle"
                              className="icon-22 stroke-none fill-primary cursor-pointer"
                              onClick={() => setShowPropertiesModal(true)}
                            />
                          </div>
                        </div>
                        <p className="text-gray-1 dark:text-gray-3 text-stroke-thin">
                          Textual traits that show up as rectangles
                        </p>
                      </div>
                      {arePropertiesReady && (
                        <div className="flex flex-wrap w-full mt-2 gap-2">
                          {properties.map((item: any, index: number) => (
                            <div
                              key={index}
                              className="px-4 py-2 border rounded-md flex justify-between items-center"
                            >
                              <div className="font-bold">
                                <p>{item.type}</p>
                                <p className="mt-0.5 text-xs text-primary-2">
                                  {item.name}
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}

                      {/* Levels */}
                      <div className="flex items-baseline mt-5">
                        <div className="lg:w-64 w-44 mr-4 xl:mr-10 flex justify-between items-center">
                          <p className="mr-3 xl:mr-5 flex-none font-medium lg:w-[90px] w-32 lg:max-w-[90px] lg:whitespace-nowrap whitespace-pre-line text-dark-primary dark:text-white">
                            Levels
                          </p>
                          <div className="w-max">
                            <SvgIcon
                              name="plusCircle"
                              className="icon-22 stroke-none fill-primary cursor-pointer"
                              onClick={() => setShowLevelsModal(true)}
                            />
                          </div>
                        </div>
                        <p className="text-gray-1 dark:text-gray-3 text-stroke-thin">
                          Numerical traits that show as a progress bar
                        </p>
                      </div>

                      {areLevelsReady && (
                        <div className=" w-full sm:w-1/2 mt-2">
                          {levels.map((item: any, index: number) => (
                            <div
                              key={index}
                              className="mt-2 px-4 py-2 border rounded-md flex-col justify-between items-center"
                            >
                              <div className="mb-1 flex justify-between">
                                <div className="text-base font-medium dark:text-white">
                                  {item.name}
                                </div>
                                <div className="text-base font-medium text-gray-2 dark:text-white">
                                  {item.value}/{item.max}
                                </div>
                              </div>
                              <div className="w-full h-4 mb-4 bg-gray-200 rounded-full dark:bg-gray-700">
                                <div
                                  className="h-4 bg-primary-2 rounded-full dark:bg-blue-500"
                                  style={{
                                    width: fillPercentage(item.value, item.max),
                                  }}
                                ></div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}

                      {/* Stats */}
                      <div className="flex items-baseline mt-5">
                        <div className="lg:w-64 w-44 mr-4 xl:mr-10 flex justify-between items-center">
                          <p className="mr-3 xl:mr-5 flex-none font-medium lg:w-[90px] w-32 lg:max-w-[90px] lg:whitespace-nowrap whitespace-pre-line text-dark-primary dark:text-white">
                            Stats
                          </p>
                          <div className="w-max">
                            <SvgIcon
                              name="plusCircle"
                              className="icon-22 stroke-none fill-primary cursor-pointer"
                              onClick={() => setShowStatsModal(true)}
                            />
                          </div>
                        </div>
                        <p className="text-gray-1 dark:text-gray-3 text-stroke-thin">
                          Numerical traits that just show as numbers
                        </p>
                      </div>

                      {areStatsReady && (
                        <div className="flex flex-wrap w-full sm:w-1/2 mt-2 gap-2">
                          {stats.map((item: any, index: number) => (
                            <div
                              key={index}
                              className="px-4 py-2 border rounded-md flex justify-between items-center"
                            >
                              <div key={index} className="flex justify-between font-medium">
                                <p>{item.name}</p>
                                <p className='ml-8'>{item.value} { item.max ? ` of ${ item.max}`: ''}</p>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}

                      {duplicatesAttribute?.length ? (
                        <div className="inline-flex items-center mt-4">
                          <span className="text-sm text-red text-stroke-thin">
                            {'Properties type, Levels type, Stats type can\'t be duplicated.'}<br/>
                            {`Duplicate type: "${duplicatesAttribute?.join('", "')}"`}
                          </span>
                        </div>
                      ): (<></>)}

                      {/* Unlockable Content */}
                      <div className="flex items-baseline mt-5">
                        <div className="lg:w-64 w-44 mr-4 xl:mr-10 flex justify-between items-center">
                          <p className="mr-3 xl:mr-5 flex-none font-medium lg:w-[90px] w-32 lg:max-w-[90px] lg:whitespace-nowrap whitespace-pre-line text-dark-primary dark:text-white">
                            Unlockable Content
                          </p>
                          <div className="w-max">
                            <Switch
                              onClick={(value) =>
                                setFormValue('unlockableContent', value)
                              }
                            />
                          </div>
                        </div>
                        <p className="text-gray-1 dark:text-gray-3 text-stroke-thin">
                          Include unlockable content that can only be revealed
                          by the owner of the item.
                        </p>
                      </div>
                      {formData.unlockableContent && (
                        <textarea
                          className="px-5 py-3 mt-2 rounded-3xl w-full sm:w-1/2 outline-none bg-gray-5 dark:bg-blue-1"
                          value={formData.unlockableContentData}
                          onChange={(e) =>
                            setFormValue(
                              'unlockableContentData',
                              e.target.value
                            )
                          }
                        />
                      )}

                      {/* Explicit & Sensitive Content */}
                      <div className="flex items-baseline mt-5">
                        <div className="lg:w-64 w-44 mr-4 xl:mr-10 flex justify-between items-center">
                          <p className="mr-3 xl:mr-5 flex-none font-medium lg:w-[90px] w-32 lg:max-w-[90px] lg:whitespace-nowrap whitespace-pre-line text-dark-primary dark:text-white">
                            Explicit & Sensitive Content
                          </p>
                          <div className="w-max">
                            <Switch
                              onClick={(value) =>
                                setFormValue('isExplicit', value)
                              }
                            />
                          </div>
                        </div>
                        <p className="text-gray-1 dark:text-gray-3 text-stroke-thin">
                          Set this item as explicit and sensitive contentinfo
                        </p>
                      </div>

                      {/* Supply */}
                      <div className="flex flex-wrap items-baseline mt-5">
                        <div className="lg:w-84 w-auto mr-4 xl:mr-10 flex justify-between items-center">
                          <p className="mr-3 xl:mr-5 flex-none font-medium lg:w-[90px] w-32 lg:max-w-[90px] lg:whitespace-nowrap whitespace-pre-line text-dark-primary dark:text-white">
                            Supply <span className="text-red-1">*</span>
                          </p>
                          <div className="w-max">
                            <Input
                              type="number"
                              width="w-full min-w-[129px] no-arrow"
                              name="supply"
                              value={formData.totalSupply}
                              disabled={formData?.contract?.schema?.isErc721()}
                              onChange={(e) =>
                                setFormValue('totalSupply', e.target.value)
                              }
                            />
                            {Number(formData.totalSupply || 0) < 1 && (
                              <div className="inline-flex items-center">
                                <span className="text-sm text-red text-stroke-thin">
                                Supply must be a number
                                </span>
                              </div>
                            )}
                            {Number(formData.totalSupply || 0) > maxTotalSupply && (
                              <div className="inline-flex items-center">
                                <span className="text-sm text-red text-stroke-thin">
                                  {`Maximum supply is ${maxTotalSupply}`}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center">
                          <p className="text-sm text-gray-1 dark:text-gray-3 text-stroke-thin">
                            The number of items that can be minted. No gas cost to you!
                          </p>
                          <Tooltip>
                            The number of items that can be minted. No gas cost to you!
                          </Tooltip>
                        </div>
                      </div>

                      {/* Blockchain */}
                      {isChainsReady && chainsSelected && (
                        <div className="mt-5 flex items-center">
                          <p className="mr-8 font-medium text-dark-primary dark:text-white">
                            Blockchain
                          </p>
                          <Select
                            selectClassName='w-64'
                            options={[chainsSelected]}
                            showOptionIcon
                            rounded
                            isBlockchainFilter={true}
                            customWidth={true}
                            arrowIconClass="w-5 h-5 stroke-none fill-primary-2"
                            defaultSelected={chainsSelected}
                            onSelect={(option) => {
                              setFormValue('chainId', option?.resource?.chainId)
                            }
                            }
                          />
                        </div>
                      )}

                      {/* Freeze metadata info */}
                      <div className="mt-5">
                        <div className="flex items-center">
                          <p className="font-medium text-dark-primary dark:text-white">
                            Freeze metadata info
                          </p>
                          <Tooltip className="-mt-1">
                            Freeze metadata info
                          </Tooltip>
                        </div>
                        <p className="text-sm text-gray-1 dark:text-gray-3 text-stroke-thin">
                          Freezing your metadata will allow you to permanently
                          lock and store all of this item’s content in
                          decentralized file storage.
                        </p>
                        <Input
                          width="w-full sm:w-1/2"
                          type="text"
                          className="mt-10p"
                          readOnly={true}
                          placeholder="To freeze your metadata, you must create your item first."
                          value={formData.metadataInfo}
                          onChange={(e) =>
                            setFormValue('metadataInfo', e.target.value)
                          }
                        />
                      </div>

                      
                      {currentChainId !== formData.chainId ? 
                        (
                          <Button
                            variant="dark"
                            type="button"
                            className="mt-6"
                            onClick={onSwitchNetwork}
                          >
                            Switch network
                          </Button>
                        ) : (
                          <Button
                            variant="dark"
                            htmltype="submit"
                            className="mt-6 flex"
                            disable={isLoading || (chainsSelected ? false : true) || (errorFile ? true : false)}
                            onClick={() => submitForm}
                          >
                            { isLoading && <Loader className='mr-2' /> } {isLoading? 'Please waiting' : 'Create'}
                          </Button>
                        )
                      }
                      
                    </div>
                  </form>
                </div>
              </div>
            </Container>

            {showPropertiesModal && (
              <AddPropertiesModal
                properties={properties}
                isShow={showPropertiesModal}
                onClose={() => setShowPropertiesModal(false)}
                onSubmit={(data: any) => {
                  setArePropertiesReady(false)
                  setProperties(data)
                  setFormValue('attributeProperties', data, true)
                  setShowPropertiesModal(false)
                }}
              />
            )}

            {showLevelsModal && (
              <AddArrayValueModal
                title="Add Levels"
                placeholderType="Ex. Level"
                levels={levels}
                isShow={showLevelsModal}
                onClose={() => setShowLevelsModal(false)}
                onSubmit={(data: any) => {
                  setAreLevelsReady(false)
                  setLevels(data)
                  setFormValue('attributeLevels', data, true)
                  setShowLevelsModal(false)
                }}
              />
            )}

            {showStatsModal && (
              <AddArrayValueModal
                title="Add Stats"
                placeholderType="Ex. Attack"
                levels={stats}
                isShow={showStatsModal}
                onClose={() => setShowStatsModal(false)}
                onSubmit={(data: any) => {
                  setAreStatsReady(false)
                  setStats(data)
                  setFormValue('attributeStats', data, true)
                  setShowStatsModal(false)
                }}
              />
            )}
          </>
        ) : (
          <RequireConnect />
        )}
      </main>
    </>
  )
}

export default CreateAsset
