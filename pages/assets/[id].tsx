import { NextPage } from 'next'
import Head from 'next/head'
import Image from 'next/image'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { useState, useEffect, useMemo } from 'react'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import { formatLocalisedCompactNumber, formatNumber } from 'lib/number'
import { useAuthentication } from 'lib/hook/auth_hook'
import { useAccount } from 'wagmi'

import Container from 'presentation/components/container'
import ButtonIcon from 'presentation/components/button/button-icon'
import SvgIcon from 'presentation/components/svg-icon'
import ToggleWrap from 'presentation/components/toggle-wrap'
import ToggleVertical from 'presentation/components/toggle-wrap/toggle-vertical'
import UserCard from 'presentation/components/card/user-card'
import Heading from 'presentation/components/heading'
import StatCard from 'presentation/components/card/stat-card'
import Button from 'presentation/components/button'
import PeriodRangesSelect from 'presentation/components/select/period-ranges-select'
import ActivityBoard from 'presentation/page_components/asset/activityBoard'
import PropertyBoard from 'presentation/page_components/asset/propertyBoard'
import DetailBoard from 'presentation/page_components/asset/detailBoard'
import LikeAsset from 'presentation/components/asset/like-asset'
import Table from 'presentation/components/table'
import ActivityChart from 'presentation/page_components/collection/activityChart'
import SocialNetworks from 'presentation/page_components/socialNetworks'
import SimilarAsset from 'presentation/components/asset/similar'
import PriceAssetDetail from 'presentation/components/asset/price-detail'
import MakeOfferModal from 'presentation/components/modal/make-offer'
import AcceptOfferModal from 'presentation/components/modal/accept-offer'
import CancelListingModal from 'presentation/components/modal/cancel-listing'
import BuylListingModal from 'presentation/components/modal/buy-listing'
import NoData from 'presentation/components/no-data'
import AssetThumbnailPreview from 'presentation/components/asset/thumbnail-preview'
import SkeletonAsset from 'presentation/components/skeleton/assets'
import { offerTableHeader, listingTableHeader } from 'presentation/page_components/asset/_tableHeaders'
import BtnSocialShare from 'presentation/page_components/account-details/btnSocialShare'
import Tooltip from 'presentation/components/tooltip'

import { FilterParams } from 'types/type'

import { toast } from 'react-toastify'
import ActivityUseCase from 'presentation/use_cases/activity_use_case'
import AssetDailyStatUseCase from 'presentation/use_cases/asset_daily_stat_use_case'
import AssetUseCase from 'presentation/use_cases/asset_use_case'
import MeUseCase from 'presentation/use_cases/me_use_case'
import OfferUseCase from 'presentation/use_cases/offer_use_case'
import ImpressionUseCase from 'presentation/use_cases/impression_use_case'
import AssetImpressionUseCase from 'presentation/use_cases/asset_impression_use_case'
import ListingUseCase from 'presentation/use_cases/listing_use_case'
import ButtonIconText from 'presentation/components/button/button-icon-text'
import { CartStateProvider } from 'presentation/providers/cart_state_provider'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'

import PeriodRange from 'domain/repositories/asset_daily_stat_repository/period_range'
import TargetEntityType from 'domain/value_objects/target_entity_type'
import Activity from 'domain/models/activity'
import Asset from 'domain/models/asset'
import AssetDailyStat from 'domain/models/asset_daily_stat'
import Offer from 'domain/models/offer'
import User from 'domain/models/user'
import Listing from 'domain/models/listing'
import { Query as OfferQuery } from 'domain/repositories/offer_repository/query'
import { Query as ActivityQuery } from 'domain/repositories/activity_repository/query'
import { Query as AssetQuery } from 'domain/repositories/asset_repository/query'
import { Query as AssetDailyStatQuery } from 'domain/repositories/asset_daily_stat_repository/query'
import { Query as AssetImpressionQuery } from 'domain/repositories/asset_impression_repository/query'
import { Query as ListingQuery } from 'domain/repositories/listing_repository/query'
import { Status as OfferStatus } from 'domain/models/offer/status'
import { Status as ListingStatus } from 'domain/models/listing/status'
import NftLandContractUtils  from 'domain/external_services/nft_land_contract_utils'
import CreateInputItemConverter from 'domain/services/nft_land_contract/converters/create_input_item'
import { getChainName } from 'lib/utils'
import { envConfig } from 'presentation/constant/env'
const assetDailyStatUseCase = new AssetDailyStatUseCase()
const activityUseCase = new ActivityUseCase()
const assetUseCase = new AssetUseCase()
const meUseCase = new MeUseCase()
const impressionUseCase = new ImpressionUseCase()
const assetImpressionUseCase = new AssetImpressionUseCase()
const listingUseCase = new ListingUseCase()
const offerUseCase = new OfferUseCase()

const periodRanges = PeriodRange.getResourceArray()

const AssetItem: NextPage = ({ assetId, assetMetadata }: any) => {
  const initAssetDailyStatParams: FilterParams = {
    periodRange: 'lastNinetyDays',
    page: 1,
    limit: 60,
  }

  const { filterParams: filterAssetDailyStatParams, changeFilterParam } = useChangeFilterParams(initAssetDailyStatParams)
  const [errorCode, setErrorCode] = useState<number>(0)
  const [isOwnerAsset, setIsOwnerAsset] = useState<boolean>(false)
  const [isAdmin, setIsAdmin] = useState<boolean>(false)
  const router = useRouter()
  const [asset, setAsset] = useState<Asset>()
  const [isFetchingAsset, setIsFetchingAsset] = useState<boolean>(true)
  const [activities, setActivities] = useState<any[]>([])
  const [isActivitiesReady, setIsActivitiesReady] = useState<boolean>(true)
  const [similarAssets, setSimilarAssets] = useState<Asset[]>([])
  const [isFetchingSimilarAssets, setIsFetchingSimilarAssets] = useState<boolean>(true)
  const [filterEventTypes, setFilterEventTypes] = useState<any>([])
  const [assetDailyStats, setAssetDailyStats] = useState<AssetDailyStat[]>([])
  const [isAssetDailyStatsReady, setIsAssetDailyStatsReady] = useState<boolean>(false)
  const [selectedPeriodRangeText, setSelectedPeriodRangeText] = useState<number>(90)
  const [offers, setOffers] = useState<any[]>([])
  const [isOffersReady, setIsOffersReady] = useState<boolean>(false)
  const [offer, setOffer] = useState<Offer>()
  const [bestOffer, setBestOffer] = useState<Offer|null>()
  const [allowMakeOffer, setAllowMakeOffer] = useState<boolean>(false)
  const [showModalMakeOffer, setShowModalMakeOffer] = useState<boolean>(false)
  const [showModalAcceptOffer, setShowModalAcceptOffer] = useState<boolean>(false)
  const [likeCount, setLikeCount] = useState<number>()
  const [impressionCount, setImpressionCount] = useState<number>()
  const [listing, setListing] = useState<Listing>()
  const [listingBuyNow, setListingBuyNow] = useState<Listing|null>()
  const [listingBid, setListingBid] = useState<Listing|null>()
  const [listings, setListings] = useState<any[]>()
  const [isListingsReady, setIsListingsReady] = useState<boolean>(false)
  const [showModalCancelListing, setShowModalCancelListing] = useState<boolean>(false)
  const [showModalBuyListing, setShowModalBuyListing] = useState<boolean>(false)
  const [user, setUser] = useState<User>()
  const [isConnected, setIsConnected] = useState<boolean>(false)
  const [urlShare, setUrlShare] = useState<string>('')
  const [quantityAvailable, setQuantityAvailable] = useState(0)

  /**
   * HOOKS
   */
  const { getProviderEthers, addressConnect} = useAuthentication()
  const authStateProvider = AuthStateProvider((state) => ({me: state.me}))
  const { chainId: currentChainId } = useAccount()

  const isConnectWallet = useMemo(() => {
    // return authStateProvider?.me?.id && addressConnect
    return authStateProvider?.me?.id 
  }, [authStateProvider, addressConnect])
  
  const fetchAsset = async () => {
    setIsFetchingAsset(true)
    await assetUseCase
      .findById(assetId)
      .then((res) => {
        console.log('-------------Asset', res)
        setAsset(res)
      })
      .catch((_error) => {
        setErrorCode(404)
      })
    setIsFetchingAsset(false)
  }

  const refreshMetadata = async () => {
    await assetUseCase
      .refreshMetadata({assetId: assetId})
      .then((res) => {
        toast.success('We\'ve queued this item for an update! Check back in a minute...')
      })
      .catch((_error) => {
        toast.error('Metadata refresh failed')
      })
  }
  
  const checkOwnership = async () => {
    try {
      if(user?.publicAddresses && user?.publicAddresses[0]) {
        const res = await assetUseCase
          .checkOwnership(assetId, user?.publicAddresses[0])
          // .checkOwnership(assetId, '0x6adC8FF16AC5b85D66A4E35c75592e0AdB896c35')
        setIsOwnerAsset(res)
        return res  
      } else {
        setIsOwnerAsset(false)
        return false
      }
    } catch (error) {
      setIsOwnerAsset(false)
      return false
    }
  }

  const fetchListing = async () => {
    const query = new ListingQuery({ 
      assetId: assetId, 
      statusIds: [ListingStatus.active().getId()],
    })
    await listingUseCase
      .search(query)
      .then((res) => {
        if (res.length > 0) {
          setListingBuyNow(listingUseCase.getListingBuyNow(res, authStateProvider?.me, asset?.minListingId ))
          setListingBid(listingUseCase.getListingToBid(res))
          setListings(listingUseCase.mapListingsTable(res, cancelListing, acceptListing, authStateProvider?.me))
        } else {
          setListings([])
          setListingBuyNow(null)
          setListingBid(null)
        }
        setIsListingsReady(true)
      })   
      .catch((_error) => {})
  }

  const fetchSimilarAssets = async () => {
    setIsFetchingSimilarAssets(true)
    const query = new AssetQuery({
      collectionIds: [asset?.collection?.id],
      limit: 5,
    })
    await assetUseCase
      .search(query)
      .then((res) => {
        res = res.filter((record: any) => record.id !== assetId)
        setSimilarAssets(res)
      })
      .catch((_error) => {
        toast.error('Failed to fetch similar assets')
      })
    setIsFetchingSimilarAssets(false)
  }

  const fetchAssetDailyStats = async () => {
    const query = new AssetDailyStatQuery({
      assetId: assetId,
      ...filterAssetDailyStatParams,
      periodRange: PeriodRange.fromName(
        filterAssetDailyStatParams.periodRange!
      ),
    })
    assetDailyStatUseCase
      .search(query)
      .then((res: any) => {
        const history = res.assetDailyStats
        const data: any = history?.map((item: any) => {
          return {
            time: item.date,
            price: parseFloat(item.averagePriceUsd).toString(),
            volume: parseFloat(item.dailyVolumeUsd).toString(),
          }
        }) || []
        setAssetDailyStats(data)
      })
      .catch((_error) => {
        setAssetDailyStats([])
      })
  }

  const fetchActivities = async () => {
    setIsActivitiesReady(false)
    const query = new ActivityQuery({
      assetIds: [assetId],
    })
    await activityUseCase
      .search(query)
      .then((res) => {
        setActivities(activityUseCase.mapActivitiesForAsset(res))
      })
      .catch((_error) => {
        toast.error('Failed to fetch asset activities')
      })
  }

  const cancelOffer = async (offer:Offer) => {
    try {
      await offerUseCase.cancel(offer)
      await fetchOffers()
    } catch (error) {
      toast.error('Failed to cancel offer')
    }
  }
  
  const acceptOffer = async (offer:Offer) => {
    if(isConnectWallet) {
      setOffer(offer)
      setShowModalAcceptOffer(true)
    } else {
      toast.error('Please connect wallet') 
    }
  }

  const cancelListing = async (listing:Listing) => {
    if(isConnectWallet) {
      setListing(listing)
      setShowModalCancelListing(true)
    } else {
      toast.error('Please connect wallet') 
    }
  }

  const acceptListing = async (listing:Listing) => {
    if(isConnectWallet) {
      setListing(listing)
      setShowModalBuyListing(true)
    } else {
      toast.error('Please connect wallet') 
    }
  }
  
  const onMakeOffer = async () => {
    if(isConnectWallet) {
      setShowModalMakeOffer(true)
    } else {
      toast.error('Please connect wallet') 
    }
  }

  const fetchOffers = async () => {
    const isOwnerAsset = await checkOwnership()
    console.log('--------isOwnerAsset', isOwnerAsset)
    setIsOffersReady(false)
    const query = new OfferQuery({
      assetId: assetId,
      statusId: OfferStatus.active().getId(),
    })
    await offerUseCase
      .search(query)
      .then((res) => {

        setBestOffer(res?.length ? res[0] : null)
        setOffers(offerUseCase.mapOffersTable(res, cancelOffer, acceptOffer, {id: authStateProvider?.me?.id, isOwnerAsset: isOwnerAsset}))
      })
      .catch((_error) => {
        toast.error('Failed to fetch asset offers')
      })
  }

  const checkAllowMakeOffer =  () => {
    const totalSupply = asset?.totalSupply || 0
    console.log('-------', {totalSupply,isOwnerAsset, quantityAvailable })
    if(totalSupply < 1) {
      return false
    }
    if(totalSupply == 1 && listingBid && !isOwnerAsset) {
      return false
    }
    if(isOwnerAsset && totalSupply == quantityAvailable) {
      return false
    }

    return true
  }

  const fetchMe = async () => {
    await meUseCase.loginStatus().then((res) => {
      setIsConnected(res.isLoggedIn)
      setUser(res.user!)
    }).catch((_error) => {})
  }

  const fetchImpression = async () => {
    if (isConnected && assetId) {
      await impressionUseCase
        .create(TargetEntityType.asset(), assetId)
        .then(async (res) => {
        })
        .catch((_error) => {})
      const query = new AssetImpressionQuery({ targetEntityId: assetId })
      await assetImpressionUseCase
        .totalCount(query)
        .then((res) => {
          setImpressionCount(res)
        })
        .catch((_error) => {})
    }
  }

  const formatFilterEventTypes = () => {
    const types = filterEventTypes.map((type: any) => {
      return {
        id: type.id,
        name: type.label,
        value: false,
        type: type.name,
      }
    })
    setFilterEventTypes(types)
  }

  const formatPeriodRanges = () => {
    const ranges = periodRanges.map((range: any) => {
      return {
        id: range.id,
        name: range.label,
        value: range.name,
        type: range.name,
      }
    })
    setFilterEventTypes(ranges)
  }

  const getQuantityToken = async () => {
    try {
      const userAddress = user?.publicAddresses[0]
      // const userAddress = '0x6adC8FF16AC5b85D66A4E35c75592e0AdB896c35'
      if(!asset?.address || !userAddress) {
        setQuantityAvailable(0)
        return
      }
      const contract = new NftLandContractUtils()
      const item = new CreateInputItemConverter()
      let itemToken = null
      if(asset?.contract?.schema.isErc1155()) {
        itemToken = item.tokenBalanceItem(item.ItemType.ERC1155, asset?.address, asset?.tokenId)
      } else if(asset?.contract?.schema.isErc721()) {
        itemToken = item.tokenBalanceItem(item.ItemType.ERC721, asset?.address, asset?.tokenId)
      }
      if(!itemToken) {
        setQuantityAvailable(0)
        return 
      }
      
      const provider = await getProviderEthers()
      const res = await contract.getBalanceOf(userAddress, itemToken, provider)
      setQuantityAvailable(Number(res))
    } catch (error) {
      setQuantityAvailable(0)
    }
  }

  const cartStateProvider = CartStateProvider((state) => ({
    cart: state.cart,
    setCart: state.setCart,
  }))

  const fillPercentage = (value: number, max: number) => {
    if(value > max) return '100%'
    let p = (value / max) * 100
    return `${p}%`
  }

  /**
   * LIFECYCLES
   */

  useEffect(() => {        
    fetchImpression()
  }, [])

  useEffect(() => {
    if (isConnected) {
      fetchImpression() 
    }
  }, [isConnected])

  useEffect(() => {
    fetchMe()
    formatPeriodRanges()
    formatFilterEventTypes()
    setIsAdmin(authStateProvider.me?.roleId === envConfig.userRole.operator)
  }, [authStateProvider.me?.id])

  useEffect(() => {
    if (asset?.id) {
      //await checkOwnership()
      fetchSimilarAssets()
      fetchAssetDailyStats()
      fetchActivities()
      fetchOffers()
      fetchListing()
    }
  }, [asset?.id, authStateProvider.me?.id])

  useEffect(() => {
    checkOwnership()
  }, [asset, isConnected, user])

  useEffect(() => {
    if(isOwnerAsset && asset && user) {
      getQuantityToken()
    } else {
      setQuantityAvailable(0)
    }
  }, [asset, user, isOwnerAsset, currentChainId])  

  useEffect(() => {
    if (router.isReady) {
      // Reset all state when the route changes
      setAsset(undefined)
      setIsFetchingAsset(true)
      setErrorCode(0)
      setIsOwnerAsset(false)
      setOffers([])
      setListings([])
      setListingBuyNow(null)
      setListingBid(null)
      setSimilarAssets([])
      setActivities([])
      setIsActivitiesReady(false)
      setIsListingsReady(false)
      setIsOffersReady(false)
      
      // Fetch new asset data
      fetchAsset()
      
      // Create URL for sharing
      const origin = typeof window !== 'undefined' ? window.location.origin : ''
      setUrlShare(`${origin}${router.asPath}`)
    }
  }, [router.query.id, router.isReady])

  useEffect(() => {
    setIsAssetDailyStatsReady(true)
  }, [assetDailyStats])

  useEffect(() => {
    fetchAssetDailyStats()
  }, [filterAssetDailyStatParams])

  useEffect(() => {
    setIsActivitiesReady(true)
  }, [activities])

  useEffect(() => {
    setIsOffersReady(true)
  }, [offers])
  
  useEffect(() => {
    const allow = checkAllowMakeOffer()
    setAllowMakeOffer(allow)
  }, [asset?.totalSupply, isOwnerAsset, quantityAvailable, listingBid])

  // useEffect(() => {
    
  // }, [authStateProvider.me?.id])

  // if (errorCode === 404) {
  //   router.push('/404')
  // }

  return (
    <>
      <Head>
        <title>{`${assetMetadata?.name} | ${assetMetadata?.collection?.name} - Quill`}</title>
        <meta name="description" content={`${assetMetadata?.description || assetMetadata?.collection?.description}`}/>
        <meta property="og:title" content={`${assetMetadata?.name} | ${assetMetadata?.collection?.name} - Quill`}/>
        <meta property="og:type" content="website"/>
        <meta property="og:url" content={`https://apps.quillnft.net/assets/${assetMetadata?.id}`}/>
        <meta property="og:image" content={`${assetMetadata?.thumbnail?.url}`}/>
        <meta property="og:site_name" content="Quill"/>
        <meta property="og:description"content={`${assetMetadata?.description || assetMetadata?.collection?.description}`}/>
        <meta property="og:locale" content="en_US"/>
      </Head>
      <main>
        <Container>
          {!isFetchingAsset && (
            <>
              <div className="container-lg pt-6 md:pt-10">
                {isOwnerAsset && asset?.contract?.schema.isErc721() && (
                  <div className="flex justify-end md:-mt-2 mt-5 md:mb-5">
                    {/* <Button variant="light" className="mr-5 min-w-[80px]">
                      Edit
                    </Button> */}
                    <Button
                      variant="light"
                      className="min-w-[80px]"
                      href={{
                        pathname: '/listings/create/',
                        query: { assetId: asset.id },
                      }}
                    >
                      Sell item
                    </Button>
                  </div>
                )}
                {isOwnerAsset && asset?.contract?.schema.isErc1155() && (
                  <div className="flex justify-end md:-mt-2 mt-5 md:mb-5">
                    {/* {isListingsReady && (
                      <Button
                        variant="light"
                        className="mr-5 min-w-[80px]"
                        href={`/listings/edit/${listing?.id}`}
                      >
                        Edit
                      </Button>
                    )} */}
                    <Button
                      variant="light"
                      className="min-w-[80px]"
                      href={{
                        pathname: '/listings/create/',
                        query: { assetId: asset.id },
                      }}
                    >
                      List item
                    </Button>
                  </div>
                )}
                <div className="flex flex-wrap lg:flex-nowrap justify-center lg:justify-start gap-8 lg:gap-16">
                  <div
                    className={`sm:w-4/12 xl:min-w-[365px] w-full md:mt-0 ${
                      isOwnerAsset ? 'mt-4' : 'mt-8'
                    }`}
                  >
                    {/* className="lg:w-full pb-[100%] relative rounded-20 overflow-hidden" */}
                    {asset && (
                      <AssetThumbnailPreview 
                        asset={asset}
                        className="lg:w-full pb-[100%] relative"
                      />
                    )}
                    
                    <LikeAsset 
                      className="text-right pr-2 mt-1" 
                      assetId={assetId} 
                      emitTotal={(total:number) => {
                        setLikeCount(total)
                      }} />
                    
                  </div>
                  <div className="lg:w-auto lg:flex-grow w-full">
                    <div className="lg:pr-8 flex flex-wrap lg:justify-between justify-center">
                      <Heading className="text-dark-primary dark:white">
                        {asset?.name}
                      </Heading>
                      <div className="flex lg:justify-start justify-center xl:w-auto w-full xl:mt-0 mt-3">
                        {(isOwnerAsset || isAdmin) && (
                          <Tooltip iconSize="w-4 h-4 -mt-1" position="bottomLeft">
                            <Tooltip.Content>
                              <div className='font-medium text-base	text-center'>Refresh metadata</div> 
                            </Tooltip.Content>
                            <Tooltip.Body>
                              <ButtonIcon
                                className="mr-[10px] h-fit"
                                svgIcon="refresh"
                                iconClassName="w-5 h-5 stroke-none fill-primary-2"
                                onClick={() => {
                                  refreshMetadata()
                                }}
                              />
                            </Tooltip.Body>
                          </Tooltip>
                        )}
                        <SocialNetworks webUrl={asset?.externalUrl} />
                        {/* {!isOwnerAsset && (
                          <ButtonIcon
                            className="mr-[10px] h-fit"
                            svgIcon="arrowTriangle"
                            iconClassName="w-5 h-5 stroke-none fill-primary-2"
                          />
                        )} */}
                        <BtnSocialShare urlShare={urlShare}/>
                        {/* <ButtonIcon
                          className="mr-[10px] h-fit"
                          svgIcon="share"
                          iconClassName="w-5 h-5 stroke-none fill-primary-2"
                        /> */}
                        {/* <ButtonIcon
                          className="mr-[10px] h-fit"
                          svgIcon="moreVertical"
                          iconClassName="w-5 h-5 stroke-none fill-primary-2"
                        /> */}
                      </div>
                    </div>
                    <div className="lg:mt-4 mt-8 flex flex-wrap lg:justify-start justify-center items-center">
                      <UserCard
                        className="mr-4 xl:mr-16 lg:mr-10"
                        name={asset?.collection?.name || ''}
                        nickname={asset?.collection?.name || ''}
                        href={`/collections/${getChainName(asset?.collection?.chainId)}/${asset?.collection?.slug}`}
                      >
                        <div className="relative shrink-0 w-[60px] h-[60px] cursor-pointer">
                          <Image
                            alt=""
                            className="rounded-full aspect-square bg-gray-4 object-cover"
                            src={
                              asset?.collection?.logoImage?.url ||
                                '/asset/images/character-ship.png'
                            }
                            fill
                          />
                        </div>
                      </UserCard>
                      {Number(asset?.totalSupply) == 1 && asset?.owners[0]  && (
                        <div className="inline-flex lg:justify-start justify-center items-center space-x-1 font-medium text-xl w-full lg:w-auto lg:mt-0 mt-5">
                          <span className="text-gray-2">Owned by </span>
                          <Link
                            href={`/users/${asset?.owners[0]?.username}`}
                            className="text-primary-2 inline-block align-text-bottom leading-none max-w-[10rem] truncate"
                          >
                            {isOwnerAsset? 'You' : asset?.owners[0]?.username}
                          </Link>
                        </div>
                      )}
                      {quantityAvailable > 0 && (asset?.totalSupply || 0) > 1 && (
                        <div className='inline-flex'>
                          <div className='text-base font-medium text-center text-dark-primary dark:text-white dark:white'>You own {quantityAvailable}</div>
                        </div>
                      )}
                    </div>

                    <div className="flex flex-wrap lg:flex-nowrap lg:justify-start justify-center mt-8 gap-2 md:gap-4">
                      {Number(asset?.totalSupply) > 1 && (
                        <>
                          <StatCard
                            iconSizeClasses="w-[18px] h-[18px]"
                            widthClasses="lg:w-32 w-32"
                            icon="userFluid"
                            title={formatLocalisedCompactNumber(asset?.numOwners || 0)}
                            subtitle="Owners"
                          />  
                          <StatCard
                            iconSizeClasses="w-[18px] h-[18px]"
                            widthClasses="lg:w-32 w-32"
                            icon="grid1"
                            title={formatLocalisedCompactNumber(asset?.totalSupply || 0)}
                            subtitle="Total"
                          />  
                        </>
                      )}
                      <StatCard
                        iconSizeClasses="w-[18px] h-[18px]"
                        widthClasses="lg:w-32 w-32"
                        icon="eye"
                        title={formatLocalisedCompactNumber(impressionCount || 0)}
                        subtitle="View"
                      />
                      <StatCard
                        iconSizeClasses="w-[18px] h-[18px]"
                        widthClasses="lg:w-32 w-32"
                        icon="heart"
                        title={formatLocalisedCompactNumber(likeCount || 0)}
                        subtitle="Favorites"
                      />
                    </div>

                    {/* Show price */}
                    <div className='mt-8'>
                      {(() => {
                        if (listingBuyNow) {
                          return (
                            <PriceAssetDetail
                              paymentToken={listingBuyNow.paymentToken}
                              price={listingBuyNow.price}
                              endTime={listingBuyNow.endAt}
                            />
                          )
                        } else if (listingBid) {
                          return (
                            <PriceAssetDetail
                              paymentToken={listingBid.paymentToken}
                              price={listingBid.price}
                              endTime={listingBid.endAt}
                              minimumbid={true}
                            />
                          )
                        } else {
                          return (
                            <div></div>
                          )
                        }
                      })()}
                    </div>
                    
                    {/* Button action */}
                    <div className="flex flex-wrap lg:justify-start justify-center w-full mt-8">
                      {/* Todo: check if asset listed */}
                      {/* <ButtonIconText
                        svgIcon="cart"
                        variant="dark"
                        className="mb-3 min-w-[112px]"
                        iconClassName="h-6 xl:w-[30px] xl:h-[30px] stroke-[2] mb-3 min-w-[52px] mr-3 xl:mr-6 stroke-white fill-white"
                        onClick={() => setShowModal(true)}
                        onClickIcon={() => cartStateProvider.setCart(asset!)}
                      >
                        Buy Now
                      </ButtonIconText> */}
                      {listingBuyNow && listingBuyNow?.user?.id != authStateProvider?.me?.id && (
                        <Button
                          variant="dark"
                          className="mb-3 min-w-[112px] mr-4"
                          onClick={() => { acceptListing(listingBuyNow) }}
                        >
                          Buy Now
                        </Button>

                      )}
                      {(() => {
                        if (asset?.totalSupply == 1 && listingBid && !isOwnerAsset) {
                          return (
                            <Button
                              variant="light"
                              className="mr-3 mb-3 xl:mr-6 min-w-[112px]"
                              onClick={() => { onMakeOffer() }}
                            >
                              Place Bid
                            </Button>
                          )
                        } else if(allowMakeOffer) {
                          return (
                            <Button
                              variant="light"
                              className="mr-3 mb-3 xl:mr-6 min-w-[112px]"
                              onClick={() => { onMakeOffer() }}
                            >
                              Make Offer
                            </Button>
                          )
                        }
                      
                      })()}
                    </div>
                  </div>
                </div>

                <div className="product-layout asset-detail mt-4">
                  <div className="product-layout-filter">
                    {/* Toggle left */}
                    <ToggleWrap contentClass="rounded-t-10 overflow-hidden">
                      {/* Description */}
                      <ToggleVertical
                        toggleTitle="Description"
                        toggleTitleWrapClass="rounded-t-10"
                        className="rounded-t-10"
                        defaultShow
                      >
                        <div>
                          <p>
                            <span className="font-medium text-xl text-gray-2">
                              Created by{' '}
                            </span>
                            {/* Todo: check if user present on nftland */}
                            <Link
                              href={`/users/${asset?.collection?.owner?.username}`}
                              className="font-medium text-xl text-primary-2 leading-none inline-block align-text-bottom max-w-[10rem] truncate"
                            >
                              {asset?.collection?.owner?.username}
                            </Link>
                          </p>
                          <p className="mt-2 text-stroke-thin">
                            {asset?.description}
                          </p>
                        </div>
                      </ToggleVertical>

                      {/* Details */}
                      <ToggleVertical toggleTitle="Details" defaultShow>
                        <DetailBoard
                          address={asset?.address}
                          token={asset?.tokenId}
                          tokenUri={asset?.tokenUri}
                          tokenStandard={asset?.contract?.schema.getLabel()}
                          blockChain={asset?.chain?.getLabel()}
                          explorerUrl={asset?.chain?.getExplorerUrl()}
                          metadata={asset?.metadataStatus.getLabel()}
                        />
                      </ToggleVertical>

                      {/* Properties */}
                      {(asset?.attributeProperties?.length || 0) > 0 && (
                        <ToggleVertical
                          toggleTitle="Properties"
                          defaultShow
                          contentClass="no-padding max-h-[316px] overflow-y-scroll"
                        >
                          <PropertyBoard
                            properties={asset?.attributeProperties!}
                          />
                        </ToggleVertical>
                      )}

                      {/* About collection */}
                      <ToggleVertical
                        heightFit={true}
                        toggleTitle={`About ${asset?.collection?.name}`}
                        defaultShow
                      >
                        <UserCard
                          name={asset?.collection?.name || ''}
                          nickname={asset?.collection?.name || ''}
                          href={`/collections/${getChainName(asset?.collection?.chainId)}/${asset?.collection?.slug}`}
                        >
                          <div className="relative shrink-0 w-[60px] h-[60px]">
                            <Image
                              className="rounded-full aspect-square bg-gray-4 object-cover"
                              alt=""
                              src={
                                asset?.collection?.logoImage?.url ||
                                '/asset/images/<EMAIL>'
                              }
                              fill
                            />
                          </div>
                        </UserCard>
                        <p className="mt-2 text-sm text-stroke-thin">
                          {asset?.collection?.description}
                        </p>
                        <div className="flex mt-2">
                          <SocialNetworks {...asset?.collection} />
                        </div>
                      </ToggleVertical>

                      {/* Stats */}
                      {(asset?.attributeStats?.length || 0) > 0 && (
                        <ToggleVertical toggleTitle="Stats" defaultShow>
                          {asset?.attributeStats.map((el, index) => (
                            <div key={index} className="flex justify-between font-medium mb-2">
                              <p>{el.name}</p>
                              <p className='ml-4'>{el.value} { el.max ? ` of ${ el.max}`: ''}</p>
                            </div>
                          ))}
                        </ToggleVertical>
                      )}
                      {/* Levels */}
                      {(asset?.attributeLevels?.length || 0) > 0 && (
                        <ToggleVertical toggleTitle="Levels" defaultShow>
                          {asset?.attributeLevels.map((el, index) => (
                            <div
                              key={index}
                              className="mb-2 flex-col justify-between items-center"
                            >
                              <div className="mb-1 flex justify-between">
                                <div className="text-base font-medium dark:text-white">
                                  {el.name}
                                </div>
                                <div className="text-base font-medium text-gray-2 dark:text-white">
                                  {el.value}/{el.max ? el.max : el.value}
                                </div>
                              </div>
                              <div className="w-full h-3 mb-4 bg-gray-200 rounded-full dark:bg-gray-700">
                                <div
                                  className="h-3 bg-primary-2 rounded-full dark:bg-blue-500"
                                  style={{
                                    width: fillPercentage(el.value, el.max),
                                  }}
                                ></div>
                              </div>
                            </div>
                          ))}
                        </ToggleVertical>
                      )}
                      {/* Unlockable Content */}
                      {isOwnerAsset && asset?.hasUnlockableContent ? (
                        <ToggleVertical toggleTitle="Unlockable Content" defaultShow>
                          <div className="text-stroke-thin">
                            <p>{asset?.unlockableContent}</p>
                          </div>
                        </ToggleVertical>
                      ): (<></>)}

                    </ToggleWrap>
                  </div>

                  <div className="w-full xl:w-[60%]">
                    <ToggleWrap contentClass="rounded-t-10 overflow-hidden">
                      {/* Price chart */}
                      {isAssetDailyStatsReady && (
                        <ToggleVertical
                          toggleTitle="Price history"
                          toggleTitleWrapClass="rounded-t-10"
                          className="rounded-t-10"
                          defaultShow
                          heightFit={true}
                        >
                          <div className="px-4 lg:px-8 flex justify-between">
                            <PeriodRangesSelect
                              periodRanges={periodRanges}
                              defaultSelected={periodRanges[4]}
                              onSelect={(option) => {
                                setSelectedPeriodRangeText(option.subValue)
                                changeFilterParam('periodRange', option.value)
                              }}
                            />
                            {/* <div className="flex gap-6">
                              <div>
                                <p className="font-medium text-sm">
                                  {selectedPeriodRangeText} Day Avg. Price
                                </p>
                                <p className="font-bold text-primary-2">
                                  2.1691
                                </p>
                              </div>
                              <div>
                                <p className="font-medium text-sm">
                                  {selectedPeriodRangeText} Day Volume
                                </p>
                                <p className="font-bold text-primary-2">
                                  51,217.8958
                                </p>
                              </div>
                            </div> */}
                          </div>
                          <ActivityChart data={assetDailyStats} />
                        </ToggleVertical>
                      )}

                      {/* Listings */}
                      {isListingsReady && (
                        <ToggleVertical
                          toggleTitle="Listings"
                          defaultShow
                          heightFit={true}
                          contentClass="no-padding-top px-4 pb-4 max-h-[315px] overflow-y-scroll"
                        >
                          <Table
                            head={listingTableHeader}
                            data={listings}
                            stickyHeader={true}
                          />
                        </ToggleVertical>
                      )}
                      {/* Offers */}
                      {isOffersReady && (
                        <ToggleVertical
                          toggleTitle="Offers"
                          defaultShow
                          contentClass="no-padding-top px-4 pb-4 max-h-[315px] overflow-y-scroll"
                        >
                          <Table
                            head={offerTableHeader}
                            data={offers}
                            stickyHeader={true}
                          />
                        </ToggleVertical>
                      )}
                    </ToggleWrap>
                  </div>
                </div>

                {/* Activities */}
                {isActivitiesReady && (
                  <ToggleWrap
                    className="mt-12"
                    contentClass="rounded-t-10 overflow-hidden"
                  >
                    <ToggleVertical
                      toggleTitle="Activity"
                      defaultShow
                      contentClass="p-4 max-h-[507px] overflow-y-scroll"
                      className="rounded-t-10"
                      toggleTitleWrapClass="rounded-t-10"
                    >
                      <ActivityBoard
                        activities={activities}
                        filterOptions={filterEventTypes}
                      />
                    </ToggleVertical>
                  </ToggleWrap>
                )}

                {/* Similar Assets */}
                <ToggleWrap
                  className="mt-12"
                  contentClass="rounded-t-10 overflow-hidden"
                >
                  <ToggleVertical
                    toggleTitle="More from this collection"
                    contentWrapClass="overflow-visible"
                    heightFit
                    defaultShow
                    className="rounded-t-10"
                    toggleTitleWrapClass="rounded-t-10"
                  >
                    <div
                      className={`product-wrap ${
                        similarAssets.length === 0 ? 'no-data' : ''
                      } px-4 lg:px-12 flex justify-center`}
                    >
                      {isFetchingSimilarAssets ? (
                        [...Array(5)].map((_, id) => <SkeletonAsset key={id} />)
                      ) : (
                        <>
                          {similarAssets.length > 0 ? (
                            similarAssets.map((asset: any, index: number) => (
                              <SimilarAsset
                                key={`${asset.id}-${index}`}
                                record={asset}
                              />
                            ))
                          ) : (
                            <div className="w-full rounded-lg border flex justify-center items-center py-24">
                              <NoData />
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  </ToggleVertical>
                </ToggleWrap>
              </div>
            </>
          )}
        </Container>
        {showModalMakeOffer && (
          <MakeOfferModal
            asset={asset}
            listingBid={listingBid}
            isShow={showModalMakeOffer}
            bestOffer={bestOffer}
            onClose={() => setShowModalMakeOffer(false)}
            onReload={() => {
              fetchListing()
              fetchOffers()
            }}
          />
        )}
        {showModalAcceptOffer && (
          <AcceptOfferModal
            offer={offer}
            isShow={showModalAcceptOffer}
            onClose={() => setShowModalAcceptOffer(false)}
            onReload={() => {
              fetchAsset()
              fetchListing()
              fetchOffers()
            }}
          />
        )}
        {showModalCancelListing && (
          <CancelListingModal
            listing={listing}
            isShow={showModalCancelListing}
            onClose={() => setShowModalCancelListing(false)}
            onReload={() => {
              fetchAsset()
              fetchListing()
              fetchOffers()
            }}
          />
        )}
        {showModalBuyListing && (
          <BuylListingModal
            listing={listing}
            isShow={showModalBuyListing}
            onClose={() => setShowModalBuyListing(false)}
            onReload={() => {
              fetchAsset()
              fetchListing()
              fetchOffers()
            }}
          />
        )}
      </main>
    </>
  )
}

export async function getServerSideProps (context: any) {
  try {
    const { id } = context.params
    const url = process.env.NEXT_PUBLIC_API_URL
    const res = await fetch(`${url}/assets/${id}`)
    const data = await res.json() || {}
    const asset = data.asset || {}
    return {
      props: {
        assetId: id,
        assetMetadata: asset,
      },
    }
  } catch (error) {
    return {
      notFound: true,
    }
  }
}

export default AssetItem
