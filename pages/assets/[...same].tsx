import { NextPage } from 'next'
import Head from 'next/head'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Container from 'presentation/components/container'
import SameNFTTab from 'presentation/page_components/asset/sameNFTTab'
import UniqueNFTTab from 'presentation/page_components/asset/uniqueNFTTab'
import AssetUseCase from 'presentation/use_cases/asset_use_case'

//Todo remove mock data
import {
  askDataTable,
  forSaleDataTable,
} from 'presentation/controllers/mockData/samenft_mock_data'
import Asset from 'domain/models/asset'
//End todo remove mock data

const assetUseCase = new AssetUseCase()

const AssetDetailSubTabs: NextPage = ({ assetId }: any) => {
  const router = useRouter()
  const [activeIndex, setActiveIndex] = useState(0)
  const [errorCode , setErrorCode] = useState(0)
  const [asset, setAsset] = useState<Asset>()
  const [isFetchingAsset, setIsFetchingAsset] = useState(true)
  const [askData, setAskData] = useState<any>([])
  const [isFetchingAskData, setIsFetchingAskData] = useState(true)
  const [buyData, setBuyData] = useState<any>([])
  const [isFetchingBuyData, setIsFetchingBuyData] = useState(true)
  const [saleData, setSaleData] = useState<any>([])
  const [isFetchingSaleData, setIsFetchingSaleData] = useState(true)

  const fetchAsset = async () => {
    setIsFetchingAsset(true)
    await assetUseCase.findById(assetId).then((res) => {
      setAsset(res)
    }).catch((error) => {
      setErrorCode(404)
    })
    setIsFetchingAsset(false)
  }

  const festAskData = () => {
    setIsFetchingAskData(true)
    setAskData(askDataTable)
    setTimeout(() => {
      setIsFetchingAskData(false)
    }, 500)
  }

  const festBuyData = () => {
    setIsFetchingBuyData(true)
    setBuyData(askDataTable)
    setTimeout(() => {
      setIsFetchingBuyData(false)
    }, 500)
  }

  const festSaleData = () => {
    setIsFetchingSaleData(true)
    setSaleData(forSaleDataTable)
    setTimeout(() => {
      setIsFetchingSaleData(false)
    }, 500)
  }

  const refreshSameNFTTab = () => {
    festAskData()
    festBuyData()
  }

  useEffect(() => {
    fetchAsset()
  }, [])

  useEffect(() => {
    if(asset?.id) {
      refreshSameNFTTab()
      festSaleData()
    }
  }, [asset])

  if (errorCode === 404) {
    router.push('/404')
  }

  return (
    <>
      <Head>
        <title>Asset Detail - Quill</title>
        <meta name="description" content=""/>
      </Head>
      <main>
        <Container>
          {!isFetchingAsset && (
            <>
              {activeIndex === 0 && (
                <SameNFTTab
                  asset={asset}
                  onChangeTab={setActiveIndex}
                  activeIndex={activeIndex}
                  isFetching={isFetchingAskData || isFetchingBuyData}
                  askData={askData}
                  buyData={buyData}
                  onRefresh={refreshSameNFTTab}
                />
              )}
              {activeIndex === 1 && (
                <UniqueNFTTab
                  asset={asset}
                  onChangeTab={setActiveIndex}
                  activeIndex={activeIndex}
                  saleData={saleData}
                  isFetching={isFetchingSaleData}
                  onRefresh={festSaleData}
                />
              )}
            </>
          )}
        </Container>
      </main>
    </>
  )
}

export async function getServerSideProps (context: any) {
  try {
    return {
      props: {
        assetId: context?.params?.same[0] || '',
      },
    }
  } catch (error) {
    return {
      notFound: true,
    }
  }
}

export default AssetDetailSubTabs
