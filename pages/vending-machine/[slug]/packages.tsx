import React, { useState, useEffect } from 'react'
import { NextPage } from 'next'
import Head from 'next/head'
import { useRouter } from 'next/router'
import Link from 'next/link'
import Container from 'presentation/components/container'
import Heading from 'presentation/components/heading'
import PaginationDot from 'presentation/components/pagination/pagination-dot'
import NoData from 'presentation/components/no-data'
import SkeletonAsset from 'presentation/components/skeleton/assets'
import { PackageItem as PackageItemType } from 'domain/entities/vending_machine'
import VendingMachineCategoryUseCase from 'presentation/use_cases/vending_machine_category_use_case'
import VendingMachinePackUseCase from 'presentation/use_cases/vending_machine_pack_use_case'
import { Query as CategoryQuery } from 'domain/repositories/vending_machine_category_repository/query'
import { Query as PackQuery } from 'domain/repositories/vending_machine_pack_repository/query'
import { NetworkStateProvider } from 'presentation/providers/network_state_provider'
import PackageItem from 'presentation/page_components/vending-machine/PackageItem'
import PackageSearchForm from 'presentation/page_components/vending-machine/PackageSearchForm'
import ToggleWrap from 'presentation/components/toggle-wrap'
import { PACKAGES_BY_CATEGORY_LIMIT } from 'lib/vendingMachineConfig'
const categoryUseCase = new VendingMachineCategoryUseCase()
const packUseCase = new VendingMachinePackUseCase()
const PageSize = PACKAGES_BY_CATEGORY_LIMIT

interface CategoryPackagesPageProps {
  categoryId: string
}

const CategoryPackagesPage: NextPage<CategoryPackagesPageProps> = ({
  categoryId,
}) => {
  const router = useRouter()

  // State
  const [category, setCategory] = useState<any>(null)
  const [packages, setPackages] = useState<PackageItemType[]>([])
  const [isLoadingCategory, setIsLoadingCategory] = useState(true)
  const [isLoadingPackages, setIsLoadingPackages] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [total, setTotal] = useState(0)
  const [searchQuery, setSearchQuery] = useState('')
  const [isNetworkReady, setIsNetworkReady] = useState(false)

  // Initialize network
  useEffect(() => {
    NetworkStateProvider.getState().init()
    const timer = setTimeout(() => {
      setIsNetworkReady(true)
    }, 100)
    return () => clearTimeout(timer)
  }, [])

  // Fetch category details
  const fetchCategory = async () => {
    if (!isNetworkReady || !categoryId) return

    try {
      setIsLoadingCategory(true)
      const categoryResult = await categoryUseCase.getById(categoryId)
      setCategory(categoryResult)
    } catch (error) {
      console.error('Error fetching category:', error)
      router.push('/vending-machine')
    } finally {
      setIsLoadingCategory(false)
    }
  }

  // Fetch packages with pagination and search
  const fetchPackages = async () => {
    if (!isNetworkReady || !categoryId) return
    setPackages([])
    try {
      setIsLoadingPackages(true)
      console.log('searchQuery', searchQuery)
      const packQuery = new PackQuery({
        page: currentPage,
        limit: PageSize,
        categoryId: categoryId,
        isEnabled: true,
        keywords: searchQuery || undefined, // Add search by name if provided
      })

      const packResult = await packUseCase.list(packQuery)

      // Map to PackageItem format
      const packageItems: PackageItemType[] = packResult.items.map(
        (pack: any) => ({
          id: pack.id,
          title: pack.name,
          price: pack.price,
          image: pack.imageUrl,
          typeId: pack.categoryId,
          slug: pack.id!.toString(),
        })
      )

      setPackages(packageItems)
      setTotal(packResult.total)
    } catch (error) {
      console.error('Error fetching packages:', error)
    } finally {
      setIsLoadingPackages(false)
    }
  }

  // Effects
  useEffect(() => {
    if (categoryId && isNetworkReady) {
      fetchCategory()
    }
  }, [categoryId, isNetworkReady])

  useEffect(() => {
    if (categoryId && isNetworkReady) {
      fetchPackages()
    }
  }, [categoryId, currentPage, isNetworkReady])

  // Search handlers
  const handleSearch = () => {
    setCurrentPage(1) // Reset to first page when searching
    fetchPackages()
  }

  const handleClearSearch = () => {
    setCurrentPage(1)
    fetchPackages()
  }

  // Page change handler
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    // Scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  return (
    <>
      <Head>
        <title>
          {category
            ? `${category.name} Packages | Vending Machine`
            : 'Packages | Vending Machine'}
        </title>
        <meta
          name="description"
          content={
            category
              ? `Browse all ${category.name} packages`
              : 'Browse packages'
          }
        />
      </Head>

      <main>
        <Container className="pt-[34px]">
          {/* Header */}
          <div className="w-full md:w-1/2 mx-auto text-center">
            <Heading className="mb-4 text-dark-primary dark:text-white">
              {isLoadingCategory
                ? 'Loading...'
                : category
                  ? category.name
                  : 'Packages'}
            </Heading>
          </div>

          {/* Main Content */}
          <div className="product-layout product-collection mt-6">
            {/* Left Sidebar - Search */}
            <div className="product-layout-filter">
              <ToggleWrap
                title="Filter"
                contentClass="border-t-0 overflow-hidden"
              >
                <PackageSearchForm
                  searchQuery={searchQuery}
                  onSearchQueryChange={setSearchQuery}
                  onSearch={handleSearch}
                  onClear={handleClearSearch}
                  total={total}
                  isLoading={isLoadingPackages}
                  defaultShow={true}
                />
              </ToggleWrap>
            </div>

            {/* Right Content - Package Grid */}
            <div className="w-full md:w-9/12">
              <div
                className={`mt-0 collection-main ${
                  !isLoadingPackages && packages.length === 0 ? 'no-data' : ''
                }`}
              >
                {isLoadingPackages ? (
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                    {[...Array(20)].map((_, i) => (
                      <SkeletonAsset key={i} />
                    ))}
                  </div>
                ) : (
                  <>
                    {packages.length > 0 ? (
                      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                        {packages.map((packageItem) => (
                          
                          <div
                            key={packageItem.id}
                            className="block package-section"
                          >
                            <PackageItem item={packageItem} />
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="w-full rounded-lg border flex justify-center items-center py-24">
                        <NoData />
                      </div>
                    )}
                  </>
                )}
              </div>

              {/* Pagination */}
              {!isLoadingPackages && packages.length > 0 && (
                <div className="flex justify-center mt-8">
                  <PaginationDot
                    currentPage={currentPage}
                    pageSize={PageSize}
                    totalCount={total}
                    onPageChange={handlePageChange}
                  />
                </div>
              )}
            </div>
          </div>
        </Container>
      </main>
    </>
  )
}

// Server-side props
export async function getServerSideProps (context: any) {
  const { slug } = context.params

  try {
    return {
      props: {
        categoryId: slug, // slug represents categoryId in this route
      },
    }
  } catch (error) {
    return {
      notFound: true,
    }
  }
}

export default CategoryPackagesPage
