import React, { useState, useEffect } from 'react'
import { NextPage } from 'next'
import Head from 'next/head'
import { useRouter } from 'next/router'
import { PackageItem, PackageDetail as PackageDetailType, PackageOdds } from 'domain/entities/vending_machine'
import Link from 'next/link'
import PackageDetail from 'presentation/page_components/vending-machine/PackageDetail'
import VendingMachinePackUseCase from 'presentation/use_cases/vending_machine_pack_use_case'
import VendingMachineCategoryUseCase from 'presentation/use_cases/vending_machine_category_use_case'
import { Query as PackQuery } from 'domain/repositories/vending_machine_pack_repository/query'
import VendingMachinePack from 'domain/models/vending_machine_pack'
import { NetworkStateProvider } from 'presentation/providers/network_state_provider'
import Image from 'next/image'

const packUseCase = new VendingMachinePackUseCase()
const categoryUseCase = new VendingMachineCategoryUseCase()

const VendingMachinePage: NextPage = ({ detailSlug, detailMetadata }: any) => {
  const router = useRouter()
  const { slug } = router.query
  
  // Get network state using the same pattern as vending-machine.tsx
  const networkStateProvider = NetworkStateProvider((state) => ({
    network: state.network,
  }))
  
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [packageDetail, setPackageDetail] = useState<PackageDetailType | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [isNetworkInitialized, setIsNetworkInitialized] = useState(false)
  
  // Initialize network state
  useEffect(() => {
    NetworkStateProvider.getState().init()
    setIsNetworkInitialized(true)
  }, [])

  useEffect(() => {
    if (!slug || !isNetworkInitialized) return
    
    setIsLoading(true)
    setError(null)
    
    const loadData = async () => {
      try {
        // Load pack detail by ID (slug is the pack ID)
        const pack = await packUseCase.getById(slug as string)
        console.log('🚀 ~ loadData ~ pack:', pack)
        
        if (pack) {
          // Convert VendingMachinePack to PackageDetail interface
          const packageDetail = await mapPackToPackageDetail(pack)
          setPackageDetail(packageDetail)
        } else {
          setError('Package not found')
          // Redirect to main vending machine page after a short delay
          setTimeout(() => {
            router.push('/vending-machine')
          }, 3000)
        }
      } catch (error) {
        console.error('Failed to load data:', error)
        setError('Failed to load data')
        // Redirect to main vending machine page after a short delay
        setTimeout(() => {
          router.push('/vending-machine')
        }, 3000)
      } finally {
        setIsLoading(false)
      }
    }
    
    loadData()
  }, [slug, router, networkStateProvider.network, isNetworkInitialized])

  // Helper function to map VendingMachinePack to PackageDetail interface
  const mapPackToPackageDetail = async (pack: VendingMachinePack): Promise<PackageDetailType> => {
    // Convert odds from pack.setting.odds to PackageOdds format
    const odds: PackageOdds[] = pack.setting.odds ? pack.setting.odds.map((odd: any) => ({
      range: `$${odd.fromPrice} - $${odd.toPrice}`,
      percentage: odd.rate,
      isActive: true,
    })) : []
    
    // Calculate expected value based on odds
    const expectedValue = pack.price
    
    // Get related packages from the same category
    let relatedPackages: PackageItem[] = []
    try {
      const relatedQuery = new PackQuery({
        page: 1,
        limit: 1000, // Get 6 related packages
        categoryId: pack.categoryId,
      })
      
      const relatedResult : any = await packUseCase.list(relatedQuery)
      relatedPackages = relatedResult.items
        .filter((relatedPack : any) => relatedPack.id !== pack.id) // Exclude current pack
        .map((relatedPack : any) => ({
          id: relatedPack.id,
          title: relatedPack.name,
          price: relatedPack.price,
          image: relatedPack.imageUrl,
          typeId: relatedPack.categoryId,
          slug: relatedPack.id!.toString(),
        }))
    } catch (error) {
      console.error('Failed to load related packages:', error)
    }
    
    return {
      id: pack.id || '',
      title: pack.name,
      price: pack.price,
      image: pack.imageUrl,
      typeId: pack.categoryId,
      slug: pack.id!.toString(),
      description: pack.description,
      expectedValue: expectedValue,
      odds: odds,
      relatedPackages: relatedPackages,
    }
  }

  const formatTitle = (slug: string) => {
    if (!slug) return ''
    return slug.replace(/-/g, ' ').replace(/\b\w/g, (c) => c.toUpperCase())
  }

  return (
    <>
      <Head>
        <title>
          {detailMetadata 
            ? `${detailMetadata.title} | Vending Machine` 
            : slug 
              ? `${formatTitle(slug as string)} | Vending Machine` 
              : 'Vending Machine'}
        </title>
        <meta 
          name="description" 
          content={
            detailMetadata
              ? detailMetadata.description
              : `Browse items in the ${slug} collection`
          } 
        />
        {detailMetadata && detailMetadata.image && (
          <>
            <meta property="og:image" content={detailMetadata.image} />
            <meta property="twitter:image" content={detailMetadata.image} />
          </>
        )}
      </Head>
      

      <div className={`relative h-100 ${packageDetail ? 'py-6' : 'container-df my-6 md:my-10 overflow-hidden'}`}>
        {/* Background image that takes up 45% of screen width */}
        <div className="absolute top-[2%] left-[2%] w-[45%] h-[623px] z-0 pointer-events-none scale-150"> 
          <Image 
            src="/asset/images/vending-machine/background-top.png"
            alt="Background"
            className="object-contain"
            height={623}
            width={1000}
          />
        </div>
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : error ? (
          <div className="container mx-auto px-4 py-12 text-center">
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              <p>{error}</p>
              <p className="mt-2 text-sm">Redirecting...</p>
            </div>
          </div>
        ) : packageDetail ? (
          // Render package detail
          <PackageDetail packageDetail={packageDetail} />
        ) :
          (
            <div className="col-span-4 py-12 text-center">
              <p className="text-gray-500 text-lg"> No item found.</p>
            </div>
          )}

      </div>
    </>
  )
}

// Add server-side rendering for SEO benefits
export async function getServerSideProps (context: any) {
  const { slug } = context.params
  console.log('🚀 ~ getServerSideProps ~ slug:', slug)

  try {
    // Create use case instances for server-side
    const serverPackUseCase = new VendingMachinePackUseCase()
    
    // Get pack detail for metadata (slug is the pack ID)
    const pack = await serverPackUseCase.getById(slug as string)
    
    if (!pack) {
      console.log('❌ Pack not found for slug:', slug)
      return {
        notFound: true,
      }
    }
    
    console.log('✅ Pack found:', pack.name)
    
    // Prepare metadata for SEO
    const metadata = {
      title: pack.name,
      description: pack.description,
      image: pack.imageUrl,
      price: pack.price,
    }
    
    return {
      props: {
        detailSlug: slug,
        detailMetadata: metadata,
      },
    }
  } catch (error) {
    console.error('❌ Error in getServerSideProps:', error)
    // Return empty props instead of notFound to allow client-side fallback
    return {
      props: {
        detailSlug: slug,
        detailMetadata: null,
        serverError: true,
      },
    }
  }
}

export default VendingMachinePage
