import Head from 'next/head'
import { NextPage } from 'next'
import Image from 'next/image'
import Heading from 'presentation/components/heading'
import Container from 'presentation/components/container'
import BreadCrumb from 'presentation/components/bread-crumb'
import SkeletonAnnouncement from 'presentation/components/skeleton/announcements/announcement'
import AnnouncementUseCase from 'presentation/use_cases/announcement_use_case'
import { useState, useEffect } from 'react'
import router from 'next/router'
import AnnouncementModel from 'domain/models/announcement'

const announcementUseCase = new AnnouncementUseCase()

const Announcement: NextPage = ({ announcementId }: any) => {
  const [errorCode, setErrorCode] = useState<number | undefined>(undefined)
  const [announcement, setAnnouncement] = useState<AnnouncementModel>()
  const [isFetchingAnnouncement, setIsFetchingAnnouncement] = useState<boolean>(true)

  const fetchAnnouncement = async () => {
    setIsFetchingAnnouncement(true)
    await announcementUseCase
      .findById(announcementId)
      .then((res) => {
        setAnnouncement(res)
      })
      .catch((_err) => {
        setErrorCode(404)
      })
    setIsFetchingAnnouncement(false)
  }

  useEffect(() => {
    if (announcementId) {
      fetchAnnouncement()
    }
  }, [])

  if (errorCode === 404) {
    router.push('/404')
  }

  return (
    <>
      <Head>
        <title>{announcement?.title || ''}</title>
        <meta name="description" content="Announcements | Enjoy Quill" />
      </Head>
      <main>
        <Container>
          {isFetchingAnnouncement ? (
            <>
              <div className="flex justify-center mt-6">
                <div className="max-w-[50rem] text-dark-primary dark:text-white text-justify place-self-center">
                  <SkeletonAnnouncement />
                </div>
              </div>
            </>
          ) : (
            <>
              {announcement?.title && announcement?.id && (
                <BreadCrumb
                  tabs={[
                    {
                      name: 'Home',
                      url: '/',
                    },
                    {
                      name: 'Announcements',
                      url: '/announcements',
                    },
                    {
                      name: announcement?.title,
                      url: `/announcements/${announcement?.id}`,
                    },
                  ]}
                />
              )}
              <div className="flex justify-center mt-6">
                <div className="max-w-[50rem] text-dark-primary dark:text-white text-justify place-self-center">
                  <span className="mt-5 mb-1 text-gray">
                    Published at :{announcement?.publishedAt?.toISODate()}
                  </span>
                  <Heading className="mt-5 mb-5">{announcement?.title}</Heading>
                  <div className="mt-10 mb-10">
                    <Image
                      className="rounded-t-lg max-h-[500px] w-full object-cover"
                      src={'/asset/images/<EMAIL>'}
                      height={500}
                      width={800}
                      layout="responsive"
                      objectFit="cover"
                      alt="announcement banner"
                    />
                  </div>
                  <p className="mt-5 font-medium">{announcement?.body}</p>
                </div>
              </div>
            </>
          )}
        </Container>
      </main>
    </>
  )
}

export async function getServerSideProps (context: any) {
  const { id } = context.params
  try {
    return {
      props: {
        announcementId: id,
      },
    }
  } catch (error) {
    return {
      notFound: true,
    }
  }
}

export default Announcement
