import React, { useState, useCallback } from 'react'
import { toast } from 'react-toastify'

import AdminLayout from 'presentation/components/admin/AdminLayout'
import Pagination from 'presentation/components/pagination'
import Table from 'presentation/components/table'
import SearchCardForm from 'presentation/components/admin/vending-machine/card/SearchCardForm'
import Tooltip from 'presentation/components/tooltip'

import { truncateString } from 'lib/utils.js'
import { useCustomQuery } from 'lib/hook/useCustomQuery'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'
import { Query } from 'domain/repositories/vending_machine_card_repository/query'
import { Status } from 'domain/models/card_vending_machine/status'
import VendingMachineCardUseCase from 'presentation/use_cases/vending_machine_card_use_case'

const cardVendingMachineUseCase = new VendingMachineCardUseCase()

const ImportProcess = () => {
  const authStateProvider = AuthStateProvider((state) => ({
    me: state.me,
  }))

  const [dataFilter, setDataFilter] = useState<any>({ limit: 10, page: 1 })

  const { data: dataImportProcesses = { list: [], total: 0 }, isFetching } =
    useCustomQuery(
      ['ListVendingMachineCard', authStateProvider?.me?.id, dataFilter],
      async () => {
        try {
          const queryCard = new Query({ ...dataFilter })
          const res = await cardVendingMachineUseCase.listImportCard(queryCard)
          return {
            list: cardVendingMachineUseCase.mapListCardProcess(res.items),
            total: res.total,
          }
        } catch (error: any) {
          toast.error(
            error?.showMsgErrors
              ? error?.showMsgErrors()
              : error?.message || error
          )
        }
      },
      { enabled: authStateProvider.me?.isOperator }
    )

  const onSearch = useCallback(
    (dataSearch: any) => {
      setDataFilter({ ...dataFilter, ...dataSearch, page: 1 })
    },
    [dataFilter]
  )

  const onChangePage = useCallback(
    (page: number) => {
      setDataFilter({ ...dataFilter, page: page })
      if (window.gotoTop) {
        window.gotoTop()
      }
    },
    [dataFilter]
  )

  const tableHeaders = [
    {
      key: 'cardId',
      title: 'Card ID',
      tdClass: 'px-2 py-4 min-w-40',
      render: (data: any) => <div>{data?.value}</div>,
    },
    {
      key: 'status',
      title: 'Status',
      tdClass: 'px-2 py-4',
      render: (status: Status) => (
        <>
          {status && (
            <span
              className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${status.getStatusBadge()}`}
            >
              {status.getLabel()}
            </span>
          )}
        </>
      ),
    },
    {
      key: 'createdAt',
      title: 'Imported At',
      tdClass: 'px-2 py-4 min-w-32',
      render: (value: any) => (
        <div className="text-center flex">
          {' '}
          {value?.day} <br /> {value?.time}
        </div>
      ),
    },
    {
      key: 'scrapeAt',
      title: 'Scrape At',
      tdClass: 'px-2 py-4 min-w-32',
      render: (value: any) => (
        <div className="text-center flex">
          {' '}
          {value?.day} <br /> {value?.time}
        </div>
      ),
    },
    {
      key: 'message',
      title: 'Message',
      tdClass: 'px-2 py-4 min-w-44 max-w-60',
      render: ({ value }: { value: string }) => (
        <div className="flex relative">
          {value.length > 50 ? (
            <>
              <Tooltip
                position="topCenter"
                contentWidth="max-w-screen w-full right-0 !-left-0 md:!left-1/2"
              >
                <Tooltip.Content>
                  <div className="break-all">{value}</div>
                </Tooltip.Content>
                <Tooltip.Body>
                  {truncateString(value, 30)}{' '}
                  <span className="text-primary-2 text-sm cursor-pointer whitespace-nowrap">
                    See more
                  </span>
                </Tooltip.Body>
              </Tooltip>
            </>
          ) : (
            <>{value}</>
          )}
        </div>
      ),
    },
  ]

  return (
    <AdminLayout title="Import Process">
      <div>
        <h2 className="text-xl font-semibold mb-4">Import Process</h2>
        <SearchCardForm onSearch={onSearch} isLoading={isFetching} />

        <div className="w-full overflow-x-auto pt-12">
          <Table
            head={tableHeaders}
            data={dataImportProcesses.list}
            isLoading={isFetching}
            className="min-w-full border-b"
          />
        </div>
        {dataImportProcesses?.total > 0 && (
          <div className="px-4 py-4">
            <Pagination
              currentPage={dataFilter.page}
              enableSelectRow={false}
              totalCount={dataImportProcesses?.total}
              pageSize={dataFilter.limit}
              onPageChange={(page: number) => {
                onChangePage(page)
              }}
            />
          </div>
        )}
      </div>
    </AdminLayout>
  )
}

export default ImportProcess
