import React from 'react'
import AdminLayout from 'presentation/components/admin/AdminLayout'

const VendingMachineAdmin = () => {
  return (
    <AdminLayout title="Vending Machine Dashboard">
      <div>
        <h2 className="text-xl font-semibold mb-4">Vending Machine Overview</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="text-sm text-gray-600">Total Packs</h3>
            <p className="text-2xl font-bold">0</p>
          </div>
          
          <div className="bg-green-50 p-4 rounded-lg">
            <h3 className="text-sm text-gray-600">Total Sales</h3>
            <p className="text-2xl font-bold">$0.00</p>
          </div>
          
          <div className="bg-yellow-50 p-4 rounded-lg">
            <h3 className="text-sm text-gray-600">Available Cards</h3>
            <p className="text-2xl font-bold">0</p>
          </div>
          
          <div className="bg-purple-50 p-4 rounded-lg">
            <h3 className="text-sm text-gray-600">Categories</h3>
            <p className="text-2xl font-bold">0</p>
          </div>
        </div>
        
        <div className="bg-gray-50 p-6 rounded-lg">
          <p className="text-gray-600">
            Welcome to the Vending Machine administration panel. Use the sidebar menu to navigate to different sections.
          </p>
        </div>
      </div>
    </AdminLayout>
  )
}

export default VendingMachineAdmin 