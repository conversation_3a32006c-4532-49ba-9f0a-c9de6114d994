import React, { useState, useCallback, useMemo } from 'react'
import { toast } from 'react-toastify'

import AdminLayout from 'presentation/components/admin/AdminLayout'
import Pagination from 'presentation/components/pagination'
import Table from 'presentation/components/table'
import SearchCardForm from 'presentation/components/admin/vending-machine/card/SearchCardForm'
import Switch from 'presentation/components/switch'
import Button from 'presentation/components/button'

import { useCustomQuery } from 'lib/hook/useCustomQuery'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'
import { Query } from 'domain/repositories/vending_machine_card_repository/query'
import VendingMachineCardUseCase from 'presentation/use_cases/vending_machine_card_use_case'

const cardVendingMachineUseCase = new VendingMachineCardUseCase()

const Inventory = () => {
  const authStateProvider = AuthStateProvider((state) => ({
    me: state.me,
  }))

  const [dataFilter, setDataFilter] = useState<any>({ limit: 10, page: 1 })
  const [idLoadingEnable, setIdLoadingEnable] = useState<string | number>(0)

  const { data: rateYJP = 0, isFetched } = useCustomQuery(
    ['currencyRate'],
    async () => {
      try {
        const res = await cardVendingMachineUseCase.currencyRate()
        return res
      } catch (error: any) {
        toast.error(
          error?.showMsgErrors
            ? error?.showMsgErrors()
            : error?.message || error
        )
      }
    },
    { enabled: true, refetchOnWindowFocus: false }
  )

  const {
    data: dataInventory = { list: [], total: 0 },
    isFetching,
    refetch,
  } = useCustomQuery(
    ['ListVendingMachineCard', authStateProvider?.me?.id, dataFilter, rateYJP],
    async () => {
      try {
        const queryCard = new Query({ ...dataFilter })
        const res = await cardVendingMachineUseCase.listCard(queryCard)
        return {
          list: cardVendingMachineUseCase.mapListCardInventory(
            res.items,
            rateYJP
          ),
          total: res.total,
        }
      } catch (error: any) {
        toast.error(
          error?.showMsgErrors
            ? error?.showMsgErrors()
            : error?.message || error
        )
      }
    },
    { enabled: authStateProvider.me?.isOperator && isFetched }
  )

  const isLoadingData = useMemo(() => {
    if (idLoadingEnable) {
      return false
    }
    return isFetching
  }, [isFetching, idLoadingEnable])

  const onSearch = useCallback(
    (dataSearch: any) => {
      setDataFilter({ ...dataFilter, ...dataSearch, page: 1 })
    },
    [dataFilter]
  )

  const onChangePage = useCallback(
    (page: number) => {
      setDataFilter({ ...dataFilter, page: page })
      if (window.gotoTop) {
        window.gotoTop()
      }
    },
    [dataFilter]
  )

  const toggleEnable = useCallback(
    async (id: string, newStatus: boolean) => {
      setIdLoadingEnable(id)
      try {
        await cardVendingMachineUseCase.updateCard(id, { isEnabled: newStatus })
        toast.success('Update successful')
        await refetch()
      } catch (error: any) {
        toast.error(
          error?.showMsgErrors
            ? error?.showMsgErrors()
            : error?.message || error
        )
      }
      setIdLoadingEnable(0)
    },
    [idLoadingEnable]
  )

  const tableHeaders = [
    {
      key: 'cardId',
      title: 'Card ID',
      tdClass: 'px-2 py-4 min-w-32',
      render: (data: any) => <div>{data?.value}</div>,
    },
    {
      key: 'status',
      title: 'Status',
      tdClass: 'px-2 py-4',
      render: (data: any) => <div>{data?.value}</div>,
    },
    {
      key: 'shop',
      title: 'Shop',
      tdClass: 'px-2 py-4',
      render: (data: any) => <div>{data?.value}</div>,
    },
    {
      key: 'category',
      title: 'Category',
      tdClass: 'px-2 py-4 min-w-32',
      render: ({ value }: { value: any }) => <span>{value}</span>,
    },
    {
      key: 'price',
      title: 'Price',
      tdClass: 'px-2 py-4 min-w-32',
      render: ({
        value,
        valueCurrency,
      }: {
        value: string
        valueCurrency: string
      }) => (
        <>
          <div>{value}</div>
          <div className="text-gray-400 text-sm">{valueCurrency}</div>
        </>
      ),
    },
    {
      key: 'stock',
      title: 'Stock',
      tdClass: 'px-2 py-4 min-w-32',
      render: ({ value }: { value: any }) => <span>{value}</span>,
    },
    {
      key: 'mintedQty',
      title: 'Minted Qty',
      tdClass: 'px-2 py-4 min-w-32',
      render: ({ value }: { value: any }) => <span>{value}</span>,
    },
    {
      key: 'buyBackQty',
      title: 'Buy Back Qty',
      tdClass: 'px-2 py-4 min-w-32',
      render: ({ value }: { value: any }) => <span>{value}</span>,
    },
    {
      key: 'convertedQty',
      title: 'Converted Qty',
      tdClass: 'px-2 py-4 min-w-32',
      render: ({ value }: { value: any }) => <span>{value}</span>,
    },
    {
      key: 'enable',
      title: 'Enable',
      tdClass: 'px-2 py-4 min-w-20',
      render: (action: any) => (
        <Switch
          defaultActive={action.value}
          value={action.value}
          loading={idLoadingEnable === action.id}
          onClick={(active) => toggleEnable(action.id, active)}
        />
      ),
    },
  ]

  return (
    <AdminLayout title="Inventory">
      <div>
        <div className="flex items-center justify-between mb-4 ">
          <h2 className="text-xl font-semibold">Card Inventory</h2>
          <Button
            href="/admin/vending-machine/inventory/edit-category"
            variant="dark"
            type="button"
            className="!px-4 md:!px-6 !py-2 md:!py-3 text-sm md:text-base"
            onClick={() => {}}
          >
            Edit categories
          </Button>
        </div>

        <SearchCardForm
          onSearch={onSearch}
          isLoading={isLoadingData}
          selectCategory
        />

        <div className="w-full overflow-x-auto pt-6">
          <Table
            head={tableHeaders}
            data={dataInventory.list}
            isLoading={isLoadingData}
            stickyHeader={true}
            className="min-w-full border-b"
          />
        </div>
        {dataInventory?.total > 0 && (
          <div className="px-4 py-4">
            <Pagination
              currentPage={dataFilter.page}
              enableSelectRow={false}
              totalCount={dataInventory?.total}
              pageSize={dataFilter.limit}
              onPageChange={(page: number) => {
                onChangePage(page)
              }}
            />
          </div>
        )}
      </div>
    </AdminLayout>
  )
}

export default Inventory
