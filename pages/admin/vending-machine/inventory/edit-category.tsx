import React, { useState, useCallback, useEffect, useRef } from 'react'
import AdminLayout from 'presentation/components/admin/AdminLayout'
import Link from 'next/link'
import Button from 'presentation/components/button'
import SvgIcon from 'presentation/components/svg-icon'
import Loader from 'presentation/components/loader'
import Select from 'presentation/components/select'
import Image from 'next/image'
import Pagination from 'presentation/components/pagination'
import SearchCardForm from 'presentation/components/admin/vending-machine/card/SearchCardForm'
import { toast } from 'react-toastify'

import { useCustomQuery } from 'lib/hook/useCustomQuery'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'
import { Query } from 'domain/repositories/vending_machine_card_repository/query'
import { Query as CategoryQuery } from 'domain/repositories/vending_machine_category_repository/query'
import VendingMachineCard from 'domain/models/vending_machine_card'
import VendingMachineCardUseCase from 'presentation/use_cases/vending_machine_card_use_case'
import VendingMachineCategoryUseCase from 'presentation/use_cases/vending_machine_category_use_case'

const cardVendingMachineUseCase = new VendingMachineCardUseCase()
const categoryUseCase = new VendingMachineCategoryUseCase()

const Settings = () => {
  const [dataFilter, setDataFilter] = useState<any>({ limit: 5, page: 1 })
  const [isLoading, setIsLoading] = useState(false)
  const [dataUpdate, setDataUpdate] = useState<any>({})
  const [idsFail, setIdsFail] = useState<string[]>([])
  const [idLoading, setIdLoading] = useState('')

  const authStateProvider = AuthStateProvider((state) => ({
    me: state.me,
  }))
  const { data: categoryOptions = [] } = useCustomQuery(
    ['CategoryOptions', authStateProvider?.me?.id],
    async () => {
      try {
        const query = new CategoryQuery({ limit: 100 })
        const result = await categoryUseCase.list(query)
        return result.items.map((i) => {
          return { value: i.id, name: i.name }
        })
      } catch (error: any) {
        return []
      }
    },
    { enabled: authStateProvider.me?.isOperator, refetchOnWindowFocus: false }
  )

  const {
    data: listCard = { list: [], total: 0 },
    isLoading: isLoadingData,
    refetch: refetchListCard,
  } = useCustomQuery(
    ['ListCardGroup', authStateProvider?.me?.id, dataFilter],
    async () => {
      try {
        const queryCard = new Query({
          limit: dataFilter.limit,
          page: dataFilter.page,
          cardId: dataFilter?.search || undefined,
          categoryId: dataFilter?.categoryId || undefined,
        })
        const cards = await cardVendingMachineUseCase.listCardIds(queryCard)
        const data = cards.items
        return { list: data, total: cards.total }
      } catch (error: any) {
        return { list: [], total: 0 }
      }
    },
    { enabled: authStateProvider.me?.isOperator, refetchOnWindowFocus: false }
  )

  const itemCard = (item: VendingMachineCard) => {
    return (
      <div className="flex items-center">
        <Image
          width={43}
          height={60}
          alt=""
          className="transition-transform duration-300 hover:scale-[3] cursor-pointer hover:z-10"
          src={item.image || '/asset/images/character-ship.png'}
        />
        <div className="ml-2">
          <p className="font-bold">{item.name}</p>
          <p className="text-sm text-gray-500">
            ID: <span className="font-medium">{item.cardId}</span>
          </p>
          <p className="text-sm text-gray-500">
            Category:{' '}
            <span className="font-medium">{item?.category?.name || 'N/A'}</span>
          </p>
        </div>
      </div>
    )
  }

  const handleChangeCategory = useCallback(
    (id: string, category: any) => {
      let tmp: any = { ...dataUpdate }
      tmp[id] = category
      setDataUpdate(tmp)
    },
    [dataUpdate]
  )

  const onChangePage = useCallback(
    (page: number) => {
      setDataFilter({ ...dataFilter, page: page })
      setIdsFail([])
      if (window.gotoTop) {
        window.gotoTop()
      }
    },
    [dataFilter]
  )

  const onSearch = useCallback(
    (dataSearch: any) => {
      setDataFilter({ ...dataFilter, ...dataSearch, page: 1 })
      setIdsFail([])
    },
    [dataFilter]
  )

  const onHandleMapCategory = useCallback(
    async (id: string) => {
      setIdLoading(id)
      setIdsFail([])
      try {
        await cardVendingMachineUseCase.mapCategory(id, dataUpdate[id].value)
        toast.success('Update successful')
        refetchListCard()
      } catch (error: any) {
        toast.error(
          error?.showMsgErrors
            ? error?.showMsgErrors()
            : error?.message || error
        )
      }
      setIdLoading('')
    },
    [dataUpdate]
  )

  const onHandleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault()
      setIdsFail([])
      setIsLoading(true)
      try {
        const ids = Object.keys(dataUpdate)
        const res = await Promise.allSettled(
          ids?.map((id: string) => {
            return cardVendingMachineUseCase.mapCategory(
              id,
              dataUpdate[id].value
            )
          })
        )
        const cardIdsFail = ids.filter(
          (cardId: string, idx: number) => res[idx].status !== 'fulfilled'
        )
        setIdsFail(cardIdsFail)
        if (cardIdsFail.length) {
          toast.error(
            // `Category mapping failed for the following card IDs: ${cardIdsFail.join(', ')}`,
            'Category mapping failed'
          )
        } else {
          toast.success('Update successful')
        }
        refetchListCard()
      } catch (error: any) {
        toast.error(
          error?.showMsgErrors
            ? error?.showMsgErrors()
            : error?.message || error
        )
      }
      setIsLoading(false)
    },
    [dataUpdate]
  )

  useEffect(() => {
    let tmp: any = {}
    listCard?.list?.forEach((card) => {
      tmp[card.cardId] = {
        value: card.categoryId,
        name: card.category?.name || 'Select category',
      }
    })
    setDataUpdate(tmp)
  }, [listCard])

  // useEffect(() => {
  //   console.log('----dataUpdate', dataUpdate)
  // }, [dataUpdate])

  return (
    <AdminLayout title="Edit categories">
      <div>
        <div className="mb-4">
          <Link
            href={'/admin/vending-machine/inventory'}
            className="inline-flex items-center text-blue-500 hover:opacity-80"
          >
            <SvgIcon
              name="arrowRight"
              className="w-5 h-5 stroke-none fill-dark-primary dark:fill-white rotate-180"
            />
            Back
          </Link>
        </div>
        <h2 className="text-xl font-semibold mb-4">
          Edit categories for inventory
        </h2>

        <section className="max-w-[1000px]">
          <SearchCardForm
            onSearch={onSearch}
            isLoading={isLoadingData}
            selectCategory
            selectCategoryEmpty
            className="!p-4 mb-4"
          />
          {listCard.list?.map((card) => {
            return (
              <div
                key={card.id}
                className="py-2 flex items-center gap-3 border-b max-md:flex-col max-md:items-start"
              >
                {itemCard(card)}

                <div className="ml-auto md:max-w-[400px] max-md:w-full">
                  <div className="flex w-full items-center gap-2">
                    {categoryOptions.length && dataUpdate[card.cardId] && (
                      <Select
                        options={categoryOptions}
                        className="w-full flex-1"
                        selectClassName="py-2 min-w-[240px]"
                        customWidth={true}
                        defaultSelected={dataUpdate[card.cardId]}
                        onSelect={(option) =>
                          handleChangeCategory(card.cardId, option)
                        }
                      />
                    )}
                    <Button
                      type="button"
                      variant="outlineDark"
                      className="flex items-center justify-center !px-5 !py-0 !h-[32px] w-[80px]"
                      disable={
                        isLoading ||
                        isLoadingData ||
                        !dataUpdate[card.cardId]?.value ||
                        idLoading == card.cardId
                      }
                      onClick={(e) => {
                        e.preventDefault()
                        onHandleMapCategory(card.cardId)
                      }}
                    >
                      {idLoading == card.cardId ? (
                        <Loader
                          className="border-gray"
                          classNameLoader="!w-4 !h-4"
                        />
                      ) : (
                        'Save'
                      )}
                    </Button>
                  </div>
                  {idsFail.includes(card.cardId) && (
                    <div className="text-sm text-red text-stroke-thin mt-1 text-right">
                      Category mapping failed
                    </div>
                  )}
                </div>
              </div>
            )
          })}

          {!listCard?.list?.length && (
            <div className="flex items-center justify-center h-[200px]">
              No data
            </div>
          )}

          {listCard?.total > 0 && (
            <div className="px-4 py-4">
              <Pagination
                currentPage={dataFilter.page}
                enableSelectRow={false}
                showCountText={false}
                totalCount={listCard?.total}
                pageSize={dataFilter.limit}
                onPageChange={(page: number) => {
                  onChangePage(page)
                }}
              />
            </div>
          )}

          {listCard?.list?.length > 1 && (
            <div className="flex justify-center mt-4">
              <Button
                type="button"
                variant="dark"
                className="flex items-center justify-center"
                disable={isLoading || isLoadingData}
                onClick={onHandleSubmit}
              >
                {isLoading ? (
                  <Loader className="border-gray" />
                ) : (
                  `Save (${listCard?.list?.length})`
                )}
              </Button>
            </div>
          )}
        </section>
      </div>
    </AdminLayout>
  )
}

export default Settings
