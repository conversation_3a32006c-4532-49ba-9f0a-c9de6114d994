import { NextPage } from 'next'
import Head from 'next/head'
import { useState, useEffect } from 'react'
import Container from 'presentation/components/container'
import Heading from 'presentation/components/heading'
import Loader from 'presentation/components/loader'
import SvgIcon from 'presentation/components/svg-icon'
import AdminLayout from 'presentation/components/admin/AdminLayout'
import VendingMachineDashboardRemoteDatasource, { DashboardMetrics } from 'data/datasources/remote/vending_machine_dashboard_remote_datasource'
import { motion } from 'framer-motion'

const dashboardDatasource = new VendingMachineDashboardRemoteDatasource()

// Animated Number Component
const AnimatedNumber: React.FC<{
  value: number
  duration?: number
  decimals?: number
}> = ({ value, duration = 2000, decimals = 0 }) => {
  const [displayValue, setDisplayValue] = useState(0)

  useEffect(() => {
    let startTime: number,
      animationFrame: number

    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime
      const progress = Math.min((currentTime - startTime) / duration, 1)
      
      // Easing function for smooth animation
      const easeOutCubic = 1 - Math.pow(1 - progress, 3)
      const currentValue = easeOutCubic * value
      
      setDisplayValue(currentValue)
      
      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate)
      }
    }

    animationFrame = requestAnimationFrame(animate)
    return () => cancelAnimationFrame(animationFrame)
  }, [value, duration])

  return <span>{displayValue.toFixed(decimals)}</span>
}

// Metric Card Component
const MetricCard: React.FC<{
  title: string
  value: number
  unit: string
  previousValue: number
  changePercent: number
  trend: 'up' | 'down' | 'stable'
  icon: string
  color: string
  decimals?: number
}> = ({ title, value, unit, previousValue, changePercent, trend, icon, color, decimals = 0 }) => {
  const isPositive = changePercent > 0
  const isNegative = changePercent < 0
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white dark:bg-blue-3 rounded-3xl p-6 shadow-lg hover:shadow-xl transition-all duration-300"
    >
      <div className="flex items-center justify-between mb-4">
        <div className={`p-3 rounded-2xl ${color}`}>
          <SvgIcon
            name={icon}
            className="w-6 h-6 fill-white"
          />
        </div>
        <div className="flex items-center space-x-1">
          {trend !== 'stable' && (
            <SvgIcon
              name={trend === 'up' ? 'arrowUp' : 'arrowDown'}
              className={`w-4 h-4 ${
                isPositive ? 'fill-green-500' : isNegative ? 'fill-red' : 'fill-gray-1'
              }`}
            />
          )}
          <span className={`text-sm font-medium ${
            isPositive ? 'text-green-500' : isNegative ? 'text-red' : 'text-gray-1'
          }`}>
            {Math.abs(changePercent).toFixed(1)}%
          </span>
        </div>
      </div>
      
      <h3 className="text-gray-1 dark:text-gray-3 text-sm font-medium mb-2">
        {title}
      </h3>
      
      <div className="flex items-baseline space-x-2">
        <span className="text-3xl font-bold text-dark-primary dark:text-white">
          <AnimatedNumber value={value} decimals={decimals} />
        </span>
        <span className="text-lg text-gray-1 dark:text-gray-3">
          {unit}
        </span>
      </div>
      
      <div className="mt-3 text-xs text-gray-1 dark:text-gray-3">
        Previous: {previousValue.toFixed(decimals)} {unit}
      </div>
    </motion.div>
  )
}

// Mini Chart Component
const MiniChart: React.FC<{
  data: Array<{ date: string; value: number }>
  color: string
}> = ({ data, color }) => {
  const maxValue = Math.max(...data.map((d) => d.value))
  const minValue = Math.min(...data.map((d) => d.value))
  const range = maxValue - minValue || 1

  return (
    <div className="h-16 flex items-end space-x-1">
      {data.map((point, index) => {
        const height = ((point.value - minValue) / range) * 100
        return (
          <motion.div
            key={point.date}
            initial={{ height: 0 }}
            animate={{ height: `${Math.max(height, 5)}%` }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            className={`flex-1 ${color} rounded-t-sm min-h-[4px]`}
            title={`${point.date}: ${point.value}`}
          />
        )
      })}
    </div>
  )
}

const VendingMachineDashboard: NextPage = () => {
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [selectedPeriod, setSelectedPeriod] = useState('7d')

  const loadDashboardData = async () => {
    setIsLoading(true)
    try {
      const response = await dashboardDatasource.getDashboardMetrics(selectedPeriod)
      if (response.success) {
        setMetrics(response.data)
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    loadDashboardData()
  }, [selectedPeriod])

  return (
    <AdminLayout title="Vending Machine Dashboard">
      <Head>
        <title>Vending Machine Dashboard - Admin</title>
        <meta name="description" content="Vending Machine Analytics Dashboard" />
      </Head>
      
      <main className="min-h-screen bg-gray-5 dark:bg-blue-8">
        <Container className="pt-8 pb-16">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <Heading className="!mb-2">Vending Machine Dashboard</Heading>
              <p className="text-gray-1 dark:text-gray-3">
                Analytics and performance metrics for vending machine operations
              </p>
            </div>
            
            {/* Period Selector */}
            <div className="flex items-center space-x-2">
              {['24h', '7d', '30d', '90d'].map((period) => (
                <button
                  key={period}
                  onClick={() => setSelectedPeriod(period)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                    selectedPeriod === period
                      ? 'bg-primary-2 text-white'
                      : 'bg-white dark:bg-blue-3 text-gray-1 dark:text-gray-3 hover:bg-gray-4 dark:hover:bg-blue-9'
                  }`}
                >
                  {period}
                </button>
              ))}
            </div>
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center py-20">
              <Loader className="w-8 h-8" />
            </div>
          ) : metrics ? (
            <>
              {/* Metrics Cards - Row 1 */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                <MetricCard
                  title="Sales Volume"
                  value={metrics.salesVolume.value}
                  unit={metrics.salesVolume.currency}
                  previousValue={metrics.salesVolume.previousValue}
                  changePercent={metrics.salesVolume.changePercent}
                  trend={metrics.salesVolume.trend}
                  icon=""
                  color="bg-gradient-to-r from-green-400 to-green-600"
                  decimals={2}
                />
                
                <MetricCard
                  title="Average Purchases"
                  value={metrics.averagePurchases.value}
                  unit={metrics.averagePurchases.unit}
                  previousValue={metrics.averagePurchases.previousValue}
                  changePercent={metrics.averagePurchases.changePercent}
                  trend={metrics.averagePurchases.trend}
                  icon=""
                  color="bg-gradient-to-r from-blue-400 to-blue-600"
                  decimals={1}
                />
                
                <MetricCard
                  title="Number of Buybacks"
                  value={metrics.numberOfBuybacks.value}
                  unit={metrics.numberOfBuybacks.unit}
                  previousValue={metrics.numberOfBuybacks.previousValue}
                  changePercent={metrics.numberOfBuybacks.changePercent}
                  trend={metrics.numberOfBuybacks.trend}
                  icon=""
                  color="bg-gradient-to-r from-orange-400 to-orange-600"
                  decimals={0}
                />
              </div>

              {/* Metrics Cards - Row 2 */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <MetricCard
                  title="Buyback Rate"
                  value={metrics.buybackRate.value}
                  unit={metrics.buybackRate.unit}
                  previousValue={metrics.buybackRate.previousValue}
                  changePercent={metrics.buybackRate.changePercent}
                  trend={metrics.buybackRate.trend}
                  icon=""
                  color="bg-gradient-to-r from-purple-400 to-purple-600"
                  decimals={1}
                />
                
                <MetricCard
                  title="Offer Acceptance Amount"
                  value={metrics.offerAcceptanceAmount.value}
                  unit={metrics.offerAcceptanceAmount.currency}
                  previousValue={metrics.offerAcceptanceAmount.previousValue}
                  changePercent={metrics.offerAcceptanceAmount.changePercent}
                  trend={metrics.offerAcceptanceAmount.trend}
                  icon="handshake"
                  color="bg-gradient-to-r from-indigo-400 to-indigo-600"
                  decimals={2}
                />
                
                <MetricCard
                  title="Buyback Circulation Rate"
                  value={metrics.buybackCirculationRate.value}
                  unit={metrics.buybackCirculationRate.unit}
                  previousValue={metrics.buybackCirculationRate.previousValue}
                  changePercent={metrics.buybackCirculationRate.changePercent}
                  trend={metrics.buybackCirculationRate.trend}
                  icon=""
                  color="bg-gradient-to-r from-teal-400 to-teal-600"
                  decimals={1}
                />
              </div>

              {/* Charts Section */}
              {/* <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  className="bg-white dark:bg-blue-3 rounded-3xl p-6 shadow-lg"
                >
                  <h3 className="text-lg font-bold text-dark-primary dark:text-white mb-4">
                    Sales Volume Trend
                  </h3>
                  <MiniChart 
                    data={metrics.chartData.salesVolumeChart}
                    color="bg-gradient-to-t from-green-400 to-green-600"
                  />
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  className="bg-white dark:bg-blue-3 rounded-3xl p-6 shadow-lg"
                >
                  <h3 className="text-lg font-bold text-dark-primary dark:text-white mb-4">
                    Average Purchases Trend
                  </h3>
                  <MiniChart 
                    data={metrics.chartData.averagePurchasesChart}
                    color="bg-gradient-to-t from-blue-400 to-blue-600"
                  />
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                  className="bg-white dark:bg-blue-3 rounded-3xl p-6 shadow-lg"
                >
                  <h3 className="text-lg font-bold text-dark-primary dark:text-white mb-4">
                    Buyback Rate Trend
                  </h3>
                  <MiniChart 
                    data={metrics.chartData.buybackRateChart}
                    color="bg-gradient-to-t from-purple-400 to-purple-600"
                  />
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.5 }}
                  className="bg-white dark:bg-blue-3 rounded-3xl p-6 shadow-lg"
                >
                  <h3 className="text-lg font-bold text-dark-primary dark:text-white mb-4">
                    Offer Acceptance Trend
                  </h3>
                  <MiniChart 
                    data={metrics.chartData.offerAcceptanceChart}
                    color="bg-gradient-to-t from-indigo-400 to-indigo-600"
                  />
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                  className="bg-white dark:bg-blue-3 rounded-3xl p-6 shadow-lg"
                >
                  <h3 className="text-lg font-bold text-dark-primary dark:text-white mb-4">
                    Circulation Rate Trend
                  </h3>
                  <MiniChart 
                    data={metrics.chartData.circulationRateChart}
                    color="bg-gradient-to-t from-teal-400 to-teal-600"
                  />
                </motion.div>
              </div> */}
            </>
          ) : (
            <div className="text-center py-20">
              <p className="text-gray-1 dark:text-gray-3">
                Failed to load dashboard data. Please try again.
              </p>
              <button
                onClick={loadDashboardData}
                className="mt-4 px-6 py-2 bg-primary-2 text-white rounded-full hover:bg-primary-1 transition-colors"
              >
                Retry
              </button>
            </div>
          )}
        </Container>
      </main>
    </AdminLayout>
  )
}

export default VendingMachineDashboard
