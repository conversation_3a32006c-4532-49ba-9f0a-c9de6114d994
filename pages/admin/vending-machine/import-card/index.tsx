import React, { useState, useMemo } from 'react'
import { useForm } from 'lib/hook/useForm'
import AdminLayout from 'presentation/components/admin/AdminLayout'
import Input from 'presentation/components/input'
import Button from 'presentation/components/button'
import Loader from 'presentation/components/loader'
import { toast } from 'react-toastify'
import VendingMachineCardUseCase from 'presentation/use_cases/vending_machine_card_use_case'

const cardVendingMachineUseCase = new VendingMachineCardUseCase()

const ImportCard = () => {
  const { formData, setFormValue } = useForm({ cardId: '', file: null })
  const [isLoadingImport, setIsLoadingImport] = useState(false)
  const [isLoadingUpload, setIsLoadingUpload] = useState(false)

  const handleImport = async (e: React.FormEvent) => {
    try {
      e.preventDefault()
      if (!formData.cardId) {
        throw 'Please fill Card Id.'
      }
      setIsLoadingImport(true)
      const res = await cardVendingMachineUseCase.importCard({
        // eslint-disable-next-line camelcase
        card_id: formData.cardId,
      })
      setIsLoadingImport(false)
      setFormValue('cardId', '')
      toast.success('Import successful')
    } catch (error: any) {
      toast.error(
        error?.showMsgErrors ? error?.showMsgErrors() : error?.message || error
      )
      setIsLoadingImport(false)
    }
  }

  const handleUpload = async (e: React.FormEvent) => {
    try {
      e.preventDefault()
      if (!formData.file) {
        throw 'Please fill File.'
      }
      const type = formData.file.type.split('/')

      if (!type.includes('csv')) {
        throw 'Unsupported file type'
      }
      setIsLoadingUpload(true)
      const res = await cardVendingMachineUseCase.batchImportCard(formData.file)
      setIsLoadingUpload(false)
      setFormValue('file', null)
      toast.success('Import successful')
    } catch (error: any) {
      toast.error(
        error?.showMsgErrors ? error?.showMsgErrors() : error?.message || error
      )
      setIsLoadingUpload(false)
    }
  }

  const handleDownload = async (e: React.FormEvent) => {
    try {
      e.preventDefault()
      await cardVendingMachineUseCase.importCardTemplate()
    } catch (error: any) {
      toast.error(
        error?.showMsgErrors ? error?.showMsgErrors() : error?.message || error
      )
    }
  }

  return (
    <AdminLayout title="Import Card">
      <div>
        <h2 className="text-xl font-semibold mb-4">Import Card</h2>

        <form onSubmit={handleImport}>
          <div className="w-full flex md:items-center items-start md:flex-row flex-col">
            <p className="font-medium text-dark-primary dark:text-white w-28">
              Card ID
            </p>
            <Input
              wrapClass="md:max-w-80 w-full md:mt-0 mt-1"
              type="text"
              value={formData.cardId}
              maxLength={50}
              disabled={isLoadingImport}
              onChange={(e) => setFormValue('cardId', e.target.value)}
            />
            <Button
              variant="dark"
              htmltype="submit"
              className="md:ml-4 md:mt-0 mt-4 flex"
              disable={isLoadingImport || !formData.cardId || isLoadingUpload}
            >
              {isLoadingImport && <Loader className="mr-2" />}{' '}
              {isLoadingImport ? 'Please waiting' : 'Import'}
            </Button>
          </div>
        </form>

        <div className="my-6 font-bold">Or</div>

        <form onSubmit={handleUpload}>
          <div className="w-full flex md:items-center items-start md:flex-row flex-col">
            <p className="font-medium text-dark-primary dark:text-white w-28">
              Batch Import
            </p>
            <div className="md:max-w-80 w-full md:mt-0 mt-1 flex items-center justify-start	">
              <input
                className="hidden"
                type="file"
                name="logoImage"
                accept=".csv"
                id="card-file"
                disabled={isLoadingUpload}
                onChange={(e: any) => setFormValue('file', e.target.files[0])}
              />
              <label
                htmlFor="card-file"
                className="rounded-full font-bold border border-black bg-white text-black py-1 px-4 text-base flex cursor-pointer w-fit flex-shrink-0"
              >
                Choose CSV File
              </label>
              <span className="ml-4 truncate">{formData?.file?.name}</span>
            </div>
            <button
              className="text-right underline relative text-sm text-primary-2 hover:no-underline block md:hidden mt-2"
              onClick={handleDownload}
            >
              Download template
            </button>

            <Button
              variant="dark"
              htmltype="submit"
              className="md:ml-4 md:mt-0 mt-4 flex"
              disable={isLoadingImport || !formData.file || isLoadingUpload}
            >
              {isLoadingUpload && <Loader className="mr-2" />}{' '}
              {isLoadingUpload ? 'Please waiting' : 'Upload'}
            </Button>
          </div>
          <div className="w-full flex md:items-center items-start md:flex-row flex-col mt-1">
            <p className="w-28"></p>
            <button
              className="text-right underline relative text-sm text-primary-2 hover:no-underline hidden md:block"
              onClick={handleDownload}
            >
              Download template
            </button>
          </div>
        </form>
      </div>
    </AdminLayout>
  )
}

export default ImportCard
