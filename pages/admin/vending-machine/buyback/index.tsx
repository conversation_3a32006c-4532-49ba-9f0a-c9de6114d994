import React, { useState, useCallback } from 'react'
import { toast } from 'react-toastify'
import AdminLayout from 'presentation/components/admin/AdminLayout'
import Pagination from 'presentation/components/pagination'
import Table from 'presentation/components/table'
import SearchBuybackForm from 'presentation/components/admin/vending-machine/buyback/SearchBuybackForm'
import Button from 'presentation/components/button'
import { useCustomQuery } from 'lib/hook/useCustomQuery'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'
import BuybackRemoteDatasource from 'data/datasources/remote/buyback_remote_datasource'

import Image from 'next/image'
const buybackDatasource = new BuybackRemoteDatasource()

interface BuybackRequest {
  id: string
  card: {
    id: string
    image: string
  }
  category: string
  tokenId: string
  packPrice: number
  cardPrice: number
  buyBackPrice: number
  mintTime: string
  buyBackRequestTime: string
  status: 'Pending' | 'Success' | 'Expired' | 'Rejected'
  action?: any
}

const AdminBuyback = () => {
  const authStateProvider = AuthStateProvider((state) => ({
    me: state.me,
  }))

  const [dataFilter, setDataFilter] = useState<any>({ 
    limit: 10, 
    page: 1,
    category: '',
    cardId: '',
    tokenId: '',
  })
  const [isLoadingExport, setIsLoadingExport] = useState(false)

  // Mock data for admin buyback requests
  const mockBuybackRequests: BuybackRequest[] = [
    {
      id: '1',
      card: {
        id: 'sv9-114-100',
        image: '/asset/images/vending-machine/card.jpg',
      },
      category: 'Pokemon',
      tokenId: 'ABCD...E1',
      packPrice: 5,
      cardPrice: 4,
      buyBackPrice: 3,
      mintTime: '2025-09-25 11:00',
      buyBackRequestTime: '2025-09-25 12:00',
      status: 'Pending',
      action: {
        id: '1',
        status: 'Pending',
      },
    },
    {
      id: '2',
      card: {
        id: 'sv9-114-100',
        image: '/asset/images/vending-machine/card.jpg',
      },
      category: 'Pokemon',
      tokenId: 'ABCD...E2',
      packPrice: 5,
      cardPrice: 7,
      buyBackPrice: 6,
      mintTime: '2025-09-25 11:00',
      buyBackRequestTime: '2025-09-25 12:00',
      status: 'Success',
      action: {
        id: '2',
        status: 'Success',
      },
    },
    {
      id: '3',
      card: {
        id: 'sv9-114-100',
        image: '/asset/images/vending-machine/card.jpg',
      },
      category: 'Pokemon',
      tokenId: 'ABCD...E3',
      packPrice: 5,
      cardPrice: 8,
      buyBackPrice: 6,
      mintTime: '2025-09-25 11:00',
      buyBackRequestTime: '2025-09-25 12:00',
      status: 'Expired',
      action: {
        id: '3',
        status: 'Expired',
      },
    },
    {
      id: '4',
      card: {
        id: 'sv9-114-100',
        image: '/asset/images/vending-machine/card.jpg',
      },
      category: 'Pokemon',
      tokenId: 'ABCD...E4',
      packPrice: 5,
      cardPrice: 10,
      buyBackPrice: 8,
      mintTime: '2025-09-25 11:00',
      buyBackRequestTime: '2025-09-25 12:00',
      status: 'Rejected',
      action: {
        id: '4',
        status: 'Rejected',
      },
    },
  ]

  const { data: dataBuybackRequests = { list: [], total: 0 }, isFetching } =
    useCustomQuery(
      ['ListBuybackRequests', authStateProvider?.me?.id, dataFilter],
      async () => {
        try {
          // Filter mock data based on search criteria
          let filteredData = mockBuybackRequests

          if (dataFilter.category) {
            filteredData = filteredData.filter((item) => 
              item.category.toLowerCase().includes(dataFilter.category.toLowerCase())
            )
          }

          if (dataFilter.cardId) {
            filteredData = filteredData.filter((item) => 
              item.card.id.toLowerCase().includes(dataFilter.cardId.toLowerCase())
            )
          }

          if (dataFilter.tokenId) {
            filteredData = filteredData.filter((item) => 
              item.tokenId.toLowerCase().includes(dataFilter.tokenId.toLowerCase())
            )
          }

          // Pagination
          const startIndex = (dataFilter.page - 1) * dataFilter.limit
          const endIndex = startIndex + dataFilter.limit
          const paginatedData = filteredData.slice(startIndex, endIndex)

          return {
            list: paginatedData,
            total: filteredData.length,
          }
        } catch (error: any) {
          toast.error(
            error?.showMsgErrors
              ? error?.showMsgErrors()
              : error?.message || error,
          )
          return { list: [], total: 0 }
        }
      },
      {
        enabled: authStateProvider.me?.isOperator,
      },
    )

  const onSearch = useCallback(
    (dataSearch: any) => {
      console.log('------dataSearch', dataSearch)
      setDataFilter({ ...dataFilter, ...dataSearch, page: 1 })
    },
    [dataFilter],
  )

  const onExport = useCallback(
    async (dataSearch: any) => {
      setIsLoadingExport(true)
      try {
        // TODO: Implement CSV export functionality
        console.log('Exporting CSV with filters:', dataSearch)
        
        // Simulate export delay
        await new Promise((resolve) => setTimeout(resolve, 2000))
        
        toast.success('CSV exported successfully!')
      } catch (error) {
        toast.error('Failed to export CSV')
      } finally {
        setIsLoadingExport(false)
      }
    },
    [],
  )

  const onChangePage = useCallback(
    (page: number) => {
      setDataFilter({ ...dataFilter, page: page })
      if (window.gotoTop) {
        window.gotoTop()
      }
    },
    [dataFilter],
  )

  const handleAction = async (action: string, id: string) => {
    console.log(`Action: ${action} for request ID: ${id}`)
    
    if (action === 'Cancel') {
      try {
        // Call API to cancel buyback request
        await buybackDatasource.cancelBuybackRequest(id)
        
        // Refetch data to update the list
        // Note: In real implementation, this would trigger a re-query
        toast.success(`Buyback request ${id} has been cancelled successfully`)
        
        // For demo purposes, we'll simulate the refetch by updating the filter
        setDataFilter({ ...dataFilter })
      } catch (error) {
        toast.error(`Failed to cancel buyback request: ${error}`)
      }
    } else {
      toast.info(`${action} action performed for request ${id}`)
    }
  }

  const getStatusBadge = (status: string) => {
    const statusClasses: { [key: string]: string } = {
      Pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100',
      Success: 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100',
      Expired: 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100',
      Rejected: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100',
    }

    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${statusClasses[status] || statusClasses.Pending}`}>
        {status}
      </span>
    )
  }

  const tableHeaders = [
    {
      key: 'card',
      title: 'Card',
      render: (data: any) => (
        <div className="flex items-center gap-2 p-2">
          <div className="w-16 bg-gray-200 rounded flex items-center justify-center text-xs font-bold">
            <Image src={data.image} alt={data.id} width={32} height={32} className="w-16 h-auto" />
          </div>
          <span className="font-medium">{data.id}</span>
        </div>
      ),
    },
    {
      key: 'category',
      title: 'Category',
    },
    {
      key: 'tokenId',
      title: 'Token Id',
      render: (data: BuybackRequest) => (
        <span className="font-mono text-blue-600 dark:text-blue-400">
          {data.tokenId}
        </span>
      ),
    },
    {
      key: 'packPrice',
      title: 'Pack Price',
      render: (data: BuybackRequest) => `$${data.packPrice}`,
    },
    {
      key: 'cardPrice',
      title: 'Card Price',
      render: (data: BuybackRequest) => `$${data.cardPrice}`,
    },
    {
      key: 'buyBackPrice',
      title: 'Buy Back Price',
      render: (data: BuybackRequest) => (
        <span className="font-semibold text-green-600 dark:text-green-400">
          $${data.buyBackPrice}
        </span>
      ),
    },
    {
      key: 'mintTime',
      title: 'Mint Time',
      render: (data: BuybackRequest) => (
        <div className="text-sm">
          <div>{data.mintTime.split(' ')[0]}</div>
          <div className="text-gray-500">{data.mintTime.split(' ')[1]}</div>
        </div>
      ),
    },
    {
      key: 'buyBackRequestTime',
      title: 'Buy back Request Time',
      render: (data: BuybackRequest) => (
        <div className="text-sm">
          <div>{data.buyBackRequestTime.split(' ')[0]}</div>
          <div className="text-gray-500">{data.buyBackRequestTime.split(' ')[1]}</div>
        </div>
      ),
    },
    {
      key: 'status',
      title: 'Status',
      render: (data: BuybackRequest) => getStatusBadge(data.status),
    },
    {
      key: 'action',
      title: 'Action',
      render: (data: BuybackRequest) => (
        <div className="flex gap-2">
          {data.status === 'Pending' && (
            <Button
              variant="outline"
              size="xs"
              className="!py-1 !px-2 text-xs border-red-500 text-red-500 hover:bg-red-50"
              onClick={() => handleAction('Cancel', data.id)}
            >
              Cancel
            </Button>
          )}
        </div>
      ),
    },
  ]

  return (
    <AdminLayout title="Buy back Request">
      <div>
        <h2 className="text-xl font-semibold mb-4">Admin: Buy back Request</h2>
        
        <SearchBuybackForm
          onSearch={onSearch}
          onExport={onExport}
          isLoading={isFetching}
          isLoadingExport={isLoadingExport}
        />

        <div className="w-full overflow-x-auto pt-12">
          <Table
            head={tableHeaders}
            data={dataBuybackRequests.list}
            isLoading={isFetching}
            className="min-w-full border-b"
          />
        </div>
        
        {dataBuybackRequests?.total > 0 && (
          <div className="px-4 py-4">
            <Pagination
              currentPage={dataFilter.page}
              enableSelectRow={false}
              totalCount={dataBuybackRequests?.total}
              pageSize={dataFilter.limit}
              onPageChange={(page: number) => {
                onChangePage(page)
              }}
            />
          </div>
        )}
      </div>
    </AdminLayout>
  )
}

export default AdminBuyback
