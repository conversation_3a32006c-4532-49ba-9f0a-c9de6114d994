import React, { useState, useCallback } from 'react'
import { toast } from 'react-toastify'

import AdminLayout from 'presentation/components/admin/AdminLayout'
import Pagination from 'presentation/components/pagination'
import Table from 'presentation/components/table'
import SearchSalesCardForm from 'presentation/components/admin/vending-machine/card/SearchSalesCardForm'
import Link from 'next/link'

import { shortenAddress } from 'lib/utils.js'
import { useCustomQuery } from 'lib/hook/useCustomQuery'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'
import { QueryCheckout } from 'domain/repositories/vending_machine_card_repository/query_checkout'
import { StatusCheckout } from 'domain/models/card_vending_machine/status_checkout'
import VendingMachineCardUseCase from 'presentation/use_cases/vending_machine_card_use_case'

const cardVendingMachineUseCase = new VendingMachineCardUseCase()

const SalesCard = () => {
  const authStateProvider = AuthStateProvider((state) => ({
    me: state.me,
  }))

  const [dataFilter, setDataFilter] = useState<any>({ limit: 10, page: 1 })

  const { data: dataImportProcesses = { list: [], total: 0 }, isFetching } =
    useCustomQuery(
      ['ListCardSales', authStateProvider?.me?.id, dataFilter],
      async () => {
        try {
          const queryCard = new QueryCheckout({ ...dataFilter })
          const res = await cardVendingMachineUseCase.listCheckout(queryCard)
          return {
            list: cardVendingMachineUseCase.mapSales(res.items),
            total: res.total,
          }
        } catch (error: any) {
          toast.error(
            error?.showMsgErrors
              ? error?.showMsgErrors()
              : error?.message || error
          )
        }
      },
      {
        enabled: authStateProvider.me?.isOperator,
      }
    )

  const onSearch = useCallback(
    (dataSearch: any) => {
      setDataFilter({ ...dataFilter, ...dataSearch, page: 1 })
    },
    [dataFilter]
  )

  const onExport = useCallback(
    async (dataSearch: any) => {
      try {
        // setDataFilter({ ...dataFilter, ...dataSearch, page: 1 })
        const queryCard = new QueryCheckout(dataSearch)
        await cardVendingMachineUseCase.exportCheckout(queryCard)
      } catch (error: any) {
        toast.error(
          error?.showMsgErrors
            ? error?.showMsgErrors()
            : error?.message || error
        )
      }
    },
    [dataFilter]
  )

  const onChangePage = useCallback(
    (page: number) => {
      setDataFilter({ ...dataFilter, page: page })
      if (window.gotoTop) {
        window.gotoTop()
      }
    },
    [dataFilter]
  )

  const tableHeaders = [
    {
      key: 'date',
      title: 'Date',
      tdClass: 'px-2 py-4 min-w-[100px]',
      render: (value: any) => (
        <div className="text-center flex">
          {value?.day} <br /> {value?.time}
        </div>
      ),
    },
    {
      key: 'status',
      title: 'Status',
      tdClass: 'px-2 py-4',
      render: (status: StatusCheckout) => (
        <>
          {status && (
            <span
              className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${status.getStatusBadge()}`}
            >
              {status.getLabel()}
            </span>
          )}
        </>
      ),
    },
    {
      key: 'category',
      title: 'Category',
      tdClass: 'px-2 py-4 min-w-32',
      render: ({ value }: { value: any }) => <span>{value}</span>,
    },
    {
      key: 'price',
      title: 'Price',
      tdClass: 'px-2 py-4 min-w-32',
      render: ({ value }: { value: any }) => <span>{value}</span>,
    },
    {
      key: 'cardId',
      title: 'Card ID',
      tdClass: 'px-2 py-4 min-w-32',
      render: (data: any) => <div>{data?.value}</div>,
    },
    {
      key: 'quantity',
      title: 'Quantity',
      tdClass: 'px-2 py-4 min-w-32',
      render: ({ value }: { value: any }) => <span>{value}</span>,
    },
    {
      key: 'buyTx',
      title: 'Buy Tx',
      tdClass: 'px-2 py-4 min-w-32',
      render: (data: any) => (
        <Link
          href={data.link}
          target="_blank"
          className="text-primary-2 underline hover:no-underline"
        >
          {shortenAddress(data?.value)}
        </Link>
      ),
    },
    {
      key: 'mintTx',
      title: 'Mint Tx',
      tdClass: 'px-2 py-4 min-w-32',
      render: (data: any) => (
        <Link
          href={data.link}
          target="_blank"
          className="text-primary-2 underline hover:no-underline"
        >
          {shortenAddress(data?.value)}
        </Link>
      ),
    },
    {
      key: 'recipient',
      title: 'To Address',
      tdClass: 'px-2 py-4 min-w-32',
      render: (data: any) => (
        <Link
          href={data.link}
          target="_blank"
          className="text-primary-2 underline hover:no-underline"
        >
          {shortenAddress(data?.value)}
        </Link>
      ),
    },
  ]

  return (
    <AdminLayout title="Sales">
      <div>
        <h2 className="text-xl font-semibold mb-4">Sales</h2>
        <SearchSalesCardForm
          onSearch={onSearch}
          onExport={onExport}
          isLoading={isFetching}
        />

        <div className="w-full overflow-x-auto pt-12">
          <Table
            head={tableHeaders}
            data={dataImportProcesses.list}
            isLoading={isFetching}
            className="min-w-full border-b"
          />
        </div>
        {dataImportProcesses?.total > 0 && (
          <div className="px-4 py-4">
            <Pagination
              currentPage={dataFilter.page}
              enableSelectRow={false}
              totalCount={dataImportProcesses?.total}
              pageSize={dataFilter.limit}
              onPageChange={(page: number) => {
                onChangePage(page)
              }}
            />
          </div>
        )}
      </div>
    </AdminLayout>
  )
}

export default SalesCard
