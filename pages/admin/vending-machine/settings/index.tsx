import React, { useState } from 'react'
import AdminLayout from 'presentation/components/admin/AdminLayout'
import SettingBuy from 'presentation/components/admin/vending-machine/setting/SettingBuy'
import SettingBuyBack from 'presentation/components/admin/vending-machine/setting/SettingBuyBack'

const Settings = () => {
  return (
    <AdminLayout title="Settings">
      <div>
        <div className="flex flex-col gap-5">
          <SettingBuy />
          <div className="w-full h-[1px] border"></div>
          <SettingBuyBack />
        </div>
      </div>
    </AdminLayout>
  )
}

export default Settings
