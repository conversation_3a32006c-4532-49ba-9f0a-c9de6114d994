import React, { useState, useEffect } from 'react'
import AdminLayout from 'presentation/components/admin/AdminLayout'
import { useRouter } from 'next/router'
import Table from 'presentation/components/table'
import SearchForm from 'presentation/components/admin/vending-machine/pack/SearchForm'
import NoData from 'presentation/components/no-data'
import Pagination from 'presentation/components/pagination'
import Switch from 'presentation/components/switch'
import Button from 'presentation/components/button'
import VendingMachinePackUseCase from 'presentation/use_cases/vending_machine_pack_use_case'
import VendingMachineCategoryUseCase from 'presentation/use_cases/vending_machine_category_use_case'
import { Query } from 'domain/repositories/vending_machine_pack_repository/query'
import { Query as CategoryQuery } from 'domain/repositories/vending_machine_category_repository/query'
import VendingMachinePack from 'domain/models/vending_machine_pack'
import VendingMachineCategory from 'domain/models/vending_machine_category'
import { toast } from 'react-toastify'

const Packs = () => {
  const router = useRouter()
  const packUseCase = new VendingMachinePackUseCase()
  const categoryUseCase = new VendingMachineCategoryUseCase()

  // State management
  const [packs, setPacks] = useState<VendingMachinePack[]>([])
  const [categories, setCategories] = useState<VendingMachineCategory[]>([])
  const [loading, setLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(15)
  const [totalItems, setTotalItems] = useState(0)

  // Search state
  const [searchForm, setSearchForm] = useState({
    name: '',
    category: '',
  })

  // Load packs from API
  const loadPacks = async () => {
    try {
      setLoading(true)
      const query = new Query({
        page: currentPage,
        limit: pageSize,
        keywords: searchForm.name || undefined,
        categoryId: searchForm.category || undefined,
      })

      const result = await packUseCase.list(query)
      console.log('🚀 ~ loadPacks ~ result:', result)
      setPacks(result.items)
      setTotalItems(result.total)
    } catch (error) {
      console.error('Error loading packs:', error)
      toast.error('Failed to load packs')
    } finally {
      setLoading(false)
    }
  }

  // Load categories from API
  const loadCategories = async () => {
    try {
      const query = new CategoryQuery({ limit: 100 }) // Get all categories
      const result = await categoryUseCase.list(query)
      setCategories(result.items)
    } catch (error) {
      console.error('Error loading categories:', error)
    }
  }

  // Load data on component mount and when dependencies change
  useEffect(() => {
    loadPacks()
  }, [currentPage, pageSize])

  useEffect(() => {
    loadCategories()
  }, [])
  
  // Transform packs data for table display with category names
  const transformedPacks = packs.map((pack) => {
    // Find category name by categoryId
    const category = categories.find((cat) => cat.id?.toString() === pack.categoryId)
    const categoryName = category ? category.name : pack.categoryId // Fallback to ID if not found

    return {
      id: pack.id,
      category: categoryName,
      name: pack.name,
      price: pack.price, // Keep as number for render function
      createdAt: pack.getFormattedCreatedAt(),
      enabled: pack.isEnabled,
      action: {
        id: pack.id,
        isEnabled: pack.isEnabled,
        categoryId: pack.categoryId,
      },
    }
  })


  // Handle search form changes
  const handleSearchFormChange = (field: keyof typeof searchForm, value: string) => {
    setSearchForm({
      ...searchForm,
      [field]: value,
    })
  }
  
  // Toggle pack status
  const togglePackStatus = async (id: string, newStatus: boolean) => {
    try {
      await packUseCase.update(id, { isEnabled: newStatus })
      toast.success('Pack status updated successfully')
      loadPacks() // Reload data
    } catch (error) {
      console.error('Error updating pack status:', error)
      toast.error('Failed to update pack status')
    }
  }

  const handleDeletePack = async (id: string) => {
    if (confirm('Are you sure you want to delete this pack?')) {
      try {
        await packUseCase.delete(id)
        toast.success('Pack deleted successfully')
        loadPacks() // Reload data
      } catch (error) {
        console.error('Error deleting pack:', error)
        toast.error('Failed to delete pack')
      }
    }
  }

  const handleCreatePack = () => {
    router.push('/admin/vending-machine/packs/create')
  }
  
  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    // The filtering happens automatically via the filteredPacks variable
    // Reset to first page when searching
    setCurrentPage(1)
    loadPacks()
  }

  // Table header configuration
  const tableHeaders = [
    {
      key: 'category',
      title: 'Category',
    },
    {
      key: 'name',
      title: 'Name',
    },
    {
      key: 'price',
      title: 'Price',
      render: (price: number) => `$${price}`,
    },
    {
      key: 'createdAt',
      title: 'Created At',
    },
    {
      key: 'action',
      title: 'Actions',
      render: (action: any) => (
        <div className="flex items-center justify-center space-x-4">
          <Switch
            defaultActive={action.isEnabled}
            onClick={(active) => togglePackStatus(action.id, active)}
          />
          <button
            onClick={() => router.push(`/admin/vending-machine/packs/edit/${action.id}`)}
            className="text-blue-500 hover:text-blue-700"
            aria-label="Edit pack"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
            </svg>
          </button>
          <button
            onClick={() => handleDeletePack(action.id)}
            className="text-red-500 hover:text-red-700"
            aria-label="Delete pack"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      ),
    },
  ]
  
  return (
    <AdminLayout title="Packs">
      <div>
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">Packs</h2>
          
          <Button 
            variant="dark"
            type="button"
            onClick={handleCreatePack}
          >
            Add
          </Button>
        </div>
        
        {/* Search Form */}
        <SearchForm
          categories={categories}
          formState={searchForm}
          onChangeForm={handleSearchFormChange}
          onSearch={handleSearch}
        />
        
        {/* Packs Table */}
        <div className="w-full overflow-x-auto mt-2">
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="text-gray-500">Loading packs...</div>
            </div>
          ) : transformedPacks.length > 0 ? (
            <>
              <Table
                head={tableHeaders}
                data={transformedPacks}
                stickyHeader={true}
                className="min-w-full border-b"
              />
              <div className="px-4 py-4">
                <Pagination
                  currentPage={currentPage}
                  enableSelectRow={false}
                  totalCount={totalItems}
                  pageSize={pageSize}
                  onPageChange={(page: number) => {
                    setCurrentPage(page)
                  }}
                />
              </div>
            </>
          ) : (
            <div className="w-full rounded-lg border flex justify-center items-center py-24">
              <NoData />
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  )
}

export default Packs 