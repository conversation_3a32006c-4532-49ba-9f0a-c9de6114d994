import React, { useState, useEffect, useMemo } from 'react'
import AdminLayout from 'presentation/components/admin/AdminLayout'
import { useRouter } from 'next/router'
import PackForm, { PackFormData } from 'presentation/components/admin/vending-machine/pack/PackForm'
import VendingMachinePackUseCase from 'presentation/use_cases/vending_machine_pack_use_case'
import VendingMachineCategoryUseCase from 'presentation/use_cases/vending_machine_category_use_case'
import MultimediaUseCase from 'presentation/use_cases/multimedia_use_case'
import VendingMachineCategory from 'domain/models/vending_machine_category'
import { Query as CategoryQuery } from 'domain/repositories/vending_machine_category_repository/query'
import { toast } from 'react-toastify'


const CreatePack = () => {
  const router = useRouter()
  const packUseCase = new VendingMachinePackUseCase()
  const categoryUseCase = new VendingMachineCategoryUseCase()
  const multimediaUseCase = new MultimediaUseCase()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [categories, setCategories] = useState<VendingMachineCategory[]>([])
  const [loadingCategories, setLoadingCategories] = useState(true)
  const [errorMessage, setErrorMessage] = useState('')
  // Initial empty form data - use useMemo to prevent recreation on every render
  const initialPackData: PackFormData = useMemo(() => ({
    name: '',
    price: '',
    description: '',
    categoryId: '',
    image: null,
    odds: [
      {
        id: Date.now(),
        fromPrice: '',
        toPrice: '',
        rate: '',
      },
    ],
  }), [])

  // Load categories on component mount
  useEffect(() => {
    const loadCategories = async () => {
      try {
        setLoadingCategories(true)
        const query = new CategoryQuery({ limit: 100 }) // Get all categories
        const result = await categoryUseCase.list(query)
        setCategories(result.items)
      } catch (error) {
        console.error('Error loading categories:', error)
        toast.error('Failed to load categories')
      } finally {
        setLoadingCategories(false)
      }
    }

    loadCategories()
  }, [])

  const handleImageUpload = async (file: File): Promise<string> => {
    try {
      const uploadResponse = await multimediaUseCase.upload(file)
      toast.success('Image uploaded successfully!')
      return uploadResponse.url // Multimedia model có property url
    } catch (error) {
      console.error('Error uploading image:', error)
      toast.error('Failed to upload image. Please try again.')
      throw error
    }
  }

  const handleSubmit = async (formData: PackFormData) => {
    setIsSubmitting(true)
    setErrorMessage('')
    try {
      // Validate form data (imageUrl should already be set from upload)
      const validation = packUseCase.validatePackData({
        name: formData.name,
        description: formData.description,
        price: parseFloat(formData.price.toString()),
        categoryId: formData.categoryId,
        imageUrl: formData.imageUrl || '',
        odds: formData.odds.map((odd) => ({
          fromPrice: parseFloat(odd.fromPrice),
          toPrice: parseFloat(odd.toPrice),
          rate: parseFloat(odd.rate),
        })),
      })

      if (!validation.isValid) {
        toast.error(validation.errors.join(', '))
        return
      }

      // Call API to create pack
      await packUseCase.create({
        name: formData.name.trim(),
        description: formData.description.trim(),
        price: parseFloat(formData.price.toString()),
        categoryId: formData.categoryId,
        imageUrl: formData.imageUrl || '',
        setting: {
          odds: formData.odds.map((odd) => ({
            fromPrice: parseFloat(odd.fromPrice),
            toPrice: parseFloat(odd.toPrice),
            rate: parseFloat(odd.rate),
          })),
        },
        isEnabled: true,
      
      })

      // Success - redirect back to packs page
      toast.success('Pack created successfully!')
      router.push('/admin/vending-machine/packs')
    } catch (error: any) {
      const errorsResponse = error.getErrorObjects()
      const errorResponse = errorsResponse[0]
      // Handle the error response
      if (errorResponse.code === 'NO_CARD') {
        setErrorMessage(errorResponse.message)
      } else {
        toast.error(errorResponse.message || 'An error occurred while creating the pack')
      }
    } finally {
      setIsSubmitting(false)
    }
  }
  
  return (
    <AdminLayout title="Add New Pack">
      <div>
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">Create New Pack</h2>
        </div>
        
        {loadingCategories ? (
          <div className="flex justify-center items-center py-8">
            <div className="text-gray-500">Loading categories...</div>
          </div>
        ) : (
          <PackForm
            initialData={initialPackData}
            onSubmit={handleSubmit}
            isSubmitting={isSubmitting}
            submitLabel={isSubmitting ? 'Creating Pack...' : 'Create Pack'}
            onImageUpload={handleImageUpload}
            categories={categories.map((cat) => ({ id: cat.id!.toString(), name: cat.name }))}
            errorMessage={errorMessage}
          />
        )}
      </div>
    </AdminLayout>
  )
}

export default CreatePack 