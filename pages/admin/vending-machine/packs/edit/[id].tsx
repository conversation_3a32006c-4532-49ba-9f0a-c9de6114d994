import React, { useState, useEffect } from 'react'
import AdminLayout from 'presentation/components/admin/AdminLayout'
import { useRouter } from 'next/router'
import PackForm, { PackFormData } from 'presentation/components/admin/vending-machine/pack/PackForm'
import VendingMachinePackUseCase from 'presentation/use_cases/vending_machine_pack_use_case'
import VendingMachineCategoryUseCase from 'presentation/use_cases/vending_machine_category_use_case'
import MultimediaUseCase from 'presentation/use_cases/multimedia_use_case'
import VendingMachinePack from 'domain/models/vending_machine_pack'
import VendingMachineCategory from 'domain/models/vending_machine_category'
import { Query as CategoryQuery } from 'domain/repositories/vending_machine_category_repository/query'
import { toast } from 'react-toastify'

const EditPack = () => {
  const router = useRouter()
  const { id } = router.query
  const packUseCase = new VendingMachinePackUseCase()
  const categoryUseCase = new VendingMachineCategoryUseCase()
  const multimediaUseCase = new MultimediaUseCase()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [packData, setPackData] = useState<PackFormData | null>(null)
  const [originalPack, setOriginalPack] = useState<VendingMachinePack | null>(null)
  const [categories, setCategories] = useState<VendingMachineCategory[]>([])
  const [loadingCategories, setLoadingCategories] = useState(true)
  const [errorMessage, setErrorMessage] = useState('')
  // Fetch pack data when the component mounts

  useEffect(() => {
    if (id) {
      const fetchPack = async () => {
        setIsLoading(true)
        try {
          const packId = id as string
          const pack = await packUseCase.getById(packId)

          setOriginalPack(pack)
          setPackData({
            id: parseInt(pack.id!),
            name: pack.name,
            price: parseFloat(pack.price.toString()),
            description: pack.description,
            categoryId: pack.categoryId,
            image: null,
            imageUrl: pack.imageUrl,
            odds: pack.setting.odds.map((odd : any,index : number) => ({
              id: odd.id || Date.now() + `_index${index}`,
              fromPrice: odd.fromPrice.toString(),
              toPrice: odd.toPrice.toString(),
              rate: odd.rate.toString(),
            })),
          })
        } catch (error) {
          console.error('Error fetching pack:', error)
          toast.error('Failed to load pack data')
          router.push('/admin/vending-machine/packs')
        } finally {
          setIsLoading(false)
        }
      }

      fetchPack()
    }
  }, [id])

  // Load categories on component mount
  useEffect(() => {
    const loadCategories = async () => {
      try {
        setLoadingCategories(true)
        const query = new CategoryQuery({ limit: 100 }) // Get all categories
        const result = await categoryUseCase.list(query)
        setCategories(result.items)
      } catch (error) {
        console.error('Error loading categories:', error)
        toast.error('Failed to load categories')
      } finally {
        setLoadingCategories(false)
      }
    }

    loadCategories()
  }, [])

  const handleImageUpload = async (file: File): Promise<string> => {
    try {
      const uploadResponse = await multimediaUseCase.upload(file)
      toast.success('Image uploaded successfully!')
      return uploadResponse.url // Multimedia model có property url
    } catch (error) {
      console.error('Error uploading image:', error)
      toast.error('Failed to upload image. Please try again.')
      throw error
    }
  }

  const handleSubmit = async (formData: PackFormData) => {
    console.log('🚀 ~ handleSubmit ~ originalPack:', originalPack)
    console.log('🚀 ~ handleSubmit ~ formData:', formData)
    if (!originalPack) return

    setIsSubmitting(true)
    setErrorMessage('')
    try {
      // Validate form data
      const price :any =formData.price
      const validation = packUseCase.validatePackData({
        name: formData.name,
        description: formData.description,
        price: parseFloat(price),
        categoryId: formData.categoryId,
        imageUrl: formData.imageUrl || originalPack.imageUrl,
        odds: formData.odds.map((odd) => ({
          fromPrice: parseFloat(odd.fromPrice),
          toPrice: parseFloat(odd.toPrice),
          rate: parseFloat(odd.rate),
        })),
      })

      if (!validation.isValid) {
        toast.error(validation.errors.join(', '))
        return
      }

      // Use imageUrl from formData (uploaded) or fallback to original
      const imageUrl = formData.imageUrl || originalPack.imageUrl
      const odds = formData.odds.map((item) => { 
        return {
          fromPrice: item.fromPrice,
          toPrice: item.toPrice,
          rate: item.rate,
        }
      })
      // Call API to update pack
      await packUseCase.update(originalPack.id!, {
        name: formData.name.trim(),
        description: formData.description.trim(),
        price: parseFloat(price),
        categoryId: formData.categoryId,
        imageUrl: imageUrl,
        setting: {
          odds: odds,
        },
        isEnabled: originalPack.isEnabled,
      })

      // Success - redirect back to packs page
      toast.success('Pack updated successfully!')
      router.push('/admin/vending-machine/packs')
    } catch (error :any) {
      const errorsResponse = error.getErrorObjects()
      const errorResponse = errorsResponse[0]
      // Handle the error response
      if (errorResponse.code === 'NO_CARD') {
        setErrorMessage(errorResponse.message)
      } else {
        toast.error(errorResponse.message || 'An error occurred while updating the pack')
      }
      
    } finally {
      setIsSubmitting(false)
    }
  }
  
  if (isLoading || !packData || loadingCategories) {
    return (
      <AdminLayout title="Edit Pack">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </AdminLayout>
    )
  }
  
  return (
    <AdminLayout title="Edit Pack">
      <div>
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">Edit Pack: {packData.name}</h2>
        </div>
        
        <PackForm
          initialData={packData}
          onSubmit={handleSubmit}
          isSubmitting={isSubmitting}
          submitLabel={isSubmitting ? 'Updating Pack...' : 'Update Pack'}
          onImageUpload={handleImageUpload}
          categories={categories.map((cat) => ({ id: cat.id!.toString(), name: cat.name }))}
          errorMessage={errorMessage}
        />
      </div>
    </AdminLayout>
  )
}

export default EditPack 