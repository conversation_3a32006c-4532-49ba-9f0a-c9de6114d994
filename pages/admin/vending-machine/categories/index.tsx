import React, { useState, useEffect } from 'react'
import AdminLayout from 'presentation/components/admin/AdminLayout'
import { useRouter } from 'next/router'
import Table from 'presentation/components/table'
import NoData from 'presentation/components/no-data'
import Pagination from 'presentation/components/pagination'
import Switch from 'presentation/components/switch'
import <PERSON><PERSON> from 'presentation/components/button'
import VendingMachineCategoryUseCase from 'presentation/use_cases/vending_machine_category_use_case'
import { Query } from 'domain/repositories/vending_machine_category_repository/query'
import VendingMachineCategory from 'domain/models/vending_machine_category'
import { toast } from 'react-toastify'

const Categories = () => {
  const router = useRouter()
  const categoryUseCase = new VendingMachineCategoryUseCase()

  // State management
  const [categories, setCategories] = useState<VendingMachineCategory[]>([])
  const [loading, setLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(15)
  const [totalItems, setTotalItems] = useState(0)
  const [searchTerm, setSearchTerm] = useState('')

  // Load categories from API
  const loadCategories = async () => {
    try {
      setLoading(true)
      const query = new Query({
        page: currentPage,
        limit: pageSize,
        keywords: searchTerm || undefined,
      })

      const result = await categoryUseCase.list(query)
      setCategories(result.items)
      setTotalItems(result.total)
    } catch (error) {
      console.error('Error loading categories:', error)
      toast.error('Failed to load categories')
    } finally {
      setLoading(false)
    }
  }

  // Load categories on component mount and when dependencies change
  useEffect(() => {
    loadCategories()
  }, [currentPage, pageSize, searchTerm])
  
  // Transform categories data for table display
  const transformedCategories = categoryUseCase.mapCategoriesForTable(categories)

  // Toggle category status
  const toggleCategoryStatus = async (id: number, newStatus: boolean) => {
    try {
      await categoryUseCase.update(id, { enable: newStatus })
      toast.success('Category status updated successfully')
      loadCategories() // Reload data
    } catch (error) {
      console.error('Error updating category status:', error)
      toast.error('Failed to update category status')
    }
  }

  const handleCreateCategory = () => {
    router.push('/admin/vending-machine/categories/create')
  }

  const handleDeleteCategory = async (id: number) => {
    if (confirm('Are you sure you want to delete this category?')) {
      try {
        await categoryUseCase.delete(id)
        toast.success('Category deleted successfully')
        loadCategories() // Reload data
      } catch (error) {
        console.error('Error deleting category:', error)
        toast.error('Failed to delete category')
      }
    }
  }
  
  // Table header configuration
  const tableHeaders = [
    {
      key: 'name',
      title: 'Name',
    },
    {
      key: 'createdAt',
      title: 'Created At',
    },
    {
      key: 'action',
      title: 'Actions',
      render: (action: any) => (
        <div className="flex items-center justify-center space-x-4">
          <Switch
            defaultActive={action.enable}
            onClick={(active) => toggleCategoryStatus(action.id, active)}
          />
          <button
            onClick={() => router.push(`/admin/vending-machine/categories/edit/${action.id}`)}
            className="text-blue-500 hover:text-blue-700"
            aria-label="Edit category"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
            </svg>
          </button>
          <button
            onClick={() => handleDeleteCategory(action.id)}
            className="text-red-500 hover:text-red-700"
            aria-label="Delete category"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      ),
    },
  ]
  
  return (
    <AdminLayout title="Categories">
      <div>
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">Categories</h2>
          
          <Button 
            variant="dark"
            type="button"
            onClick={handleCreateCategory}
          >
            Add
          </Button>
        </div>
        
        {/* Categories Table */}
        <div className="w-full overflow-x-auto mt-2">
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="text-gray-500">Loading categories...</div>
            </div>
          ) : transformedCategories.length > 0 ? (
            <>
              <Table
                head={tableHeaders}
                data={transformedCategories}
                stickyHeader={true}
                className="min-w-full border-b"
              />
              <div className="px-4 py-4">
                <Pagination
                  currentPage={currentPage}
                  enableSelectRow={false}
                  totalCount={totalItems}
                  pageSize={pageSize}
                  onPageChange={(page: number) => {
                    setCurrentPage(page)
                  }}
                />
              </div>
            </>
          ) : (
            <div className="w-full rounded-lg border flex justify-center items-center py-24">
              <NoData />
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  )
}

export default Categories 