import React, { useState, useEffect } from 'react'
import AdminLayout from 'presentation/components/admin/AdminLayout'
import { useRouter } from 'next/router'
import CategoryForm, { CategoryFormData } from 'presentation/components/admin/vending-machine/category/CategoryForm'
import Button from 'presentation/components/button'
import VendingMachineCategoryUseCase from 'presentation/use_cases/vending_machine_category_use_case'
import VendingMachineCategory from 'domain/models/vending_machine_category'
import { toast } from 'react-toastify'

const EditCategory = () => {
  const router = useRouter()
  const { id } = router.query
  const categoryUseCase = new VendingMachineCategoryUseCase()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [categoryData, setCategoryData] = useState<CategoryFormData | null>(null)
  const [originalCategory, setOriginalCategory] = useState<VendingMachineCategory | null>(null)
  
  // Fetch category data when the component mounts
  useEffect(() => {
    if (id) {
      const fetchCategory = async () => {
        setIsLoading(true)
        try {
          const categoryId: any = id
          const category = await categoryUseCase.getById(categoryId)

          setOriginalCategory(category)
          setCategoryData({
            name: category.name,
            description: '', // CategoryForm expects description, but our API doesn't have it
          })
        } catch (error) {
          console.error('Error fetching category:', error)
          toast.error('Failed to load category data')
          router.push('/admin/vending-machine/categories')
        } finally {
          setIsLoading(false)
        }
      }

      fetchCategory()
    }
  }, [])
  
  const handleSubmit = async (formData: CategoryFormData) => {
    if (!originalCategory) return

    setIsSubmitting(true)

    try {
      // Validate form data
      const validation = categoryUseCase.validateCategoryData({
        name: formData.name,
        enable: originalCategory.enable,
      })

      if (!validation.isValid) {
        toast.error(validation.errors.join(', '))
        return
      }

      // Call API to update category
      await categoryUseCase.update(originalCategory.id!, {
        name: formData.name.trim(),
        enable: originalCategory.enable, // Keep the current enable status
      })

      // Success - redirect back to categories page
      toast.success('Category updated successfully!')
      router.push('/admin/vending-machine/categories')
    } catch (error) {
      console.error('Error updating category:', error)
      toast.error('Failed to update category. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }
  
  if (isLoading || !categoryData) {
    return (
      <AdminLayout title="Edit Category">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </AdminLayout>
    )
  }
  
  return (
    <AdminLayout title="Edit Category">
      <div>
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">Edit Category: {categoryData.name}</h2>
        </div>
        
        <div className="bg-white border border-gray-200 rounded-md p-6">
          <CategoryForm
            initialData={categoryData}
            onSubmit={handleSubmit}
            isSubmitting={isSubmitting}
            submitLabel="Save"
          />
        </div>
      </div>
    </AdminLayout>
  )
}

export default EditCategory 