import React, { useState } from 'react'
import AdminLayout from 'presentation/components/admin/AdminLayout'
import { useRouter } from 'next/router'
import CategoryForm, { CategoryFormData } from 'presentation/components/admin/vending-machine/category/CategoryForm'
import Button from 'presentation/components/button'
import VendingMachineCategoryUseCase from 'presentation/use_cases/vending_machine_category_use_case'
import { toast } from 'react-toastify'

const CreateCategory = () => {
  const router = useRouter()
  const categoryUseCase = new VendingMachineCategoryUseCase()
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Initial empty form data
  const initialCategoryData: CategoryFormData = {
    name: '',
    description: '',
  }

  const handleSubmit = async (formData: CategoryFormData) => {
    setIsSubmitting(true)

    try {
      // Validate form data
      const validation = categoryUseCase.validateCategoryData({
        name: formData.name,
        enable: true, // Default to enabled
      })

      if (!validation.isValid) {
        toast.error(validation.errors.join(', '))
        return
      }

      // Call API to create category
      await categoryUseCase.create({
        name: formData.name.trim(),
        enable: true, // Default to enabled for new categories
      })

      // Success - redirect back to categories page
      toast.success('Category created successfully!')
      router.push('/admin/vending-machine/categories')
    } catch (error) {
      console.error('Error creating category:', error)
      toast.error('Failed to create category. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }
  
  return (
    <AdminLayout title="Add Category">
      <div>
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">Add Category</h2>
        </div>
        
        <div className="bg-white border border-gray-200 rounded-md p-6">
          <CategoryForm
            initialData={initialCategoryData}
            onSubmit={handleSubmit}
            isSubmitting={isSubmitting}
            submitLabel="Save"
          />
        </div>
      </div>
    </AdminLayout>
  )
}

export default CreateCategory 