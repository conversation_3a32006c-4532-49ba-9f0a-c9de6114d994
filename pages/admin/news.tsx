import clsx from 'clsx'
import { NextPage } from 'next'
import Head from 'next/head'
import { useRouter } from 'next/router'
import Button from 'presentation/components/button'
import Container from 'presentation/components/container'
import Heading from 'presentation/components/heading'
import SvgIcon from 'presentation/components/svg-icon'
import RequireConnect from 'presentation/components/require-connect'
import Image from 'next/image'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'
import MeUseCase from 'presentation/use_cases/me_use_case'
import Loader from 'presentation/components/loader' 
import { useEffect, useState, useCallback } from 'react'
import { toast } from 'react-toastify'
import Input from 'presentation/components/input'

import AdminLayout from 'presentation/components/admin/AdminLayout'
import SortableList, { SortableItem, SortableKnob } from 'react-easy-sort'
import { arrayMoveImmutable } from 'array-move'
import DatePicker from 'presentation/components/datetimePicker' // Using existing datepicker component
import NewsUseCase from 'presentation/use_cases/news_use_case'
const newsUseCase = new NewsUseCase()
// Mock data structure
const initialNews = [
  {
    id: 1,
    date: '',
    description: '',
    url: '',
  },
  {
    id: 2,
    date: '',
    description: '',
    url: '',
  },
  {
    id: 3,
    date: '',
    description: '',
    url: '',
  },
  {
    id: 4,
    date: '',
    description: '',
    url: '',
  },
  {
    id: 5,
    date: '',
    description: '',
    url: '',
  },
]


const meUseCase = new MeUseCase()

const NewsAdminPage: NextPage = () => {
  const [isConnected, setIsConnected] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  // Add these state and handlers near other state declarations:
  const [news, setNews] = useState(initialNews)

  /**
   * HOOKS
   */
  const router = useRouter()
  const authStateProvider = AuthStateProvider((state) => ({
    me: state.me,
  }))
  const handleDateChange = (index: number, date: string) => {
    const updatedNews = [...news]
    console.log('🚀 ~ handleDateChange ~ updatedNews:', updatedNews)
    updatedNews[index].date = date
    setNews(updatedNews)
  }

  const handleDescriptionChange = (index: number, description: string) => {
    const updatedNews = [...news]
    updatedNews[index].description = description
    setNews(updatedNews)
  }
  // Add this state to track validation errors
  const [urlErrors, setUrlErrors] = useState<{[key: number]: string}>({})
  
  // Modify the handleUrlChange function to include validation
  const handleUrlChange = (index: number, url: string) => {
    const updatedNews = [...news]
    updatedNews[index].url = url
    setNews(updatedNews)
    
    // Clear error when user is typing
    if (urlErrors[index]) {
      const newErrors = {...urlErrors}
      delete newErrors[index]
      setUrlErrors(newErrors)
    }
  }
  
  // Add a validation function
  const validateUrl = (index: number) => {
    const url = news[index].url
    if (!url) return // Empty is allowed
    
    try {
      new URL(url)
      // URL is valid, clear any error
      if (urlErrors[index]) {
        const newErrors = {...urlErrors}
        delete newErrors[index]
        setUrlErrors(newErrors)
      }
    } catch (e) {
      // URL is invalid
      setUrlErrors({...urlErrors, [index]: 'Please enter a valid URL (e.g., https://example.com)'})
    }
  }
  

  const onSortEnd = (oldIndex: number, newIndex: number) => {
    setNews((array : any) => arrayMoveImmutable(array, oldIndex, newIndex))
  }
  const handleSubmit = async () => {
    try {
      setIsLoading(true)
      // Convert array of news items to array of NewsModel
      let newsDataArray = news.map((item,index) => ({
        id: item?.id,
        title: item.date,
        thumbnailId: null,
        url: item.url,
        index: index,
        body: item.description,
        publishedAt: new Date(),
      }))
      const params = {
        news: newsDataArray,
      }
    
      await newsUseCase.editNews(params)
      toast.success('News updated successfully')
    } catch (error) {
      toast.error('Failed to update news')
      console.error(error)
    } finally {
      setIsLoading(false)
    }
  }
  const mapNewsData = (data: any) => {
    if (data?.length > 0) {
      const rs: any = news.map((item, index) => {
        const itemData = data[index]
        if (itemData) {
          return {
            id: itemData.id,
            date: itemData.title,
            description: itemData.body,
            url: itemData.url,
          }
        } else { 
          return item
        }
      })
      setNews(rs)
    }
  }
  const fetchNews = async () => {
    try {
      setIsLoading(true)
      const response: any = await newsUseCase.getNews()
      console.log('🚀 ~ fetchNews ~ response:', response)
      if (response) {
        mapNewsData(response)
      }
    } catch (error) {
      toast.error('Failed to fetch news')
      console.error(error)
    } finally {
      setIsLoading(false)
    }
  }
  const fetchMe = async () => {
    await meUseCase
      .loginStatus()
      .then((res) => {
        console.log('🚀 ~ .then ~ res:', res)
        setIsConnected(res.isLoggedIn)
        // if (res.isLoggedIn) {
        //   setOwners([res.user!])
        //   setCollaborators([{ value: res.user! }])
        // }
      })
      .catch(() => {
        setIsConnected(false)
      })
  }

  useEffect(() => {
    fetchMe()
    fetchNews()
  }, [])

  return (
    <>
      <Head>
        <title>News - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>
      <main>
        {isConnected ? (
          <AdminLayout title="News">
            <div className="relative mb-5">
              <div className="absolute top-[-100px] right-0 xs:hidden md:block">
                <Button
                  variant="dark"
                  className="mt-6 flex"
                  disable={isLoading || Object.keys(urlErrors).length > 0}
                  type="button"
                  onClick={() => handleSubmit()}
                > 
                  { isLoading && <Loader className='mr-2' /> }  Submit
                </Button>
              </div>
            </div>
            <div className="">
              <div className="md:hidden xs:flex justify-end w-full">
                <Button
                  variant="dark"
                  className="mt-0 flex"
                  disable={isLoading || Object.keys(urlErrors).length > 0}
                  type="button"
                  onClick={() => handleSubmit()}
                > 
                  { isLoading && <Loader className='mr-2' /> }  Submit
                </Button>
              </div>
              <SortableList onSortEnd={onSortEnd} className="list" draggedItemClassName="dragged">
                {news.map((item,index) => (
                  <SortableItem key={item.id}>
                    <div className="flex gap-4 mt-4">
                      <SortableKnob>
                        <div className="w-24 h-[140px] flex items-center cursor-grab">
                          <SvgIcon viewBox="0 0 158 158" name="sort" className='w-20'></SvgIcon>
                        </div>
                      </SortableKnob>
                      <div className="w-full">
                        <div className="flex w-full gap-4 mb-2">
                          <DatePicker
                            selectedDate={item.date ? item.date : ''}
                            onChangeDate={(date : any) => {
                              console.log('🚀 ~ date:', date)
                              if (date) {
                              // format date to MM/dd/yyyy
                                const formatDate = date.toLocaleDateString('en-US', {
                                  year: 'numeric',
                                  month: '2-digit',
                                  day: '2-digit',
                                })
                                console.log('Selected date:', formatDate)
                                handleDateChange(index, formatDate)
                              } else { 
                                handleDateChange(index, '')
                              }
                            }}
                            className="bg-gray-5 dark:bg-blue-1 border border-gray-3 rounded-lg popup-mode"
                            dateFormat="MM/dd/yyyy"
                            inline={false}
                            monthsShown={1}
                          />
                          <div className="w-full"> 
                            <Input 
                              value={item.url}
                              onChange={(e) => handleUrlChange(index, e.target.value)}
                              onBlur={() => validateUrl(index)}
                              className="w-full !rounded-10 border border-gray-3 bg-white dark:bg-blue-8"
                              placeholder="Enter description url"
                            />
                            {urlErrors[index] && (
                              <div className="text-red-500 text-sm mt-1">
                                {urlErrors[index]}
                              </div>
                            )}
                          </div>
                        </div>
                        <div className='w-full'>
                          <textarea
                            value={item.description}
                            onChange={(e) => handleDescriptionChange(index, e.target.value)}
                            className="w-full min-h-[96px] border border-gray-3 rounded-lg p-2"
                            placeholder="Enter news description"
                          />
                      
                        </div>
                      </div>
                    </div>
                  </SortableItem>
                ))}
              </SortableList>
            </div>
          </AdminLayout>
        ) : (
          <RequireConnect />
        )}
      </main>
    </>
  )
}
export default NewsAdminPage

