import { NextPage } from 'next'
import clsx from 'clsx'
import Head from 'next/head'
import Image from 'next/image'
import { useState, useEffect } from 'react'
import Container from 'presentation/components/container'
import SelectSearch from 'presentation/components/select/select-search'
import ToggleWrap from 'presentation/components/toggle-wrap'
import ToggleVertical from 'presentation/components/toggle-wrap/toggle-vertical'
import Table from 'presentation/components/table'
import SvgIcon from 'presentation/components/svg-icon'
import Button from 'presentation/components/button'
import Input from 'presentation/components/input'
import LoadingData from 'presentation/components/skeleton/loading'
import NoData from 'presentation/components/no-data'
import RequireConnect from 'presentation/components/require-connect'
import { useForm } from 'lib/hook/useForm'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import { adminCategoryHeader } from 'presentation/page_components/admin/_adminTableHeaders'
import { categoriesIcons } from 'lib/valueObjects'
import { toast } from 'react-toastify'
import CategoryUseCase from 'presentation/use_cases/category_use_case'
import MultimediaUseCase from 'presentation/use_cases/multimedia_use_case'
import MeUseCase from 'presentation/use_cases/me_use_case'
import { Query as CategoryQuery } from 'domain/repositories/category_repository/query'

const categoryUseCase = new CategoryUseCase()
const multimediaUseCase = new MultimediaUseCase()
const meUseCase = new MeUseCase()

//Todo remove mock data
import { selectSearchAccount } from 'presentation/controllers/mockData/common_mock_data'
import { adminSidebar } from 'presentation/controllers/mockData/admin_mock_data'
import { pageSizes } from 'lib/hook/customHook'
import CategoryModel from 'domain/models/category'
//End todo remove mock data

const PageSize = pageSizes.pageSize20

const Category: NextPage = () => {
  const initTableParams: any = {
    page: 1,
    limit: PageSize,
  }
  const initFormData = {
    id: '',
    slug: '',
    name: '',
    thumbnail: null,
    description: '',
  }

  const {
    filterParams: filterTableParams,
    //changeFilterParam: changeFilterTableParam,
    //setFilterParams: changeFilterTableParams,
  } = useChangeFilterParams(initTableParams)
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [currentLimit, setCurrentLimit] = useState<number>(PageSize)
  const [total, setTotal] = useState<number>(0)
  const [isFetching, setIsFetching] = useState<boolean>(true)
  const [isConnected, setIsConnected] = useState<boolean>(false)
  const [categories, setCategories] = useState<CategoryModel[]>([])
  //TODO remove any type
  const [tableData, setTableData] = useState<any>([])
  const { formData, setFormValue } = useForm(initFormData)
  //End TODO

  const fetchMe = async () => {
    await meUseCase
      .loginStatus()
      .then((res) => {
        setIsConnected(res.isLoggedIn)
      })
      .catch((_error) => {
        setIsConnected(false)
        toast.error('Failed to fetch user')
      })
  }

  const fetchTableData = async () => {
    setIsFetching(true)
    const query = new CategoryQuery()
    await categoryUseCase
      .search(query)
      .then((res) => {
        setCategories(res)
      })
      .catch((_error) => {
        setCategories([])
        toast.error('Failed to fetch categories')
      })
  }

  const formatCategories = () => {
    const formattedCategories = categories.map((category: any) => {
      return {
        id: category.id,
        name: {
          name: category.name,
          thumbnail: category.thumbnail || {
            url: categoriesIcons[category.slug],
          },
        },
        slug: category.slug,
        description: category.description,
        count: {
          data: 2000,
        }, // TODO
        more: {
          categoryId: category.id,
          onClick: () => {
            alert('TODO')
          }, // TODO
        },
        delete: {
          categoryId: category.id,
          onClick: () => deleteCategory(category.id),
        },
      }
    })
    setTotal(formattedCategories.length)
    setTableData(formattedCategories)
  }

  const deleteCategory = async (id: any) => {
    //TODO
    tableData.splice(
      tableData.findIndex((category: any) => category.id === id),
      1
    )
    toast.success('The category has been deleted.')
    //End TODO
  }

  const uploadFile = async (file: any, key: string) => {
    if (file) {
      await multimediaUseCase
        .upload(file)
        .then((res) => {
          setFormValue(key, res)
        })
        .catch((_error) => {
          toast.error('Failed to upload file')
        })
    }
  }

  const submitForm = async (e: any) => {
    //TODO
    e.preventDefault()
    toast.success('This category has been created/updated.')
    //End TODO
  }

  useEffect(() => {
    fetchMe()
    formatCategories()
  }, [categories])

  useEffect(() => {
    setTimeout(() => {
      setIsFetching(false)
    }, 200)
  }, [tableData])

  useEffect(() => {
    if (categories.length === 0) {
      fetchTableData()
    }
  }, [])

  return (
    <>
      <Head>
        <title>Category Edit - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>
      <main>
        {isConnected ? (
          <Container className="pt-9">
            <div className="w-full flex-col-reverse md:w-1/2 flex md:flex-row items-center md:items-baseline md:justify-between gap-4 md:gap-0">
              <SelectSearch
                options={selectSearchAccount}
                nonSelectText="Account"
                className="md:w-4/6"
                optionListClass="w-max"
                childNode={(option, index) => (
                  <li className="px-6 py-1 font-medium cursor-pointer hover:bg-green-2 transition">
                    {option.name}
                  </li>
                )}
              />
              <p
                className={clsx(
                  'shrink-0 text-xl md:text-3xl xl:text-4xl',
                  'font-medium md:italic md:translate-x-1/2 text-dark-primary dark:text-white'
                )}
              >
                Category Edit
              </p>
            </div>

            <div className="mt-5 w-full grid gap-30p grid-cols-1 md:grid-cols-[250px_minmax(0,_1fr)]">
              <div>
                <ToggleWrap contentClass="rounded-t-10">
                  {adminSidebar.map((item, index) => (
                    <ToggleVertical
                      key={index}
                      toggleTitle={item.name}
                      defaultShow
                      titleLink={item.slug}
                      contentWrapClass={
                        index === adminSidebar.length - 1 ? 'rounded-b-10' : ''
                      }
                    >
                      {item.children && (
                        <p className="font-medium">TEXT LINK</p>
                      )}
                    </ToggleVertical>
                  ))}
                </ToggleWrap>
              </div>

              <div className="w-full">
                <div className="grid grid-cols-1 gap-30p lg:grid-cols-[364px_minmax(0,_1fr)]">
                  <form>
                    <p className="font-bold text-dark-primary dark:text-white">
                      New Category / Edit Category
                    </p>
                    <div className="mt-5">
                      <p className="font-medium text-dark-primary dark:text-white">
                        Name
                      </p>
                      <Input
                        className="mt-10p"
                        width="w-11/12"
                        value={formData.name}
                        onChange={(e: any) =>
                          setFormValue('name', e.target.value)
                        }
                      />
                    </div>
                    <div className="mt-5">
                      <p className="font-medium text-dark-primary dark:text-white">
                        Slug
                      </p>
                      <Input
                        className="mt-10p"
                        width="w-11/12"
                        value={formData.slug}
                        onChange={(e: any) =>
                          setFormValue('slug', e.target.value)
                        }
                      />
                    </div>
                    <div className="w-full sm:w-3/4 lg:w-1/2 pr-8 mt-5">
                      <p className="font-medium text-dark-primary dark:text-white">
                        Image
                      </p>
                      <div className="relative w-fit pt-3 pr-3 mt-3">
                        <div className="absolute top-0 right-0">
                          <input
                            className="w-5 h-5 opacity-0 absolute"
                            type="file"
                            id="img"
                            name="thumbnail"
                            accept="image/*"
                            onChange={(e: any) =>
                              uploadFile(e.target.files[0], 'thumbnail')
                            }
                          />
                          <SvgIcon
                            name="plusCircle"
                            className="w-5 h-5 stroke-none fill-primary-2 cursor-pointer"
                          />
                        </div>
                        <div className="upload-image-circle bg-gray-5 dark:bg-blue-1">
                          {formData.thumbnail && (
                            <Image
                              alt=""
                              fill
                              style={{ objectFit: 'cover' }}
                              className="rounded-full"
                              src={formData.thumbnail.url}
                            />
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="mt-5">
                      <p className="font-medium text-dark-primary dark:text-white">
                        Description
                      </p>
                      <p className="mt-1 text-sm text-gray-1 dark:text-gray-3 text-stroke-thin">
                        {/* <span className="text-primary-2">Markdown syntax</span>{' '}
                        is supported. */}
                        {formData.description.length} of 1000 characters used.
                      </p>
                      <textarea
                        className="px-5 py-3 mt-2 rounded-3xl w-full outline-none bg-gray-5 dark:bg-blue-1"
                        rows={3}
                        maxLength={1000}
                        value={formData.description}
                        onChange={(e: any) =>
                          setFormValue('description', e.target.value)
                        }
                      />
                    </div>
                    <Button
                      variant="dark"
                      className="mt-25p"
                      onClick={submitForm}
                    >
                      Save
                    </Button>
                  </form>

                  <div className="flex flex-wrap justify-end">
                    {isFetching ? (
                      <LoadingData />
                    ) : (
                      <>
                        {tableData.length > 0 ? (
                          <>
                            <p className="shrink-0 text-gray-1 mb-1">
                              Showing{' '}
                              {currentLimit * currentPage - currentLimit + 1}-
                              {currentLimit * currentPage > total
                                ? total
                                : currentLimit * currentPage}{' '}
                              out of {total.toLocaleString()}
                            </p>
                            <div className="rounded-20 border border-gray-3 w-full overflow-x-auto pb-15p">
                              <Table
                                head={adminCategoryHeader}
                                data={tableData}
                                className="table-no-border admin-categories min-w-[500px]"
                              />
                            </div>
                          </>
                        ) : (
                          <div className="w-full pr-5">
                            <div className="w-full rounded-lg border flex justify-center items-center py-24">
                              <NoData />
                            </div>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </Container>
        ) : (
          <RequireConnect />
        )}
      </main>
    </>
  )
}

export default Category
