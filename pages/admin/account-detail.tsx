import { NextPage } from 'next'
import Head from 'next/head'
import Image from 'next/image'
import { useEffect, useRef, useState } from 'react'
import Container from 'presentation/components/container'
import SelectSearch from 'presentation/components/select/select-search'
import ToggleWrap from 'presentation/components/toggle-wrap'
import ToggleVertical from 'presentation/components/toggle-wrap/toggle-vertical'
import Table from 'presentation/components/table'
import Pagination from 'presentation/components/pagination'
import Copy from 'presentation/components/copy'
import Tooltip from 'presentation/components/tooltip'
import CollectionTableItem from 'presentation/components/collection/table-item'
import Switch from 'presentation/components/switch'
import Button from 'presentation/components/button'
import SocialNetworks from 'presentation/page_components/socialNetworks'
import LoadingData from 'presentation/components/skeleton/loading'
import NoData from 'presentation/components/no-data'
import RequireConnect from 'presentation/components/require-connect'
import { useForm } from 'lib/hook/useForm'
import { toast } from 'react-toastify'
import { adminDetailHeader } from 'presentation/page_components/admin/_adminTableHeaders'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import MeUseCase from 'presentation/use_cases/me_use_case'
import { pageSizes } from 'lib/hook/customHook'

const meUseCase = new MeUseCase()

//Todo remove mock data
import { selectSearchAccount } from 'presentation/controllers/mockData/common_mock_data'
import {
  adminAccountRecord,
  adminAccountDetailTempData,
  adminSidebar,
} from 'presentation/controllers/mockData/admin_mock_data'
//End todo remove mock data

const PageSize = pageSizes.pageSize10

const AccountDetail: NextPage = () => {
  const initAccountFormData = {
    id: '',
    kyc: true,
    ownerLock: false,
    blackList: false,
  }

  const initTableParams: any = {
    page: 1,
    limit: PageSize,
  }

  const [isFetchingAccount, setIsFetchingccount] = useState<boolean>(true)
  const [isConnected, setIsConnected] = useState<boolean>(false)
  const [isFetchingTable, setIsFetchingTable] = useState<boolean>(true)
  const [total, setTotal] = useState<number>(0)
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [currentLimit, setCurrentLimit] = useState<number>(PageSize)
  const { filterParams, setFilterParams } = useChangeFilterParams(initTableParams)
  const { formData, setFormValue } = useForm(initAccountFormData)
  //TODO remove any type
  const [tableData, setTableData] = useState<any>([])
  const [account, setAccount] = useState<any>({})
  //End TODO

  const textRef = useRef(null)

  const fetchMe = async () => {
    await meUseCase.loginStatus().then((res) => {
      setIsConnected(res.isLoggedIn)
    }).catch((err) => {
      setIsConnected(false)
    })
  }

  const fetchAccount = () => {
    setIsFetchingccount(true)
    // TODO
    setAccount(adminAccountRecord)
    setTimeout(() => {
      setIsFetchingccount(false)
    }, 500)
    // End TODO
  }

  const fetchTableData = () => {
    setIsFetchingTable(true)
    // TODO
    setTotal(adminAccountDetailTempData.length)
    const data = adminAccountDetailTempData.slice((currentPage - 1) * currentLimit, currentPage * currentLimit)
    setTableData(data)
    setTimeout(() => {
      setIsFetchingTable(false)
    }, 500)
    // End TODO
  }

  const submitForm = async (e: any) => {
    e.preventDefault()
    //TODO
    toast.success('This account has been updated successfully.')
    fetchAccount()
    //End TODO
  }

  useEffect(() => {
    fetchTableData()
  }, [filterParams])

  useEffect(() => {
    fetchMe()
    fetchAccount()
  }, [])

  return (
    <>
      <Head>
        <title>Accounts List - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>
      <main>
        {isConnected ? (
          <Container className="pt-9">
            <div className="w-full flex-col-reverse md:w-1/2 flex md:flex-row items-center md:items-baseline md:justify-between gap-4 md:gap-0">
              <SelectSearch
                options={selectSearchAccount}
                nonSelectText="Account"
                className="md:w-4/6"
                optionListClass="w-max"
                childNode={(option, index) => (
                  <li className="px-6 py-1 font-medium cursor-pointer hover:bg-green-2 transition">{option.name}</li>
                )}
              />
              <p className="shrink-0 text-xl md:text-3xl xl:text-4xl font-medium md:italic md:translate-x-1/2">Accounts List</p>
            </div>

            <div className="mt-5 w-full grid gap-10 grid-cols-1 md:grid-cols-[250px_minmax(0,_1fr)]">
              <div>
                <ToggleWrap contentClass="rounded-t-10">
                  {adminSidebar.map((item, index) => (
                    <ToggleVertical
                      key={index}
                      toggleTitle={item.name}
                      defaultShow
                      titleLink={item.slug}
                      contentWrapClass={index === adminSidebar.length - 1 ? 'rounded-b-10' : ''}
                    >
                      {item.children && (
                        <p className="font-medium">TEXT LINK</p>
                      )}
                    </ToggleVertical>
                  ))}
                </ToggleWrap>
              </div>

              <div className="w-full">
                {isFetchingAccount ? (
                  <LoadingData />
                ) : (
                  <div className="pt-5 pb-7 lg:px-10 px-5 xl:px-100p rounded-10 border border-gray-3">
                    <div className="xl:px-10">
                      <div className="flex items-center flex-wrap gap-4 lg:gap-6 xl:gap-9">
                        <div className="flex items-center">
                          {account.owner?.thumbnail?.url && (
                            <div className="relative w-20 h-20 rounded-full">
                              <Image
                                alt={account.owner?.username || ''} fill
                                className="rounded-full"
                                src={account.owner?.thumbnail?.url}
                              />
                            </div>
                          )}
                          <div className="ml-5">
                            <p className="text-xl text-black dark:text-white font-bold">
                              {account.owner?.username}
                            </p>
                            <p className="text-sm text-black dark:text-white text-stroke-thin mt-1.5">
                              🐋 Whale
                            </p>
                          </div>
                        </div>
                        <div className="shrink-0">
                          <p className="font-medium text-dark-primary dark:text-white">Status</p>
                          <p className="mt-10p">
                            <span className="text-primary-2 text-stroke-thin mr-10p">Active</span>
                            <span className="text-sm text-gray-1 dark:text-gray-3 text-stroke-thin">5 hours ago</span>
                          </p>
                        </div>
                        <div className="shrink-0">
                          <p className="font-medium text-dark-primary dark:text-white">Email Address</p>
                          <p className="mt-10p text-dark-primary dark:text-white text-stroke-thin">
                            {account.owner?.email}
                          </p>
                        </div>
                        <div className="shrink-0">
                          <p className="font-medium text-dark-primary dark:text-white pl-2">Links</p>
                          <div className="flex gap-1">
                            <SocialNetworks {...account.owner} iconSize="icon-22" spacing="mr-0" hasShadow={false} />
                          </div>
                        </div>
                      </div>

                      <div className="mt-31p">
                        <p className="font-medium text-dark-primary dark:text-white">Wallet Address</p>
                        <Copy ref={textRef}>
                          <p
                            ref={textRef}
                            className="text-sm w-fit max-w-[60%] truncate text-dark-primary dark:text-white text-stroke-thin"
                          >
                            {account.owner?.address}
                          </p>
                        </Copy>
                      </div>
                      <div className="pt-10p pb-3 px-2 sm:px-4 mt-30p w-fit flex sm:flex-nowrap flex-wrap items-baseline gap-3 sm:gap-5 rounded-10 bg-blue-2 dark:bg-blue-1">
                        <div>
                          <p className="text-sm text-gray-1 dark:text-gray-3 text-stroke-thin">NFTs</p>
                          <p className="font-bold mt-1 text-dark-primary dark:text-white">3,975</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-1 dark:text-gray-3 text-stroke-thin">Collections</p>
                          <p className="font-bold mt-1 text-dark-primary dark:text-white">222</p>
                        </div>
                        <div className="overflow-hidden">
                          <div className="flex items-baseline">
                            <p className="text-sm text-gray-1 dark:text-gray-3 text-stroke-thin">Most Holding</p>
                            <Tooltip iconSize="w-4 h-4">Some Description</Tooltip>
                          </div>
                          <CollectionTableItem
                            imagesrc="/asset/images/character-bird.png"
                            name="By the Digital Art"
                            nickname="@endilo"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-12 gap-3 md:gap-5 mt-5">
                        <div className="col-span-12 sm:col-span-6 lg:col-span-4 rounded-10 bg-blue-2 dark:bg-blue-1 pt-10p pb-3 px-5">
                          <p className="flex justify-between items-center flex-wrap">
                            <span className="font-bold sm:text-xl md:text-lg xl:text-xl truncate text-dark-primary dark:text-white">$9,003,025.61</span>
                            <span className="text-blue-4 text-sm text-stroke-thin">#97</span>
                          </p>
                          <div className="mt-1 text-sm font-medium flex flex-wrap">
                            <p>
                              <span className="mr-1 text-dark-primary dark:text-white">7D</span>
                              <span className="text-increase mr-15p">+0.15%</span>
                            </p>
                            <p>
                              <span className="mr-1 text-dark-primary dark:text-white">30D</span>
                              <span className="text-decrease">-0.15%</span>
                            </p>
                          </div>
                          <div className="mt-1 flex items-center">
                            <p className="text-sm text-gray-1 dark:text-gray-3 text-stroke-thin">Est Holding Value</p>
                            <Tooltip iconSize="w-4 h-4 -mt-0.5">Est Holding Value</Tooltip>
                          </div>
                        </div>

                        <div className="col-span-12 sm:col-span-6 lg:col-span-4 rounded-10 bg-blue-2 dark:bg-blue-1 pt-10p pb-3 px-5">
                          <p className="flex justify-between items-center flex-wrap">
                            <span className="font-bold sm:text-xl md:text-lg xl:text-xl truncate text-dark-primary dark:text-white">$1,265,335.09</span>
                            <span className="text-blue-4 text-sm text-stroke-thin">#681</span>
                          </p>
                          <div className="mt-1 text-sm font-medium flex flex-wrap">
                            <p>
                              <span className="mr-1 text-dark-primary dark:text-white">7D</span>
                              <span className="text-increase mr-15p">+0.15%</span>
                            </p>
                            <p>
                              <span className="mr-1 text-dark-primary dark:text-white">30D</span>
                              <span className="text-decrease">-0.15%</span>
                            </p>
                          </div>
                          <div className="mt-1 flex items-center">
                            <p className="text-sm text-gray-1 dark:text-gray-3 text-stroke-thin">Holding Value</p>
                            <Tooltip iconSize="w-4 h-4 -mt-0.5">Holding Value</Tooltip>
                          </div>
                        </div>

                        <div className="col-span-12 sm:col-span-6 lg:col-span-4 rounded-10 bg-blue-2 dark:bg-blue-1 pt-10p pb-3 px-5">
                          <p className="flex justify-between items-center flex-wrap">
                            <span className="font-bold sm:text-xl md:text-lg xl:text-xl truncate text-dark-primary dark:text-white">$10,712,110.11</span>
                            <span className="text-blue-4 text-sm text-stroke-thin">#96</span>
                          </p>
                          <div className="mt-1 text-sm font-medium flex flex-wrap">
                            <p>
                              <span className="mr-1 text-dark-primary dark:text-white">7D</span>
                              <span className="text-increase mr-15p">+0.15%</span>
                            </p>
                            <p>
                              <span className="mr-1 text-dark-primary dark:text-white">30D</span>
                              <span>—</span>
                            </p>
                          </div>
                          <div className="mt-1 flex items-center">
                            <p className="text-sm text-gray-1 dark:text-gray-3 text-stroke-thin">PnL</p>
                            <Tooltip iconSize="w-4 h-4 -mt-0.5">PnL</Tooltip>
                          </div>
                        </div>

                        <div className="col-span-12 md:col-span-6 lg:col-span-4 xl:col-span-3 rounded-10 bg-blue-2 dark:bg-blue-1 pt-10p pb-2 px-5">
                          <p className="font-bold text-lg text-dark-primary dark:text-white">$1,716,160.60</p>
                          <p className="mt-1 text-sm font-medium">
                            <span className="mr-1 text-dark-primary dark:text-white">7D</span>
                            <span className='text-dark-primary dark:text-white'>$65.73K</span>
                          </p>
                          <p className="mt-1 text-sm font-medium">
                            <span className="mr-1 text-dark-primary dark:text-white">7D</span>
                            <span className='text-dark-primary dark:text-white'>$65.73K</span>
                          </p>
                          <div className="mt-1 flex items-center">
                            <p className="text-sm text-gray-1 dark:text-gray-3 text-stroke-thin">Buy Volume</p>
                            <Tooltip iconSize="w-4 h-4 -mt-0.5">Buy Volume</Tooltip>
                          </div>
                        </div>

                        <div className="col-span-12 md:col-span-6 lg:col-span-4 xl:col-span-3 rounded-10 bg-blue-2 dark:bg-blue-1 pt-10p pb-2 px-5">
                          <p className="font-bold text-lg text-dark-primary dark:text-white">$3,368,665.50</p>
                          <p className="mt-1 text-sm font-medium">
                            <span className="mr-1 text-dark-primary dark:text-white">7D</span>
                            <span>$65.73K</span>
                          </p>
                          <p className="mt-1 text-sm font-medium">
                            <span className="mr-1 text-dark-primary dark:text-white">7D</span>
                            <span>$65.73K</span>
                          </p>
                          <div className="mt-1 flex items-center">
                            <p className="text-sm text-gray-1 dark:text-gray-3 text-stroke-thin">Sell Volume</p>
                            <Tooltip iconSize="w-4 h-4 -mt-0.5">Sell Volume</Tooltip>
                          </div>
                        </div>

                        <div className="col-span-12 md:col-span-6 lg:col-span-4 xl:col-span-3 rounded-10 bg-blue-2 dark:bg-blue-1 pt-10p pb-2 md:px-3 px-5">
                          <p className="font-bold text-lg text-primary-1">6</p>
                          <p className="mt-1 text-sm font-medium">
                            <span className="mr-1">🐋</span>
                            <span className='text-dark-primary dark:text-white'>Whales 0</span>
                          </p>
                          <p className="mt-1 text-sm font-medium">
                            <span className="mr-1">📜</span>
                            <span className='text-dark-primary dark:text-white'>Contracts 4</span>
                          </p>
                          <div className="mt-1 flex items-center">
                            <p className="text-sm text-gray-1 dark:text-gray-3 text-stroke-thin">Related Addresses</p>
                            <Tooltip iconSize="w-4 h-4 -mt-0.5">Related Addresses</Tooltip>
                          </div>
                        </div>

                        <div className="col-span-12 md:col-span-6 lg:col-span-4 xl:col-span-3 rounded-10 bg-blue-2 dark:bg-blue-1 pt-10p pb-2 px-5">
                          <p className="font-bold text-lg text-primary-1">5,398</p>
                          <p className="mt-1 text-sm font-medium">
                            <span className="mr-1 text-dark-primary dark:text-white">7D</span>
                            <span className='text-dark-primary dark:text-white'>77</span>
                          </p>
                          <p className="mt-1 text-sm font-medium">
                            <span className="mr-1 text-dark-primary dark:text-white">7D</span>
                            <span className='text-dark-primary dark:text-white'>403</span>
                          </p>
                          <div className="mt-1 flex items-center">
                            <p className="text-sm text-gray-1 dark:text-gray-3 text-stroke-thin">Activities</p>
                            <Tooltip iconSize="w-4 h-4 -mt-0.5">Activities</Tooltip>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="mt-30p">
                      <div className="flex items-center gap-10 mt-5">
                        <div className="w-1/2 md:w-1/4 flex justify-between items-center">
                          <p className="font-medium min-w-[100px] text-dark-primary dark:text-white">KYC</p>
                          <Switch
                            defaultActive={formData.kyc}
                            onClick={(activeState) => setFormValue('kyc', activeState)}
                          />
                        </div>
                        <p className="text-gray-1 dark:text-gray-3 text-stroke-thin">
                          When someone purchased one of your items
                        </p>
                      </div>
                      <div className="flex items-center gap-10 mt-5">
                        <div className="w-1/2 md:w-1/4 flex justify-between items-center">
                          <p className="font-medium min-w-[100px] text-dark-primary dark:text-white">Owner Lock</p>
                          <Switch
                            defaultActive={formData.ownerLock}
                            onClick={(activeState) => setFormValue('ownerLock', activeState)}
                          />
                        </div>
                        <p className="text-gray-1 dark:text-gray-3 text-stroke-thin">
                          When someone bids on one of your items
                        </p>
                      </div>
                      <div className="flex items-center gap-10 mt-5">
                        <div className="w-1/2 md:w-1/4 flex justify-between items-center">
                          <p className="font-medium min-w-[100px] text-dark-primary dark:text-white">Blacklist</p>
                          <Switch
                            defaultActive={formData.blackList}
                            onClick={(activeState) => setFormValue('blackList', activeState)}
                          />
                        </div>
                        <p className="text-gray-1 dark:text-gray-3 text-stroke-thin">
                          When an item you made an offer on changes in price
                        </p>
                      </div>
                    </div>
                    <div className="mt-8 text-center">
                      <Button variant="dark" onClick={submitForm}>Save</Button>
                    </div>
                  </div>
                )}

                {isFetchingTable ? (
                  <LoadingData />
                ) : (
                  <>
                    {tableData.length > 0 ? (
                      <>
                        <p className="mt-12 text-center text-lg font-medium">Version</p>
                        <div className="table-wrap mt-5 lg:px-11">
                          <div className="w-full overflow-x-auto pb-3">
                            <Table
                              head={adminDetailHeader}
                              data={tableData}
                              className="admin-account-detail min-w-[320px]"
                            />
                          </div>

                          <div className="mt-2 mb-22p">
                            <Pagination
                              currentPage={currentPage}
                              totalCount={total}
                              pageSize={currentLimit}
                              onPageChange={(page: number) => {
                                setCurrentPage(page)
                                setFilterParams({ ...filterParams, page })
                              }}
                              onChangePageSize={(limit: number) => {
                                setCurrentPage(1)
                                setCurrentLimit(limit)
                                setFilterParams({ ...filterParams, limit, page: 1 })
                              }}
                            />
                          </div>
                        </div>
                      </>
                    ) : (
                      <div className="w-full pr-5">
                        <div className="w-full rounded-lg border flex justify-center items-center py-24">
                          <NoData />
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </Container>
        ) : (
          <RequireConnect />
        )}
      </main>
    </>
  )
}

export default AccountDetail
