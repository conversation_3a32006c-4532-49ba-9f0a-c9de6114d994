import { NextPage } from 'next'
import clsx from 'clsx'
import Head from 'next/head'
import { useState, useEffect } from 'react'
import Container from 'presentation/components/container'
import SelectSearch from 'presentation/components/select/select-search'
import ToggleWrap from 'presentation/components/toggle-wrap'
import ToggleVertical from 'presentation/components/toggle-wrap/toggle-vertical'
import Table from 'presentation/components/table'
import Select from 'presentation/components/select'
import Pagination from 'presentation/components/pagination'
import LoadingData from 'presentation/components/skeleton/loading'
import RequireConnect from 'presentation/components/require-connect'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import { adminAccountHeader } from 'presentation/page_components/admin/_adminTableHeaders'
import CategoryUseCase from 'presentation/use_cases/category_use_case'
import MeUseCase from 'presentation/use_cases/me_use_case'
import { pageSizes } from 'lib/hook/customHook'
import { Query } from 'domain/repositories/category_repository/query'

const categoryUseCase = new CategoryUseCase()
const meUseCase = new MeUseCase()

//Todo remove mock data
import { selectSearchAccount } from 'presentation/controllers/mockData/common_mock_data'
import {
  hourOptions,
  rowOptions,
} from 'presentation/controllers/mockData/options'
import {
  adminAccountTempData,
  adminSidebar,
} from 'presentation/controllers/mockData/admin_mock_data'
import Category from 'domain/models/category'
import { toast } from 'react-toastify'
//End todo remove mock data

const PageSize = pageSizes.pageSize20

const AccountList: NextPage = () => {
  const initParams: any = {
    categoryId: '',
    page: 1,
    limit: PageSize,
  }

  const { filterParams, changeFilterParam, setFilterParams } =
    useChangeFilterParams(initParams)
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [currentLimit, setCurrentLimit] = useState<number>(PageSize)
  const [total, setTotal] = useState<number>(0)
  const [isFetching, setIsFetching] = useState<boolean>(true)
  const [isConnected, setIsConnected] = useState<boolean>(false)
  const [categories, setCategories] = useState<Category[]>([])
  //TODO remove any type
  const [tableData, setTableData] = useState<any>([])
  const [filterCategories, setFilterCategories] = useState<any>([])
  //End TODO

  const fetchMe = async () => {
    await meUseCase
      .loginStatus()
      .then((res) => {
        setIsConnected(res.isLoggedIn)
      })
      .catch((_error) => {
        setIsConnected(false)
        toast.error('Failed to fetch user')
      })
  }

  const fetchCategories = async () => {
    const query = new Query()
    await categoryUseCase
      .search(query)
      .then((res) => {
        setCategories(res)
      })
      .catch((_err) => {
        toast.error('Failed to fetch categories')
      })
  }

  const formatCategories = () => {
    const formattedCategories = categories.map((category: any) => {
      return {
        id: category.id,
        name: category.name,
        value: category.slug,
      }
    })
    setFilterCategories(
      [
        {
          id: '',
          name: 'All',
          value: '',
        },
      ].concat(formattedCategories)
    )
  }

  const fetchTableData = () => {
    setIsFetching(true)
    //TODO
    setTotal(adminAccountTempData.length)
    let data = adminAccountTempData.slice(
      (currentPage - 1) * currentLimit,
      currentPage * currentLimit
    )
    setTableData(data)
    setTimeout(() => {
      setIsFetching(false)
    }, 500)
  }

  useEffect(() => {
    if (categories.length > 0) {
      formatCategories()
    }
  }, [categories])

  useEffect(() => {
    fetchTableData()
  }, [filterParams])

  useEffect(() => {
    fetchMe()
    fetchCategories()
  }, [])

  return (
    <>
      <Head>
        <title>Accounts List - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>
      <main>
        {isConnected ? (
          <Container className="pt-9">
            <div className="w-full flex-col-reverse md:w-1/2 flex md:flex-row items-center md:items-baseline md:justify-between gap-4 md:gap-0">
              <SelectSearch
                options={selectSearchAccount}
                nonSelectText="Account"
                className="md:w-4/6"
                optionListClass="w-max"
                childNode={(option, index) => (
                  <li className="px-6 py-1 font-medium cursor-pointer hover:bg-green-2 transition">
                    {option.name}
                  </li>
                )}
              />
              <p
                className={clsx(
                  'shrink-0 text-xl md:text-3xl xl:text-4xl',
                  'font-medium md:italic md:translate-x-1/2 text-dark-primary dark:text-white'
                )}
              >
                Accounts List
              </p>
            </div>

            <div className="mt-5 w-full grid gap-10 grid-cols-1 md:grid-cols-[250px_minmax(0,_1fr)]">
              <div>
                <ToggleWrap contentClass="rounded-t-10">
                  {adminSidebar.map((item, index) => (
                    <ToggleVertical
                      key={index}
                      toggleTitle={item.name}
                      defaultShow
                      titleLink={item.slug}
                      contentWrapClass={
                        index === adminSidebar.length - 1 ? 'rounded-b-10' : ''
                      }
                    >
                      {item.children && (
                        <p className="font-medium">TEXT LINK</p>
                      )}
                    </ToggleVertical>
                  ))}
                </ToggleWrap>
              </div>

              <div className="w-full">
                <div className="flex flex-wrap gap-4 items-center justify-between">
                  <p className="text-gray-1 dark:text-gray-3 text-stroke-thin shrink-0">
                    Showing {currentLimit * currentPage - currentLimit + 1}-
                    {currentLimit * currentPage > total
                      ? total
                      : currentLimit * currentPage}{' '}
                    out of {total.toLocaleString()}
                  </p>
                  <div className="flex sm:flex-nowrap flex-wrap items-center gap-5">
                    {filterCategories.length > 0 && (
                      <Select
                        options={filterCategories}
                        onSelect={(option) =>
                          changeFilterParam('categoryId', option.value)
                        }
                        className="shrink-0"
                      />
                    )}
                    {/* TODO */}
                    <Select
                      options={hourOptions}
                      onSelect={(option) =>
                        changeFilterParam('sortBy', option.value)
                      }
                    />
                    {/* End TODO */}
                    <Select
                      defaultSelected={rowOptions[0]}
                      options={rowOptions}
                      onSelect={(option) => {
                        setCurrentPage(1)
                        setCurrentLimit(option.value)
                        setFilterParams({
                          ...filterParams,
                          limit: option.value,
                          page: 1,
                        })
                      }}
                    />
                  </div>
                </div>

                <div className="table-wrap mt-5 border rounded-20">
                  {isFetching ? (
                    <LoadingData />
                  ) : (
                    <>
                      <div className="w-full overflow-x-auto pb-3">
                        <Table
                          head={adminAccountHeader}
                          data={tableData}
                          className="common-table admin-accounts min-w-[900px] text-dark-primary dark:text-white"
                        />
                      </div>
                      <div className="mt-2 px-5 mb-22p">
                        <Pagination
                          currentPage={currentPage}
                          totalCount={total}
                          pageSize={currentLimit}
                          showCountText={false}
                          onPageChange={(page: number) => {
                            setCurrentPage(page)
                            setFilterParams({ ...filterParams, page })
                          }}
                          onChangePageSize={(limit: number) => {
                            setCurrentPage(1)
                            setCurrentLimit(limit)
                            setFilterParams({
                              ...filterParams,
                              limit,
                              page: 1,
                            })
                          }}
                        />
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          </Container>
        ) : (
          <RequireConnect />
        )}
      </main>
    </>
  )
}

export default AccountList
