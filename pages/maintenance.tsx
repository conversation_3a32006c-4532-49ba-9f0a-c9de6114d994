import React from 'react'
import Head from 'next/head'
import Image from 'next/image'

const MaintenancePage: React.FC = () => {
  return (
    <>
      <Head>
        <title>Site Under Maintenance - NFT Land</title>
        <meta name="description" content="NFT Land is currently under maintenance. We'll be back soon!" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col items-center justify-center px-4">
        {/* Header Logo */}
        <div className="mb-8">
          <Image 
            width={200} 
            height={86} 
            src="/asset/images/quill.png" 
            alt="NFT Land Logo" 
            className="h-16 w-auto object-contain"
          />
        </div>

        {/* Big Impressive Title */}
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-black text-transparent bg-clip-text bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 mb-4 tracking-tight">
            UNDER MAINTENANCE
          </h1>
        </div>

        {/* Maintenance Illustration - Bigger and More Impressive */}
        <div className="mb-8">
          <div className="w-64 h-64 mx-auto bg-gradient-to-br from-purple-100 via-blue-100 to-indigo-100 rounded-full flex items-center justify-center shadow-2xl border-4 border-white">
            <div className="text-8xl animate-bounce filter drop-shadow-lg">🔧</div>
          </div>
        </div>

        {/* Simple Message */}
        <div className="text-center">
          <p className="text-gray-600 text-xl mb-8 font-medium">
            We&apos;re working to improve your experience. We&apos;ll be back soon!
          </p>
          
        </div>
      </div>
    </>
  )
}

export default MaintenancePage