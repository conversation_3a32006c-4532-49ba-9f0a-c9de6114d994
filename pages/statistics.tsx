import { NextPage } from 'next'
import { useState, useEffect } from 'react'
import Head from 'next/head'
import Heading from 'presentation/components/heading'
import Container from 'presentation/components/container'
import StatisticsStats from 'presentation/page_components/statistics/statisticsStats'
import TrendChart from 'presentation/page_components/statistics/trendChart'
import TradersChart from 'presentation/page_components/statistics/tradersChart'
import CatLiquidityChart from 'presentation/page_components/statistics/catLiquidityChart'
import CatByValuePie from 'presentation/page_components/statistics/catByValuePie'
import Welcome from 'presentation/page_components/analytics/welcome'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'

//Todo remove mock data
import {
  statisticsTrends,
  statisticsTraders,
  statisticsCategoryLiquidity,
  statisticsCategoryShare,
  filterDays,
  filterTrends,
  filterCollectionCategory,
  filterValueVolume,
} from 'presentation/controllers/mockData/statistics_mock_data'
//End todo remove mock data

const Statistics: NextPage = () => {
  const initTrendsParams: any = {
    periodRange: '',
    type: '',
  }
  const {
    filterParams: filterTrendsParams,
    changeFilterParam: changeTrendsFilterParam,
  } = useChangeFilterParams(initTrendsParams)
  const [isFetchingTrends, setIsFetchingTrends] = useState<boolean>(true)
  const [trendsData, setTrendsData] = useState<any>({}) //TODO remove any

  const fetchTrends = async () => {
    setIsFetchingTrends(true)
    //TODO
    setTrendsData(statisticsTrends)
    //END TODO
  }

  const initTradersParams: any = {
    periodRange: '',
    type: '',
  }
  const {
    filterParams: filterTradersParams,
    changeFilterParam: changeTradersFilterParam,
  } = useChangeFilterParams(initTradersParams)
  const [tradersData, setTradersData] = useState<any>({})
  const [isFetchingTraders, setIsFetchingTraders] = useState(true)

  const fetchTraders = async () => {
    setIsFetchingTrends(true)
    //TODO
    setTradersData(statisticsTraders)
    //END TODO
  }

  const initCategoryLiquidityParams: any = {
    periodRange: '',
    type: '',
  }
  const {
    filterParams: filterCategoryLiquidityParams,
    changeFilterParam: changeCategoryLiquidityFilterParam,
  } = useChangeFilterParams(initCategoryLiquidityParams)
  const [categoryLiquidityData, setCategoryLiquidityData] = useState<any>({})
  const [isFetchingCategoryLiquidity, setIsFetchingCategoryLiquidity] =
    useState(true)

  const fetchCategoryLiquidity = async () => {
    setIsFetchingCategoryLiquidity(true)
    //TODO
    setCategoryLiquidityData(statisticsCategoryLiquidity)
    //END TODO
  }

  const initCategoryShareParams: any = {
    type: '',
  }
  const {
    filterParams: filterCategoryShareParams,
    changeFilterParam: changeCategoryShareFilterParam,
  } = useChangeFilterParams(initCategoryShareParams)
  const [categoryShareData, setCategoryShareData] = useState<any>({})
  const [isFetchingCategoryShare, setIsFetchingCategoryShare] = useState(true)

  const fetchCategoryShare = async () => {
    setIsFetchingCategoryShare(true)
    //TODO
    setCategoryShareData(statisticsCategoryShare)
    //END TODO
  }

  useEffect(() => {
    setIsFetchingTrends(false)
  }, [trendsData])

  useEffect(() => {
    setIsFetchingTraders(false)
  }, [tradersData])

  useEffect(() => {
    setIsFetchingCategoryLiquidity(false)
  }, [categoryLiquidityData])

  useEffect(() => {
    setIsFetchingCategoryShare(false)
  }, [categoryShareData])

  useEffect(() => {
    fetchTrends()
  }, [filterTrendsParams])

  useEffect(() => {
    fetchTraders()
  }, [filterTradersParams])

  useEffect(() => {
    fetchCategoryLiquidity()
  }, [filterCategoryLiquidityParams])

  useEffect(() => {
    fetchCategoryShare()
  }, [filterCategoryShareParams])

  return (
    <>
      <Head>
        <title>Statistics - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>
      <main>
        <div className="pt-8 pb-8 bg-gradient-to-b from-gradient-sky to-gradient-ocean">
          <div className="container-lg text-center">
            <Heading className="md:mt-0 mt-5" textColor="text-white">
              The Latest Data of Global NFT Market
            </Heading>
            <p className="md:text-lg xl:text-24 mt-10p text-white md:px-0 px-2">
              Know more about the rule of data acquisition.
            </p>
          </div>
        </div>

        <Container className="container-lg">
          <StatisticsStats />
          {!isFetchingTrends && (
            <TrendChart
              filterTypes={filterTrends}
              filterTimeOptions={filterDays}
              chartData={trendsData}
              onChangeFilterParam={changeTrendsFilterParam}
            />
          )}
          {!isFetchingTraders && (
            <TradersChart
              filterTimeOptions={filterDays}
              chartData={tradersData}
              onChangeFilterParam={changeTradersFilterParam}
            />
          )}
          {!isFetchingCategoryLiquidity && (
            <CatLiquidityChart
              filterTimeOptions={filterDays}
              chartData={categoryLiquidityData}
              onChangeFilterParam={changeCategoryLiquidityFilterParam}
            />
          )}
          {!isFetchingCategoryShare && (
            <CatByValuePie
              filterCollectionCategory={filterCollectionCategory}
              filterValueVolume={filterValueVolume}
              chartData={categoryShareData}
              onChangeFilterParam={changeCategoryShareFilterParam}
            />
          )}
        </Container>
        <Welcome />
      </main>
    </>
  )
}

export default Statistics
