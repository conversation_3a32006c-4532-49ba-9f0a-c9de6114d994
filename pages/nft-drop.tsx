import { NextPage } from 'next'
import Head from 'next/head'
import { useEffect, useState } from 'react'
import Container from 'presentation/components/container'
import Heading from 'presentation/components/heading'
import NftDropCard from 'presentation/components/nft-drop'
import PaginationDot from 'presentation/components/pagination/pagination-dot'
import ShowMore from 'presentation/components/show-more'
import Button from 'presentation/components/button'
import LoadingData from 'presentation/components/skeleton/loading'
import NoData from 'presentation/components/no-data'
import RequireConnect from 'presentation/components/require-connect'
import 'react-datepicker/dist/react-datepicker.css'
import { FilterParams } from 'types/type'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import DatetimeRange from 'presentation/components/datetimeRange'
import Input from 'presentation/components/input'
import MeUseCase from 'presentation/use_cases/me_use_case'

const meUseCase = new MeUseCase()
const PageSize = pageSizes.pageSize25

//Todo remove mock data
import { nftDropTempData } from 'presentation/controllers/mockData/nft_drop_mock_data'
import { pageSizes } from 'lib/hook/customHook'
import { toast } from 'react-toastify'
//End todo remove mock data

const NFTDrops: NextPage = () => {
  const initParams: FilterParams = {
    page: 1,
    limit: PageSize,
  }
  const [startDate, setStartDate] = useState<Date>(new Date())
  const [endDate, setEndDate] = useState<Date>(new Date())
  const [isConnected, setIsConnected] = useState<boolean>(false)
  const [isFetching, setIsFetching] = useState<boolean>(true)
  const [showDateRange, setShowDateRange] = useState<boolean>(false)
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [total, setTotal] = useState<number>(0)
  const { filterParams, changeFilterParam } = useChangeFilterParams(initParams)
  const [nftDropData, setNFTDropData] = useState<any>([]) //TODO remove any

  const fetchMe = async () => {
    await meUseCase
      .loginStatus()
      .then((res) => {
        setIsConnected(res.isLoggedIn)
      })
      .catch((_error) => {
        setIsConnected(false)
        toast.error('Error while fetching user data')
      })
  }

  const fetchNFTDropData = async () => {
    setIsFetching(true)
    //TODO
    const data = nftDropTempData.slice(
      (currentPage - 1) * PageSize,
      currentPage * PageSize
    )
    setNFTDropData(data)
    setTotal(nftDropTempData.length)
    //END TODO
  }

  useEffect(() => {
    fetchNFTDropData()
  }, [filterParams])

  useEffect(() => {
    setIsFetching(false)
  }, [nftDropData])

  useEffect(() => {
    fetchMe()
  }, [])

  return (
    <>
      <Head>
        <title>NFT Drops - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>
      <main>
        {isConnected ? (
          <Container className="pt-9">
            <div className="mb-30p text-center">
              <Heading className="text-dark-primary dark:text-white">
                NFT Drops
              </Heading>
              <div className="mt-4 flex items-center justify-center">
                <ShowMore
                  height="1rem"
                  showMoreClass="flex items-center justify-center mt-2"
                  wrapClass="md:w-3/4"
                >
                  <p className="text-gray-1 dark:text-gray-3">
                    Check our Free NFT Drops and don&#39;t miss any upcoming NFT
                    Drop, News, Giveaway or Event.
                  </p>
                </ShowMore>
              </div>
            </div>

            <div className="nft-drop-wrap">
              <div className="calendar-left md:w-auto w-full z-50">
                <div className="rounded-10">
                  <p className="px-5 pt-2 pb-[11px] text-xl font-medium rounded-t-10 text-white bg-gradient-to-b from-gradient-sky to-gradient-ocean">
                    Filter
                  </p>
                </div>
                <div className="border border-gray-3 rounded-b-10 p-4 w-full">
                  <Input
                    className="w-full cursor-pointer font-medium"
                    type="text"
                    value="Day"
                    onClick={() => setShowDateRange(!showDateRange)}
                    prefixIcon="calendar"
                    prefixIconClassName="absolute left-7 top-3 w-6 h-6 stroke-none fill-primary cursor-pointer"
                    suffixIcon="chevronDownSelect"
                    suffixIconViewBox="0 0 12.962 7.411"
                    suffixIconClassName="absolute right-7 top-5 w-[13px] h-[8px] fill-primary-2 dark:fill-white stroke-none cursor-pointer"
                    readOnly
                  />
                  {showDateRange && (
                    <div>
                      <DatetimeRange
                        inline
                        onCloseDateRange={() => setShowDateRange(false)}
                        showDuration={false}
                        showCloseButton={true}
                        onChangeStartDatetime={(value: any) =>
                          setStartDate(value)
                        }
                        onChangeEndDatetime={(value: any) => setEndDate(value)}
                        className="custom max-w-[245px] min-w-[245px]"
                        onlyOneDatepicker
                      />
                    </div>
                  )}
                </div>
              </div>

              <div className="nft-right">
                <Button
                  href="/nft-drop/submit"
                  variant="dark"
                  className="nft-submit-btn"
                  onClick={(e) => {
                    e.preventDefault()
                  }}
                >
                  Submit
                </Button>
                {isFetching ? (
                  <LoadingData />
                ) : (
                  <>
                    {nftDropData.length > 0 ? (
                      <>
                        <div className="grid gap-5 grid-cols-2 sm:grid-cols-3 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
                          {nftDropData.map((record: any, i: number) => (
                            <NftDropCard key={i} record={record} />
                          ))}
                        </div>
                        <div className="flex justify-center mt-7">
                          <PaginationDot
                            currentPage={currentPage}
                            totalCount={total}
                            pageSize={PageSize}
                            onPageChange={(page: number) => {
                              setCurrentPage(page)
                              changeFilterParam('page', page)
                            }}
                          />
                        </div>
                      </>
                    ) : (
                      <div className="w-full rounded-lg border flex justify-center items-center py-24">
                        <NoData />
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </Container>
        ) : (
          <RequireConnect />
        )}
      </main>
    </>
  )
}

export default NFTDrops
