import clsx from 'clsx'
import Collection from 'domain/models/collection'
import User from 'domain/models/user'
import Chain from 'domain/value_objects/chain'
import Schema from 'domain/value_objects/schemas'
import PaymentToken from 'domain/models/payment_tokens'
import { useAuthentication } from 'lib/hook/auth_hook'
import { useForm } from 'lib/hook/useForm'
import { shortenAddress } from 'lib/utils'
import { DateTime } from 'luxon'
import { NextPage } from 'next'
import Head from 'next/head'
import Image from 'next/image'
import Link from 'next/link'
import { useRouter } from 'next/router'
import Button from 'presentation/components/button'
import Container from 'presentation/components/container'
import Heading from 'presentation/components/heading'
import Input from 'presentation/components/input'
import InputCollectionURL from 'presentation/components/input/input-collection-url'
import Loader from 'presentation/components/loader'
import AddCollaborator from 'presentation/components/modal/add-collaborator'
import RequireConnect from 'presentation/components/require-connect'
import Select from 'presentation/components/select'
import SelectCategories from 'presentation/components/select/select-categories'
import SelectTag from 'presentation/components/select/select-tag'
import SvgIcon from 'presentation/components/svg-icon'
import Switch from 'presentation/components/switch'
import Tooltip from 'presentation/components/tooltip'
import { envConfig } from 'presentation/constant/env'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'
import CollectionUseCase from 'presentation/use_cases/collection_use_case'
import MeUseCase from 'presentation/use_cases/me_use_case'
import MultimediaUseCase from 'presentation/use_cases/multimedia_use_case'
import PaymentTokensUseCase from 'presentation/use_cases/payment_tokens_use_case'
import { useEffect, useState, useCallback } from 'react'
import { toast } from 'react-toastify'
import { useAccount } from 'wagmi'
import { isURL, isUrlDiscord, isUrlXUser } from 'lib/validate'

const multimediaUseCase = new MultimediaUseCase()
const collectionsUseCase = new CollectionUseCase()
const meUseCase = new MeUseCase()
const paymentTokensUseCase = new PaymentTokensUseCase()
const MAX_TOTAL_OWNERS_PERCENT = 10
const CreateCollection: NextPage = () => {
  const initFormData = {
    name: '',
    symbol: '',
    logoImage: '',
    featuredImage: '',
    bannerImage: '',
    slug: '',
    description: '',
    categories: [],
    paymentTokens: [],
    chainId: '',
    siteUrl: '',
    discordUrl: '',
    instagramUrl: '',
    mediumUrl: '',
    telegramUrl: '',
    twitterUrl: '',
    owners: [],
    collaborators: [],
    isExplicit: false,
    schema: null,
  }

  const [showModal, setShowModal] = useState(false)
  const [isCreator, setIsCreator] = useState(true)
  const { formData, setFormValue } = useForm(initFormData)
  const [owners, setOwners] = useState<User[]>([])
  const [collaborators, setCollaborators] = useState<{ value: User }[]>([])
  const [filterChains, setFilterChains] = useState<Chain[]>([])
  const [isFilterChainsReady, setIsFilterChainsReady] = useState(false)
  const [filterPaymentTokens, setFilterPaymentTokens] = useState<any>([])
  const [paymentFixedOptions, setPaymentFixedOptions] = useState<any>([])
  const [isFilterPaymentTokensReady, setIsFilterPaymentTokensReady] =
    useState(false)
  const [isConnected, setIsConnected] = useState(false)
  const [error, setError] = useState('')
  const [canSubmit, setCanSubmit] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [currentContracts, setCurrentContracts] = useState<any>({})
  const [isSwitchNetwork, setIsSwitchNetwork] = useState(false)
  const { chainId } = useAccount()
  const [minPercent, setMinPercent] = useState<number>(0)
  const [maxPercent, setMaxPercent] = useState<number>(100)
  const router = useRouter()
  const [schemas, setSchemas] = useState<any[]>([])
  const [urlErrors, setUrlErrors] = useState({
    siteUrl: '',
    twitterUrl: '',
    discordUrl: '',
  })
  /**
   * HOOKS
   */
  const { getProviderEthers, switchNetwork, addressConnect} = useAuthentication()
  const authStateProvider = AuthStateProvider((state) => ({
    me: state.me,
  }))
  const formatChains = () => {
    const chains = Chain.getResourceArray().map((chain) => {
      return Chain.fromName<Chain>(chain.name)
    })
    setFilterChains(chains)
    setIsFilterChainsReady(true)
    if (chains && chains.length > 0) {
      setFormValue('chainId', chains[0].getChainId() || '')
      formatPaymentTokens(chains[0].getChainId())
    }
  
  }
  const formatSchemas = async () => {
    let schemas = Schema.getResourceArray()
    schemas = schemas.map((schema : any) => {
      return {
        ...schema,
        value: schema.name, // for set checked  dropdown
      }
    })
    setSchemas(schemas)
    // set default schemaId selected
    if (schemas && schemas.length > 0) {
      let schema1155 = schemas.find((schema : any) => {
        return schema.name.toUpperCase() === 'ERC1155'
      })
      setFormValue('schema',schema1155)
    }
  }
  const fetchMe = async () => {
    await meUseCase
      .loginStatus()
      .then((res) => {
        setIsConnected(res.isLoggedIn)
        if (res.isLoggedIn) {
          setOwners([res.user!])
          setCollaborators([{ value: res.user! }])
        }
      })
      .catch(() => {
        setIsConnected(false)
      })
  }

  const formatPaymentTokens = async (chainIdSelect ?: number) => {
    try {
      setIsFilterPaymentTokensReady(false)
      setFilterPaymentTokens([])
      setPaymentFixedOptions([])
      const paymentTokens:PaymentToken[] = await paymentTokensUseCase.getPaymentTokens(chainIdSelect)
      setFilterPaymentTokens(paymentTokens)
      // const fixedOptions = paymentTokens.filter((token: any) => {
      //   return token.isNative || token.isWrappedEther
      // })
      const fixedOptions = [...paymentTokens]
      console.log('🚀 ~ fixedOptions ~ fixedOptions:', fixedOptions)
      setPaymentFixedOptions(fixedOptions)
      setIsFilterPaymentTokensReady(true)
      setFormValue('paymentTokens', fixedOptions) // set default value
    } catch (error) {
      console.log('🚀 ~ formatPaymentTokens ~ error:', error)
      
    }
  }

  const uploadFile = async (file: any, key: string) => {
    if (file) {
      multimediaUseCase
        .upload(file)
        .then((res) => {
          setFormValue(key, res)
        })
        .catch((err) => {
          toast.error('Failed to upload file.')
        })
    }
  }

  const validateUrls = () => {
    const errors = {
      siteUrl: '',
      twitterUrl: '',
      discordUrl: '',
    }
    
    // Validate site URL
    if (formData.siteUrl && !isURL(formData.siteUrl)) {
      errors.siteUrl = 'Please enter a valid website URL'
    }
    
    // Validate Twitter URL
    if (formData.twitterUrl && !isUrlXUser(formData.twitterUrl)) {
      errors.twitterUrl = 'Please enter a valid Twitter URL (https://x.com/username)'
    }
    
    // Validate Discord URL
    if (formData.discordUrl && !isUrlDiscord(formData.discordUrl)) {
      errors.discordUrl = 'Please enter a valid Discord URL (https://discord.gg/invite)'
    }
    
    setUrlErrors(errors)
    
    // Return true if there are no errors
    return !errors.siteUrl && !errors.twitterUrl && !errors.discordUrl
  }

  const submitForm = async (e: any) => {
    try {
      e.preventDefault()
      
      // Validate URLs first
      if (!validateUrls()) {
        return
      }

      // check total earning owners - start
      const totalOwnerPercent= formData?.owners?.reduce((total :any, owner :any ) => {  
        return total + (owner?.receivePercent || 0)
      },0)
      if(totalOwnerPercent > MAX_TOTAL_OWNERS_PERCENT){
        setError('Total owners percent must be less than 10%')
        toast.error('Please fill owner percent less than 10%')
        return
      }
      // check total earning owners - end
      if (!formData.logoImage || (!formData.name && !isConnected)) {
        setError('Required fields must have data.')
        toast.error('Please fill the required fields')
      } else {
        setError('')
        setIsLoading(true)
        const collectionData = {
          ...formData,
          schemaId: formData.schema?.id,
          owner: owners[0],
          collaborators: collaborators.map((collaborator) => collaborator.value),
          chainId: formData.chainId,
        }

        const newCollection = new Collection(
          undefined,
          collectionData.slug,
          collectionData.name,
          collectionData.symbol,
          collectionData.description,
          collectionData.siteUrl,
          collectionData.discordUrl,
          collectionData.telegramUrl,
          collectionData.twitterUrl,
          collectionData.mediumUrl,
          null,
          collectionData.instagramUrl,
          collectionData.isExplicit,
          0,
          0,
          0,
          0,
          0,
          0,
          DateTime.now(),
          DateTime.now(),
          collectionData.logoImage,
          collectionData.featuredImage,
          collectionData.bannerImage,
          collectionData.categories,
          collectionData.owner,
          collectionData.paymentTokens,
          [],
          collectionData.collaborators,
          collectionData.collectionAddress,
          collectionData.owners,
          collectionData.chainId,
          collectionData.schemaId,
          null,
          null,
          [],
        )
        console.log('🚀 ~ submitForm ~ formData:', formData)
        console.log('🚀 ~ submitForm ~ collectionData:', collectionData)
        console.log('🚀 ~ submitForm ~ newCollection:', newCollection)
        //call contract to create collection -START
        const provider = await getProviderEthers()
        const accountAddress = authStateProvider.me?.publicAddresses[0] || ''
        const domainRegistryAddress = currentContracts?.domainRegistryAddress || ''
        const marketplaceAddress = currentContracts?.marketplaceAddress || ''
        const erc1155SeaDropConfigurerAddress = currentContracts?.erc1155SeaDropConfigurer || ''
        const erc1155CloneFactoryAddress = currentContracts?.erc1155SeaDropCloneFactory || ''
        const erc721CloneFactoryAddress = currentContracts?.erc721CloneFactoryAddress || ''
        console.log('🚀 ~ submitForm ~ erc721CloneFactoryAddress:', erc721CloneFactoryAddress)
        if (!domainRegistryAddress || !marketplaceAddress) {
          throw new Error('Domain registry or marketplace address is not support in this network')
        }
        console.log('🚀 ~ submitForm ~ currentContracts:', currentContracts)
        const tokenContractType = envConfig.itemType.find((type: any) => type.name === formData.schema?.name)?.value || 3 // 3 is ERC1155
        console.log('🚀 ~ submitForm ~ tokenContractType:', tokenContractType)
        const res = await collectionsUseCase.scCreateCollection(newCollection, accountAddress,tokenContractType, provider, currentContracts)
        console.log('🚀 ~ submitForm ~ res:', res)
        const collectionAddress = res?.logs[0]?.address || ''
        console.log('🚀 ~ submitForm ~ collectionAddress:', collectionAddress)
        newCollection.collectionAddress = collectionAddress
        //call contract to create collection -END
        collectionsUseCase
          .save(newCollection)
          .then(async () => {
            toast.success('Your new collection has been created.')
            router.push('/account/collections')
            setIsLoading(false)
          })
          .catch((_err) => {
            toast.error('Failed to create new collection.')
            setIsLoading(false)
          })
      }
    } catch (error: any) {
      console.log('🚀 ~ submitForm ~ error:', error)
      toast.error(error?.message || error)
      setIsLoading(false)
    }
  }
  const initEnvConfig = () => {
    const config: any = envConfig
    setCurrentContracts(config.contracts[chainId || 1])
    // check switch network
    if(chainId) {
      const chains = Chain.getResourceArray()
      const network = chains.find((network: any) => network.chainId === chainId)
      if (!network) {
        setIsSwitchNetwork(true)
      } else { 
        setIsSwitchNetwork(false)
      }
    }
  }

  const switchDefaultNetwork = useCallback(async () => {
    try {
      setIsLoading(true)
      const config: any = envConfig
      const chains = Chain.getResourceArray()
      const networkSupported = chains.find((network: any) => network.chainId === formData.chainId)
      await switchNetwork(networkSupported)
      console.log('🚀 ~ switchDefaultNetwork ~ switchNetwork:')
      setIsLoading(false)
    } catch (error : any) {
      console.log('🚀 ~ switchDefaultNetwork ~ error:', error)
      toast.error(error?.message || error)
      setIsLoading(false)
    }
   
  },[formData.chainId])

  const checkNetwork = () => {
    console.log('🚀 ~ checkNetwork ~ chainId:', chainId)
    console.log('🚀 ~ checkNetwork ~ formData.chainId:', formData.chainId)
    if (formData.chainId !== chainId) {
    
      setIsSwitchNetwork(true)
    } else { 
      setIsSwitchNetwork(false)
    }
  }
  useEffect(() => {
    if (formData.name && formData.logoImage && formData.slug && 
        !urlErrors.siteUrl && !urlErrors.twitterUrl && !urlErrors.discordUrl) {
      setCanSubmit(true)
    } else {
      setCanSubmit(false)
    }
    console.log('🚀 ~ useEffect ~ formData:', formData)
  }, [formData, urlErrors])

  useEffect(() => {
    console.log('🚀 ~ useEffect ~ owners:', owners)
    setFormValue('owners', owners)
  }, [owners])

  useEffect(() => {
    setFormValue('collaborators', collaborators)
  }, [collaborators])

  // useEffect(() => {
  //   setFormValue('blockchain', collaborators)
  // }, [collaborators])

  useEffect(() => {
    fetchMe()
    formatChains()
    formatSchemas()
  }, [])
  useEffect(() => {
    initEnvConfig()
  }, [chainId])
  useEffect(() => {
    checkNetwork()
  },[chainId, formData.chainId])
  return (
    <>
      <Head>
        <title>Create Collection - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>
      <main>
        {isConnected && addressConnect ? (
          <Container className="pt-9 md:pb-0 pb-9">
            <div className="text-center">
              <Heading>Create a Collection</Heading>
            </div>
            <div className="container-lg mt-5 pb-[6px]">
              <div className="collection-form-cont">
                <form className="mx-auto collection-form" onSubmit={submitForm}>
                  {/* <div className="text-right">
                    <Button
                      variant="light"
                      onClick={(e) => {
                        e.preventDefault()
                      }}
                    >
                      Preview
                      <SvgIcon
                        name="eye"
                        className="w-5 h-5 ml-2 stroke-none fill-primary-2 dark:fill-white"
                      />
                    </Button>
                  </div> */}

                  <p>
                    <span className="text-red-1">{'* '}</span>
                    <span className="text-gray-1">Required Fields</span>
                  </p>

                  <div className="mt-5 flex xl:flex-nowrap flex-wrap">
                    {/* Logo image */}
                    <div className="md:w-1/2 w-full pr-8 mb-5 flex-none">
                      <div className="flex justify-between items-center">
                        <p className="font-medium text-dark-primary dark:text-gray-3">
                          Logo image <span className="text-red-1">{' *'}</span>
                        </p>
                        <Tooltip contentWidth="lg:w-[295px]" iconSize="w-4 h-4">
                          File types supported: JPG, PNG, GIF, SVG, MP4, WEBM,
                          MP3, WAV, OGG, GLB, GLTF. Max size: 100 MB
                        </Tooltip>
                      </div>
                      <p className="mt-2 text-sm text-gray-1">
                        This image will also be used for navigation. 350 x 350
                        recommended.
                      </p>
                      <div className="relative w-fit pt-3 pr-3 mt-3">
                        <div className="absolute top-0 right-0">
                          <input
                            className="w-5 h-5 opacity-0 absolute"
                            type="file"
                            id="img"
                            name="logoImage"
                            accept="image/*"
                            onChange={(e: any) =>
                              uploadFile(e.target.files[0], 'logoImage')
                            }
                          />
                          <SvgIcon
                            name="plusCircle"
                            className="w-5 h-5 stroke-none fill-primary-2 cursor-pointer"
                          />
                        </div>
                        <div className="upload-image-circle bg-gray-5 dark:bg-blue-1">
                          {formData.logoImage && (
                            <Image
                              alt=""
                              fill
                              style={{ objectFit: 'cover' }}
                              className="rounded-full"
                              src={formData.logoImage?.url}
                            />
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Featured image */}
                    <div className="md:w-1/2 xl:min-w-[480px] w-full">
                      <p className="font-medium text-dark-primary dark:text-gray-3">
                        Featured image
                      </p>
                      <p className="mt-2 text-sm text-gray-1">
                        This image will be used for featuring your collection on
                        the homepage, category pages, or other promotional
                        areas. 600 x 375 recommended.
                      </p>
                      <div className="relative w-fit mt-2">
                        <div className="absolute top-3 right-3 z-10">
                          <input
                            className="w-5 h-5 opacity-0 absolute"
                            type="file"
                            id="img"
                            name="featuredImage"
                            accept="image/*"
                            onChange={(e: any) =>
                              uploadFile(e.target.files[0], 'featuredImage')
                            }
                          />
                          <SvgIcon
                            name="plusCircle"
                            className="w-5 h-5 stroke-none fill-primary-2 cursor-pointer"
                          />
                        </div>
                        <div className="upload-image-square bg-gray-5 dark:bg-blue-1">
                          {formData.featuredImage && (
                            <Image
                              alt=""
                              fill
                              style={{ objectFit: 'cover' }}
                              className="rounded-10"
                              src={formData.featuredImage?.url}
                            />
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Featured image */}
                  <div className="mt-7">
                    <p className="font-medium text-dark-primary dark:text-gray-3">
                      Banner image
                    </p>
                    <p className="mt-1 text-sm text-gray-1">
                      This image will appear at the top of your collection page.
                      Avoid including too much text in this banner image, as the
                      dimensions change on different devices. 1280 x 200
                      recommended.
                    </p>
                  </div>
                  <div className="relative w-full mt-2">
                    <div className="absolute top-3 right-3 z-10 cursor-pointer">
                      <input
                        className="w-5 h-5 opacity-0 absolute cursor-pointer"
                        type="file"
                        id="img"
                        name="bannerImage"
                        accept="image/*"
                        onChange={(e: any) =>
                          uploadFile(e.target.files[0], 'bannerImage')
                        }
                      />
                      <SvgIcon
                        name="plusCircle"
                        className="w-5 h-5 stroke-none fill-primary-2 cursor-pointer"
                      />
                    </div>
                    <div className="upload-image-reactangle bg-gray-5 dark:bg-blue-1">
                      {formData.bannerImage && (
                        <Image
                          alt=""
                          fill
                          style={{ objectFit: 'cover' }}
                          className="rounded-10"
                          src={formData.bannerImage?.url}
                        />
                      )}
                    </div>
                  </div>

                  {/* Name */}
                  <div className="md:w-1/2 w-full mt-7">
                    <p className="font-medium text-dark-primary dark:text-gray-3">
                      Name <span className="text-red-1">*</span>
                    </p>
                    <Input
                      type="text"
                      className="mt-10p"
                      value={formData.name}
                      onChange={(e: any) =>
                        setFormValue('name', e.target.value)
                      }
                    />
                  </div>
                  {/* Token Symbol */}
                  <div className="md:w-1/2 w-full mt-7">
                    <p className="font-medium text-dark-primary dark:text-gray-3">
                      Token symbol <span className="text-red-1">*</span>
                    </p>
                    <Input
                      type="text"
                      className="mt-10p"
                      value={formData.symbol}
                      onChange={(e: any) =>
                        setFormValue('symbol', e.target.value)
                      }
                    />
                  </div>    
                  {/* Schema ID */}
                  <div className="md:w-1/2 w-full mt-7">
                    <p className="font-medium text-dark-primary dark:text-gray-3">
                      Token Standard <span className="text-red-1">*</span>
                    </p>
                    {schemas?.length > 0 && <Select
                      options={schemas}
                      defaultSelected={formData.schema}
                      rounded
                      onSelect={(option) => {
                        console.log('🚀 ~ option:', option)
                        setFormValue('schema', option || null)
                      }
                      }
                    />
                    }
                  </div>    
                  {/* URL */}
                  <InputCollectionURL
                    defaultValue={formData.slug}
                    chainId={formData.chainId}
                    onChange={(value) => setFormValue('slug', value)}
                  />

                  {/* Description */}
                  <div className="mt-5">
                    <p className="font-medium text-dark-primary dark:text-gray-3">Description</p>
                    <p className="mt-1 text-sm text-gray-1">
                      {/* <span className="text-primary-2">Markdown syntax</span> is
                      supported. {formData.description.length} of 1000 characters used. */}
                      {formData.description.length} of 1000 characters used.
                    </p>
                    <textarea
                      className="px-5 py-3 mt-2 rounded-3xl w-full outline-none bg-gray-5 dark:bg-blue-1"
                      rows={3}
                      value={formData.description}
                      maxLength={1000}
                      onChange={(e: any) =>
                        setFormValue('description', e.target.value)
                      }
                    />
                  </div>

                  {/* Category */}
                  <SelectCategories
                    length={1}
                    onSelect={(option) => {
                      console.log('🚀 ~ option:', option)
                      
                      setFormValue('categories', option)
                    }}
                  />

                  {/* Link */}
                  <div className="mt-5">
                    <p className="font-medium text-dark-primary dark:text-gray-3">Links</p>
                    <div className="mt-[10px] flex items-center">
                      <SvgIcon
                        name="globe"
                        className="w-7 h-5 mr-3 stroke-none fill-primary-2"
                      />
                      <p className="mr-7 w-[110px] font-medium text-dark-primary  dark:text-gray-3 flex-none whitespace-nowrap">
                        Your Site <span className='text-xs font-normal'>(URL)</span>
                      </p>
                      <div className="flex flex-col w-full">
                        <Input
                          type="text"
                          placeholder="Your site"
                          width="sm:w-80 w-full"
                          value={formData.siteUrl}
                          onChange={(e) => {
                            setFormValue('siteUrl', e.target.value)
                            // Validate on change
                            if (e.target.value && !isURL(e.target.value)) {
                              setUrlErrors({...urlErrors, siteUrl: 'Please enter a valid website URL'})
                            } else {
                              setUrlErrors({...urlErrors, siteUrl: ''})
                            }
                          }}
                        />
                        {urlErrors.siteUrl && <p className="text-red-500 text-xs mt-1">{urlErrors.siteUrl}</p>}
                      </div>
                    </div>
                    {/* X (Twitter) URL Input */}
                    <div className="mt-[10px] flex items-center">
                      <SvgIcon
                        name="twitter"
                        className="w-7 h-5 mr-3 stroke-none fill-primary-2"
                      />
                      <p className="mr-7 w-[110px] font-medium text-dark-primary dark:text-gray-3 flex-none whitespace-nowrap">
                        X (Twitter) <span className='text-xs font-normal'>(URL)</span>
                      </p>
                      <div className="flex flex-col w-full">
                        <Input
                          type="text"
                          placeholder="Your X (Twitter)"
                          width="sm:w-80 w-full"
                          value={formData.twitterUrl}
                          onChange={(e) => {
                            setFormValue('twitterUrl', e.target.value)
                            // Validate on change
                            if (e.target.value && !isUrlXUser(e.target.value)) {
                              setUrlErrors({...urlErrors, twitterUrl: 'Please enter a valid Twitter URL (https://x.com/username)'})
                            } else {
                              setUrlErrors({...urlErrors, twitterUrl: ''})
                            }
                          }}
                        />
                        {urlErrors.twitterUrl && <p className="text-red-500 text-xs mt-1">{urlErrors.twitterUrl}</p>}
                      </div>
                    </div>
                    <div className="mt-[10px] flex items-center">
                      <SvgIcon
                        name="discord"
                        className="w-7 h-5 mr-3 stroke-none fill-primary-2"
                      />
                      <p className="mr-7 w-[110px] font-medium text-dark-primary dark:text-gray-3 flex-none whitespace-nowrap">
                        Discord  <span className='text-xs font-normal'>(URL)</span>
                      </p>
                      <div className="flex flex-col w-full">
                        <Input
                          type="text"
                          placeholder="Your Discord invite link"
                          width="sm:w-80 w-full"
                          value={formData.discordUrl}
                          onChange={(e) => {
                            setFormValue('discordUrl', e.target.value)
                            // Validate on change
                            if (e.target.value && !isUrlDiscord(e.target.value)) {
                              setUrlErrors({...urlErrors, discordUrl: 'Please enter a valid Discord URL (https://discord.gg/invite)'})
                            } else {
                              setUrlErrors({...urlErrors, discordUrl: ''})
                            }
                          }}
                        />
                        {urlErrors.discordUrl && <p className="text-red-500 text-xs mt-1">{urlErrors.discordUrl}</p>}
                      </div>
                    </div>
                    <div className="mt-[10px] flex items-center">
                      <SvgIcon
                        name="instagram"
                        className="w-7 h-5 mr-3 stroke-none fill-primary-2"
                      />
                      <p className="mr-7 w-[110px] font-medium text-dark-primary dark:text-gray-3">
                        Instagram
                      </p>
                      <Input
                        type="text"
                        placeholder="Your Instagram"
                        width="sm:w-56 w-full"
                        value={formData.instagramUrl}
                        onChange={(e) =>
                          setFormValue('instagramUrl', e.target.value)
                        }
                      />
                    </div>
                    <div className="mt-[10px] flex items-center">
                      <SvgIcon
                        name="medium"
                        className="w-7 h-5 mr-3 stroke-none fill-primary-2"
                      />
                      <p className="mr-7 w-[110px] font-medium text-dark-primary dark:text-gray-3">
                        Medium
                      </p>
                      <Input
                        type="text"
                        placeholder="Your Medium"
                        width="sm:w-56 w-full"
                        value={formData.mediumUrl}
                        onChange={(e) =>
                          setFormValue('mediumUrl', e.target.value)
                        }
                      />
                    </div>
                    <div className="mt-[10px] flex items-center">
                      <SvgIcon
                        name="telegram"
                        className="w-7 h-5 mr-3 stroke-none fill-primary-2"
                      />
                      <p className="mr-7 w-[110px] font-medium text-dark-primary dark:text-gray-3">
                        Telegram
                      </p>
                      <Input
                        type="text"
                        placeholder="Your Telegram"
                        width="sm:w-56 w-full"
                        value={formData.telegramUrl}
                        onChange={(e) =>
                          setFormValue('telegramUrl', e.target.value)
                        }
                      />
                    </div>
                  </div>

                  {/* Creator Earnings */}
                  <p className="mt-5 font-medium text-dark-primary dark:text-gray-3">
                    Creator Earnings
                  </p>
                  <p className="mt-2 text-sm text-gray-1">
                    Collect a fee when a user re-sells an item you originally
                    created. This is deducted from the final sale price and paid
                    monthly to a payout address of your choosing.
                  </p>
                  {owners && owners.length > 0 && (
                    <>
                      <div className="flex mt-3">
                        <div className="h-fit mt-3 mr-5">
                          <SvgIcon
                            name="plusCircle"
                            className="w-5 h-5 cursor-pointer stroke-none fill-primary-2"
                            onClick={() => {
                              setShowModal(true)
                              setIsCreator(true)
                            }}
                          />
                        </div>
                        <div className="w-1/2">
                          <ul className="space-y-3">
                            {owners?.map((owner: any, index: number) => (
                              <li
                                key={`owner-${index}`}
                                className={clsx(
                                  'rounded-3xl py-2 px-2 bg-gray-4 dark:bg-blue-1 flex items-center',
                                  index !== 0 && 'relative'
                                )}
                              >
                                {index === 0 ? (
                                  <>
                                    <Image
                                      src="/asset/images/owner-avatar.png"
                                      alt=""
                                      width={32}
                                      height={32}
                                      className="rounded-full"
                                    />
                                    <span className="ml-2 text-primary-2 font-medium">
                                      you
                                    </span>
                                    <span className="ml-3 text-dark-blue">
                                      <input
                                        type="text"
                                        name="owners-percents"
                                        className="bg-gray-4 dark:bg-transparent outline-0 rounded-3xl w-36 no-arrow ml-2 md:ml-5 !px-0 !py-0"
                                        placeholder='e.g.2.5'
                                        value={owner.receivePercent}
                                        min={minPercent}
                                        max={maxPercent}
                                        onChange={(e) => {
                                          let value : any = e.target.value
                                          if (value === '') {
                                            owner.receivePercent = ''
                                            setOwners([...owners])
                                            return
                                          }
                                          // Regular expression to allow only valid numbers and decimals
                                          const regex = /^\d*\.?\d*$/

                                          if (regex.test(value)) {
                                            if (value.endsWith('.') || value === '') {
                                              owner.receivePercent = value
                                              setOwners([...owners])
                                              // Allow input of only the decimal point or empty input
                                              return
                                            }
                                            if (value > maxPercent) {
                                              value = maxPercent
                                            } else if (value < minPercent) {
                                              value = minPercent
                                          
                                            }
                                            owner.receivePercent = Number(
                                              value
                                            )
                                            setOwners([...owners])
                                          }
                                        
                                        }
                                      
                                        }
                                      />
                                    </span>
                                  </>
                                ) : (
                                  <>
                                    <SvgIcon
                                      name="plusCircle"
                                      className="w-5 h-5 absolute -top-1 right-0 rotate-45 stroke-none fill-primary-2 cursor-pointer"
                                      onClick={() => {
                                        let filteredArr = owners.filter(
                                          (record) =>
                                            JSON.stringify(record) !==
                                            JSON.stringify(owner)
                                        )
                                        console.log('🚀 ~ filteredArr:', filteredArr)
                                        setOwners(filteredArr)
                                      }}
                                    />
                                    <Image
                                      src="/asset/images/owner-avatar.png"
                                      alt=""
                                      width={32}
                                      height={32}
                                      className="rounded-full"
                                    />
                                    <span className="ml-2 text-primary-2 font-medium">
                                      {shortenAddress(owner.username)}
                                    </span>
                                    <span className="ml-3 text-dark-blue">
                                      <input
                                        type="text"
                                        name="owners-percents"
                                        className="bg-gray-4 dark:bg-transparent outline-0 rounded-3xl w-36 no-arrow ml-2 md:ml-5 !px-0 !py-0"
                                        placeholder='e.g.2.5'
                                        value={owner.receivePercent}
                                        min={minPercent}
                                        max={maxPercent}
                                        onChange={(e) => {
                                          let value : any = e.target.value
                                          if (value === '') {
                                            owner.receivePercent = ''
                                            setOwners([...owners])
                                            return
                                          }
                                          // Regular expression to allow only valid numbers and decimals
                                          const regex = /^\d*\.?\d*$/

                                          if (regex.test(value)) {
                                            if (value.endsWith('.') || value === '') {
                                              owner.receivePercent = value
                                              setOwners([...owners])
                                              // Allow input of only the decimal point or empty input
                                              return
                                            }
                                            if (value > maxPercent) {
                                              value = maxPercent
                                            } else if (value < minPercent) {
                                              value = minPercent
                                          
                                            }
                                            owner.receivePercent = Number(
                                              value
                                            )
                                            setOwners([...owners])
                                          }
                                        
                                        }
                                      
                                        }
                                      />
                                    </span>
                                  </>
                                )}
                              </li>
                            ))}
                          </ul>
                        </div>
                       
                      </div>
                    </>
                  )}

                  {/* Blockchain */}
                  <div className="mt-5 flex items-center">
                    <p className="mr-8 font-medium text-dark-primary dark:text-gray-3">
                      Blockchain
                    </p>
                    {isFilterChainsReady && (
                      <Select
                        options={filterChains}
                        showOptionIcon
                        defaultSelected={() =>{

                          let chainIdSelect = formData.chainId
                          const chainSelect = filterChains.find((chain: any) => chain?.getChainId() === chainIdSelect)
                          return chainSelect
                        }} // fit with envConfig.defaultNetwork

                        rounded
                        customWidth={true}
                        isBlockchainFilter={true}
                        onSelect={(option) => {
                          console.log('🚀 ~ option:', option, option.chainId )
                          setFormValue('chainId', option.getChainId() || '')
                          formatPaymentTokens(option.getChainId())
                        }
                        }
                      />
                    )}
                  </div>

                  {/* Payment */}
                  <div className="mt-5">
                    <p className="font-medium text-dark-primary dark:text-gray-3">
                      Payment tokens
                    </p>
                    <div className="flex items-center">
                      <p className="text-sm text-gray-1">
                        These tokens can be used to buy and sell your items.
                      </p>
                      {/* <Tooltip>This is tool tip</Tooltip> */}
                    </div>
                    {isFilterPaymentTokensReady && (
                      <SelectTag
                        fixedOptions={paymentFixedOptions}
                        isHideSelected={true}
                        options={filterPaymentTokens}
                        onSelect={(option: Array<{}>) => {
                          console.log('🚀 ~ option:', option)
                          if (option.length > 0) {
                            setFormValue('paymentTokens', [...paymentFixedOptions, ...option])
                          } else { 
                            setFormValue('paymentTokens', [...paymentFixedOptions])
                          }
                        }
                        }
                      />
                    )}
                  </div>

                  {/* Explicit & sensitive content */}
                  <div className="mt-5">
                    <p className="font-medium text-dark-primary dark:text-gray-3">
                      Explicit & sensitive content
                    </p>
                    <div className="flex items-center">
                      <p className="text-sm text-gray-1">
                        Set this item as explicit and sensitive content
                      </p>
                      {/* <Tooltip>This is tool tip</Tooltip> */}
                    </div>
                    <Switch
                      className="mt-[10px]"
                      defaultActive={formData.isExplicit}
                      onClick={(isExplicit) =>
                        setFormValue('isExplicit', isExplicit)
                      }
                    />
                  </div>

                  {/* Collaborators */}
                  <div className="mt-5">
                    <p className="font-medium text-dark-primary dark:text-gray-3">
                      Collaborators
                    </p>
                    <p className="text-sm text-gray-1">
                      Collaborators can modify collection settings, receive
                      payments for items they created and create new items.
                      <br />
                      Collaborators can not change creator earnings percentages
                      or payout address.
                    </p>
                  </div>
                  {collaborators && collaborators.length > 0 && (
                    <div className="mt-[14px]">
                      <ul className="flex flex-wrap items-center gap-3">
                        <SvgIcon
                          name="plusCircle"
                          className="w-5 h-5 mr-5 stroke-none fill-primary-2 cursor-pointer"
                          onClick={() => {
                            setShowModal(true)
                            setIsCreator(false)
                          }}
                        />
                        {collaborators?.map(
                          (collaborator: any, index: number) => (
                            <li
                              key={`collaborator-${index}`}
                              className={clsx(
                                'px-[9px] py-[10px] rounded-10 border border-gray-3 flex items-center',
                                index !== 0 && 'relative'
                              )}
                            >
                              {index === 0 ? (
                                <>
                                  <Image
                                    src="/asset/images/owner-avatar.png"
                                    alt=""
                                    width={32}
                                    height={32}
                                    className="rounded-full"
                                  />
                                  <p className="ml-3 text-primary-3 font-medium p-1">
                                    you
                                  </p>
                                </>
                              ) : (
                                <>
                                  <SvgIcon
                                    name="plusCircle"
                                    className="w-5 h-5 absolute -top-1 -right-1 rotate-45 stroke-none fill-primary-2 cursor-pointer"
                                    onClick={() => {
                                      let filteredArr = collaborators.filter(
                                        (record) =>
                                          JSON.stringify(record) !==
                                          JSON.stringify(collaborator)
                                      )
                                      setCollaborators(filteredArr)
                                    }}
                                  />
                                  <Image
                                    src="/asset/images/owner-avatar.png"
                                    alt=""
                                    width={32}
                                    height={32}
                                    className="rounded-full"
                                  />
                                  <p className="ml-3 text-primary-2 font-medium">
                                    {shortenAddress(
                                      collaborator.value.username
                                    )}
                                  </p>
                                </>
                              )}
                            </li>
                          )
                        )}
                      </ul>
                    </div>
                  )}

                  <div className="flex flex-wrap w-full">
                    {isSwitchNetwork ? (
                      <Button
                        variant="dark"
                        className="mt-6 flex"
                        disable={isLoading}
                        type="button"
                        onClick={() => switchDefaultNetwork()}
                      > 
                        { isLoading && <Loader className='mr-2' /> }  Switch network
                      </Button>
                    ) : (
                      <Button
                        variant="dark"
                        className="mt-6 flex"
                        disable={!canSubmit || isLoading}
                      > 
                        { isLoading && <Loader className='mr-2' /> }  Create
                      </Button>
                    )}
                    {error && (
                      <span className="text-red-1 py-3.5 font-medium w-full">
                        {error}
                      </span>
                    )}
                  </div>
                </form>
              </div>
            </div>
          </Container>
        ) : (
          <RequireConnect />
        )}

        {showModal && (
          <AddCollaborator
            isShow={showModal}
            onClose={() => setShowModal(false)}
            onSubmit={(user) => {
              console.log('🚀 ~ user:', user)
              if (isCreator) {
                let duplicateArr = owners.filter(
                  (owner) => owner.id === user.id
                )
                console.log('🚀 ~ owners:', owners)
                if (duplicateArr.length === 0) {
                  setOwners([...owners, user])
                }
              } else {
                let duplicateArr = collaborators.filter(
                  (collaborator) => collaborator.value.id === user.id
                )
                if (duplicateArr.length === 0) {
                  setCollaborators([...collaborators, { value: user }])
                }
              }
              setShowModal(false)
            }}
          />
        )}
      </main>
    </>
  )
}

export default CreateCollection
