import { Query as CollectionWatchQuery } from 'domain/repositories/collection_watch_repository/query'
import TargetEntityType from 'domain/value_objects/target_entity_type'
import { NextPage } from 'next'
import Head from 'next/head'
import Image from 'next/image'
import Link from 'next/link'
import { useRouter } from 'next/router'
import ButtonIcon from 'presentation/components/button/button-icon'
import ButtonTag from 'presentation/components/button/button-tag'
import StatCard from 'presentation/components/card/stat-card'
import Container from 'presentation/components/container'
import RecommendationModal from 'presentation/components/modal/recommendation'
import ShowMore from 'presentation/components/show-more'
import SvgIcon from 'presentation/components/svg-icon'
import ActivityTag from 'presentation/page_components/collection/activityTag'
import BundlesTag from 'presentation/page_components/collection/bundlesTag'
import SocialNetworks from 'presentation/page_components/socialNetworks'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'
import CollectionUseCase from 'presentation/use_cases/collection_use_case'
import CollectionWatchUseCase from 'presentation/use_cases/collection_watch_use_case'
import MeUseCase from 'presentation/use_cases/me_use_case'
import WatchUseCase from 'presentation/use_cases/watch_use_case'
import { useEffect, useState } from 'react'
import { formatNumberPostfix } from 'lib/number'
import {getChainName} from 'lib/utils'
import CollectionDescription from 'presentation/components/show-more/collection-description'
const collectionUseCase = new CollectionUseCase()
const collectionWatchUseCase = new CollectionWatchUseCase()
const watchUseCase = new WatchUseCase()
const meUseCase = new MeUseCase()

const Collection: NextPage = ({ chainName, collectionSlug, activeTab }: any) => {
  console.log('🚀 ~ chainName, collectionSlug, activeTab:', chainName, collectionSlug, activeTab)
  const router = useRouter()
  const [errorCode, setErrorCode] = useState(0)
  const [showModal, setShowModal] = useState(false)
  const [collectionDetail, setCollectionDetail] = useState<any>({})
  const [isOwner, setIsOwner] = useState(false)
  const [isConnected, setIsConnected] = useState(false)
  const [isFetchingCollectionDetail, setIsFetchingCollectionDetail] =
    useState(true)
  const [isWatched, setIsWatched] = useState(false)

  const authStateProvider = AuthStateProvider((state) => ({
    me: state.me,
  }))

  const fetchCollectionDetail = async () => {
    setIsFetchingCollectionDetail(true)
    collectionUseCase
      .findBySlug(collectionSlug)
      .then((resCollection) => {
        setCollectionDetail(resCollection)
        meUseCase.loginStatus().then((res) => {
          if (res.user?.id === resCollection.owner.id) {
            setIsOwner(true)
          }
          setIsConnected(res.isLoggedIn)
        })
      })
      .catch((_error) => {
        setErrorCode(404)
      })
    setIsFetchingCollectionDetail(false)
  }

  const fetchCollectionWatch = async () => {
    const query = new CollectionWatchQuery({
      collectionId: collectionDetail.id,
      userId: authStateProvider.me?.id,
    })
    collectionWatchUseCase
      .search(query)
      .then((res) => {
        if (res.length > 0) {
          setIsWatched(true)
        } else {
          setIsWatched(false)
        }
      })
      .catch((_error) => {
        setErrorCode(404)
      })
  }

  const toggleWatchingCollection = async () => {
    if (isWatched) {
      watchUseCase
        .delete(TargetEntityType.collection(), collectionDetail.id)
        .then(() => {
          setIsWatched(false)
        })
        .catch((_error) => {
          setErrorCode(404)
        })
    } else {
      watchUseCase
        .create(TargetEntityType.collection(), collectionDetail.id)
        .then(() => {
          setIsWatched(true)
        })
        .catch((_error) => {
          setErrorCode(404)
        })
    }
  }

  useEffect(() => {
    if (collectionDetail.id && isConnected) {
      fetchCollectionWatch()
    }
  }, [collectionDetail, isConnected])


  useEffect(() => {
    fetchCollectionDetail()
  }, [])

  useEffect(() => {
    if (
      activeTab !== 'items' &&
      activeTab !== 'bundles' &&
      activeTab !== 'activity'
    ) {
      setErrorCode(404)
    }
  }, [activeTab])

  if (errorCode === 404) {
    //router.push('/404')
  }

  return (
    <>
      <Head>
        <title>
          {collectionDetail?.name
            ? `${collectionDetail?.name} | Collection - Quill`
            : 'Collection - Quill'}
        </title>
        <meta name="description" content="Enjoy Quill" />
      </Head>
      <main>
        <Container className="pt-9">
          {!isFetchingCollectionDetail && (
            <>
              <div className="relative w-full h-52">
                <Image
                  className="rounded-lg"
                  alt="NFT banner"
                  src={
                    collectionDetail.bannerImage?.url
                      ? collectionDetail.bannerImage?.url
                      : '/asset/images/NFT-banner.png'
                  }
                  fill
                  style={{ objectFit: 'cover' }}
                />
              </div>
              <div className="relative">
                <div className="pt-16 md2:pt-4 flex flex-wrap md2:justify-end justify-center">
                  {isConnected && (
                    <>
                      {isWatched ? (
                        <ButtonIcon
                          className="mr-3 h-fit px-4 py-2 sm:mb-0 mb-3 bg-primary-2"
                          svgIcon="eye"
                          iconClassName="w-5 h-5 stroke-none fill-white"
                          onClick={toggleWatchingCollection}
                        >
                          <span className="align-middle font-medium text-white ml-1">
                            Watched
                          </span>
                        </ButtonIcon>
                      ) : (
                        <ButtonIcon
                          className="mr-3 h-fit px-4 py-2 sm:mb-0 mb-3"
                          svgIcon="plus"
                          iconClassName="w-5 h-5 stroke-none fill-primary-2"
                          onClick={toggleWatchingCollection}
                        >
                          <span className="align-middle font-medium text-primary-2">
                            Watchlist
                          </span>
                        </ButtonIcon>
                      )}
                    </>
                  )}
                  <div className="inline-flex sm:w-auto w-full sm:justify-start justify-center min-h-[36px]">
                    <SocialNetworks {...collectionDetail} />
                  </div>
                </div>
                <div className="absolute w-[100px] h-[100px] top-0 left-1/2 -translate-y-1/2 -translate-x-1/2">
                  <Image
                    className="rounded-full"
                    src={
                      collectionDetail.logoImage?.url
                        ? collectionDetail.logoImage?.url
                        : '/asset/images/character.png'
                    }
                    alt={collectionDetail.name || 'Quill'}
                    fill
                  />
                </div>
                <div className="w-full md:w-1/2 mx-auto flex flex-col items-center">
                  <div className="text-center">
                    <p className="mt-4 font-medium text-lg">
                      {collectionDetail?.name}
                      <SvgIcon
                        name="verify"
                        className="w-5 h-5 ml-1 stroke-none fill-primary-2"
                      />
                    </p>
                    <p className="inline-flex justify-center font-medium mt-2 space-x-1">
                      <span>Created by </span>
                      <Link
                        href={`/users/${collectionDetail.owner?.username}`}
                        className="flex justify-center cursor-pointer truncate max-w-[100px]"
                      >
                        <span className="text-primary-2 max-w-[100px] truncate">
                          {collectionDetail.owner?.username}
                        </span>
                      </Link>
                    </p>
                    <CollectionDescription description={collectionDetail.description} />
                  </div>
                  <div className="flex flex-wrap justify-center md:justify-start md:flex-nowrap mt-4 gap-2 md:gap-4 xl:gap-5">
                    <StatCard
                      widthClasses="w-[130px] max-w-[130px] h-24 justify-center flex-none"
                      title={collectionDetail.numAssets?.toLocaleString() || 0}
                      subtitle={
                        collectionDetail.numAssets === 1 ? 'Item' : 'Items'
                      }
                    />
                    <StatCard
                      widthClasses="w-[130px] max-w-[130px] h-24 justify-center flex-none"
                      title={collectionDetail.numOwners?.toLocaleString() || 0}
                      subtitle={
                        collectionDetail.numOwners === 1 ? 'Owner' : 'Owners'
                      }
                    />
                    {/* <StatCard
                      widthClasses="w-[130px] max-w-[130px] h-24 justify-center flex-none"
                      title={collectionDetail.floorPrice?.toLocaleString() || 0}
                      subtitle="Floor Price"
                    /> */}
                    <StatCard
                      widthClasses="w-[130px] max-w-[130px] h-24 justify-center flex-none"
                      title={
                        collectionDetail.totalVolumeUsd ? formatNumberPostfix(collectionDetail.totalVolumeUsd) : '0'
                      }
                      subtitle={
                        collectionDetail.totalVolume === 1
                          ? 'Total Volume'
                          : 'Total Volume'
                      }
                    />
                  </div>
                  {/* {authStateProvider.me?.publicAddresses[0] && (
                    <Button
                      onClick={() => setShowModal(true)}
                      variant="light"
                      className="mt-4 outline-none"
                    >
                      Recommendation
                    </Button>
                  )} */}
                  <div className="flex gap-4 mt-6">
                    <Link href={`/collections/${getChainName(collectionDetail.chainId)}/${collectionDetail.slug}`}>
                      <ButtonTag active={activeTab === 'items'}>
                        Items
                      </ButtonTag>
                    </Link>
                    {/* <Link
                      href={`/collections/${getChainName(collectionDetail.chainId)}/${collectionDetail.slug}/bundles`}
                    >
                      <ButtonTag active={activeTab === 'bundles'}>
                        Bundle
                      </ButtonTag>
                    </Link> */}
                    <Link
                      href={`/collections/${getChainName(collectionDetail.chainId)}/${collectionDetail.slug}/activity`}
                    >
                      <ButtonTag active={activeTab === 'activity'}>
                        Activity
                      </ButtonTag>
                    </Link>
                  </div>
                </div>
              </div>
              {activeTab === 'bundles' && (
                <BundlesTag collection={collectionDetail} />
              )}

              {activeTab === 'activity' && (
                <ActivityTag collection={collectionDetail} />
              )}
            </>
          )}
        </Container>

        {showModal && (
          <RecommendationModal
            isShow={showModal}
            onClose={() => setShowModal(false)}
            onSubmit={() => setShowModal(false)}
          />
        )}
      </main>
    </>
  )
}

export async function getServerSideProps (context: any) {
  const { chainName, id, activeTab } = context.params
  try {
    return {
      props: {
        chainName: chainName,
        collectionSlug: id,
        activeTab: activeTab,
      },
    }
  } catch (error) {
    return {
      notFound: true,
    }
  }
}

export default Collection
