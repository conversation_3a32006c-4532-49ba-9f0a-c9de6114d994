import { NextPage } from 'next'
import Head from 'next/head'
import { useRouter } from 'next/router'
import { useState, useEffect } from 'react'
import Container from 'presentation/components/container'
import Welcome from 'presentation/page_components/analytics/welcome'
import RarityTab from 'presentation/page_components/collection-analytics/rarityTab'
import AnalyticsHolder from 'presentation/page_components/collection-analytics/holder'
import OverviewTab from 'presentation/page_components/collection-analytics/overviewTab'
import LeaderboardTab from 'presentation/page_components/collection-analytics/leaderboardTab'
import ActivityTab from 'presentation/page_components/collection-analytics/activityTab'
import CollectionAnalyticsCard from 'presentation/page_components/collection-analytics/collectionAnalyticsCard'
import ButtonTabs from 'presentation/components/button-tabs'
import Collection from 'domain/models/collection'
import CollectionUseCase from 'presentation/use_cases/collection_use_case'
import { analyticCards } from 'presentation/controllers/mockData/collection_analytics_mock_data'

const collectionUseCase = new CollectionUseCase()

export const collectionAnalyticTabs = [
  {
    name: 'Overview',
    slug: '',
  },
  {
    name: 'NFT & Rarity',
    slug: 'nftrarity',
  },
  {
    name: 'Holder',
    slug: 'holder',
  },
  {
    name: 'Leaderboard',
    slug: 'leaderboard',
  },
  {
    name: 'Activity',
    slug: 'activity',
  },
]

const CollectionAnalytics: NextPage = ({ initTab, collectionSlug }: any) => {
  const router = useRouter()
  const [errorCode , setErrorCode] = useState<number>(0)
  const [activeTab, setActiveTab] = useState<string>(initTab)
  const [collection, setCollection] = useState<Collection>()
  const [isFetchingCollection, setIsFetchingCollection] = useState<boolean>(true)

  const fetchCollectionDetail = async () => {
    setIsFetchingCollection(true)
    collectionUseCase.findBySlug(collectionSlug).then((res) => {
      setCollection(res)
    }).catch(() => {
      setErrorCode(404)
    })
    setIsFetchingCollection(false)
  }

  const changeRequestTab = (tab: string) => {
    router.push({
      query: { ...router.query, tab },
    }, undefined, { shallow: true })

    setActiveTab(tab)
  }

  useEffect(() => {
    fetchCollectionDetail()
  }, [])

  if (errorCode === 404) {
    router.push('/404')
  }

  return (
    <>
      <Head>
        <title>Collection Analytics - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>
      <main>
        {!isFetchingCollection && (
          <Container className="pt-10">
            <div className="container-lg">
              <CollectionAnalyticsCard collection={{...collection, analyticCards}} />
              <ButtonTabs
                tabs={collectionAnalyticTabs}
                onChangeTab={changeRequestTab}
                activeTab={activeTab}
                className="mt-30p mb-22p flex flex-wrap justify-center lg:flex-nowrap gap-2 md:gap-4 xl:gap-5"
              />
            </div>

            {activeTab === '' && (
              <OverviewTab collectionId={collection?.id} />
            )}
            {activeTab === 'nftrarity' && (
              <RarityTab collectionId={collection?.id} />
            )}
            {activeTab === 'holder' && (
              <AnalyticsHolder collectionId={collection?.id} />
            )}
            {activeTab === 'leaderboard' && (
              <LeaderboardTab
                collectionId={collection?.id}
                showHeadline={false}
              />
            )}
            {activeTab === 'activity' && (
              <ActivityTab collectionId={collection?.id} />
            )}
          </Container>
        )}
        <Welcome/>
      </main>
    </>
  )
}

export async function getServerSideProps (context: any) {
  let initTab = ''
  const { id } = context.params

  if (context.query.tab) {
    initTab = context.query.tab
  }
  try {
    return {
      props: {
        initTab,
        collectionSlug: id,
      },
    }
  } catch (error) {
    return {
      notFound: true,
    }
  }
}

export default CollectionAnalytics
