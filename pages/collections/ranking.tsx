import { Query } from 'domain/repositories/category_repository/query'
import PeriodRange from 'domain/repositories/collection_daily_stat_repository/period_range'
import { Query as CollectionRankingQuery } from 'domain/repositories/collection_ranking/query'
import Chain from 'domain/value_objects/chain'
import { pageSizes } from 'lib/hook/customHook'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import { NextPage } from 'next'
import Head from 'next/head'
import Container from 'presentation/components/container'
import Heading from 'presentation/components/heading'
import Pagination from 'presentation/components/pagination'
import Select from 'presentation/components/select'
import PeriodRangesSelect from 'presentation/components/select/period-ranges-select'
import LoadingData from 'presentation/components/skeleton/loading'
import Table from 'presentation/components/table'
import CollectionTableItem from 'presentation/components/table/collection-item'
import CategoryUseCase from 'presentation/use_cases/category_use_case'
import CollectionRankingUseCase from 'presentation/use_cases/collection_ranking_use_case'
import { useEffect, useState } from 'react'
import { FilterParams } from 'types/type'
import { NetworkStateProvider } from 'presentation/providers/network_state_provider'
const periodRanges = PeriodRange.getResourceArray()

//Todo remove mock data
import { rankingData as rankingMockData } from 'presentation/controllers/mockData/ranking_mock_data'
import { render } from 'nprogress'
//End todo remove mock data

const categoryUseCase = new CategoryUseCase()

const rankingHead = [
  {
    title: '#',
    key: 'rank',
  },
  {
    title: 'Collection',
    key: 'collection',
    render: (record: any) => <CollectionTableItem record={record} />,
  },
  {
    title: 'Volume',
    key: 'volume',
  },
  // {
  //   title: 'Floor Price',
  //   key: 'floorPrice',
  // },
  {
    title: 'Owners',
    key: 'owners',
  },
  {
    title: 'Items',
    key: 'items',
  },
]

const Ranking: NextPage = () => {
  // Add NetworkStateProvider
  const networkStateProvider = NetworkStateProvider((state) => ({
    network: state.network,
  }))
  
  // Add state to track if network is ready
  const [isNetworkReady, setIsNetworkReady] = useState<boolean>(false)
  
  // Add state to track previous chainId to detect changes
  const [prevChainId, setPrevChainId] = useState<string>('')

  let PageSize = pageSizes.pageSize20
  const initParams: FilterParams = {
    periodRange: 'lastNinetyDays',
    categoryId: '',
    chain: '',
    page: 1,
    limit: PageSize,
    chainId: '',
  }

  const [currentPage, setCurrentPage] = useState(1)
  const [currentLimit, setCurrentLimit] = useState(PageSize)
  const [rankingData, setRankingData] = useState<any>([])
  const [total, setTotal] = useState(0)
  const { filterParams, changeFilterParam, setFilterParams } =
    useChangeFilterParams(initParams)
  const [isFetching, setIsFetching] = useState(true)
  const [filterChains, setFilterChains] = useState<Chain[]>([])
  const [isFilterChainsReady, setIsFilterChainsReady] = useState(false)
  const [filterCategories, setFilterCategories] = useState<any>([])
  const [isFilterCategoriesReady, setIsFilterCategoriesReady] = useState(false)
  const collectionRankingUseCase = new CollectionRankingUseCase()
  // Monitor network state changes and reset page when chainId changes
  useEffect(() => {
    if (networkStateProvider.network !== undefined) {
      setIsNetworkReady(true)
      
      const currentChainId = networkStateProvider.network?.chainId || ''
      
      // Only reset if chainId actually changed and is not the initial load
      if (prevChainId !== currentChainId) {
        // Reset page to 1 when chainId changes
        setCurrentPage(1)
        changeFilterParam('chainId', currentChainId, false)
        changeFilterParam('page', 1, false)
      }
      
      // Update previous chainId
      setPrevChainId(currentChainId)
    }
  }, [networkStateProvider.network])

  const fetchRanking = async () => {
    // Only proceed if network is ready
    if (!isNetworkReady) return
    
    setIsFetching(true)
    console.log('filterParams',filterParams)
    
    // Get chainId from network provider
    const chainId = networkStateProvider.network?.chainId || ''
    
    const query = new CollectionRankingQuery({
      ...filterParams,
      chainId: chainId, // Add chainId to query
    })
    
    await collectionRankingUseCase
      .totalCount(query)
      .then((res) => {
        setTotal(res)
      })
      .catch((_error : any) => {
        setTotal(0)
      })
    
    await collectionRankingUseCase
      .search(query)
      .then((res) => {
        const page = filterParams?.page ?? 1
        const limit = filterParams?.limit ?? pageSizes.pageSize20
        const rs : any = res?.map((item: any, index: number) => {
          return {
            rank: (page - 1) * limit + index + 1,
            collection: item,
            volume: item?.rankingVolume
              ? Number(item?.rankingVolume).toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 6 })
              : '--',
            owners: item?.numOwners,
            items: item?.numAssets,
          }
        }) || []
        setRankingData(rs)
      })
      .catch((_error : any) => {
        console.log('🚀 ~ fetchRanking ~ _error:', _error)
        setRankingData([])
      })
    setIsFetching(false)
  }

  const formatChains = () => {
    const chains = Chain.getResourceArray().map((chain: any) => {
      return Chain.fromName<Chain>(chain.name)
    })
    // Define the "All Chains" item
    const resource = {
      chainId: '',
      currency: '',
      id: '',
      label: 'All Chains',
      name: '',
    }
    const allChainsItem = new Chain(resource)
  
    // Add "All Chains" item to the beginning of the array
    chains.unshift(allChainsItem)
    setFilterChains(chains)
    setIsFilterChainsReady(true)
  }

  const formatCategories = async () => {
    const query = new Query()
    await categoryUseCase
      .search(query)
      .then((res) => {
        const data = res.map((category) => {
          return {
            id: category.id,
            name: category.name,
            value: category.id,
          }
        })
        const allCategory = {
          id: '',
          name: 'All Categories',
          value: '',
        }
        data.unshift(allCategory)
        setFilterCategories(data)
      })
      .catch((err) => {
        setFilterCategories([])
      })
    setIsFilterCategoriesReady(true)
  }

  useEffect(() => {
    if (isFilterCategoriesReady && isNetworkReady) {
      fetchRanking()
    }
  }, [filterParams, isFilterCategoriesReady, isNetworkReady])

  useEffect(() => {
    formatCategories()
    formatChains()
  }, [])

  return (
    <>
      <Head>
        <title>Ranking - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>
      <main>
        <Container className="pt-9">
          <div className="text-center">
            <Heading className="text-dark-primary dark:text-white">
              Top NFTs
            </Heading>
            <p className="mt-4 text-gray-1 dark:text-gray-3 text-stroke-thin">
              A wholesome farm owner in funbig. Upcoming gallery solo show in
              USA
            </p>
          </div>
          <div className="container-lg mt-9">
            <div className="w-full flex flex-wrap md:flex-nowrap gap-y-4 justify-center md:justify-end space-x-5">
              <PeriodRangesSelect
                periodRanges={periodRanges}
                defaultSelected={periodRanges[4]}
                onSelect={(option) => {
                  console.log('🚀 ~ option:', option)
                  changeFilterParam('periodRange', option.value)
                }}
              />
              {isFilterCategoriesReady && (
                <Select
                  options={filterCategories}
                  defaultSelected={filterCategories[0]}
                  onSelect={(option) =>
                    changeFilterParam('categoryId', option.value)
                  }
                  className="shrink-0"
                />
              )}
              {/* {isFilterChainsReady && (
                <Select
                  options={filterChains}
                  defaultSelected={filterChains[0]}
                  isBlockchainFilter={true}
                  onSelect={(option) => {
                    console.log('🚀 ~ option:', option?.getName())
                    changeFilterParam('chain', option?.getName(), true)
                  }
                   
                  }
                  className="shrink-0"
                />
              )} */}
            </div>
            {isFetching ? (
              <LoadingData />
            ) : (
              <div className="mt-5 rounded-20 border border-gray-3 text-dark-primary">
                <div className="w-full overflow-x-auto pb-3">
                  <Table
                    head={rankingHead}
                    data={rankingData}
                    className="ranking-table"
                  />
                </div>
                <div className="px-9 lg:mt-0.5 mt-5 pb-9">
                  <Pagination
                    currentPage={currentPage}
                    totalCount={total}
                    pageSize={currentLimit}
                    onPageChange={(page: number) => {
                      setCurrentPage(page)
                      setFilterParams({ ...filterParams, page })
                      console.log('🚀 ~ filterParams:', filterParams)
                    }}
                    onChangePageSize={(limit: number) => {
                      setCurrentPage(1)
                      setCurrentLimit(limit)
                      setFilterParams({ ...filterParams, limit, page: 1 })
                    }}
                  />
                </div>
              </div>
            )}
          </div>
        </Container>
      </main>
    </>
  )
}

export default Ranking
