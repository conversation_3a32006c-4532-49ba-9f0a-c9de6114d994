import { NextPage } from 'next'
import Head from 'next/head'
import Link from 'next/link'
import Image from 'next/image'
import Heading from 'presentation/components/heading'
import Container from 'presentation/components/container'
import DropdownItem from 'presentation/components/dropdown/dropdown-item'
import ShowMore from 'presentation/components/show-more'
import { useAuthentication } from 'lib/hook/auth_hook'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'
import UserUseCase from 'presentation/use_cases/user_use_case'

const userUseCase = new UserUseCase()

const Wallet: NextPage = () => {
  const { login } = useAuthentication()
  const authStateProvider = AuthStateProvider((state) => ({
    init: state.init,
  }))
  const handleConnectWallet = async () => {
    await login()
  }
  return (
    <>
      <Head>
        <title>Asset Item - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>
      <main>
        <Container className="pt-16 xl:pb-8">
          <div className="text-center md:w-[637px] mx-auto">
            <Heading>Connect Your Wallet</Heading>
            <p className="mt-4 md:mt-7 text-sm md:text-2xl md:text-left text-dark-primary dark:text-white">
              Connect with one of our available
              <Link href="/wallet" className="text-primary-2">
                {' '}
                wallet
              </Link>
              providers or create a new one.
            </p>
          </div>
          <div className="container-lg mt-6 md:mt-10">
            <div className="container-lg">
              <ShowMore
                showMoreText="Show more options"
                height="132px"
                arrowPosition="right"
                textClass="text-md text-gray-1 dark:text-gray-3 text-stroke-regular"
                className="overflow-y-hidden"
                showMoreClass="flex items-center justify-center mt-12 text-dark-primary"
              >
                <div className="flex flex-wrap justify-center gap-4 md:gap-[33px] pb-4">
                  {[...Array(15)].map((e, i) => (
                    <DropdownItem
                      key={i}
                      icon="metamask"
                      iconElemment={
                        <Image
                          src={'/asset/icons/metamask.png'}
                          alt=""
                          className="transition"
                          width={18}
                          height={18}
                        />
                      }
                      title="MetaMask"
                      className="pl-4 pr-5 py-[6px] font-bold rounded-full shadow-normal bg-white"
                      onClick={handleConnectWallet}
                    />
                  ))}
                </div>
              </ShowMore>
            </div>
          </div>
        </Container>
      </main>
    </>
  )
}

export default Wallet
