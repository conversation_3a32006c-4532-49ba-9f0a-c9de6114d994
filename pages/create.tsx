/* eslint-disable react/no-unescaped-entities */
import Head from 'next/head'
import { useState, useEffect } from 'react'
import MainBannerUseCase from 'presentation/use_cases/main_banner_use_case'
import { Query } from 'domain/repositories/main_banner_repository/query'
import MainBanner from 'domain/models/main_banner'
import Link from 'next/link'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Navigation, Pagination, Autoplay } from 'swiper'
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'
import Image from 'next/image'
import RequireConnect from 'presentation/components/require-connect'
import MeUseCase from 'presentation/use_cases/me_use_case'

const useCase = new MainBannerUseCase()
const meUseCase = new MeUseCase()

const CreatePage = () => {
  const [mainBanners, setMainBanners] = useState<MainBanner[]>([])
  const [isFetching, setIsFetching] = useState<boolean>(true)
  const [isConnected, setIsConnected] = useState<boolean>(false)

  const fetchMainBanners = () => {
    setIsFetching(true)
    const query = new Query()
    useCase.search(query)
      .then((banners: MainBanner[]) => {
        console.log('🚀 ~ .then ~ banners:', banners)
        setMainBanners(banners)
        setIsFetching(false)
      })
  }

  const fetchMe = async () => {
    await meUseCase
      .loginStatus()
      .then((res) => {
        setIsConnected(res.isLoggedIn)
      })
      .catch((_error) => {
        setIsConnected(false)
      })
  }

  useEffect(() => {
    fetchMainBanners()
    fetchMe()
  }, [])

  const renderContent = () => (
    <main className="container-df py-8">
      <div className="flex flex-col md:flex-row gap-8 items-center">
        {/* Left side - Create Options */}
        <div className="w-full md:w-1/2 space-y-6">
          <h1 className="text-4xl font-bold mb-8 flex items-center">
            <Image className='mr-2 relative -top-2' src="/asset/images/quill_square.png" alt="create-logo" width={56} height={56} />
            Create
          </h1>
          
          {/* Collection Option */}
          <Link href="/collections/create" className="block">
            <div className="bg-white dark:bg-dark-2 rounded-2xl p-6 shadow-sm hover:shadow-md transition-shadow cursor-pointer group">
              <div className="flex items-start justify-between">
                <div>
                  <div className="text-2xl font-bold mb-4">Collection</div>
                  <p className="text-gray-600 dark:text-gray-300">
                  Start your journey by creating a new NFT collection. Group your NFTs under one collection to showcase your unique items. 
                  Once created, you can add NFTs to it and list them for sale when you're ready. Let your collection tell a story and share it with the world.
                  </p>
                </div>
                <div className="text-2xl transform transition-transform duration-300 group-hover:translate-x-2">→</div>
              </div>
            </div>
          </Link>

          {/* Item Option */}
          <Link href="/assets/create" className="block">
            <div className="bg-white dark:bg-dark-2 rounded-2xl p-6 shadow-sm hover:shadow-md transition-shadow cursor-pointer group">
              <div className="flex items-start justify-between">
                <div>
                  <div className="text-2xl font-bold mb-4">Item</div>
                  <p className="text-gray-600 dark:text-gray-300">
                    Create a new NFT item or add an NFT to an existing one. 
                    Your items will display immediately. List for sale when you're ready.
                  </p>
                </div>
                <div className="text-2xl transform transition-transform duration-300 group-hover:translate-x-2">→</div>
              </div>
            </div>
          </Link>
          {/* import your NFT Option */}
          <Link href="/import-your-nft" className="block">
            <div className="bg-white dark:bg-dark-2 rounded-2xl p-6 shadow-sm hover:shadow-md transition-shadow cursor-pointer group">
              <div className="flex items-start justify-between">
                <div>
                  <div className="text-2xl font-bold mb-4">Import your NFTs</div>
                  <p className="text-gray-600 dark:text-gray-300">
                  Bring your existing NFTs into NFT Land to expand your portfolio. Once imported, you can list them for sale, manage your collection, or utilize them for various marketplace features seamlessly.
                  </p>
                </div>
                <div className="text-2xl transform transition-transform duration-300 group-hover:translate-x-2">→</div>
              </div>
            </div>
          </Link>
        </div>

        {/* Right side - Banner Slider */}
        <div className="w-full md:w-1/2 h-[600px] relative">
          {!isFetching && mainBanners.length > 0 && (
            <Swiper
              modules={[Pagination, Autoplay]}
              spaceBetween={0}
              slidesPerView={1}
              pagination={{ clickable: true }}
              autoplay={{ delay: 5000 }}
              loop={true}
              className="w-full h-full rounded-2xl overflow-hidden"
            >
              {mainBanners.map((item, index) => (
                <SwiperSlide key={index}>
                  <div className="relative w-full h-full">
                    <Image
                      src={item?.banner?.url || '/placeholder-image.jpg'}
                      alt={item?.title || 'Banner'}
                      fill
                      className="object-cover"
                    />
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>
          )}
          {isFetching && (
            <div className="w-full h-full bg-gray-100 dark:bg-dark-2 rounded-2xl animate-pulse" />
          )}
        </div>
      </div>
    </main>
  )

  return (
    <>
      <Head>
        <title>Create NFTs | Quill</title>
        <meta name="description" content="Create your NFTs on Quill" />
      </Head>
      {/* {isConnected ? renderContent() : <RequireConnect />} */}
      {renderContent()}
    </>
  )
}

export default CreatePage
