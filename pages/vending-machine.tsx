import React, { useEffect, useState, useRef } from 'react'
import { NextPage } from 'next'
import Head from 'next/head'
import { useRouter } from 'next/router'
import { PackageType, PackageItem } from 'domain/entities/vending_machine'
import VendingMachineCategoryUseCase from 'presentation/use_cases/vending_machine_category_use_case'
import VendingMachinePackUseCase from 'presentation/use_cases/vending_machine_pack_use_case'
import { Query as CategoryQuery } from 'domain/repositories/vending_machine_category_repository/query'
import { Query as PackQuery } from 'domain/repositories/vending_machine_pack_repository/query'
import PackageTypeTabs from 'presentation/page_components/vending-machine/PackageTypeTabs'
import PackageSection from 'presentation/page_components/vending-machine/PackageSection'
import { NetworkStateProvider } from 'presentation/providers/network_state_provider'
import { menu } from 'lib/header'
import Image from 'next/image'
import { PACKAGES_HOME_LIMIT } from 'lib/vendingMachineConfig'
const categoryUseCase = new VendingMachineCategoryUseCase()
const packUseCase = new VendingMachinePackUseCase()

const VendingMachinePage: NextPage = () => {
  const [packageTypes, setPackageTypes] = useState<PackageType[]>([])
  const [packagesByType, setPackagesByType] = useState<Record<string, PackageItem[]>>({})
  const [packageTotals, setPackageTotals] = useState<Record<string, number>>({})
  const [isLoading, setIsLoading] = useState(true)
  const [isNetworkInitialized, setIsNetworkInitialized] = useState(false)
  const sectionsRef = useRef<Record<string, HTMLElement | null>>({})
  const router = useRouter()
  
  // Get current network from provider
  const networkStateProvider = NetworkStateProvider((state) => ({
    network: state.network,
  }))
  
  // Initialize network state
  useEffect(() => {
    NetworkStateProvider.getState().init()
    setIsNetworkInitialized(true)
  }, [])
  
  const fetchData = async () => {
    try {
      setIsLoading(true)
      
      // Load categories from API (equivalent to package types)
      const categoryQuery = new CategoryQuery({
        page: 1,
        limit: 1000, // Use high limit to get all categories
        enable: true,
      })
      
      const categoryResult = await categoryUseCase.list(categoryQuery)
      
      // Map categories to PackageType format
      const types: PackageType[] = categoryResult.items.map((category) => ({
        id: category.id!.toString(),
        title: category.name,
        isEnabled: true,
      }))
      
      setPackageTypes(types)
      
      // Fetch packs for each category
      const packagesMap: Record<string, PackageItem[]> = {}
      const totalsMap: Record<string, number> = {}
      
      await Promise.all(
        types.map(async (type) => {
          // Load packs from API filtered by category
          const packQuery = new PackQuery({
            page: 1,
            limit: PACKAGES_HOME_LIMIT, 
            categoryId: type.id,
            isEnabled: true,
          })

          const packResult = await packUseCase.list(packQuery)
          console.log('🚀 ~ fetchData ~ packResult:', packResult)
          
          // Store total count
          totalsMap[type.id] = packResult.total
          
          // Map packs to PackageItem format
          const packages: PackageItem[] = packResult.items.map((pack) => ({
            id: parseInt(pack.id!),
            title: pack.name,
            price: pack.price,
            image: pack.imageUrl,
            typeId: pack.categoryId,
            slug: pack.id!.toString(), // Using pack id as slug
          }))
          console.log('🚀 ~ fetchData ~ packages:', packages)
          
          packagesMap[type.id] = packages
        })
      )
      
      setPackageTotals(totalsMap)
      
      setPackagesByType(packagesMap)
    } catch (error) {
      console.error('Error fetching vending machine data:', error)
    } finally {
      setIsLoading(false)
    }
  }
  
  useEffect(() => {
    if (isNetworkInitialized) {
      fetchData()
    }
  }, [networkStateProvider.network, router, isNetworkInitialized])

  // Scroll to section when tab is clicked
  const handleTabClick = (typeId: string) => {
    const section = sectionsRef.current[typeId]
    if (section) {
      // Find the scrollable container
      const scrollContainer = document.getElementById('screen-main')
      if (scrollContainer) {
        // Calculate position relative to the scroll container
        const containerRect = scrollContainer.getBoundingClientRect()
        const sectionRect = section.getBoundingClientRect()
        // Account for header height (about 100px)
        const offsetTop = sectionRect.top - containerRect.top - 100 + scrollContainer.scrollTop
        
        // Scroll the container element
        scrollContainer.scrollTo({
          top: offsetTop,
          behavior: 'smooth',
        })
      }
    }
  }

  // Store refs to section elements
  const setSectionRef = (id: string, element: HTMLElement | null) => {
    sectionsRef.current[id] = element
  }

  return (
    <>
      <Head>
        <title>Vending Machine | NFT Land</title>
        <meta name="description" content="Explore NFT vending machines" />
      </Head>

      <div className="container-df my-6 md:my-12 overflow-hidden relative z-10">
        {/* Background image that takes up 45% of screen width */}
        <div className="absolute top-[2%] left-[2%] w-[45%] h-[623px] z-0 pointer-events-none scale-150"> 
          <Image 
            src="/asset/images/vending-machine/background-top.png"
            alt="Background"
            className="object-contain"
            height={623}
            width={1000}
          />
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <>
            {/* Package Type Tabs */}
            <PackageTypeTabs 
              packageTypes={packageTypes} 
              onTabClick={handleTabClick} 
            />
            
            {/* Package Sections */}
            {packageTypes.map((type, index) => (
              <div key={type.id} id={type.id} ref={(el) => setSectionRef(type.id, el)}>
                <PackageSection 
                  id={type.id}
                  title={type.title}
                  packages={packagesByType[type.id] || []}
                  total={packageTotals[type.id] || 0}
                  index={index}
                />
              </div>
            ))}
          </>
        )}
      </div>
    </>
  )
}

export default VendingMachinePage 