import { NextPage } from 'next'
import { useEffect, useState } from 'react'
import Head from 'next/head'
import Container from 'presentation/components/container'

import RequireConnect from 'presentation/components/require-connect'
import MyProfileSection from 'presentation/page_components/account-details/userProfileSection'
import CollectedTab from 'presentation/page_components/account-details/collectedTab'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'
import User from 'domain/models/user'

const MyAccount: NextPage = () => {
  const [owner, setOwner] = useState<User>()
  const [isMe, setIsMe] = useState(false)
  const [ready, setReady] = useState(false)
  const [loading, setLoading] = useState(true)

  const authStateProvider = AuthStateProvider((state) => ({
    me: state.me,
  }))

  useEffect(() => {
    if (authStateProvider.me && authStateProvider.me.id) {
      const owner = authStateProvider.me
      setOwner(owner)
      setIsMe(true)
    }
  }, [authStateProvider.me])

  useEffect(() => {
    setLoading(true)
    if (owner?.id) {
      setReady(true)
    } else {
      setReady(false)
    }
    setLoading(false)
  }, [owner])

  return (
    <>
      <Head>
        <title>Account - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>

      <main>
        {!loading && (
          <Container className="pt-9">
            {ready ? (
              <>
                <MyProfileSection
                  activeTab={''}
                  owner={owner}
                  isMe={isMe}
                  path="/account"
                />
                <CollectedTab userId={owner?.id!} isMe={isMe} />
              </>
            ) : (
              <RequireConnect />
            )}
          </Container>
        )}
      </main>
    </>
  )
}

export default MyAccount
