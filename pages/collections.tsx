import { Query as CategoryQuery } from 'domain/repositories/category_repository/query'
import { Query as CollectionQuery } from 'domain/repositories/collection_repository/query'
import Marketplace from 'domain/value_objects/marketplace'
import { NextPage } from 'next'
import Head from 'next/head'
import CollectionComponent from 'presentation/components/collection'
import NoData from 'presentation/components/no-data'
import PaginationDot from 'presentation/components/pagination/pagination-dot'
import Select from 'presentation/components/select'
import SkeletonAsset from 'presentation/components/skeleton/assets'
import ToggleWrap from 'presentation/components/toggle-wrap'
import ToggleVerticalFilterCategories from 'presentation/components/toggle-wrap/toggle-vertical-filter-categories'
import ToggleVerticalFilterMarketplaces from 'presentation/components/toggle-wrap/toggle-vertical-filter-marketplaces'
import ToggleVerticalFilterPrice from 'presentation/components/toggle-wrap/toggle-vertical-filter-price'
import ToggleVerticalFilterStatues from 'presentation/components/toggle-wrap/toggle-vertical-filter-statues'
import ToggleVerticalSearchCollection from 'presentation/components/toggle-wrap/toggle-vertical-search-collections'
import CategoryUseCase from 'presentation/use_cases/category_use_case'
import CollectionUseCase from 'presentation/use_cases/collection_use_case'
import { useCallback, useEffect, useState } from 'react'
import ToggleVertical from 'presentation/components/toggle-wrap/toggle-vertical'
import Category from 'domain/models/category'
import Collection from 'domain/models/collection'
import FilterStatus from 'domain/repositories/asset_repository/filter_status'
import SortBy from 'domain/repositories/collection_repository/sort_by'
import Chain from 'domain/value_objects/chain'
import { pageSizes } from 'lib/hook/customHook'
import Link from 'next/link'
import Image from 'next/image'
import Switch from 'presentation/components/switch'
import { getChainName } from 'lib/utils'
import { NetworkStateProvider } from 'presentation/providers/network_state_provider'
const categoryUseCase = new CategoryUseCase()
const collectionUseCase = new CollectionUseCase()
const marketplaces = Marketplace.getResourceArray().map((marketplace) => {
  return Marketplace.fromName<Marketplace>(marketplace.name)
})
const filterStatus = FilterStatus.getResourceArray()
const sortList = SortBy.getResourceArray().map((sort: any) => {
  return {
    id: sort.id,
    name: sort.label,
    value: sort.name,
  }
})
const PageSize = pageSizes.pageSize30

const Collections: NextPage = () => {
  // Add NetworkStateProvider
  const networkStateProvider = NetworkStateProvider((state) => ({
    network: state.network,
  }))
  
  // Add state to track if network is ready
  const [isNetworkReady, setIsNetworkReady] = useState<boolean>(false)
  
  // Add state to track previous chainId to detect changes
  const [prevChainId, setPrevChainId] = useState<string>('')
  
  const [isFetchingCollections, setIsFetchingCollections] =
    useState<boolean>(false)
  const [isFilterChainsReady, setIsFilterChainsReady] =
    useState<boolean>(false)
  const [filterChains, setFilterChains] = useState<Chain[]>([])
  const [collections, setCollections] = useState<Collection[]>([])
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [totalCollection, setTotalCollection] = useState<number>(0)
  const [categories, setCategories] = useState<Category[]>([])
  const [showFilterStatuses, setShowFilterStatuses] = useState<boolean>(false)
  const [showSortList, setShowSortList] = useState<boolean>(false)
  const [showCategories, setShowCategories] = useState<boolean>(false)
  const [showMarketplaces, setShowMarketplaces] = useState<boolean>(false)
  const [filter, setFilter] = useState<any>()
  const [chain] = useState<Chain>(Chain.ethereum())
  const [ownerId] = useState<string>('')
  const [sortedBy, setSortedBy] = useState<SortBy>(SortBy.recentlyListed())
  const [filterCategories, setFilterCategories] = useState<any>([])

  // Monitor network state changes and reset page when chainId changes
  useEffect(() => {
    if (networkStateProvider.network !== undefined) {
      setIsNetworkReady(true)
      
      const currentChainId = networkStateProvider.network?.chainId || ''
      
      // Only reset if chainId actually changed and is not the initial load
      if (prevChainId !== currentChainId) {
        // set timeout to make sure the page is reset after the network state has been updated
        setTimeout(() => {
          setCurrentPage(1)
        }, 100)
      }
      
      // Update previous chainId
      setPrevChainId(currentChainId)
    }
  }, [networkStateProvider.network?.chainId])

  const fetchCollections = useCallback(async () => {
    // Only proceed if network is ready
    if (!isNetworkReady) return
    
    setIsFetchingCollections(true)
    console.log(filter,'filter')
    
    // Get chainId from network provider
    const chainId = networkStateProvider.network?.chainId || ''
    
    const query = new CollectionQuery({
      chains: filter?.chains || [],
      sortBy: sortedBy,
      limit: PageSize,
      page: currentPage,
      categoryIds: filterCategories,
      chainId: chainId, // Add chainId to query
      filterStatuses: filter?.filterStatuses || [],
      priceTokenUnit: filter?.priceTokenUnit || null,
      minPrice: filter?.minPrice || null,
      maxPrice: filter?.maxPrice || null,
      collectionIds: filter?.collectionIds || [],
    })
    await collectionUseCase
      .totalCount(query)
      .then((res) => {
        setTotalCollection(res)
      })
      .catch((_error) => {})
    await collectionUseCase
      .search(query)
      .then((res) => {
        setCollections(res)
      })
      .catch((_error) => {
        setCollections([])
      })
    setIsFetchingCollections(false)
  }, [
    currentPage,
    chain,
    ownerId,
    sortedBy,
    filterCategories,
    PageSize,
    filter,
    networkStateProvider.network?.chainId, // Add network chainId as dependency
    isNetworkReady, // Add isNetworkReady as dependency
  ])

  const getStatusArray = (statusArray: any, name: string, value: any) => {
    if (!statusArray) {
      statusArray = []
    }
    if (value) {
      if (!statusArray.includes(name)) {
        statusArray.push(name)
      }
    } else { 
      statusArray = statusArray.filter((status : string) => status !== name)
    }
    return statusArray
  }
  const updateFilterParams = (name: string, status: string, value: any) => {
    console.log('🚀 ~ updateFilterParams ~ value:', value)
    console.log('🚀 ~ updateFilterParams ~ status:', status)
    console.log('🚀 ~ updateFilterParams ~ name:', name)
    // reset current page
    setCurrentPage(1)
    setFilter({ ...filter, ['page']: 1 })
    if (name === 'categoryIds') {
      if (value) {
        setFilterCategories([...filterCategories, status])
      } else {
        setFilterCategories(
          filterCategories.filter((item: string) => item !== status)
        )
      }
    } else if (name === 'filterStatuses') {
      setFilter({ ...filter, [status]: value, [name]: getStatusArray(filter?.filterStatuses || [],status, value) })
    } else if (name === 'priceTokenUnit' || name === 'minPrice' || name === 'maxPrice') {
      setFilter({ ...filter, [name]: status })
    } else if (name === 'collectionIds') {
      setFilter({ ...filter, [name]: [status] })
    }else if (name === 'marketplaces') {
      setFilter({ ...filter, [name]: status })
    }else if (name === 'chains') {
      setFilter({ ...filter,  [name]: getStatusArray(filter?.chains || [],status, value) })
    }
  }
  const formatChains = () => {
    const chains: Chain[] = Chain.getResourceArray().map((chain: any) => {
      return Chain.fromName(chain.name)
    })
    setFilterChains(chains)
    setIsFilterChainsReady(true)
  }
  useEffect(() => {
    setShowFilterStatuses(true)
    setShowSortList(true)
    setShowMarketplaces(true)
    formatChains()
    if (categories.length === 0) {
      const query = new CategoryQuery()
      categoryUseCase.search(query).then((res) => {
        setCategories(res)
      })
    } else {
      setShowCategories(true)
    }
  }, [])

  useEffect(() => {
    if (categories.length > 0) {
      setShowCategories(true)
    }
  }, [categories])

  useEffect(() => {
    if (isNetworkReady) {
      fetchCollections()
    }
  }, [
    fetchCollections,
    isNetworkReady, // Add isNetworkReady as dependency
  ])

  return (
    <>
      <Head>
        <title>Collections - Quill</title>
        <meta name="description" content="NFT marketplace" />
        <link rel="icon" href="/favicon.ico" />
      </Head>
      <main>
        <div className="container-df assets-page pt-10">
          <div className="product-layout">
            <div className="product-layout-filter">
              <ToggleWrap
                title="Filter"
                contentClass="border-t-0 overflow-hidden"
              >
                {/* {showFilterStatuses && (
                  <ToggleVerticalFilterStatues
                    defaultShow
                    filterStatues={filterStatus}
                    changeFilterParam={updateFilterParams}
                  />
                )}
                <ToggleVerticalFilterPrice
                  defaultShow
                  minPrice={''}
                  maxPrice={''}
                  changeFilterParam={updateFilterParams}
                /> */}
                <ToggleVerticalSearchCollection
                  defaultShow
                  isExact={false}
                  changeFilterParam={updateFilterParams}
                />
                {showCategories && (
                  <ToggleVerticalFilterCategories
                    defaultShow
                    categories={categories}
                    changeFilterParam={updateFilterParams}
                  />
                )}
                {/* {showMarketplaces && (
                  <ToggleVerticalFilterMarketplaces
                    defaultShow
                    marketplaces={marketplaces}
                    changeFilterParam={updateFilterParams}
                  />
                )} */}

                {/* Filter chains */}
                {/* {isFilterChainsReady && (
                  <ToggleVertical
                    toggleTitle="Chains"
                    defaultShow
                    contentWrapClass="rounded-b-lg"
                  >
                    <ul className="fist-mt-none">
                      {filterChains.map((chain: Chain, index: number) => (
                        <li
                          key={index}
                          className="mt-5 font-medium flex items-center justify-between"
                        >
                          <p className="flex items-center">
                            <span className="mr-3">{chain.getLabel()}</span>
                            <Image
                              src={chain.getIconPath() || ''}
                              width={25}
                              height={25}
                              alt={chain.getName()}
                            />
                          </p>
                          <Switch
                            onClick={(activeState) => {
                              updateFilterParams('chains', chain.getName(), activeState)
                            }}
                          />
                        </li>
                      ))}
                    </ul>
                  </ToggleVertical>
                )} */}
              </ToggleWrap>
            </div>
            <div className="xl:mr-14 mt-4 md:mt-0 w-full">
              <div className="mb-6 flex justify-between items-center w-full">
                {isFetchingCollections ? (
                  <p className="w-36 h-4 bg-gray-3 rounded-lg animate-pulse"></p>
                ) : (
                  <p className="text-gray-1 text-stroke-thin">
                    {totalCollection.toLocaleString()} results
                  </p>
                )}
                {/* <div>
                  {showSortList && (
                    <Select
                      options={sortList}
                      onSelect={(option) => {
                        setSortedBy(SortBy.fromName<SortBy>(option.value))
                      }}
                    />
                  )}
                </div> */}
              </div>
              <div
                className={`product-wrap collection-main ${
                  !isFetchingCollections &&
                  collections.length === 0 &&
                  'no-data'
                }`}
              >
                {isFetchingCollections ? (
                  [...Array(5)].map((_, id) => <SkeletonAsset key={id} />)
                ) : (
                  <>
                    {collections.length > 0 ? (
                      collections.map((collection: any, index: any) => (
                        <div key={index} className="collection">
                          <Link href={`/collections/${getChainName(collection.chainId)}/${collection.slug}`}>
                            <CollectionComponent
                              {...collection}
                              record={collection}
                              coinSymbolSize={15}
                            />
                          </Link>
                        </div>
                      ))
                    ) : (
                      <div className="w-full rounded-lg border flex justify-center items-center py-24">
                        <NoData />
                      </div>
                    )}
                  </>
                )}
              </div>
              {totalCollection > PageSize && !isFetchingCollections && (
                <div className="flex justify-center mt-5">
                  <PaginationDot
                    currentPage={currentPage}
                    totalCount={totalCollection}
                    pageSize={PageSize}
                    onPageChange={(page: number) => {
                      setCurrentPage(page)
                    }}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
    </>
  )
}

export default Collections
