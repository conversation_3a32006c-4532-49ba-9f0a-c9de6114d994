import { NextPage } from 'next'
import Head from 'next/head'
import Image from 'next/image'
import { useState, useEffect } from 'react'
import { dimensionOptions } from 'presentation/controllers/mockData/options'
import Container from 'presentation/components/container'
import Heading from 'presentation/components/heading'
import ToggleVertical from 'presentation/components/toggle-wrap/toggle-vertical'
import Select from 'presentation/components/select'
import ToggleWrap from 'presentation/components/toggle-wrap'
import Switch from 'presentation/components/switch'
import Tooltip from 'presentation/components/tooltip'
import Button from 'presentation/components/button'
import SvgIcon from 'presentation/components/svg-icon'
import Input from 'presentation/components/input'
import SelectLayer from 'presentation/page_components/generator/selectLayer'
import BadgeText from 'presentation/components/badge/badgeText'
import ProcessFlow from 'presentation/page_components/generator/processFlow'
import ProductGenerator from 'presentation/components/product/product-generator'
import PaginationDot from 'presentation/components/pagination/pagination-dot'
import RaritySettingModal from 'presentation/page_components/generator/raritySettingModal'
import AttributesModal from 'presentation/page_components/generator/attributesModal'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import MultimediaUseCase from 'presentation/use_cases/multimedia_use_case'

const multimediaUseCase = new MultimediaUseCase()

//Todo remove mock data
import {
  listTempData,
  layersTempData,
} from 'presentation/controllers/mockData/generator_mock-data'
import { pageSizes } from 'lib/hook/customHook'
import { toast } from 'react-toastify'
import Multimedia from 'domain/models/multimedia'
//End todo remove mock data

const PageSize = pageSizes.pageSize25

const Generator: NextPage = () => {
  const [showRaritySettingModal, setShowRaritySettingModal] =
    useState<boolean>(false)
  const [showAttributesModal, setShowAttributesModal] =
    useState<boolean>(false)
  const [isAdvanced, setIsAdvanced] = useState<boolean>(false)
  const [isFetchingLayers, setIsFetchingLayers] = useState<boolean>(true)
  const [isFetching, setIsFetching] = useState<boolean>(true)
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [total, setTotal] = useState<number>(0)
  const { filterParams, changeFilterParam } = useChangeFilterParams({
    page: 1,
    limit: PageSize,
  })
  const [newPropertyMultimedia, setNewPropertyMultimedia] =
    useState<Multimedia>()
  //TODO remove any type
  const [selectedRarityLayer, setSelectedRarityLayer] = useState<any>({})
  const [layersData, settLayersData] = useState<any>([])
  const [listData, settListData] = useState<any>([])
  const [selectedLayer, setSelectedLayer] = useState<any>(null)
  const [selectedItem, setSelectedItem] = useState<any>({})
  //TODO remove mock data

  const handleClickBadge = (layer: any) => {
    setSelectedRarityLayer(layer)
    setShowRaritySettingModal(true)
  }

  const handleClickLayerOption = (layer: any) => {
    setSelectedLayer(layer)
  }

  const handleClickItem = (item: any) => {
    setSelectedItem(item)
    if (!selectedLayer) {
      setShowAttributesModal(true)
    }
  }

  const uploadFile = async (file: any) => {
    if (file) {
      await multimediaUseCase
        .upload(file)
        .then((res) => {
          setNewPropertyMultimedia(res)
        })
        .catch((_error) => {
          toast.error('Failed to upload file')
        })
    }
  }

  const fetchListData = () => {
    setIsFetching(true)
    //TODO
    setTotal(listTempData.length)
    let data = listTempData.slice(
      (currentPage - 1) * PageSize,
      currentPage * PageSize
    )
    settListData(data)
    setTimeout(() => {
      setIsFetching(false)
    }, 500)
  }

  const fetchLayersData = () => {
    setIsFetchingLayers(true)
    //TODO
    settLayersData(layersTempData)
    setTimeout(() => {
      setIsFetchingLayers(false)
    }, 500)
  }

  const getAttributeDetail = (record: any, type: any, key: string) => {
    if (record) {
      const result = record.attributes?.find(
        (attribute: any) => attribute.type === type
      )
      switch (key) {
        case 'name':
          return result ? result.name : ''
        case 'thumbnail':
          return result ? result.thumbnail?.url : record.thumbnail?.url
        case 'percentage':
          return result ? result.percentage : ''
        default:
          return ''
      }
    }
    return ''
  }

  useEffect(() => {
    fetchListData()
  }, [filterParams, selectedLayer])

  useEffect(() => {
    fetchLayersData()
  }, [])

  return (
    <>
      <Head>
        <title>NFT Drops - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>
      <main>
        <Container className="pt-9">
          <div className="mb-30p md:w-3/4 mx-auto text-center">
            <Heading className="text-dark-primary dark:text-white italic">
              {isAdvanced
                ? 'NFT Collection Generator Advanced'
                : 'NFT Collection Generator'}
            </Heading>
            <p className="text-gray-1 dark:text-gray-3 text-stroke-thin mt-4">
              Generate your NFT Collection and deploy it to a blockchain.
              <br />
              We support images, gifs and videos and deploy to Ethereum, Polygon
              and more.
            </p>
          </div>

          <div className="lg:flex lg:justify-end">
            <div className="lg:w-3/4">
              <div className="flex justify-between flex-wrap gap-5">
                <div className="inline-flex gap-5 shrink-0 self-end lg:pl-12">
                  <p className="text-gray-1 dark:text-gray-3 text-stroke-thin">
                    Layer: {selectedLayer?.name || 'All'}
                  </p>
                  <p className="text-gray-1 dark:text-gray-3 text-stroke-thin">
                    {total.toLocaleString()} image(s)
                  </p>
                </div>
                <div className="flex items-center flex-wrap gap-5 lg:pr-10">
                  <div className="flex items-center">
                    <Switch
                      onClick={(active) => setIsAdvanced(active)}
                      className="mr-2"
                    />
                    <div className="flex items-center">
                      <p className="text-sm font-bold text-dark-primary dark:text-white">
                        Advanced
                      </p>
                      <Tooltip iconSize="w-4 h-4 -mt-1">Advanced</Tooltip>
                    </div>
                  </div>
                  <Button variant="dark" className="shrink-0 px-md">
                    <span className="mr-2">Preview</span>
                    <SvgIcon
                      name="eye"
                      className="w-5 h-5 stroke-none fill-white"
                    />
                  </Button>
                  <Button variant="light" className="shrink-0 px-md">
                    <span className="mr-2">Randomize</span>
                    <SvgIcon
                      name="reload"
                      className="w-5 h-5 stroke-none fill-primary-2 dark:fill-white"
                    />
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {isAdvanced && (
            <div className="container-lg w-full h-48 lg:h-80">
              <ProcessFlow />
            </div>
          )}

          <div className="generator-cont mt-22p">
            <div className="filter-left">
              <ToggleWrap contentClass="rounded-t-10">
                {!isFetchingLayers && (
                  <ToggleVertical
                    toggleTitle="Layers"
                    defaultShow
                    heightFit={true}
                    contentClass="toggle-generator"
                    contentWrapClass="rounded-t-10"
                    className="rounded-t-10"
                    toggleTitleWrapClass="rounded-t-10"
                  >
                    <SelectLayer
                      onClickBadge={handleClickBadge}
                      onClickLayerOption={handleClickLayerOption}
                      data={layersData}
                    />
                  </ToggleVertical>
                )}
                <ToggleVertical
                  toggleTitle="Collection"
                  defaultShow
                  contentClass="toggle-collection"
                  contentWrapClass="rounded-b-10"
                  toggleTitleWrapClassWhenClose="rounded-b-10"
                >
                  <div className="w-full text-dark-primary dark:text-white">
                    <div className="flex items-center font-medium">
                      <p className="font-medium">Collection Size</p>
                      <Tooltip iconSize="w-4 h-4">Collection Size</Tooltip>
                    </div>
                    <Input type="number" width="w-1/2" className="mt-10p" />
                  </div>

                  <div className="mt-5 text-dark-primary dark:text-white">
                    <div className="flex items-center">
                      <p className="font-medium">Dimension</p>
                      <Tooltip iconSize="w-4 h-4">Collection Size</Tooltip>
                    </div>
                    <div className="grid grid-cols-2 gap-10p mt-10p">
                      <Input type="number" className="no-arrow" />
                      <Input type="number" className="no-arrow" />
                    </div>
                  </div>

                  <Select
                    rounded
                    options={dimensionOptions}
                    arrowIconClass="stroke-none fill-primary-2"
                    className="w-full mt-5"
                    placeholder="Export to:"
                  />
                  <div className="grid grid-cols-2 gap-10p mt-30p">
                    <Button variant="light">Save</Button>
                    <Button variant="dark" href="/generator/create">
                      Next
                    </Button>
                  </div>
                  <button className="mt-5 ml-10">
                    <BadgeText size="xs" color="red">
                      Delete All
                    </BadgeText>
                  </button>
                </ToggleVertical>
              </ToggleWrap>
            </div>

            <div className="product-right">
              {!isFetching && (
                <>
                  <div className="w-full grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5">
                    {listData.map((item: any, i: number) => (
                      <ProductGenerator
                        key={i}
                        name={
                          selectedLayer
                            ? getAttributeDetail(
                              item,
                              selectedLayer.name,
                              'name'
                            )
                            : ''
                        }
                        imageSrc={
                          selectedLayer
                            ? getAttributeDetail(
                              item,
                              selectedLayer.name,
                              'thumbnail'
                            )
                            : item.thumbnail?.url
                        }
                        percentage={
                          selectedLayer
                            ? getAttributeDetail(
                              item,
                              selectedLayer.name,
                              'percentage'
                            )
                            : ''
                        }
                        closeable={
                          selectedLayer &&
                          selectedItem &&
                          selectedItem.id === item.id
                        }
                        onClick={() => handleClickItem(item)}
                      />
                    ))}
                  </div>
                  {total > PageSize && (
                    <div className="mt-6 flex justify-center">
                      <PaginationDot
                        currentPage={currentPage}
                        totalCount={total}
                        pageSize={PageSize}
                        onPageChange={(page: number) => {
                          setCurrentPage(page), changeFilterParam('page', page)
                        }}
                      />
                    </div>
                  )}
                </>
              )}

              <div className="w-2/3 mt-30p mx-auto text-center">
                <p className="font-medium text-dark-primary dark:text-white">
                  Click or drop images here! Image, Video, Audio, or 3D Model{' '}
                  <span className="text-red-1">*</span>
                </p>
                {/* Image */}
                <div className="relative w-full h-20 mt-10p">
                  <div className="absolute top-3 right-3">
                    <input
                      className="w-5 h-5 opacity-0 absolute"
                      type="file"
                      id="img"
                      name="logoImage"
                      accept="image/*"
                      onChange={(e: any) => uploadFile(e.target.files[0])}
                    />
                    <SvgIcon
                      name="plusCircle"
                      className="w-5 h-5 stroke-none fill-primary-2 cursor-pointer"
                    />
                  </div>
                  <div className="w-full h-20 rounded-10 bg-gray-5 dark:bg-blue-1">
                    {newPropertyMultimedia?.url && (
                      <Image
                        alt=""
                        fill
                        style={{ objectFit: 'cover' }}
                        className="rounded-10"
                        src={newPropertyMultimedia.url}
                      />
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Container>

        {showRaritySettingModal && (
          <RaritySettingModal
            isShow={showRaritySettingModal}
            onClose={() => setShowRaritySettingModal(false)}
            record={selectedRarityLayer}
          />
        )}

        {showAttributesModal && (
          <AttributesModal
            isShow={showAttributesModal}
            onClose={() => setShowAttributesModal(false)}
            record={selectedItem}
          />
        )}
      </main>
    </>
  )
}

export default Generator
