import Asset from 'domain/models/asset'
import Category from 'domain/models/category'
import FilterStatus from 'domain/repositories/asset_repository/filter_status'
import { Query as AssetQuery } from 'domain/repositories/asset_repository/query'
import SortBy from 'domain/repositories/asset_repository/sort_by'
import { Query as CategoryQuery } from 'domain/repositories/category_repository/query'
import Marketplace from 'domain/value_objects/marketplace'
import { pageSizes } from 'lib/hook/customHook'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import { NextPage } from 'next'
import Head from 'next/head'
import { useRouter } from 'next/router'
import AssetComponent from 'presentation/components/asset'
import NoData from 'presentation/components/no-data'
import PaginationDot from 'presentation/components/pagination/pagination-dot'
import Select from 'presentation/components/select'
import SkeletonAsset from 'presentation/components/skeleton/assets'
import ToggleWrap from 'presentation/components/toggle-wrap'
import ToggleVerticalFilterCategories from 'presentation/components/toggle-wrap/toggle-vertical-filter-categories'
import ToggleVerticalFilterMarketplaces from 'presentation/components/toggle-wrap/toggle-vertical-filter-marketplaces'
import ToggleVerticalFilterPrice from 'presentation/components/toggle-wrap/toggle-vertical-filter-price'
import ToggleVerticalSearchCollection from 'presentation/components/toggle-wrap/toggle-vertical-search-collections'
import AssetUseCase from 'presentation/use_cases/asset_use_case'
import CategoryUseCase from 'presentation/use_cases/category_use_case'
import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import { FilterParams } from 'types/type'
import ToggleVerticalFilterStatuesV2 from 'presentation/components/toggle-wrap/toggle-vertical-filter-statues-v2'
import { NetworkStateProvider } from 'presentation/providers/network_state_provider'

const assetUseCase = new AssetUseCase()
const categoryUseCase = new CategoryUseCase()
const marketplace = Marketplace.getResourceArray().map(
  (marketplace) => {
    return Marketplace.fromName<Marketplace>(marketplace.name)
  }
)
const filterStatuses = FilterStatus.getResourceArray()

let PageSize = pageSizes.pageSize30

const Assets: NextPage = () => {
  const initParams: FilterParams = {
    categoryIds: [],
    collectionIds: [],
    filterStatuses: [],
    // marketplaces: [],
    priceTokenUnit: '',
    minPrice: '',
    maxPrice: '',
    chains: [],
    page: 1,
    limit: PageSize,
    sortBy: SortBy.recentlyListed().getName(),
  }

  const router = useRouter()
  const { search } = router.query

  const { filterParams, setFilterParams, changeFilterParam } =
    useChangeFilterParams(initParams)
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [marketPlaces] = useState(marketplace)
  const [assets, setAssets] = useState<Asset[]>([])
  const [isFetchingAssets, setIsFetchingAssets] = useState<boolean>(false)
  const [totalAsset, setTotalAsset] = useState<number>(0)
  const [showFilterStatuses, setShowFilterStatuses] = useState<boolean>(false)
  const [sortList, setSortList] = useState<any>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [showSortList, setShowSortList] = useState<boolean>(false)
  const [showCategories, setShowCategories] = useState<boolean>(false)

  // Add NetworkStateProvider
  const networkStateProvider = NetworkStateProvider((state) => ({
    network: state.network,
  }))
  
  // Add state to track if network is ready
  const [isNetworkReady, setIsNetworkReady] = useState<boolean>(false)
  
  // Add state to track previous chainId to detect changes
  const [prevChainId, setPrevChainId] = useState<string>('')

  const fetchAssets = async () => {
    // Only proceed if network is ready
    if (!isNetworkReady) return
    
    setIsFetchingAssets(true)
    
    // Get chainId from network provider
    const chainId = networkStateProvider.network?.chainId || ''
    
    const query = new AssetQuery({
      ...filterParams,
      keywords: [search],
      chainId: chainId, // Add chainId to query
    })
    
    await assetUseCase
      .totalCount(query)
      .then((res) => {
        setTotalAsset(res)
      })
      .catch((_error) => {
        toast.error('Error while fetching assets')
      })

    await assetUseCase
      .search(query)
      .then((res) => {
        setAssets(res)
      })
      .catch(() => { })
    setIsFetchingAssets(false)
  }

  const formatSortList = () => {
    let sortList = SortBy.getResourceArray().map((sort: any) => {
      return {
        id: sort.id,
        name: sort.label,
        value: sort.name,
      }
    })
    // filter sortList by client request 
    sortList = sortList.filter((sort: any) => {
      return sort.value === 'recentlyListed'
        || sort.value === 'priceLowToHigh'
        || sort.value === 'priceHighToLow'
        || sort.value === 'highestLastSale'
    })
    setSortList(sortList)
    setShowSortList(true)
  }

  const fetchCategoryList = async () => {
    const query = new CategoryQuery()
    await categoryUseCase
      .search(query)
      .then((res) => {
        setCategories(res)
      })
      .catch(() => {
        toast.error('Error while fetching categories')
      })
    setShowCategories(true)
  }

  // Monitor network state changes and reset page when chainId changes
  useEffect(() => {
    if (networkStateProvider.network !== undefined) {
      setIsNetworkReady(true)
      
      const currentChainId = networkStateProvider.network?.chainId || ''
      
      // Only reset if chainId actually changed and is not the initial load
      if (prevChainId !== currentChainId) {
        setCurrentPage(1)
        // Use changeFilterParam with false to prevent immediate trigger
        changeFilterParam('page', 1, false)
        
        // We'll handle the fetch in the other useEffect that depends on chainId
        // No need to call fetchAssets() here
      }
      
      // Update previous chainId
      setPrevChainId(currentChainId)
    }
  }, [networkStateProvider.network?.chainId])

  useEffect(() => {
    setShowFilterStatuses(true)
    formatSortList()
    fetchCategoryList()
  }, [])

  // Modify this useEffect to avoid duplicate calls
  useEffect(() => {
    if (isNetworkReady) {
      fetchAssets()
    }
  }, [search, filterParams, isNetworkReady]) // Remove networkStateProvider.network?.chainId from dependencies

  return (
    <>
      <Head>
        <title>Assets - Quill</title>
        <meta name="description" content="NFT marketplace" />
        <link rel="icon" href="/favicon.ico" />
      </Head>
      <main>
        <div className="container-df assets-page pt-10">
          <div className="product-layout">
            <div className="product-layout-filter">
              <ToggleWrap
                title="Filter"
                contentClass="border-t-0 overflow-hidden"
              >
                {showFilterStatuses && (
                  <ToggleVerticalFilterStatuesV2
                    defaultShow
                    filterStatues={FilterStatus.getResourceArray()}
                    changeFilterParam={changeFilterParam}
                  />
                )}
                <ToggleVerticalFilterPrice
                  defaultShow
                  minPrice={filterParams.minPrice || ''}
                  maxPrice={filterParams.maxPrice || ''}
                  changeFilterParam={changeFilterParam}
                />
                <ToggleVerticalSearchCollection
                  defaultShow
                  changeFilterParam={(key, value, activeState) => {
                    // reset collectionIds
                    if( filterParams?.collectionIds && filterParams?.collectionIds?.length > 0){
                      for (let i = 0; i < filterParams?.collectionIds?.length; i++) {
                        changeFilterParam('collectionIds', filterParams.collectionIds[i], false)
                      }
                    }
                    changeFilterParam(key, value, activeState)
                    setCurrentPage(1)
                    changeFilterParam('page', 1)
                  }}
                />
                {showCategories && (
                  <ToggleVerticalFilterCategories
                    defaultShow
                    categories={categories}
                    changeFilterParam={changeFilterParam}
                  />
                )}
                {/* <ToggleVerticalFilterMarketplaces
                  defaultShow
                  marketplaces={marketPlaces}
                  changeFilterParam={changeFilterParam}
                /> */}
              </ToggleWrap>
            </div>
            <div className="xl:mr-14 mt-4 md:mt-0 w-full">
              <div className="mb-6 flex justify-between items-center w-full">
                {isFetchingAssets ? (
                  <p className="w-36 h-4 bg-gray-3 rounded-lg animate-pulse"></p>
                ) : (
                  <p className="text-gray-1 text-stroke-thin">
                    {totalAsset.toLocaleString()} results
                  </p>
                )}
                <div>
                  {showSortList && (
                    <Select
                      options={sortList}
                      onSelect={(option) => {
                        setFilterParams({
                          ...filterParams,
                          sortBy: SortBy.fromName(option.value)?.getName(),
                        })
                      }}
                    />
                  )}
                </div>
              </div>
              <div
                className={`product-wrap ${!isFetchingAssets && assets.length === 0 ? 'no-data' : ''
                }`}
              >
                {isFetchingAssets ? (
                  [...Array(5)].map((_, id) => <SkeletonAsset key={id} />)
                ) : (
                  <>
                    {assets.length > 0 ? (
                      assets.map((asset: any, index: number) => (
                        <AssetComponent
                          key={`${asset.id}-${index}`}
                          record={asset}
                          showDaysLeft={false}
                        />
                      ))
                    ) : (
                      <div className="w-full rounded-lg border flex justify-center items-center py-24">
                        <NoData />
                      </div>
                    )}
                  </>
                )}
              </div>
              {totalAsset > PageSize && !isFetchingAssets && (
                <div className="flex justify-center mt-5">
                  <PaginationDot
                    currentPage={currentPage}
                    totalCount={totalAsset}
                    pageSize={PageSize}
                    onPageChange={(page: number) => {
                      setCurrentPage(page),
                      setFilterParams({ ...filterParams, page })
                    }}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
    </>
  )
}

export default Assets
