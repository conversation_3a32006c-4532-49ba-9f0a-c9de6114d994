import { NextPage } from 'next'
import Head from 'next/head'
import { useRouter } from 'next/router'
import {  useRef, useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import { pageSizes } from 'lib/hook/customHook'

import Container from 'presentation/components/container'
import Table from 'presentation/components/table'
import Pagination from 'presentation/components/pagination'
import LoadingData from 'presentation/components/skeleton/loading'
import RequireConnect from 'presentation/components/require-connect'
import ClaimNFTModal from 'presentation/components/modal/claim-nft'
import { orderTableHeader } from 'presentation/page_components/payment/_orderTableHeaders'
import ListingUseCase from 'presentation/use_cases/listing_use_case'
import { QueryOrder } from 'data/datasources/interfaces/listing_datasource/query_order'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'
import ListingOrder from 'domain/models/listing_order'

const listingUseCase = new ListingUseCase()

const PaymentList: NextPage = () => {
  const router = useRouter()
  const count = useRef<number>(0)
  const [isFetching, setIsFetching] = useState<boolean>(true)
  const [paramPage, setParamPage] = useState({limit: pageSizes.pageSize20, page: 1})
  const [total, setTotal] = useState<number>(0)
  const [orders, setOrders] = useState<any[]>()
  const [orderSelect, setOrderSelect] = useState<ListingOrder>()
  const [showModalClaimNft, setShowModalClaimNft] = useState(false)

  const userLogin = AuthStateProvider((state) => ({me: state.me}))?.me

  const claim = (order: ListingOrder) => {
    setOrderSelect(order)
    setShowModalClaimNft(true)
  }
  const refund = () => {}

  const loadData = async () => {
    try {
      if(!userLogin?.id) {
        return
      }
      const query = new QueryOrder({...paramPage})
      const listOrder = await listingUseCase.searchOrder(query)
      const data =  listingUseCase.mapOrderTable(listOrder, claim, refund)
      setOrders(data)
      const total = await listingUseCase.totalCountOrder(query)
      setTotal(total)
      if(window.gotoTop) {
        window.gotoTop()
      }
    } catch (error:any) {
      toast.error(error?.showMsgErrors ? error?.showMsgErrors() : error?.message || error)
    }
  }

  const loadDataToClaim = async () => {
    try {
      if(!userLogin?.id) {
        return
      }
      const paymentToken: any = router?.query?.payment_token || ''
      if(!paymentToken) {
        return
      }
      const order: ListingOrder = await listingUseCase.findAssetIdByPaymentToken(paymentToken)
      if(!order || !order?.listing?.isActiveFixedPrice() || !order?.status?.isActive()) {
        return
      }
      claim(order)
      
    } catch (error:any) {
      console.log('-----error', error)
    }
  }

  const initData = async () => {
    setIsFetching(true)
    await loadData()
    await loadDataToClaim()
    setIsFetching(false)
  }

  useEffect(() => {
    if(userLogin?.id) {
      if (count.current == 0) {
        initData()
      }
      count.current++
    }    
  }, [userLogin])
  
  useEffect(() => {
    if (count.current > 0) {
      loadData()
    }
  }, [paramPage])

  return (
    <>
      <Head>
        <title>Payment List - Quill</title>
      </Head>
      <main>
        <Container>
          {userLogin?.id ? (
            <div className="container-lg pt-6 md:pt-10">
              <p className="text-xl md:text-3xl xl:text-4xl font-medium md:italic text-center text-dark-primary dark:text-white ">Payment List</p>
              
              {isFetching ? (
                <LoadingData />
              ) : (
                <div className='mt-12'>
                  <div className="w-full overflow-x-auto border-b max-w-[1200px] mx-auto">
                    <Table
                      className='w-full min-w-[600px] border-b'
                      head={orderTableHeader}
                      data={orders}
                      stickyHeader={true}
                    />
                  </div>

                  {total > 0 ? (
                    <div className="mt-6 px-5 mb-22p">
                      <Pagination
                        currentPage={paramPage.page}
                        totalCount={total}
                        pageSize={paramPage.limit}
                        showCountText={false}
                        enableSelectRow={false}
                        onPageChange={(page: number) => {
                          setParamPage({limit: paramPage.limit, page: page})
                        }}
                        onChangePageSize={(limit: number) => {
                          setParamPage({limit: limit, page: 1})
                        }}
                      />
                    </div>
                  ) : (<></>)}
                </div>
              )}

              {showModalClaimNft && (
                <ClaimNFTModal
                  order={orderSelect}
                  isShow={showModalClaimNft}
                  onClose={() => setShowModalClaimNft(false)}
                  onReload={() => {
                    loadData()
                  }}
                />
              )}
            </div>
          ) : (
            <RequireConnect />
          )}
        </Container>
      </main>
    </>
  )
}


export default PaymentList
