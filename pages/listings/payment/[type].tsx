import { NextPage } from 'next'
import Head from 'next/head'
import { useRouter } from 'next/router'
import {  useRef, useEffect } from 'react'
import { toast } from 'react-toastify'

import Container from 'presentation/components/container'
import ListingUseCase from 'presentation/use_cases/listing_use_case'

const listingUseCase = new ListingUseCase()
const FAILURE = 'failure'
const SUCCESS = 'success'
const types = [FAILURE, SUCCESS] 

const PaymentResults: NextPage = ({ paymentToken, type }: any) => {
  const router = useRouter()
  const count = useRef<number>(0)
  const back404 = () => {
    router.push('/404')
  }
  
  const initData = async () => {
    try {
      if(!paymentToken || !types.includes(type)) {
        back404()
        return
      }
      const order = await listingUseCase.findAssetIdByPaymentToken(paymentToken)
      const assetId = order?.asset?.id
      if(!assetId) {
        back404()
        return
      }
      if(type == FAILURE) {
        toast.error('Slash payment failed. Please try again in a few minutes.')
      } else {
        toast.success('Purchase successful. It takes a while to update the data. Please waiting')
      }
      router.push(`/assets/${assetId}`)
    } catch (error) {
      back404()
    }
  }

  useEffect(() => {
    if (count.current !== 0) {
      initData()
    }
    count.current++
  }, [])
  
  return (
    <>
      <Head>
        <title>Payment Results - Quill</title>
      </Head>
      <main>
        <Container>
          <div></div>
        </Container>
      </main>
    </>
  )
}

export async function getServerSideProps (context: any) {
  try {
    return {
      props: {
        paymentToken: context?.query?.payment_token,
        type: context?.params?.type,
      },
    }
  } catch (error) {
    return {
      notFound: true,
    }
  }
}

export default PaymentResults
