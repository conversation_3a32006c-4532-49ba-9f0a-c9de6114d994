import { NextPage } from 'next'
import Head from 'next/head'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import { DateTime } from 'luxon'
import { auctionMethodIcons } from 'lib/valueObjects'

import { AuthStateProvider } from 'presentation/providers/auth_state_provider'
import PreviewAsset from 'presentation/components/asset/preview'
import ButtonTag from 'presentation/components/button/button-tag'
import Container from 'presentation/components/container'
import Heading from 'presentation/components/heading'
import InputListingAssetQuantity from 'presentation/components/input/input-listing-asset-quantity/index'
import RequireConnect from 'presentation/components/require-connect'
import SvgIcon from 'presentation/components/svg-icon'
import SelectAppendItem from 'presentation/components/select/select-append-item'
import Tooltip from 'presentation/components/tooltip'
import SellFixedPrice from 'presentation/page_components/asset/sellFixedPrice'
import SellTimedAuction from 'presentation/page_components/asset/sellTimedAuction'
import AssetUseCase from 'presentation/use_cases/asset_use_case'
import MeUseCase from 'presentation/use_cases/me_use_case'
import AssetThumbnail from 'presentation/components/asset/thumbnail'
import { envConfig } from 'presentation/constant/env'
import { useAuthentication } from 'lib/hook/auth_hook'

import Asset from 'domain/models/asset'
import ListingType from 'domain/models/listing/listing_type'
import AuctionMethod from 'domain/models/listing/auction_method'
import User from 'domain/models/user'
import PaymentTokens from 'domain/models/payment_tokens'

const assetUseCase = new AssetUseCase()
const meUseCase = new MeUseCase()
const listingType = ListingType.getResourceArray()

const ListAsset: NextPage = ({ assetId }: any) => {
  const router = useRouter()
  const [owner, setOwner] = useState<any>({})
  const [isMe, setIsMe] = useState<boolean>(false)
  const [ready, setReady] = useState<boolean>(false)
  const [errorCode, setErrorCode] = useState(0)
  const [asset, setAsset] = useState<Asset>()
  const [isFetchingAsset, setIsFetchingAsset] = useState<boolean>(true)
  const [paymentTokens, setPaymentTokens] = useState<PaymentTokens[]>([])
  const [paymentTokensErc20, setPaymentTokensErc20] = useState<PaymentTokens[]>([])
  const [selectedPaymentToken, setSelectedPaymentToken] = useState<any>({})
  const [price, setPrice] = useState('')
  const [activeTag, setActiveTag] = useState<any>('')
  const [auctionMethods, setAuctionMethods] = useState<any>({})
  const [bundleItems, setBundleItems] = useState<any>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [isSelectType, setIsSelectType] = useState<boolean>(false)
  const [currentContracts, setCurrentContracts] = useState<any>({})
  const [creators, setCreators] = useState<any>({})
  
  const [formData, setFormData] = useState({
    assets: [],
    isBundle: false,
  })
  
  const authStateProvider = AuthStateProvider((state) => ({me: state.me}))
  const { addressConnect} = useAuthentication()
  
  const setFormValue = (field: any, value: any) => {
    setFormData({ ...formData, [field]: value })
  }

  const fetchAssetAndOwner = async () => {
    setIsFetchingAsset(true)
    assetUseCase
      .findById(assetId)
      .then(async (res) => {
        setAsset(res)
        console.log('==============resAsset', res)
        setPaymentTokens(res?.collection?.paymentTokens)
        setPaymentTokensErc20(res?.collection?.paymentTokens?.filter((i:any) => !i.isNative))
        initEnvConfig(res?.chainId)
        setIsSelectType(Number(res?.totalSupply) === 1)
        
        await meUseCase.loginStatus().then( async (loginStatus) => {
          if (loginStatus.isLoggedIn && loginStatus?.user?.publicAddresses[0]) {
            const owner = loginStatus.user
            const isOwnerAsset = await assetUseCase.checkOwnership(assetId, owner?.publicAddresses[0])
            if ( isOwnerAsset ) {
              setOwner(owner)
              setIsMe(true)
            } else {
              setIsMe(false)
              setOwner(null)
              router.push(`/assets/${assetId}`)
            }
          } else {
            setErrorCode(404)
          }
        })
      })
      .catch((_error) => {
        setErrorCode(404)
      })
    setIsFetchingAsset(false)
  }

  const formatAuctionMethods = () => {
    const methods = AuctionMethod.getResourceArray().map((method: any) => {
      return {
        id: method.id,
        name: method.label,
        value: method.name,
        icon: auctionMethodIcons[method.name],
      }
    })
    setAuctionMethods(methods)
  }

  // const fetchBundleItems = async () => {
  //   const query = new AssetQuery({
  //     ownerId: owner.id,
  //     collectionIds: [asset?.collection?.id],
  //     sortBy: SortBy.recentlyListed().getName(),
  //   })
  //   assetUseCase.search(query).then((res) => {
  //     res = res.filter((item: any) => item.id !== assetId)
  //     setBundleItems(res)
  //   }).catch((_error) => {})
  // }


  const handleSelectBundleItem = (item: any) => {
    let arr: any = formData.assets.filter(
      (arrayItem: any) => arrayItem.id != item.id
    )
    setFormValue('assets', [...arr, item])
  }

  const handleDeleteBundleItem = (item: any) => {
    let arr: any = formData.assets.filter(
      (arrayItem: any) => arrayItem.id != item.id
    )
    setFormValue('assets', arr)
  }
  const initEnvConfig = (chainId:any) => {
    const config: any = envConfig
    setCurrentContracts(config.contracts[chainId || 1])
  }

  // useEffect(() => {
  //   fetchBundleItems()
  // }, [asset, owner])


  useEffect(() => {
    setActiveTag(listingType[0].name)
    fetchAssetAndOwner()
    formatAuctionMethods()
  }, [authStateProvider?.me])

  useEffect(() => {
    if(asset) {
      const userAddress = owner?.publicAddresses?.length ? owner?.publicAddresses[0] : ''
      setCreators(asset?.getCreatorEarnings(userAddress))
    }
  }, [owner, asset])

  useEffect(() => {
    setFormValue('assets', [asset])
  }, [asset])

  useEffect(() => {
    setLoading(true)
    if (owner?.id) {
      setReady(true)
    } else {
      setReady(false)
    }
    setLoading(false)
  }, [owner])
  
  // if (errorCode === 404) {
  //   router.push('/404')
  // }

  return (
    <>
      <Head>
        <title>Asset Item - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>
      <main>
        <Container>
          {ready && isMe && addressConnect ? (
            <>
              {!isFetchingAsset && (
                <div className="container-lg pt-9 md:pb-0 pb-9">
                  <Heading className="text-dark-primary dark:text-white">
                    List item for sale
                  </Heading>

                  <div className="w-full mt-4 md:mt-0">
                    <Link
                      href={`/assets/${assetId}`}
                      className="flex items-center"
                    >
                      <SvgIcon
                        name="chevronRight"
                        className="w-5 h-5 stroke-none fill-primary-2 rotate-180 mr-4"
                      />
                      {asset && (
                        <AssetThumbnail 
                          className="h-[50px] !w-[50px] !rounded-10" 
                          classNameImage="!rounded-10"
                          onlyShowImage={true}
                          asset={asset}/>
                      )}

                      <div className="ml-10p">
                        <p className="font-medium text-sm text-gray-1">
                          {asset?.collection?.name}
                        </p>
                        <p className="font-bold text-dark-primary dark:text-white">
                          {asset?.name}
                        </p>
                      </div>
                    </Link>
                  </div>

                  <div className="sell-item-container">
                    {isSelectType && (
                      <div className="w-full">
                        <div className="flex items-center">
                          <p className="font-medium text-dark-primary dark:text-white">
                          Type
                          </p>
                          <Tooltip contentWidth="lg:w-[295px]" iconSize="w-4 h-4">
                          Type
                          </Tooltip>
                        </div>
                        <div className="flex mt-10p">
                          {listingType.map((type, index) => (
                            <ButtonTag
                              key={index}
                              onClick={() => {
                                setActiveTag(type.name)
                              }}
                              active={activeTag === type.name}
                              className="mr-5 shrink-0"
                            >
                              {type.label}
                            </ButtonTag>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    <div className="sell-item-flex ">
                      <div className="sell-item-left">
                        {activeTag === ListingType.fixedPrice.name && (
                          <SellFixedPrice 
                            paymentTokens={paymentTokens} 
                            asset={asset} 
                            contracts={currentContracts} 
                            creators={creators} 
                            onChangeData={(value: any) => {
                              setSelectedPaymentToken(value?.paymentToken)
                              setPrice(value?.price)
                            }}
                          />)}
                        {activeTag === ListingType.timedAuction.name && (
                          <SellTimedAuction 
                            paymentTokens={paymentTokensErc20} 
                            asset={asset} 
                            contracts={currentContracts} 
                            creators={creators} 
                            onChangeData={(value: any) => {
                              setSelectedPaymentToken(value?.paymentToken)
                              setPrice(value?.price)
                            }}
                          />)}
                      </div>
                      <div className="sell-item-right xl:pr-10 xl:pl-0 md2:pl-10 md2:sticky top-28">
                        { selectedPaymentToken && (
                          <div className="w-full">
                            <p className="font-medium mb-5">Preview </p>
                            <PreviewAsset
                              record={asset}
                              paymentToken={selectedPaymentToken}
                              price={price}
                            />
                          </div>
                        )}
                        {activeTag === 'fixedPrice' && formData.isBundle && (
                          <>
                            {formData.assets.map((item: Asset) => {
                              return (
                                <InputListingAssetQuantity
                                  asset={item}
                                  defaultValue={1}
                                  canDelete={item.id != assetId}
                                  onChange={() => {}}
                                  key={item.id}
                                  onDelete={(item) => {
                                    handleDeleteBundleItem(item)
                                  }}
                                />
                              )
                            })}
                            <p className="font-medium">1 Item</p>
                            <SelectAppendItem
                              rounded
                              options={bundleItems}
                              className="w-full mt-3"
                              selectClassName="w-full justify-between"
                              arrowIconClass="stroke-none fill-primary-2"
                              placeholder="Add item"
                              customWidth={true}
                              onSelect={(item) => {
                                handleSelectBundleItem(item)
                              }}
                            />
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </>
          ) : (
            <RequireConnect />
          )}
        </Container>
      </main>
    </>
  )
}

export async function getServerSideProps (context: any) {
  let assetId = ''
  if (context.query.assetId) {
    assetId = context.query.assetId
  }
  try {
    return {
      props: {
        assetId: assetId,
      },
    }
  } catch (error) {
    return {
      notFound: true,
    }
  }
}

export default ListAsset
