// Declare dataLayer type for Google Analytics
declare global {
  interface Window {
    dataLayer: any[],
    gotoTop: any,
    
  }
}

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { createWeb3Modal } from '@web3modal/wagmi/react'
import { defaultWagmiConfig } from '@web3modal/wagmi/react/config'
import { NextPage } from 'next'
import { ThemeProvider } from 'next-themes'
import { AppProps } from 'next/app'
import Router, { useRouter } from 'next/router'
import NProgress, { set } from 'nprogress'
import 'nprogress/nprogress.css'
import Footer from 'presentation/components/layout/footer'
import Header from 'presentation/components/layout/header'
import { ToastContainer, toast } from 'react-toastify'
import '../styles/main.scss'
import { WagmiProvider } from 'wagmi'
import { chains } from 'presentation/constant/chains'
import Script from 'next/script'
import { useEffect, useState, useRef } from 'react'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'
import { envConfig } from 'presentation/constant/env'
import MeUseCase from 'presentation/use_cases/me_use_case'
import { NetworkProvider, NetworkStateProvider } from 'presentation/providers/network_state_provider'
import { DateTime } from 'luxon'
import MaintenancePage from './maintenance'

Router.events.on('routeChangeStart', () => NProgress.start())
Router.events.on('routeChangeComplete', () => NProgress.done())
Router.events.on('routeChangeError', () => NProgress.done())

// 0. Setup queryClient
const queryClient = new QueryClient()

// 1. Your WalletConnect Cloud project ID
const projectId = process.env.NEXT_PUBLIC_WALLET_CONNECT_PROJECT_ID || ''

// 2. Create wagmiConfig
const metadata = {
  name: 'nft-land',
  description: 'nft-land',
  url: 'nft-land.me',
  icons: ['https://nft-land.me/favicon.ico'],
}
const wagmiOptions = {} // Optional - for overriding default options if necessary

const config = defaultWagmiConfig({
  chains: chains,
  projectId,
  metadata,
  auth: {
    email: false, // default to true
    socials: [],
    showWallets: false, // default to true
    walletFeatures: false, // default to true
  },
  ...wagmiOptions, // Optional - override createConfig parameters
})

// 3. Create Web3Modal
createWeb3Modal({
  wagmiConfig: config,
  projectId,
  enableAnalytics: true, // Optional - defaults to your Cloud configuration
  enableOnramp: false, // Optional - false as default
})

const MyApp: NextPage<AppProps> = ({ Component, ...props }: AppProps) => {
  const isProduction = process.env.NEXT_PUBLIC_NODE_ENV === 'production'
  const isMaintenance = process.env.NEXT_PUBLIC_IS_MAINTAIN === 'true'

  const [offset, setOffset] = useState(0)
  const count = useRef<string>('')

  const listenScrollEvent = async (event: any) => {
    setOffset(event?.target?.scrollTop || 0)
  }

  const gotoTop = () => {
    const myDiv: any = document?.getElementById('screen-main')
    if(myDiv) {
      myDiv.scroll({top:0, behavior:'smooth'})

    }
  }
  const meUseCase = new MeUseCase()
  useEffect(() => {
    if (isProduction) {
      // Initialize dataLayer
      window.dataLayer = window.dataLayer || []
    }
    window.gotoTop = gotoTop
    // Initialize network state from localStorage
    NetworkStateProvider.getState().init()
  }, [isProduction])
  const router = useRouter()
  const authStateProvider = AuthStateProvider((state) => ({
    me: state.me,
    init: state.init,
  }))
  const checkIsAdmin = async () => {
    if (router.pathname.startsWith('/admin')) {
      let account:any = authStateProvider.me
      console.log('🚀 ~ checkIsAdmin ~ account:', account)
      if (!account) {
        // fetch again to check 
        const loginStatus = await meUseCase.loginStatus()
        if (loginStatus?.isLoggedIn) {
          account = { ...loginStatus.user}
        }
        console.log('🚀 ~ checkIsAdminAfter ~ account:', account)
      }
      // Check operator role

      if (!account || account?.roleId !== envConfig.userRole.operator) {
        router.push('/')
        return
      }
      
    
    }
  }
  useEffect(() => {
    checkIsAdmin()
    // Check if path starts with /admin
  }, [router.pathname, authStateProvider.me, checkIsAdmin])
  
  useEffect(() => {
    if (!router.isReady) return
    const query =  {...router.query}
    const token = Array.isArray(query.token) ? query.token[0] : query.token || ''
    const expiresAtRaw = Array.isArray(query.expiresAt) ? query.expiresAt[0] : query.expiresAt
    const expiresAt = expiresAtRaw ? DateTime.fromISO(expiresAtRaw.replace(' ', '+')) : DateTime.utc()
    const now = DateTime.utc()
    if(token && count.current != token && expiresAt > now) {
      meUseCase.setTokenUser(token)
      authStateProvider.init()
      count.current = token
      delete query.token
      delete query.expiresAt
      delete query.tokenHash
      delete query.type
      router.replace(
        { pathname: router.pathname, query: query },
        undefined,
        { shallow: true }
      )
      return
    }
    const error = Array.isArray(query.error) ? query.error[0] : query.error || ''
    const message = Array.isArray(query.message) ? query.message[0] : query.message || ''
    if(error && message && count.current != message) {
      toast.error(message)
      count.current = message
      delete query.error
      delete query.message
      router.replace(
        { pathname: router.pathname, query: query },
        undefined,
        { shallow: true }
      )
      return
    }
  }, [router.isReady, router])

  // Show maintenance page if maintenance mode is enabled
  if (isMaintenance) {
    return <MaintenancePage />
  }

  return (
    <WagmiProvider config={config}>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider attribute="class" defaultTheme="light">

          {isProduction && (
            <>
              <Script
                id="gtm-script"
                strategy="afterInteractive"
                dangerouslySetInnerHTML={{
                  __html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                  new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                  j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                  'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                  })(window,document,'script','dataLayer','GTM-KJNXC4ZQ');`}}
              />
              <noscript>
                <iframe
                  src="https://www.googletagmanager.com/ns.html?id=GTM-KJNXC4ZQ"
                  height="0"
                  width="0"
                  style={{ display: 'none', visibility: 'hidden' }}
                />
              </noscript>
            </>
          )}
          <div className='screen-main overflow-y-scroll' id="screen-main">
            <NetworkProvider>
              <Header offset={11} />
              <Component {...props.pageProps} />
              <Footer />
            </NetworkProvider>
          </div>
          <ToastContainer
            position="top-right"
            autoClose={5000}
            style={{ zIndex: 1000 }}
            hideProgressBar={false}
            newestOnTop={false}
            closeOnClick
            rtl={false}
            pauseOnFocusLoss
            draggable
            pauseOnHover
            theme="colored"
          />
        </ThemeProvider>
      </QueryClientProvider>
    </WagmiProvider>
  )
}

export default MyApp
