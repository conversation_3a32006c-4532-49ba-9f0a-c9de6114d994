import { NextPage } from 'next'
import Head from 'next/head'
import { useState, useEffect } from 'react'
import Container from 'presentation/components/container'
import AnnouncementsCard from 'presentation/page_components/announcements/announcementCard'
import AnnouncementsCarousal from 'presentation/page_components/announcements/announcementCarousal'
import Heading from 'presentation/components/heading'
import NoData from 'presentation/components/no-data'
import BreadCrumb from 'presentation/components/bread-crumb'
import SkeletonAnnouncements from 'presentation/components/skeleton/announcements'
import { pageSizes } from 'lib/hook/customHook'
import AnnouncementUseCase from 'presentation/use_cases/announcement_use_case'
import { Query } from 'domain/repositories/announcement_repository/query'
import Announcement from 'domain/models/announcement'
import { toast } from 'react-toastify'

const announcementUseCase = new AnnouncementUseCase()
let PageSize = pageSizes.pageSize25

const Announcements: NextPage = () => {
  const [announcements, setAnnouncements] = useState<Announcement[]>([])
  const [isFetchingAnnouncements, setIsFetchingAnnouncements] =
    useState<boolean>(false)

  const fetchAnnouncements = async () => {
    setIsFetchingAnnouncements(true)
    const query = new Query({ limit: PageSize, offset: 0 })
    await announcementUseCase
      .search(query)
      .then((res) => {
        setAnnouncements(res)
      })
      .catch((_err) => {
        toast.error('Failed to fetch announcements')
      })
    setIsFetchingAnnouncements(false)
  }

  useEffect(() => {
    fetchAnnouncements()
  }, [])

  return (
    <>
      <Head>
        <title>Announcements</title>
        <meta
          name="description"
          content="Recent Announcements | Enjoy Quill"
        />
      </Head>
      <main>
        <Container>
          <BreadCrumb
            tabs={[
              {
                name: 'Home',
                url: '/',
              },
              {
                name: 'Announcements',
                url: '/announcements',
              },
            ]}
          />

          <div className="h-[30rem] mb-10">
            <AnnouncementsCarousal />
          </div>

          <Heading className="mr-1 mb-10">Annoucements</Heading>

          <div
            className={` ${
              !isFetchingAnnouncements && announcements.length === 0
                ? 'no-data'
                : 'grid 2xl:grid-cols-5 xl:grid-cols-4 lg:grid-cols-3 sm:grid-cols-2 gap-4'
            }`}
          >
            {isFetchingAnnouncements ? (
              [...Array(5)].map((_, id) => <SkeletonAnnouncements key={id} />)
            ) : (
              <>
                {announcements.length > 0 ? (
                  announcements.map((announcement: any, index: number) => (
                    <AnnouncementsCard
                      key={`${announcement.id}-${index}`}
                      record={announcement}
                    />
                  ))
                ) : (
                  <div className="w-full rounded-lg border flex justify-center items-center py-24">
                    <NoData />
                  </div>
                )}
              </>
            )}
          </div>
          {/* //Add pagination */}
        </Container>
      </main>
    </>
  )
}

export default Announcements
