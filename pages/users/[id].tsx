import { NextPage } from 'next'
import { useEffect, useState } from 'react'
import Head from 'next/head'
import Container from 'presentation/components/container'

import MyProfileSection from 'presentation/page_components/account-details/userProfileSection'
import CollectedTab from 'presentation/page_components/account-details/collectedTab'
import UserUseCase from 'presentation/use_cases/user_use_case'
import { useRouter } from 'next/router'
import User from 'domain/models/user'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'

const userUseCase = new UserUseCase()

const UserPage: NextPage = ({ id }: any) => {
  const [user, setUser] = useState<User>()
  const [isMe, setIsMe] = useState(false)
  const [loading, setLoading] = useState(true)
  const [errorCode, setErrorCode] = useState(0)
  const router = useRouter()

  const authStateProvider = AuthStateProvider((state: any) => ({
    me: state.me,
  }))

  const fetchUser = async () => {
    await userUseCase
      .findByUsername(id)
      .then((res) => {
        setUser(res)
      })
      .catch(async (_error) => {
        await userUseCase
          .findByPublicAddress(id)
          .then((res) => {
            setUser(res)
          })
          .catch((_error) => {
            setErrorCode(404)
          })
      })
    setLoading(false)
    checkIfMe()
  }

  const checkIfMe = async () => {
    if (authStateProvider.me == null) {
      return
    }
    if (authStateProvider.me!.id === user?.id) {
      setIsMe(true)
    }
  }

  useEffect(() => {
    setLoading(true)
    fetchUser()
  }, [])

  useEffect(() => {
    if (router.isReady) {
      setUser(undefined)
      setIsMe(false)
      setErrorCode(0)
      setLoading(true)
      
      fetchUser()
    }
  }, [router.query.id, router.isReady])

  useEffect(() => {
    if (errorCode == 404) {
      router.push('/404')
    }
  }, [errorCode])

  return (
    <>
      <Head>
        <title>Account - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>

      <main>
        {!loading && user != null && (
          <Container className="pt-9">
            <>
              <MyProfileSection
                activeTab={''}
                owner={user}
                isMe={isMe}
                path={'/users/' + id}
              />
              <CollectedTab userId={user?.id!} isMe={isMe} />
            </>
          </Container>
        )}
      </main>
    </>
  )
}

export async function getServerSideProps (context: any) {
  const { id } = context.params
  try {
    return {
      props: {
        id: id,
      },
    }
  } catch (error) {
    return {
      notFound: true,
    }
  }
}

export default UserPage
