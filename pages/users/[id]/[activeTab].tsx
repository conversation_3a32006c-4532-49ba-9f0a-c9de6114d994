import { NextPage } from 'next'
import { useEffect, useState } from 'react'
import Head from 'next/head'
import Container from 'presentation/components/container'

import MyProfileSection from 'presentation/page_components/account-details/userProfileSection'
import CreatedTab from 'presentation/page_components/account-details/createdTab'
import FavoritedTab from 'presentation/page_components/account-details/favoritedTab'
import ActivityTab from 'presentation/page_components/account-details/activityTab'
import UserUseCase from 'presentation/use_cases/user_use_case'
import { useRouter } from 'next/router'
import User from 'domain/models/user'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'

const userUseCase = new UserUseCase()

const UserPage: NextPage = ({ id, activeTab }: any) => {
  const [user, setUser] = useState<User>()
  const [isMe, setIsMe] = useState(false)
  const [loading, setLoading] = useState(true)
  const [errorCode, setErrorCode] = useState(0)
  const router = useRouter()

  const authStateProvider = AuthStateProvider((state: any) => ({
    me: state.me,
  }))

  const fetchUser = async () => {
    await userUseCase
      .findByUsername(id)
      .then((res) => {
        setUser(res)
      })
      .catch(async (_error) => {
        await userUseCase
          .findByPublicAddress(id)
          .then((res) => {
            setUser(res)
          })
          .catch((_error) => {
            setErrorCode(404)
          })
      })
    setLoading(false)
    checkIfMe()
  }

  const checkIfMe = async () => {
    if (authStateProvider.me == null) {
      return
    }
    if (authStateProvider.me!.id === user?.id) {
      setIsMe(true)
    }
  }

  useEffect(() => {
    setLoading(true)
    fetchUser()
  }, [])

  useEffect(() => {
    if (errorCode == 404) {
      router.push('/404')
    }
  }, [errorCode])

  return (
    <>
      <Head>
        <title>Account - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>

      <main>
        {!loading && user != null && (
          <Container className="pt-9">
            <>
              <MyProfileSection
                activeTab={activeTab}
                owner={user}
                isMe={isMe}
                path={'/users/' + id}
              />
              {activeTab === 'created' && (
                <CreatedTab userId={user?.id!} isMe={isMe} />
              )}
              {activeTab === 'favorited' && (
                <FavoritedTab userId={user?.id!} isMe={isMe} />
              )}
              {activeTab === 'activities' && <ActivityTab userId={user?.id!} />}
            </>
          </Container>
        )}
      </main>
    </>
  )
}

export async function getServerSideProps (context: any) {
  const { id, activeTab } = context.params
  const ValidTabNames = ['created', 'favorited', 'activities']

  if (!ValidTabNames.includes(activeTab)) {
    return {
      notFound: true,
    }
  }
  try {
    return {
      props: {
        activeTab: activeTab,
        id: id,
      },
    }
  } catch (error) {
    return {
      notFound: true,
    }
  }
}

export default UserPage
