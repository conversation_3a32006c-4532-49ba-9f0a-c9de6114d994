import { NextPage } from 'next'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import Head from 'next/head'
import Container from 'presentation/components/container'
import MyProfileSection from 'presentation/page_components/account-details/userProfileSection'
import OfferMadeTab from 'presentation/page_components/account-details/offerMadeTab'
import OfferReceivedTab from 'presentation/page_components/account-details/offerReceivedTab'
import UserUseCase from 'presentation/use_cases/user_use_case'
import User from 'domain/models/user'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'

const userUseCase = new UserUseCase()

const UserPage: NextPage = ({ id, tab }: any) => {
  const [user, setUser] = useState<User>()
  const [isMe, setIsMe] = useState(false)
  const [loading, setLoading] = useState(true)
  const [errorCode, setErrorCode] = useState(0)
  const router = useRouter()

  const authStateProvider = AuthStateProvider((state: any) => ({
    me: state.me,
  }))

  const fetchUser = async () => {
    await userUseCase
      .findByUsername(id)
      .then((res) => {
        setUser(res)
      })
      .catch(async (_error) => {
        await userUseCase
          .findByPublicAddress(id)
          .then((res) => {
            setUser(res)
          })
          .catch((_error) => {
            setErrorCode(404)
          })
      })
    setLoading(false)
    checkIfMe()
  }

  const checkIfMe = async () => {
    if (authStateProvider.me == null) {
      return
    }
    if (authStateProvider.me!.id === user?.id) {
      setIsMe(true)
    }
  }

  useEffect(() => {
    setLoading(true)
    fetchUser()
  }, [])

  useEffect(() => {
    if (errorCode == 404) {
      router.push('/404')
    }
  }, [errorCode])

  return (
    <>
      <Head>
        <title>Account - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>

      <main>
        {!loading && (
          <Container className="pt-9">
            <>
              <MyProfileSection
                activeTab={tab}
                owner={user}
                isMe={isMe}
                path={'/users/' + id}
              />
              {tab === 'received' && <OfferReceivedTab userId={user?.id!} isMe={isMe} />}
              {tab === 'made' && <OfferMadeTab userId={user?.id!} isMe={isMe}  />}
            </>
          </Container>
        )}
      </main>
    </>
  )
}

export async function getServerSideProps (context: any) {
  const { id, tab } = context.params
  const ValidTabNames = ['made', 'received']

  if (!ValidTabNames.includes(tab)) {
    return {
      notFound: true,
    }
  }
  try {
    return {
      props: {
        tab: tab,
        id: id,
      },
    }
  } catch (error) {
    return {
      notFound: true,
    }
  }
}

export default UserPage
