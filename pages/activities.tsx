import ActivityModel from 'domain/models/activity'
import FilterEventType from 'domain/repositories/activity_repository/filter_event_type'
import { Query } from 'domain/repositories/activity_repository/query'
import Chain from 'domain/value_objects/chain'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import { NextPage } from 'next'
import Head from 'next/head'
import Image from 'next/image'
import Link from 'next/link'
import Container from 'presentation/components/container'
import Heading from 'presentation/components/heading'
import NoData from 'presentation/components/no-data'
import Pagination from 'presentation/components/pagination'
import LoadingData from 'presentation/components/skeleton/loading'
import SvgIcon from 'presentation/components/svg-icon'
import Switch from 'presentation/components/switch'
import Table from 'presentation/components/table'
import ToggleWrap from 'presentation/components/toggle-wrap'
import ToggleVertical from 'presentation/components/toggle-wrap/toggle-vertical'
import ToggleVerticalSearchCollection from 'presentation/components/toggle-wrap/toggle-vertical-search-collections'
import ActivityUseCase from 'presentation/use_cases/activity_use_case'
import AssetThumbnail from 'presentation/components/asset/thumbnail'
import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import { FilterParams } from 'types/type'
import { NetworkStateProvider } from 'presentation/providers/network_state_provider'

import { isAddress } from 'lib/validate'
const activityUseCase = new ActivityUseCase()



const activityTableHead = [
  {
    title: 'Event',
    key: 'event',
    render: (event: any) => (
      <div className="px-2">

        <SvgIcon
          name={event.icon}
          className="w-5 h-5 stroke-none fill-dark-primary dark:fill-white"
        />
        <span className="ml-2 align-middle dark:text-white">{event.label} </span>
      </div>
    ),
  },
  {
    title: 'Item',
    key: 'asset',
    render: (asset: any) => (
      <Link
        href={`/assets/${asset?.id}`}
        className="py-3 flex items-center relative w-max"
      >
        {asset && <AssetThumbnail asset={asset}
          className="h-[60px] !w-[60px] !rounded-none"
          classNameImage="!rounded-none !border-none" />}
        <div className="max-w-[7rem] py-1 ml-2">
          <p className="font-medium truncate text-dark-primary dark:text-white">{asset?.name}</p>
          <span className="text-sm font-medium text-primary-2 break-words">
            {asset?.collection?.name}
          </span>
        </div>
      </Link> 
    ),
  },
  {
    title: 'Unit Price',
    key: 'unitPrice',
    render: (unitPrice : any) => (
      <div className="px-2 uppercase dark:text-white">
        {unitPrice?.price} {unitPrice?.paymentToken?.name}
      </div>
    ),
  },
  {
    title: 'Quantity',
    key: 'quantity',
    render: (quantity : any) => (
      <div className="px-2 uppercase  dark:text-white">
        {quantity}
      </div>
    ),
  },
  {
    title: 'From',
    key: 'fromUser',
    render: (fromUser: any) => (
      <>
        {fromUser?.username  ? (
          <Link href={`/users/${fromUser?.username}`} className="text-primary-2 px-2 py-4 block truncate w-[120px]">
            {fromUser?.username}
          </Link>
        ) : (
          <div className=" px-2 py-4 block truncate w-[120px] break-all dark:text-white">
            {fromUser?.username || fromUser?.recipient}
          </div>
        )}
       
      </>
      
    ),
  },
  {
    title: 'To',
    key: 'toUser',
    render: (toUser: any) => (
      <>
        {toUser?.username && !isAddress(toUser?.username) ? (
          <Link href={`/users/${toUser?.username}`} className="text-primary-2 px-2 py-4 block truncate w-[120px]">
            {toUser?.username}
          </Link>
        ) : (
          <div className=" px-2 py-4 block truncate w-[120px] dark:text-white">
            {toUser?.username || toUser?.offerer}
          </div>
        )}
       
      </>
    
    ),
  },
  {
    title: 'Time',
    key: 'time',
    render: (time: any) => (
      <div  className="text-dark-primary px-2 py-4 dark:text-white">
        {time}
      </div>
    ),
  },
]

const Activity: NextPage = () => {
  // Add NetworkStateProvider
  const networkStateProvider = NetworkStateProvider((state) => ({
    network: state.network,
  }))
  
  // Add state to track if network is ready
  const [isNetworkReady, setIsNetworkReady] = useState<boolean>(false)
  
  // Add state to track previous chainId to detect changes
  const [prevChainId, setPrevChainId] = useState<string>('')

  const initParams: FilterParams = {
    filterEventTypes: [],
    collectionIds: [],
    chains: [],
    page: 1,
    limit: 20,
  }

  const { filterParams, changeFilterParam, setFilterParams } =
    useChangeFilterParams(initParams)
  const [activities, setActivities] = useState<ActivityModel[]>([])
  const [isActivitiesReady, setIsActivitiesReady] = useState<boolean>(false)
  const [isFilterEventTypesReady, setIsFilterEventTypesReady] =
    useState<boolean>(false)
  const [isFilterChainsReady, setIsFilterChainsReady] =
    useState<boolean>(false)
  const [total, setTotal] = useState<number>(0)
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [currentLimit, setCurrentLimit] = useState<number>(20)
  const [filterEventTypes, setFilterEventTypes] = useState<any>([])
  const [filterChains, setFilterChains] = useState<Chain[]>([])
  const [activitiesTable, setActivitiesTable] = useState<any>([])
  // Monitor network state changes and reset page when chainId changes
  useEffect(() => {
    if (networkStateProvider.network) {
      setIsNetworkReady(true)
      
      const currentChainId = networkStateProvider.network.chainId || ''
      
      // Only reset if chainId actually changed and is not the initial load
      if (prevChainId !== currentChainId) {
        // Reset page to 1 when chainId changes
        setCurrentPage(1)
        changeFilterParam('page', 1, false)

      }
      
      // Update previous chainId
      setPrevChainId(currentChainId)
    }
  }, [networkStateProvider.network])

  const fetchActivities = async () => {
    // Only proceed if network is ready and exists
    if (!isNetworkReady || !networkStateProvider.network) return
    
    setIsActivitiesReady(false)
    console.log('🚀 ~ fetchActivities ~ filterParams:', filterParams)
    
    const filterEventTypes = filterParams.filterEventTypes?.map((type) => {
      let eventType = FilterEventType.fromName(type)?.getName()
      return eventType
    })
    
    // Get chainId from network provider with null check
    const chainId = networkStateProvider?.network?.chainId || ''
    
    const query = new Query({
      page: filterParams.page,
      limit: filterParams.limit,
      filterEventTypes: filterEventTypes,
      chains: filterParams.chains,
      collectionIds: filterParams.collectionIds,
      chainId: chainId, // Add chainId to query
    })
    
    await activityUseCase
      .totalCount(query)
      .then((res) => {
        setTotal(res)
      })
      .catch((_err) => {
        toast.error('Error while fetching activities')
      })
    await activityUseCase
      .search(query)
      .then((res) => {
        console.log('🚀 ~ .then ~ res:', res)
        setActivities(res)
      })
      .catch((_err) => {})
    setIsActivitiesReady(true)
  }

  const formatFilterEventTypes = () => {
    const types = FilterEventType.getResourceArray().map((type: any) => {
      return {
        id: type.id,
        name: type.label,
        value: true,
        type: type.name,
      }
    })
    setFilterEventTypes(types)
    setIsFilterEventTypesReady(true)
    
    // Initialize filterParams with all event types
    const allEventTypes = types.map((type) => type.type)
    setFilterParams({...filterParams, filterEventTypes: allEventTypes})
  }

  const formatChains = () => {
    const chains: Chain[] = Chain.getResourceArray().map((chain: any) => {
      return Chain.fromName(chain.name)
    })
    setFilterChains(chains)
    setIsFilterChainsReady(true)
  }
  const mapActivities = (activities: ActivityModel[]) => {
    try {
      let rs = []
      rs = activityUseCase.mapActivitiesForAssetV2(activities)
      setActivitiesTable(rs)
    }catch (err) {
      console.log('🚀 ~ file: activities.tsx:mapActivities ~ err:', err)
    }
  }
  useEffect(() => {
    fetchActivities()
  }, [filterParams, isNetworkReady])

  useEffect(() => {
    setIsActivitiesReady(true)
    mapActivities(activities)
  }, [activities])

  useEffect(() => {
    formatFilterEventTypes()
    formatChains()
  }, [])

  return (
    <>
      <Head>
        <title>Activity - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>
      <main>
        <Container className="pt-9">
          <div className="text-center">
            <Heading>Activity</Heading>
            <p className="mt-4 text-gray-1 dark:text-gray-3 text-stroke-thin">
              A wholesome farm owner in funbig. Upcoming gallery solo show in
              USA
            </p>
          </div>
          <div className="product-layout activity mt-12">
            <div className="product-layout-filter">
              <ToggleWrap title="Filter" contentClass="border-t-0">
                {/* Filter event type */}
                {isFilterEventTypesReady && (
                  <ToggleVertical toggleTitle="Event Type" defaultShow>
                    <ul className="fist-mt-none">
                      {filterEventTypes.map((type: any, index: number) => (
                        <li
                          key={index}
                          className="mt-5 font-medium flex items-center justify-between"
                        >
                          {type.name}
                          <Switch
                            defaultActive={type.value}
                            onClick={(activeState) => {
                              setCurrentPage(1)
                              changeFilterParam('page', 1)
                              changeFilterParam(
                                'filterEventTypes',
                                type.type,
                                activeState
                              )
                            }}
                          />
                        </li>
                      ))}
                    </ul>
                  </ToggleVertical>
                )}
                {/* Search collections */}
                <ToggleVerticalSearchCollection
                  defaultShow
                  changeFilterParam={(key, value, activeState) => {
                    // reset collectionIds
                    if( filterParams?.collectionIds && filterParams?.collectionIds?.length > 0){
                      for (let i = 0; i < filterParams?.collectionIds?.length; i++) {
                        changeFilterParam('collectionIds', filterParams.collectionIds[i], false)
                      }
                    }
                    changeFilterParam(key, value, activeState)
                    setCurrentPage(1)
                    changeFilterParam('page', 1)
                  }}
                />
                {/* Filter chains */}
                {/* {isFilterChainsReady && (
                  <ToggleVertical
                    toggleTitle="Chains"
                    defaultShow
                    contentWrapClass="rounded-b-lg"
                  >
                    <ul className="fist-mt-none">
                      {filterChains.map((chain: Chain, index: number) => (
                        <li
                          key={index}
                          className="mt-5 font-medium flex items-center justify-between"
                        >
                          <p className="flex items-center">
                            <span className="mr-3">{chain.getLabel()}</span>
                            <Image
                              src={chain.getIconPath() || ''}
                              width={25}
                              height={25}
                              alt={chain.getName()}
                            />
                          </p>
                          <Switch
                            onClick={(activeState) => {
                              setCurrentPage(1)
                              changeFilterParam('page', 1)
                              changeFilterParam('chains', chain, activeState)
                            }}
                          />
                        </li>
                      ))}
                    </ul>
                  </ToggleVertical>
                )} */}
              </ToggleWrap>
            </div>
            {!isActivitiesReady ? (
              <LoadingData />
            ) : (
              <>
                {activitiesTable?.length > 0 ? (
                  <div className="activity-board-wrapper">
                    <div className="w-full overflow-x-auto border-b">
                      <Table
                        head={activityTableHead}
                        data={activitiesTable}
                        className="min-w-[760px]"
                      />
                    </div>
                    <div className="w-full px-9 mt-5 pb-9">
                      <Pagination
                        currentPage={currentPage}
                        totalCount={total}
                        pageSize={currentLimit}
                        onPageChange={(page: number) => {
                          setCurrentPage(page)
                          setFilterParams({ ...filterParams, page })
                        }}
                        onChangePageSize={(limit: number) => {
                          setCurrentPage(1)
                          setCurrentLimit(limit)
                          setFilterParams({ ...filterParams, limit, page: 1 })
                        }}
                      />
                    </div>
                  </div>
                ) : (
                  <div className="w-full rounded-lg border flex justify-center items-center py-24">
                    <NoData />
                  </div>
                )}
              </>
            )}
          </div>
        </Container>
      </main>
    </>
  )
}

export default Activity
