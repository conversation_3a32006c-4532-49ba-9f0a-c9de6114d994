import { useState, useEffect } from 'react'
import { NextPage } from 'next'
import clsx from 'clsx'
import Head from 'next/head'
import Container from 'presentation/components/container'
import SelectSearch from 'presentation/components/select/select-search'
import ToggleWrap from 'presentation/components/toggle-wrap'
import ToggleVertical from 'presentation/components/toggle-wrap/toggle-vertical'
import FilterTime from 'presentation/components/filter-time'
import ButtonIcon from 'presentation/components/button/button-icon'
import Table from 'presentation/components/table'
import ActivityChart from 'presentation/page_components/collection/activityChart'
import AmountDistributionPie from 'presentation/page_components/collection-analytics/amountDistributionPie'
import HoldingPeriodDistributionPie from 'presentation/page_components/collection-analytics/holdingPeriodDistributionPie'
import DatetimeRange from 'presentation/components/datetimeRange'
import PeriodRangesSelect from 'presentation/components/select/period-ranges-select'
import RequireConnect from 'presentation/components/require-connect'
import { FilterParams } from 'types/type'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import { adminTableHeader } from 'presentation/page_components/admin/_adminTableHeaders'
import { Query } from 'domain/repositories/collection_daily_stat_repository/query'
import MeUseCase from 'presentation/use_cases/me_use_case'
import AssetDailyStatUseCase from 'presentation/use_cases/asset_daily_stat_use_case'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'

import { useRouter } from 'next/router'
const meUseCase = new MeUseCase()
const assetDailyStatUseCase = new AssetDailyStatUseCase()

const periodRanges = PeriodRange.getResourceArray()

//Todo remove mock data
import { selectSearchAccount } from 'presentation/controllers/mockData/common_mock_data'
import { adminFilterTime } from 'presentation/controllers/mockData/options'
import {
  adminSidebar,
  adminTableData,
} from 'presentation/controllers/mockData/admin_mock_data'
import {
  amountDistributionPieTempData,
  holdingPeriodDistributionTempData,
} from 'presentation/controllers/mockData/collection_analytics_mock_data'
import PeriodRange from 'domain/repositories/collection_daily_stat_repository/period_range'
import { toast } from 'react-toastify'
//Todo remove mock data

const Admin: NextPage = () => {
  const initDailyStatParams: FilterParams = {
    periodRange: 'lastNinetyDays',
    page: 1,
    limit: 60,
  }

  const {
    filterParams: filterDailyStatParams,
    changeFilterParam: changeDailyStatFilterParam,
  } = useChangeFilterParams(initDailyStatParams)

  const [showDateRange, setShowDateRange] = useState<boolean>(false)
  const [selectedPeriodRangeText, setSelectedPeriodRangeText] =
    useState<number>(90)
  const [isFetchingDailyStats, setIsFetchingDailyStats] =
    useState<boolean>(true)
  const [isFetchingAmountDistribution, setIsFetchingAmountDistribution] =
    useState(true)
  const [
    isFetchingHoldingPeriodDistribution,
    setIsFetchingHoldingPeriodDistribution,
  ] = useState<boolean>(true)
  const [isConnected, setIsConnected] = useState<boolean>(false)
  // Todo: remove any type
  const [holdingPeriodDistributionData, setHoldingPeriodDistributionData] =
    useState<any>([])
  const [dailyStats, setDailyStats] = useState<any>([])
  const [amountDistributionData, setAmountDistributionData] = useState<any>([])
  const router = useRouter()
  const authStateProvider = AuthStateProvider((state) => ({
    me: state.me,
  }))
  // End Todo

  const fetchMe = async () => {
    await meUseCase
      .loginStatus()
      .then((res) => {
        setIsConnected(res.isLoggedIn)
      })
      .catch((_err) => {
        toast.error('Failed to fetch user')
      })
  }

  const fetchDailyStats = async () => {
    // setIsFetchingDailyStats(true)
    // const query = new Query({
    //   periodRage: PeriodRange.fromName(filterDailyStatParams.periodRange!),
    //   page: filterDailyStatParams.page,
    //   limit: filterDailyStatParams.limit,
    // })
    // await assetDailyStatUseCase
    //   .search(query)
    //   .then((res) => {
    //     setDailyStats(res)
    //   })
    //   .catch((_err) => {
    //     toast.error('Failed to fetch daily stats')
    //   })
  }

  const fetchAmountDistribution = () => {
    setIsFetchingAmountDistribution(true)
    //TODO
    if (process.env.NEXT_PUBLIC_NODE_ENV === 'development') {
      let data = [...amountDistributionPieTempData]
      setAmountDistributionData(data)
    }
    //END TODO
    setIsFetchingAmountDistribution(false)
  }

  const fetchHoldingPeriodDistribution = () => {
    setIsFetchingHoldingPeriodDistribution(true)
    //TODO
    if (process.env.NEXT_PUBLIC_NODE_ENV === 'development') {
      let data = [...holdingPeriodDistributionTempData]
      setHoldingPeriodDistributionData(data)
    }
    //END TODO
    setIsFetchingHoldingPeriodDistribution(false)
  }

  useEffect(() => {
    setIsFetchingDailyStats(false)
  }, [dailyStats])

  useEffect(() => {
    fetchDailyStats()
  }, [filterDailyStatParams])

  useEffect(() => {
    fetchMe()
    fetchAmountDistribution()
    fetchHoldingPeriodDistribution()
  }, [])


  return (
    <>
      <Head>
        <title>Admin - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>
      <main>
        {isConnected ? (
          <Container className="pt-9">
            <div
              className={clsx(
                'flex flex-col-reverse md:flex-row',
                'items-center md:items-baseline md:justify-between',
                'w-full md:w-1/2 gap-4 md:gap-0'
              )}
            >
              <SelectSearch
                options={selectSearchAccount}
                nonSelectText="Account"
                className="md:w-4/6"
                optionListClass="w-max"
                childNode={(option, index) => (
                  <li
                    key={index}
                    className={clsx(
                      'px-6 py-1 font-medium cursor-pointer',
                      'hover:bg-green-2 dark:hover:bg-blue-1 transition'
                    )}
                  >
                    {option.name}
                  </li>
                )}
              />
              <p
                className={clsx(
                  'shrink-0 text-xl md:text-3xl xl:text-4xl',
                  'font-medium md:italic md:translate-x-1/2 text-dark-primary dark:text-white'
                )}
              >
                Dashboard
              </p>
            </div>

            <div className="mt-5 w-full grid gap-10 grid-cols-1 md:grid-cols-[250px_minmax(0,_1fr)]">
              <div>
                <ToggleWrap contentClass="rounded-t-10">
                  {adminSidebar.map((item, index) => (
                    <ToggleVertical
                      key={index}
                      toggleTitle={item.name}
                      titleLink={item.slug}
                      toggleTitleWrapClass={index === 0 ? 'rounded-t-10' : ''}
                      toggleTitleWrapClassWhenClose={
                        index === adminSidebar.length - 1 ? 'rounded-b-10' : ''
                      }
                      contentWrapClass={
                        index === adminSidebar.length - 1 ? 'rounded-b-10' : ''
                      }
                      defaultShow
                    >
                      {item.children && (
                        <p className="font-medium">TEXT LINK</p>
                      )}
                    </ToggleVertical>
                  ))}
                </ToggleWrap>
              </div>

              <div className="w-full">
                <div className="flex flex-wrap gap-4 items-center justify-between">
                  <p className="text-gray-1 dark:text-gray-3 text-stroke-thin shrink-0">
                    Showing 1 out of 1
                  </p>
                  <div className="flex items-center gap-5">
                    <FilterTime
                      options={adminFilterTime}
                      defaultSelected={adminFilterTime[0]}
                      onSelect={() => { }}
                    />
                    <div className="flex relative w-10 h-10 z-50">
                      <ButtonIcon
                        svgIcon="calendar"
                        iconClassName="w-5 h-5 stroke-primary-2 fill-primary-2"
                        onClick={() => setShowDateRange(!showDateRange)}
                        strokeWidth="1"
                      />
                      {showDateRange && (
                        <div className="absolute top-10 right-0">
                          <DatetimeRange
                            showTimeInput={false}
                            showDuration={false}
                            onCloseDateRange={() => {
                              setShowDateRange(false)
                            }}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="mt-4 rounded-20 w-full border border-gray-3 overflow-x-auto">
                  <Table
                    head={adminTableHeader}
                    data={adminTableData}
                    className="admin-dashboard-table min-w-[950px]"
                  />
                </div>

                {!isFetchingDailyStats && (
                  <div className="flex flex-col mt-10 px-3 md:px-16 py-4 rounded-10 border border-gray-3">
                    <span className="font-bold text-dark-primary dark:text-white text-lg pb-3.5">
                      Volume
                    </span>
                    <div className="flex flex-col w-full md:px-8">
                      <div className="flex justify-between items-center flex-wrap gap-4 mb-4">
                        <PeriodRangesSelect
                          periodRanges={periodRanges}
                          defaultSelected={periodRanges[4]}
                          onSelect={(option) => {
                            setSelectedPeriodRangeText(option.subValue)
                            changeDailyStatFilterParam(
                              'periodRange',
                              option.value
                            )
                          }}
                        />
                        <div className="flex flex-wrap justify-end w-full md:w-auto gap-4">
                          <div>
                            <p className="font-medium text-sm text-dark-3 dark:text-white">
                              {selectedPeriodRangeText} Day Avg. Price
                            </p>
                            <p className="font-bold text-primary-2">2.1691</p>
                          </div>
                          <div>
                            <p className="font-medium text-sm text-dark-3 dark:text-white">
                              {selectedPeriodRangeText} Day Volume
                            </p>
                            <p className="font-bold text-primary-2">
                              51,217.8958
                            </p>
                          </div>
                        </div>
                      </div>
                      <ActivityChart data={dailyStats} />
                    </div>
                  </div>
                )}

                <div className="mt-10 grid grid-cols-1 lg:grid-cols-2 gap-8 xl:px-50p">
                  {!isFetchingAmountDistribution && (
                    <AmountDistributionPie chartData={amountDistributionData} />
                  )}
                  {!isFetchingHoldingPeriodDistribution && (
                    <HoldingPeriodDistributionPie
                      chartData={holdingPeriodDistributionData}
                    />
                  )}
                </div>
              </div>
            </div>
          </Container>
        ) : (
          <RequireConnect />
        )}
      </main>
    </>
  )
}

export default Admin
