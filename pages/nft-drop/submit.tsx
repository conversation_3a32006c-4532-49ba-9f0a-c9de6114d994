import { NextPage } from 'next'
import Head from 'next/head'
import Image from 'next/image'
import { useState, useEffect } from 'react'
import DatePicker from 'react-datepicker'
import { useForm } from 'lib/hook/useForm'
import { formatDate, validateDate } from 'lib/utils'
import Container from 'presentation/components/container'
import Heading from 'presentation/components/heading'
import SvgIcon from 'presentation/components/svg-icon'
import Tooltip from 'presentation/components/tooltip'
import Select from 'presentation/components/select'
import Input from 'presentation/components/input'
import Button from 'presentation/components/button'
import NftDropCard from 'presentation/components/nft-drop'
import 'react-datepicker/dist/react-datepicker.css'
import Timepicker from 'presentation/components/timepicker'
import SelectCategories from 'presentation/components/select/select-categories'
import RequireConnect from 'presentation/components/require-connect'
import { paymentTokenImages } from 'lib/valueObjects'
import { toast } from 'react-toastify'
import { useRouter } from 'next/router'
import MultimediaUseCase from 'presentation/use_cases/multimedia_use_case'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'

const multimediaUseCase = new MultimediaUseCase()

//Todo remove mock data
import { nftDropItem } from 'presentation/controllers/mockData/nft_drop_mock_data'
import PaymentToken from 'domain/value_objects/payment_token'
//End todo remove mock data

const SubmitNftDrop: NextPage = () => {
  const initFormData = {
    email: '',
    logoImage: null,
    name: '',
    categories: [],
    description: '',
    paymentToken: null,
    floorPrice: 10,
    totalSupply: 10,
    startDatetime: new Date(),
    mintUrl: '',
    webUrl: '',
    discordUrl: '',
    twitterUsername: '',
  }

  const router = useRouter()
  const { formData, setFormValue } = useForm(initFormData)
  //Todo remove type any
  const [startDateInput, setStartDateInput] = useState<any>(new Date())
  const [startDate, setStartDate] = useState<any>(new Date())
  const [startTime, setStartTime] = useState<any>('00:00')
  const [paymentTokens, setPaymentTokens] = useState<any[]>([])
  //End Todo remove type any

  const authStateProvider = AuthStateProvider((state: any) => ({
    me: state.me,
  }))

  const uploadFile = async (file: any) => {
    if (file) {
      await multimediaUseCase
        .upload(file)
        .then((res) => {
          setFormValue('logoImage', res)
        })
        .catch((_error) => {
          toast.error('Upload failed')
          setFormValue('logoImage', null)
        })
    }
  }

  const formatPaymentTokens = () => {
    const tokens = PaymentToken.getResourceArray().map((token: any) => {
      return {
        id: token.id,
        name: token.label,
        value: token.name,
        icon: paymentTokenImages[token.name],
        chainName: token.chainName,
      }
    })
    setPaymentTokens(tokens)
  }

  const submitForm = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    //TODO: Post to server
    if (formData.name && formData.logoImage) {
      toast.success('Your nft drop has been submitted.')
      router.push('/nft-drop')
    } else {
      toast.error('Please fill all requied fields.')
    }
  }

  useEffect(() => {
    if (paymentTokens.length === 0) {
      formatPaymentTokens()
      setFormValue('paymentToken', paymentTokens[0])
    }
  }, [paymentTokens])

  useEffect(() => {
    const newDate = formatDate(startDate, true)
    setFormValue('startDatetime', new Date(`${newDate} ${startTime}`))
    setStartDateInput(formatDate(startDate))
  }, [startDate, startTime])

  return (
    <>
      <Head>
        <title>Submit NFT Drop - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>
      <main>
        {authStateProvider.me ? (
          <Container>
            <div className="container-lg pt-9 md:pb-0 pb-9">
              <Heading>Submit Your NFT Drops</Heading>
              <p className="text-gray-1 dark:text-gray-3 text-stroke-thin mt-4 text-center">
                Get your Project listed on NFT Drops Calendar! And We’ll review
                your submission as soon as possible!
              </p>

              <div className="sell-item-container">
                <div className="sell-item-flex">
                  <div className="sell-item-left">
                    <form
                      className="mx-auto collection-form"
                      onSubmit={submitForm}
                    >
                      <p>
                        <span className="text-red-1">*</span>
                        <span className="text-gray-1 dark:text-gray-3 text-stroke-thin">
                          Required Fields
                        </span>
                      </p>

                      <div className="w-full sm:w-1/2 mt-7">
                        <p className="font-medium mb-10p">
                          Email Address <span className="text-red-1">*</span>
                        </p>
                        <Input
                          type="email"
                          value={formData.email}
                          onChange={(e) =>
                            setFormValue('email', e.target.value)
                          }
                        />
                      </div>

                      <div className="mt-5">
                        {/* Image */}
                        <div className="w-full pr-8">
                          <p className="font-medium">
                            Logo image <span className="text-red-1">*</span>
                          </p>
                          <p className="mt-2 text-sm text-gray-1 dark:text-gray-3 text-stroke-thin">
                            This image will also be used for navigation.
                            <br />
                            350 x 350 recommended.
                          </p>
                          <div className="relative w-fit pt-3 pr-3 mt-3">
                            <div className="absolute top-0 right-0 cursor-pointer z-20">
                              <input
                                className="w-[22px] h-[22px] opacity-0 absolute"
                                type="file"
                                id="img"
                                name="logoImage"
                                accept="image/*"
                                onChange={(e: any) =>
                                  uploadFile(e.target.files[0])
                                }
                              />
                              <SvgIcon
                                name="plusCircle"
                                className="icon-22 stroke-none fill-primary-2"
                              />
                            </div>
                            <div className="upload-image-circle bg-gray-5 dark:bg-blue-1">
                              {formData.logoImage && (
                                <Image
                                  alt=""
                                  fill
                                  style={{ objectFit: 'cover' }}
                                  className="rounded-full"
                                  src={formData.logoImage.url}
                                />
                              )}
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="w-full sm:w-1/2 mt-7">
                        <p className="font-medium mb-10p">
                          Project Name <span className="text-red-1">*</span>
                        </p>
                        <Input type="text" value={formData.name} />
                      </div>

                      <SelectCategories
                        length={1}
                        onSelect={(option) =>
                          setFormValue('categories', option)
                        }
                      />

                      <div className="mt-5">
                        <p className="font-medium">Description</p>
                        <p className="mt-1 text-sm text-gray-1 dark:text-gray-3 text-stroke-thin">
                          {/* <span className="text-primary-2">
                            Markdown syntax
                          </span>{' '}
                          is supported. 0 of 1000 characters used. */}
                          {formData.description.length} of 1000 characters used.
                        </p>
                        <textarea
                          className="px-5 py-3 mt-2 rounded-3xl w-full outline-none bg-gray-5 dark:bg-blue-1"
                          value={formData.description}
                          onChange={(e) =>
                            setFormValue('description', e.target.value)
                          }
                          rows={3}
                        />
                      </div>

                      {/* Price */}
                      <div className="mt-5 flex items-center">
                        <p className="font-medium mr-1">Price</p>
                        <Tooltip contentWidth="lg:w-[295px]" iconSize="w-4 h-4">
                          File types supported: JPG, PNG, GIF, SVG, MP4, WEBM,
                          MP3, WAV, OGG, GLB, GLTF. Max size: 100 MB.
                        </Tooltip>
                      </div>
                      <div className="mt-3 flex sm:flex-nowrap flex-wrap">
                        {paymentTokens.length > 0 && (
                          <Select
                            options={paymentTokens}
                            showOptionIcon
                            rounded
                            onSelect={(option) =>
                              setFormValue('paymentToken', {
                                id: option.id,
                                name: option.value,
                                label: option.name,
                                chainName: option.chainName,
                              })
                            }
                            className="sm:mb-0 mb-2"
                            selectClassName="font-medium"
                          />
                        )}
                        <div>
                          <Input
                            type="number"
                            name="starting-price"
                            width="w-36"
                            className="no-arrow ml-2 md:ml-5"
                            value={formData.floorPrice}
                            onChange={(e) =>
                              setFormValue('floorPrice', e.target.value)
                            }
                          />
                          <p className="mt-1 text-sm text-gray-1 dark:text-gray-3 text-stroke-thin text-right">
                            $27,408.40
                          </p>
                        </div>
                      </div>

                      <div className="my-5 flex sm:flex-nowrap flex-wrap items-center">
                        <p className="font-medium mr-4 xl:mr-16">Supply</p>
                        <Input
                          className="no-arrow mr-4 xl:mr-10 sm:mb-0 mb-2"
                          width="w-[130px]"
                          type="number"
                          value={formData.totalSupply}
                          onChange={(e) =>
                            setFormValue('totalSupply', e.target.value)
                          }
                        />
                        <p className="text-gray-1 dark:text-gray-3 text-stroke-thin">
                          The total number of your drops
                        </p>
                      </div>

                      {/* Drop calendar */}
                      <p className="font-medium mb-10p">
                        Drop Date (UTC) <span className="text-red-1">*</span>
                      </p>
                      <div className="duration-daterange drop-date">
                        <Input
                          type="text"
                          name="drop-date"
                          width="w-[147px]"
                          className="mt-6 mb-8"
                          value={startDateInput}
                          onBlur={(e) => {
                            if (validateDate(e.target?.value)) {
                              const [day, month, year] = (
                                e.target?.value || '0/0/0'
                              ).split('/')
                              setStartDate(
                                new Date(year, parseInt(month) - 1, day)
                              )
                            } else {
                              setStartDateInput('')
                              setTimeout(() => {
                                setStartDateInput(formatDate(startDate))
                              }, 200)
                            }
                          }}
                        />
                        <DatePicker
                          dateFormat="DD/MM/YYYY"
                          selected={startDate}
                          onChange={(date: Date) => {
                            setStartDate(date)
                          }}
                          selectsStart
                          inline
                        />
                        <Timepicker
                          onSelect={(value) =>
                            setStartTime(`${value.hour}:${value.minute}`)
                          }
                        />
                      </div>

                      {/* Link */}
                      <div className="mt-5">
                        <p className="font-medium">Links</p>
                        <div className="mt-[10px] flex items-center">
                          <SvgIcon
                            name="coin"
                            className="w-6 h-6 mr-3 stroke-none fill-primary-2"
                          />
                          <p className="mr-7 w-20 font-medium">
                            Mint Link <span className="text-red-1">*</span>
                          </p>
                          <Input
                            type="text"
                            placeholder="Your Site"
                            width="sm:w-80 w-full"
                            value={formData.mintUrl}
                            onChange={(e) =>
                              setFormValue('mintUrl', e.target.value)
                            }
                          />
                        </div>
                        <div className="mt-[10px] flex items-center">
                          <SvgIcon
                            name="globe"
                            className="w-6 h-6 mr-3 stroke-none fill-primary-2"
                          />
                          <p className="mr-7 w-20 font-medium">Your Site</p>
                          <Input
                            type="text"
                            placeholder="Your Site"
                            width="sm:w-80 w-full"
                            value={formData.siteUrl}
                            onChange={(e) =>
                              setFormValue('siteUrl', e.target.value)
                            }
                          />
                        </div>
                        <div className="mt-[10px] flex items-center">
                          <SvgIcon
                            name="discord"
                            className="w-6 h-6 mr-3 stroke-none fill-primary-2"
                          />
                          <p className="mr-7 w-20 font-medium">Discord</p>
                          <Input
                            type="text"
                            placeholder="Your Handle"
                            width="sm:w-56 w-full"
                            value={formData.discordUrl}
                            onChange={(e) =>
                              setFormValue('discordUrl', e.target.value)
                            }
                          />
                        </div>
                        <div className="mt-[10px] flex items-center">
                          <SvgIcon
                            name="twitter"
                            className="w-6 h-6 mr-3 stroke-none fill-primary-2"
                          />
                          <p className="mr-7 w-20 font-medium">Twitter</p>
                          <Input
                            type="text"
                            placeholder="Your Handle"
                            width="sm:w-56 w-full"
                            value={formData.twitterUsername}
                            onChange={(e) =>
                              setFormValue('twitterUsername', e.target.value)
                            }
                          />
                        </div>
                      </div>

                      <Button
                        variant="dark"
                        htmltype="submit"
                        className="mt-6"
                        onClick={(e) => submitForm(e)}
                      >
                        Submit
                      </Button>
                    </form>
                  </div>

                  <div className="sell-item-right xl:pt-[130px]">
                    <p className="font-medium">Preview</p>
                    <div className="w-40 mx-auto mt-5">
                      <NftDropCard record={nftDropItem} />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Container>
        ) : (
          <RequireConnect />
        )}
      </main>
    </>
  )
}

export default SubmitNftDrop
