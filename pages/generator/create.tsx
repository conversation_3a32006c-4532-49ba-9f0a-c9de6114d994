import Head from 'next/head'
import Image from 'next/image'
import Link from 'next/link'
import clsx from 'clsx'
import { NextPage } from 'next'
import { useState, useEffect } from 'react'
import Container from 'presentation/components/container'
import Heading from 'presentation/components/heading'
import Select from 'presentation/components/select'
import Button from 'presentation/components/button'
import SvgIcon from 'presentation/components/svg-icon'
import Input from 'presentation/components/input'
import SelectTag from 'presentation/components/select/select-tag'
import SelectCategories from 'presentation/components/select/select-categories'
import Tooltip from 'presentation/components/tooltip'
import Switch from 'presentation/components/switch'
import InputCollectionURL from 'presentation/components/input/input-collection-url'
import AddCollaborator from 'presentation/components/modal/add-collaborator'
import { useForm } from 'lib/hook/useForm'
import { shortenAddress } from 'lib/utils'
import { toast } from 'react-toastify'
import { useRouter } from 'next/router'
import RequireConnect from 'presentation/components/require-connect'
import { paymentTokenImages } from 'lib/valueObjects'
import User from 'domain/models/user'
import CollectionUseCase from 'presentation/use_cases/collection_use_case'
import MultimediaUseCase from 'presentation/use_cases/multimedia_use_case'
import Chain from 'domain/value_objects/chain'
import Collection from 'domain/models/collection'
import { DateTime } from 'luxon'
import PaymentToken from 'domain/value_objects/payment_token'
import MeUseCase from 'presentation/use_cases/me_use_case'

const multimediaUseCase = new MultimediaUseCase()
const collectionsUseCase = new CollectionUseCase()
const meUseCase = new MeUseCase()
const CreateCollection: NextPage = () => {
  const initFormData = {
    name: '',
    logoImage: '',
    featuredImage: '',
    bannerImage: '',
    slug: '',
    description: '',
    categories: [],
    paymentTokens: [],
    blockchain: '',
    siteUrl: '',
    discordUrl: '',
    instagramUrl: '',
    mediumUrl: '',
    telegramUrl: '',
    twitterUrl: '',
    owners: [],
    collaborators: [],
    isExplicit: true,
    isNFTDrops: true,
  }

  const [showModal, setShowModal] = useState(false)
  const [isCreator, setIsCreator] = useState(true)
  const { formData, setFormValue } = useForm(initFormData)
  const [owners, setOwners] = useState<User[]>([])
  const [collaborators, setCollaborators] = useState<{ value: User }[]>([])
  const [filterChains, setFilterChains] = useState<Chain[]>([])
  const [isFilterChainsReady, setIsFilterChainsReady] = useState(false)
  const [filterPaymentTokens, setFilterPaymentTokens] = useState<any>([])
  const [paymentFixedOptions, setPaymentFixedOptions] = useState<any>([])
  const [isFilterPaymentTokensReady, setIsFilterPaymentTokensReady] =
    useState(false)
  const [isConnected, setIsConnected] = useState(false)
  const [error, setError] = useState('')
  const [canSubmit, setCanSubmit] = useState(false)
  const router = useRouter()

  const formatChains = () => {
    const chains = Chain.getResourceArray().map((chain) => {
      return Chain.fromName<Chain>(chain.name)
    })
    setFilterChains(chains)
    setIsFilterChainsReady(true)
  }

  const fetchMe = async () => {
    await meUseCase
      .loginStatus()
      .then((res) => {
        setIsConnected(res.isLoggedIn)
        if (res.isLoggedIn) {
          setOwners([res.user!])
          setCollaborators([{ value: res.user! }])
        }
      })
      .catch(() => {
        setIsConnected(false)
      })
  }

  const formatPaymentTokens = () => {
    const paymentTokens = PaymentToken.getResourceArray().map((token) => {
      return {
        id: token.id,
        name: token.label,
        value: false,
        type: token.name,
        icon: paymentTokenImages[token.name] || '',
      }
    })
    setFilterPaymentTokens(paymentTokens)
    const fixedOptions = [PaymentToken.eth(), PaymentToken.weth()]
    setPaymentFixedOptions(fixedOptions)
    setIsFilterPaymentTokensReady(true)
  }

  const uploadFile = async (file: any, key: string) => {
    if (file) {
      multimediaUseCase
        .upload(file)
        .then((res) => {
          setFormValue(key, res)
        })
        .catch((err) => {
          toast.error('Failed to upload file.')
        })
    }
  }

  const submitForm = async (e: any) => {
    e.preventDefault()
    if (!formData.logoImage || (!formData.name && !isConnected)) {
      setError('Required fields must have data.')
      toast.error('Please fill the required fields')
    } else {
      setError('')
      const collectionData = {
        ...formData,
        owner: owners[0],
        collaborators: collaborators.map((collaborator) => collaborator.value),
      }
      const newCollection = new Collection(
        undefined,
        collectionData.slug,
        collectionData.name,
        collectionData.symbol,
        collectionData.description,
        collectionData.siteUrl,
        collectionData.discordUrl,
        collectionData.telegramUrl,
        collectionData.twitterUrl,
        collectionData.mediumUrl,
        null,
        collectionData.instagramUrl,
        collectionData.isExplicit,
        0,
        0,
        0,
        0,
        0,
        0,
        DateTime.now(),
        DateTime.now(),
        collectionData.logoImage,
        collectionData.featuredImage,
        collectionData.bannerImage,
        collectionData.categories,
        collectionData.owner,
        collectionData.paymentTokens,
        [],
        collectionData.collaborators,
        collectionData.collectionAddress,
        collectionData.owners,
        collectionData.chainId,
        collectionData.schemaId,
        null,
        null,
        []
      )
      collectionsUseCase
        .save(newCollection)
        .then((res) => {
          toast.success('Your new collection has been created.')
          router.push('/account/collections')
        })
        .catch((err) => {
          toast.error('Failed to create new collection.')
        })
    }
  }

  useEffect(() => {
    if (formData.name && formData.logoImage && formData.slug) {
      setCanSubmit(true)
    } else {
      setCanSubmit(false)
    }
  }, [formData])

  useEffect(() => {
    setFormValue('owners', owners)
  }, [owners])

  useEffect(() => {
    setFormValue('collaborators', collaborators)
  }, [collaborators])

  useEffect(() => {
    fetchMe()
    formatChains()
    formatPaymentTokens()
  }, [])

  return (
    <>
      <Head>
        <title>Create Collection - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>
      <main>
        {isConnected ? (
          <Container className="pt-9 md:pb-0 pb-9">
            <div className="text-center">
              <Heading>Create a Collection</Heading>
            </div>
            <div className="container-lg mt-5 pb-[6px]">
              <div className="collection-form-cont">
                <form className="mx-auto collection-form" onSubmit={submitForm}>
                  <div className="text-right">
                    <Button
                      variant="light"
                      onClick={(e) => {
                        e.preventDefault()
                      }}
                    >
                      Preview
                      <SvgIcon
                        name="eye"
                        className="w-5 h-5 ml-2 stroke-none fill-primary-2 dark:fill-white"
                      />
                    </Button>
                  </div>

                  <p>
                    <span className="text-red-1">{'* '}</span>
                    <span className="text-gray-1">Required Fields</span>
                  </p>

                  <div className="mt-5 flex xl:flex-nowrap flex-wrap">
                    {/* Logo image */}
                    <div className="md:w-1/2 w-full pr-8 mb-5 flex-none">
                      <div className="flex justify-between items-center">
                        <p className="font-medium text-dark-primary">
                          Logo image <span className="text-red-1">{' *'}</span>
                        </p>
                        <Tooltip contentWidth="lg:w-[295px]" iconSize="w-4 h-4">
                          File types supported: JPG, PNG, GIF, SVG, MP4, WEBM,
                          MP3, WAV, OGG, GLB, GLTF. Max size: 100 MB
                        </Tooltip>
                      </div>
                      <p className="mt-2 text-sm text-gray-1">
                        This image will also be used for navigation. 350 x 350
                        recommended.
                      </p>
                      <div className="relative w-fit pt-3 pr-3 mt-3">
                        <div className="absolute top-0 right-0">
                          <input
                            className="w-5 h-5 opacity-0 absolute"
                            type="file"
                            id="img"
                            name="logoImage"
                            accept="image/*"
                            onChange={(e: any) =>
                              uploadFile(e.target.files[0], 'logoImage')
                            }
                          />
                          <SvgIcon
                            name="plusCircle"
                            className="w-5 h-5 stroke-none fill-primary-2 cursor-pointer"
                          />
                        </div>
                        <div className="upload-image-circle bg-gray-5 dark:bg-blue-1">
                          {formData.logoImage && (
                            <Image
                              alt=""
                              fill
                              style={{ objectFit: 'cover' }}
                              className="rounded-full"
                              src={formData.logoImage?.url}
                            />
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Featured image */}
                    <div className="md:w-1/2 xl:min-w-[480px] w-full">
                      <p className="font-medium text-dark-primary">
                        Featured image
                      </p>
                      <p className="mt-2 text-sm text-gray-1">
                        This image will be used for featuring your collection on
                        the homepage, category pages, or other promotional
                        areas. 600 x 375 recommended.
                      </p>
                      <div className="relative w-fit mt-2">
                        <div className="absolute top-3 right-3">
                          <input
                            className="w-5 h-5 opacity-0 absolute"
                            type="file"
                            id="img"
                            name="featuredImage"
                            accept="image/*"
                            onChange={(e: any) =>
                              uploadFile(e.target.files[0], 'featuredImage')
                            }
                          />
                          <SvgIcon
                            name="plusCircle"
                            className="w-5 h-5 stroke-none fill-primary-2 cursor-pointer"
                          />
                        </div>
                        <div className="upload-image-square bg-gray-5 dark:bg-blue-1">
                          {formData.featuredImage && (
                            <Image
                              alt=""
                              fill
                              style={{ objectFit: 'cover' }}
                              className="rounded-10"
                              src={formData.featuredImage?.url}
                            />
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Featured image */}
                  <div className="mt-7">
                    <p className="font-medium  text-dark-primary">
                      Banner image
                    </p>
                    <p className="mt-1 text-sm text-gray-1">
                      This image will appear at the top of your collection page.
                      Avoid including too much text in this banner image, as the
                      dimensions change on different devices. 1280 x 200
                      recommended.
                    </p>
                  </div>
                  <div className="relative w-full mt-2">
                    <div className="absolute top-3 right-3">
                      <input
                        className="w-5 h-5 opacity-0 absolute"
                        type="file"
                        id="img"
                        name="bannerImage"
                        accept="image/*"
                        onChange={(e: any) =>
                          uploadFile(e.target.files[0], 'bannerImage')
                        }
                      />
                      <SvgIcon
                        name="plusCircle"
                        className="w-5 h-5 stroke-none fill-primary-2 cursor-pointer"
                      />
                    </div>
                    <div className="upload-image-reactangle bg-gray-5 dark:bg-blue-1">
                      {formData.bannerImage && (
                        <Image
                          alt=""
                          fill
                          style={{ objectFit: 'cover' }}
                          className="rounded-10"
                          src={formData.bannerImage?.url}
                        />
                      )}
                    </div>
                  </div>

                  {/* Name */}
                  <div className="md:w-1/2 w-full mt-7">
                    <p className="font-medium text-dark-primary">
                      Name <span className="text-red-1">*</span>
                    </p>
                    <Input
                      type="text"
                      className="mt-10p"
                      value={formData.name}
                      onChange={(e: any) =>
                        setFormValue('name', e.target.value)
                      }
                    />
                  </div>

                  {/* URL */}
                  <InputCollectionURL
                    defaultValue={formData.slug}
                    chainId={formData.chainId}
                    onChange={(value) => setFormValue('slug', value)}
                  />

                  {/* Description */}
                  <div className="mt-5">
                    <p className="font-medium text-dark-primary">Description</p>
                    <p className="mt-1 text-sm text-gray-1">
                      {/* <span className="text-primary-2">Markdown syntax</span> is
                      supported. 0 of 1000 characters used. */}
                      {formData.description.length} of 1000 characters used.
                    </p>
                    <textarea
                      className="px-5 py-3 mt-2 rounded-3xl w-full outline-none bg-gray-5 dark:bg-blue-1"
                      rows={3}
                      value={formData.description}
                      maxLength={1000}
                      onChange={(e: any) =>
                        setFormValue('description', e.target.value)
                      }
                    />
                  </div>

                  {/* Category */}
                  <SelectCategories
                    length={1}
                    onSelect={(option) => setFormValue('categories', option)}
                  />

                  {/* Link */}
                  <div className="mt-5">
                    <p className="font-medium text-dark-primary">Links</p>
                    <div className="mt-[10px] flex items-center">
                      <SvgIcon
                        name="globe"
                        className="w-7 h-5 mr-3 stroke-none fill-primary-2"
                      />
                      <p className="mr-7 w-[110px] font-medium text-dark-primary flex-none">
                        Your Site
                      </p>
                      <Input
                        type="text"
                        placeholder="Your site"
                        width="sm:w-80 w-full"
                        value={formData.siteUrl}
                        onChange={(e) =>
                          setFormValue('siteUrl', e.target.value)
                        }
                      />
                    </div>
                    {/* X (Twitter) URL Input */}
                    <div className="mt-[10px] flex items-center">
                      <SvgIcon
                        name="twitter"
                        className="w-7 h-5 mr-3 stroke-none fill-primary-2"
                      />
                      <p className="mr-7 w-[110px] font-medium text-dark-primary flex-none whitespace-nowrap">
                        X (Twitter) <span className='text-xs font-normal'>(URL)</span>
                      </p>
                      <Input
                        type="text"
                        placeholder="Your X (Twitter)"
                        width="sm:w-80 w-full"
                        value={formData.twitterUrl}
                        onChange={(e) =>
                          setFormValue('twitterUrl', e.target.value)
                        }
                      />
                    </div>
                    <div className="mt-[10px] flex items-center">
                      <SvgIcon
                        name="discord"
                        className="w-7 h-5 mr-3 stroke-none fill-primary-2"
                      />
                      <p className="mr-7 w-[110px] font-medium text-dark-primary flex-none">
                        Discord
                      </p>
                      <Input
                        type="text"
                        placeholder="Your placeholder"
                        width="sm:w-56 w-full"
                        value={formData.discordUrl}
                        onChange={(e) =>
                          setFormValue('discordUrl', e.target.value)
                        }
                      />
                    </div>
                    <div className="mt-[10px] flex items-center">
                      <SvgIcon
                        name="instagram"
                        className="w-7 h-5 mr-3 stroke-none fill-primary-2"
                      />
                      <p className="mr-7 w-[110px] font-medium text-dark-primary">
                        Instagram
                      </p>
                      <Input
                        type="text"
                        placeholder="Your placeholder"
                        width="sm:w-56 w-full"
                        value={formData.instagramUrl}
                        onChange={(e) =>
                          setFormValue('instagramUrl', e.target.value)
                        }
                      />
                    </div>
                    <div className="mt-[10px] flex items-center">
                      <SvgIcon
                        name="medium"
                        className="w-7 h-5 mr-3 stroke-none fill-primary-2"
                      />
                      <p className="mr-7 w-[110px] font-medium text-dark-primary">
                        Medium
                      </p>
                      <Input
                        type="text"
                        placeholder="Your placeholder"
                        width="sm:w-56 w-full"
                        value={formData.mediumUrl}
                        onChange={(e) =>
                          setFormValue('mediumUrl', e.target.value)
                        }
                      />
                    </div>
                    <div className="mt-[10px] flex items-center">
                      <SvgIcon
                        name="telegram"
                        className="w-7 h-5 mr-3 stroke-none fill-primary-2"
                      />
                      <p className="mr-7 w-[110px] font-medium text-dark-primary">
                        Telegram
                      </p>
                      <Input
                        type="text"
                        placeholder="Your placeholder"
                        width="sm:w-56 w-full"
                        value={formData.telegramUrl}
                        onChange={(e) =>
                          setFormValue('telegramUrl', e.target.value)
                        }
                      />
                    </div>
                  </div>

                  {/* Apply for NFT Drops */}
                  <p className="mt-5 font-medium">Apply for NFT Drops</p>
                  <div className="my-10p flex items-center">
                    <p className="text-sm text-gray-1">
                      Would you like to be listed on
                      <Link href="" className="text-primary-2"> NFT Drops?</Link>
                    </p>
                    <Tooltip>Would you like to be listed on NFT Drops?</Tooltip>
                  </div>
                  <Switch
                    defaultActive={true}
                    onClick={(activeState) =>
                      setFormValue('isNFTDrops', activeState)
                    }
                  />

                  {/* Creator Earnings */}
                  <p className="mt-5 font-medium text-dark-primary">
                    Creator Earnings
                  </p>
                  <p className="mt-2 text-sm text-gray-1">
                    Collect a fee when a user re-sells an item you originally
                    created. This is deducted from the final sale price and paid
                    monthly to a payout address of your choosing.
                  </p>
                  {owners && owners.length > 0 && (
                    <>
                      <div className="flex mt-3">
                        <div className="w-1/2">
                          <ul className="space-y-3">
                            {owners?.map((owner: any, index: number) => (
                              <li
                                key={`owner-${index}`}
                                className={clsx(
                                  'rounded-3xl py-2 px-2 bg-gray-4 dark:bg-blue-1 flex items-center',
                                  index !== 0 && 'relative'
                                )}
                              >
                                {index === 0 ? (
                                  <>
                                    <Image
                                      src="/asset/images/owner-avatar.png"
                                      alt=""
                                      width={32}
                                      height={32}
                                      className="rounded-full"
                                    />
                                    <span className="ml-2 text-primary-2 font-medium">
                                      you
                                    </span>
                                    <span className="ml-3 text-dark-blue">
                                      e.g.2.5
                                    </span>
                                  </>
                                ) : (
                                  <>
                                    <SvgIcon
                                      name="plusCircle"
                                      className="w-5 h-5 absolute -top-1 right-0 rotate-45 stroke-none fill-primary-2 cursor-pointer"
                                      onClick={() => {
                                        let filteredArr = owners.filter(
                                          (record) =>
                                            JSON.stringify(record) !==
                                            JSON.stringify(owner)
                                        )
                                        setOwners(filteredArr)
                                      }}
                                    />
                                    <Image
                                      src="/asset/images/owner-avatar.png"
                                      alt=""
                                      width={32}
                                      height={32}
                                      className="rounded-full"
                                    />
                                    <span className="ml-2 text-primary-2 font-medium">
                                      {shortenAddress(owner.username)}
                                    </span>
                                    <span className="ml-3 text-dark-blue">
                                      e.g.2.5
                                    </span>
                                  </>
                                )}
                              </li>
                            ))}
                          </ul>
                        </div>
                        <div className="h-fit mt-3 ml-5">
                          <SvgIcon
                            name="plusCircle"
                            className="w-5 h-5 cursor-pointer stroke-none fill-primary-2"
                            onClick={() => {
                              setShowModal(true)
                              setIsCreator(true)
                            }}
                          />
                        </div>
                      </div>
                    </>
                  )}

                  {/* Blockchain */}
                  <div className="mt-5 flex items-center">
                    <p className="mr-8 font-medium text-dark-primary">
                      Blockchain
                    </p>
                    {isFilterChainsReady && (
                      <Select
                        options={filterChains}
                        showOptionIcon
                        rounded
                        isBlockchainFilter={true}
                        onSelect={(option) =>
                          setFormValue('blockchain', option)
                        }
                      />
                    )}
                  </div>

                  {/* Payment */}
                  <div className="mt-5">
                    <p className="font-medium text-dark-primary">
                      Payment tokens
                    </p>
                    <div className="flex items-center">
                      <p className="text-sm text-gray-1">
                        These tokens can be used to buy and sell your items.
                      </p>
                      <Tooltip>This is tool tip</Tooltip>
                    </div>
                    {isFilterPaymentTokensReady && (
                      <SelectTag
                        fixedOptions={paymentFixedOptions}
                        options={filterPaymentTokens}
                        onSelect={(option: Array<{}>) =>
                          setFormValue('paymentTokens', option)
                        }
                      />
                    )}
                  </div>

                  {/* Explicit & sensitive content */}
                  <div className="mt-5">
                    <p className="font-medium text-dark-primary">
                      Explicit & sensitive content
                    </p>
                    <div className="flex items-center">
                      <p className="text-sm text-gray-1">
                        Set this item as explicit and sensitive content
                      </p>
                      <Tooltip>This is tool tip</Tooltip>
                    </div>
                    <Switch
                      className="mt-[10px]"
                      defaultActive={formData.isExplicit}
                      onClick={(isExplicit) =>
                        setFormValue('isExplicit', isExplicit)
                      }
                    />
                  </div>

                  {/* Collaborators */}
                  <div className="mt-5">
                    <p className="font-medium text-dark-primary">
                      Collaborators
                    </p>
                    <p className="text-sm text-gray-1">
                      Collaborators can modify collection settings, receive
                      payments for items they created and create new items.
                      <br />
                      Collaborators can not change creator earnings percentages
                      or payout address.
                    </p>
                  </div>
                  {collaborators && collaborators.length > 0 && (
                    <div className="mt-[14px]">
                      <ul className="flex flex-wrap items-center gap-3">
                        {collaborators?.map(
                          (collaborator: any, index: number) => (
                            <li
                              key={`collaborator-${index}`}
                              className={clsx(
                                'px-[9px] py-[10px] rounded-10 border border-gray-3 flex items-center',
                                index !== 0 && 'relative'
                              )}
                            >
                              {index === 0 ? (
                                <>
                                  <Image
                                    src="/asset/images/owner-avatar.png"
                                    alt=""
                                    width={32}
                                    height={32}
                                    className="rounded-full"
                                  />
                                  <p className="ml-3 text-primary-3 font-medium p-1">
                                    you
                                  </p>
                                </>
                              ) : (
                                <>
                                  <SvgIcon
                                    name="plusCircle"
                                    className="w-5 h-5 absolute -top-1 -right-1 rotate-45 stroke-none fill-primary-2 cursor-pointer"
                                    onClick={() => {
                                      let filteredArr = collaborators.filter(
                                        (record) =>
                                          JSON.stringify(record) !==
                                          JSON.stringify(collaborator)
                                      )
                                      setCollaborators(filteredArr)
                                    }}
                                  />
                                  <Image
                                    src="/asset/images/owner-avatar.png"
                                    alt=""
                                    width={32}
                                    height={32}
                                    className="rounded-full"
                                  />
                                  <p className="ml-3 text-primary-2 font-medium">
                                    {shortenAddress(
                                      collaborator.value.username
                                    )}
                                  </p>
                                </>
                              )}
                            </li>
                          )
                        )}
                        <SvgIcon
                          name="plusCircle"
                          className="w-5 h-5 ml-5 stroke-none fill-primary-2 cursor-pointer"
                          onClick={() => {
                            setShowModal(true)
                            setIsCreator(false)
                          }}
                        />
                      </ul>
                    </div>
                  )}

                  <div className="flex flex-wrap w-full">
                    <Button
                      variant="dark"
                      className="mt-6 flex-none"
                      disable={!canSubmit}
                    >
                      Create
                    </Button>
                    {error && (
                      <span className="text-red-1 py-3.5 font-medium w-full">
                        {error}
                      </span>
                    )}
                  </div>
                </form>
              </div>
            </div>
          </Container>
        ) : (
          <RequireConnect />
        )}

        {showModal && (
          <AddCollaborator
            isShow={showModal}
            onClose={() => setShowModal(false)}
            onSubmit={(user) => {
              if (isCreator) {
                let duplicateArr = owners.filter(
                  (owner) => owner.id === user.id
                )
                if (duplicateArr.length === 0) {
                  setOwners([...owners, user])
                }
              } else {
                let duplicateArr = collaborators.filter(
                  (collaborator) => collaborator.value.id === user.id
                )
                if (duplicateArr.length === 0) {
                  setCollaborators([...collaborators, { value: user }])
                }
              }
              setShowModal(false)
            }}
          />
        )}
      </main>
    </>
  )
}

export default CreateCollection
