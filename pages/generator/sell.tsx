import { NextPage } from 'next'
import Head from 'next/head'
import Image from 'next/image'
import Link from 'next/link'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import Container from 'presentation/components/container'
import Heading from 'presentation/components/heading'
import SvgIcon from 'presentation/components/svg-icon'
import Tooltip from 'presentation/components/tooltip'
import Input from 'presentation/components/input'
import Button from 'presentation/components/button'
import ButtonTag from 'presentation/components/button/button-tag'
import PreviewAsset from 'presentation/components/asset/preview'
import SellFixedPriceTab from 'presentation/page_components/generator/sellFixedPriceTab'
import SellTimedAuctionTab from 'presentation/page_components/generator/sellTimedAuctionTab'
import { paymentTokenImages, auctionMethodIcons } from 'lib/valueObjects'
import { toast } from 'react-toastify'
import PaymentToken from 'domain/value_objects/payment_token'
import AuctionMethod from 'domain/models/listing/auction_method'
import ListingUseCase from 'presentation/use_cases/listing_use_case'
import MultimediaUseCase from 'presentation/use_cases/multimedia_use_case'
import ListingType from 'domain/models/listing/listing_type'
import Listing from 'domain/models/listing'
import { DateTime } from 'luxon'
//import Status from 'domain/models/listing/status'
import { Asset } from 'types/type'
import { useAuthentication } from 'lib/hook/auth_hook'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'

const multimediaUseCase = new MultimediaUseCase()
const listingUseCase = new ListingUseCase()

const Sell: NextPage = () => {
  // const router = useRouter()
  // const [activeTag, setActiveTag] = useState('fixedPrice')
  // const [asset, setAsset] = useState<Asset>()
  // const [paymentTokens, setPaymentTokens] = useState<any>([])
  // const [selectedPaymentToken, setSelectedPaymentToken] = useState<any>({})
  // const [auctionMethods, setAuctionMethods] = useState<any>({})
  // const { getProvider } = useAuthentication()

  // const [formData, setFormData] = useState({
  //   listingTypeId: 1,
  //   auctionMethodId: 1,
  //   mysteryBox: {},
  //   revealDatetime: '',
  //   paymentTokenId: 1,
  //   price: 0,
  //   startingPrice: 0,
  //   startAt: new Date(),
  //   endAt: new Date(),
  //   isBundle: true,
  //   bundleName: '',
  //   bundleDescription: '',
  //   isReservedForBuyer: true,
  //   reservedBuyerAddress: '',
  //   hasReservePrice: true,
  //   reservePrice: 0,
  //   size: 10000,
  // })

  // const authStateProvider = AuthStateProvider((state: any) => ({
  //   me: state.me,
  // }))

  // const uploadFile = async (file: File) => {
  //   if (file) {
  //     multimediaUseCase
  //       .upload(file)
  //       .then((res) => {
  //         setFormValue('mysteryBox', res)
  //       })
  //       .catch((err) => {
  //         toast.error('Failed to upload file')
  //       })
  //   }
  // }

  // const formatPaymentTokens = () => {
  //   const tokens = PaymentToken.getResourceArray().map((token) => {
  //     return {
  //       id: token.id,
  //       name: token.label,
  //       value: token.name,
  //       icon: paymentTokenImages[token.name],
  //     }
  //   })
  //   setPaymentTokens(tokens)
  // }

  // const formatAuctionMethods = () => {
  //   const methods = AuctionMethod.getResourceArray().map((method) => {
  //     return {
  //       id: method.id,
  //       name: method.label,
  //       value: method.name,
  //       icon: auctionMethodIcons[method.name],
  //     }
  //   })
  //   setAuctionMethods(methods)
  // }

  // const setFormValue = (field: any, value: any) => {
  //   setFormData({ ...formData, [field]: value })
  // }

  // const submitForm = async (e: any) => {
  //   e.preventDefault()
  //   //Todo update the asset with the form data
  //   const newListing = Listing.newEmtyListing()
  //   // const newListing = new Listing(
  //   //   undefined,
  //   //   null,
  //   //   [],
  //   //   [],
  //   //   [1],
  //   //   ListingType.fromId(formData.listingTypeId),
  //   //   formData.auctionMethodId == 1
  //   //     ? AuctionMethod.sellToHighestBidder()
  //   //     : AuctionMethod.sellWithDecliningPrice(),
  //   //   PaymentToken.eth(),
  //   //   formData.price,
  //   //   formData.startingPrice,
  //   //   formData.startingPrice,
  //   //   DateTime.now(),
  //   //   DateTime.now(),
  //   //   formData.isBundle,
  //   //   formData.bundleName,
  //   //   formData.bundleDescription,
  //   //   formData.isReservedForBuyer,
  //   //   formData.reservedBuyerAddress,
  //   //   formData.hasReservePrice,
  //   //   formData.reservePrice,
  //   //   Status.fromId(1),
  //   //   DateTime.now(),
  //   //   DateTime.now(),
  //   //   '',
  //   //   ''
  //   // )
  //   const provider = await getProvider()
  //   listingUseCase
  //     .save(
  //       newListing,
  //       provider,
  //       authStateProvider.me.publicAddresses[0],
  //       process.env.NEXT_PUBLIC_ETHEREUM_SEPOLIA_DOMAIN_REGISTRY_ADDRESS!,
  //       process.env.NEXT_PUBLIC_ETHEREUM_SEPOLIA_MARKETPLACE_ADDRESS!,
  //     )
  //     .then((res) => {
  //       toast.success('Your asset has been listed.')
  //       router.push('/generator')
  //     })
  //     .catch((err) => {
  //       toast.error('Failed to list asset')
  //     })
  // }

  // useEffect(() => {
  //   formatPaymentTokens()
  //   formatAuctionMethods()
  // }, [])

  // return (
  //   <>
  //     <Head>
  //       <title>Asset Item - Quill</title>
  //       <meta name="description" content="Enjoy Quill" />
  //     </Head>
  //     <main>
  //       <Container>
  //         <div className="container-lg pt-9 md:pb-0 pb-9">
  //           <Heading className="text-dark-primary dark:text-white">
  //             List item for sale
  //           </Heading>

  //           <div className="flex sm:flex-nowrap flex-wrap justify-between">
  //             <div className="flex justify-start sm:w-fit w-full mt-4 md:mt-0">
  //               <Link href="" className="flex items-center">
  //                 <SvgIcon
  //                   name="chevronRight"
  //                   className="w-5 h-5 stroke-none fill-primary-2 rotate-180 mr-4"
  //                 />
  //                 <Image
  //                   className="rounded-10"
  //                   src="/asset/images/<EMAIL>"
  //                   alt=""
  //                   width={50}
  //                   height={50}
  //                 />
  //                 <div className="ml-10p">
  //                   <p className="font-medium text-sm text-gray-1">
  //                     Alex Smith
  //                   </p>
  //                   <p className="font-bold text-dark-primary dark:text-white">
  //                     20 beautiful borpas
  //                   </p>
  //                 </div>
  //               </Link>
  //             </div>
  //             <div className="flex justify-end sm:w-fit w-full mt-4 md:mt-0">
  //               <Link href="" className="flex items-center">
  //                 <Image
  //                   className="rounded-10"
  //                   src="/asset/images/<EMAIL>"
  //                   alt=""
  //                   width={50}
  //                   height={50}
  //                 />
  //                 <div className="ml-10p">
  //                   <p className="font-medium text-sm text-gray-1">
  //                     Alex Smith
  //                   </p>
  //                   <p className="font-bold text-dark-primary dark:text-white">
  //                     20 beautiful borpas
  //                   </p>
  //                 </div>
  //                 <SvgIcon
  //                   name="chevronRight"
  //                   className="w-5 h-5 stroke-none fill-primary-2 ml-4"
  //                 />
  //               </Link>
  //             </div>
  //           </div>

  //           <div className="sell-item-container">
  //             <div className="w-full mb-5">
  //               <div className="flex items-center">
  //                 <p className="font-medium text-dark-primary dark:text-white">
  //                   Type
  //                 </p>
  //                 <Tooltip
  //                   contentWidth="lg:w-[295px]"
  //                   iconSize="w-4 h-4 -mt-0.5"
  //                 >
  //                   Type
  //                 </Tooltip>
  //               </div>
  //               <div className="flex flex-wrap">
  //                 {ListingType.getResourceArray().map((type, index) => (
  //                   <ButtonTag
  //                     key={index}
  //                     onClick={() => {
  //                       setActiveTag(type.name)
  //                       setFormValue('listingTypeId', type.id)
  //                     }}
  //                     active={activeTag === type.name}
  //                     className="mr-5 shrink-0 mt-10p"
  //                   >
  //                     {type.label}
  //                   </ButtonTag>
  //                 ))}
  //                 {activeTag === 'fixedPrice' && (
  //                   <ButtonTag onClick={() => {}} className="shrink-0 mt-10p">
  //                     AirDrop
  //                   </ButtonTag>
  //                 )}
  //               </div>
  //             </div>
  //             <div className="sell-item-flex generator">
  //               <div className="sell-item-left">
  //                 {/* Fixed Price */}
  //                 {activeTag === 'fixedPrice' && (
  //                   <SellFixedPriceTab
  //                     {...formData}
  //                     paymentTokens={paymentTokens}
  //                     onUploadFile={(file: File) => uploadFile(file)}
  //                     onChangeRevealDatetime={(value: any) =>
  //                       setFormValue('revealDatetime', value)
  //                     }
  //                     onChangePaymentToken={(option: any) => {
  //                       setFormValue('paymentTokenId', option.id)
  //                       setSelectedPaymentToken(option)
  //                     }}
  //                     onChangePrice={(value: any) =>
  //                       setFormValue('price', parseInt(value ? value : 0))
  //                     }
  //                     onChangeIsBundle={(value: any) =>
  //                       setFormValue('isBundle', value)
  //                     }
  //                     onChangeBundleName={(value: any) =>
  //                       setFormValue('bundleName', value)
  //                     }
  //                     onChangeBundleDescription={(value: any) =>
  //                       setFormValue('bundleDescription', value)
  //                     }
  //                     onChangeIsReservedForBuyer={(value: any) =>
  //                       setFormValue('isReservedForBuyer', value)
  //                     }
  //                     onChangeReservedBuyerAddress={(value: any) =>
  //                       setFormValue('reservedBuyerAddress', value)
  //                     }
  //                     onChangeStartAt={(value: any) =>
  //                       setFormValue('startAt', new Date(value))
  //                     }
  //                     onChangeEndAt={(value: any) =>
  //                       setFormValue('endAt', new Date(value))
  //                     }
  //                   />
  //                 )}

  //                 {activeTag === 'timedAuction' && (
  //                   <SellTimedAuctionTab
  //                     {...formData}
  //                     paymentTokens={paymentTokens}
  //                     auctionMethods={auctionMethods}
  //                     onChangePaymentToken={(option: any) => {
  //                       setFormValue('paymentTokenId', option.id)
  //                       setSelectedPaymentToken(option)
  //                     }}
  //                     onChangeStartingPrice={(value: any) =>
  //                       setFormValue(
  //                         'startingPrice',
  //                         parseInt(value ? value : 0)
  //                       )
  //                     }
  //                     onChangeReservePrice={(value: any) =>
  //                       setFormValue(
  //                         'reservePrice',
  //                         parseInt(value ? value : 0)
  //                       )
  //                     }
  //                     onChangeHasReservePrice={(value: any) =>
  //                       setFormValue('hasReservePrice', value)
  //                     }
  //                     onChangeStartAt={(value: any) =>
  //                       setFormValue('startAt', new Date(value))
  //                     }
  //                     onChangeEndAt={(value: any) =>
  //                       setFormValue('endAt', new Date(value))
  //                     }
  //                   />
  //                 )}

  //                 {/* Fees */}
  //                 <div className="mt-5">
  //                   <div className="flex items-center">
  //                     <p className="font-medium text-dark-primary dark:text-white">
  //                       Fees
  //                     </p>
  //                     <Tooltip
  //                       contentWidth="lg:w-[295px]"
  //                       iconSize="w-4 h-4 -mt-0.5"
  //                     >
  //                       Fees
  //                     </Tooltip>
  //                   </div>
  //                   <p className="text-gray-1 dark:text-gray-3 text-stroke-thin mt-10p">
  //                     <span className="mr-6">Service Fee</span>
  //                     <span>2.5%</span>
  //                   </p>
  //                 </div>

  //                 <div className="flex gap-4 md:gap-5 mt-7">
  //                   <Button
  //                     variant="light"
  //                     className="mt-6 flex-none"
  //                     onClick={() => router.push('/generator')}
  //                   >
  //                     Back
  //                   </Button>
  //                   <Button
  //                     variant="light"
  //                     className="mt-6 flex-none"
  //                     onClick={(e) => submitForm(e)}
  //                   >
  //                     Save
  //                   </Button>
  //                   <Button
  //                     variant="dark"
  //                     className="mt-6 flex-none"
  //                     onClick={(e) => submitForm(e)}
  //                   >
  //                     Complete
  //                   </Button>
  //                 </div>
  //               </div>

  //               <div className="sell-item-right">
  //                 <p className="font-medium text-dark-primary dark:text-white">
  //                   Preview
  //                 </p>
  //                 <PreviewAsset
  //                   record={asset}
  //                   paymentToken={selectedPaymentToken}
  //                   paymentTokenIcon={
  //                     paymentTokenImages[selectedPaymentToken.name || 'eth']
  //                   }
  //                   price={formData.price}
  //                   className="mx-auto"
  //                 />
  //                 {activeTag === 'fixedPrice' && (
  //                   <>
  //                     <div className="mt-4 flex items-center">
  //                       <p className="font-medium text-dark-primary dark:text-white">
  //                         Size
  //                       </p>
  //                       <Tooltip className="-mt-0.5">Description</Tooltip>
  //                     </div>
  //                     <Input
  //                       type="number"
  //                       className="mt-10p no-arrow"
  //                       width="w-36"
  //                       value={formData.size}
  //                       onChange={(value: any) => setFormValue('size', value)}
  //                     />
  //                   </>
  //                 )}
  //               </div>
  //             </div>
  //           </div>
  //         </div>
  //       </Container>
  //     </main>
  //   </>
  // )
  return (
    <> </>
  )
}

export default Sell
