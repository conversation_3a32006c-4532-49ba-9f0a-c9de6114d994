import { NextPage } from 'next'
import Head from 'next/head'
import Link from 'next/link'
import Image from 'next/image'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import { FilterParams } from 'types/type'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import Container from 'presentation/components/container'
import Heading from 'presentation/components/heading'
import ShowMore from 'presentation/components/show-more'
import ToggleWrap from 'presentation/components/toggle-wrap'
import PaginationDot from 'presentation/components/pagination/pagination-dot'
import CollectionCommon from 'presentation/components/collection/common'
import NoData from 'presentation/components/no-data'
import SkeletonAsset from 'presentation/components/skeleton/assets'
import ToggleVerticalFilterChains from 'presentation/components/toggle-wrap/toggle-vertical-filter-chains'
import CategoryUseCase from 'presentation/use_cases/category_use_case'
import CollectionUseCase from 'presentation/use_cases/collection_use_case'
import Chain from 'domain/value_objects/chain'
import { toast } from 'react-toastify'
import { pageSizes } from 'lib/hook/customHook'
import Collection from 'domain/models/collection'
import CategoryModel from 'domain/models/category'
import { Query as CategoryQuery } from 'domain/repositories/category_repository/query'
import { Query as CollectionQuery } from 'domain/repositories/collection_repository/query'
import { categoriesIcons } from 'lib/valueObjects'
import { getChainName } from 'lib/utils'
import { NetworkStateProvider } from 'presentation/providers/network_state_provider'
import CollectionDescription from 'presentation/components/show-more/collection-description'
const categoryUseCase = new CategoryUseCase()
const collectionUseCase = new CollectionUseCase()
let PageSize = pageSizes.pageSize20

const Category: NextPage = ({ categorySlug }: any) => {
  const initParams: FilterParams = {
    chains: [],
    page: 1,
    limit: PageSize,
  }

  // Add NetworkStateProvider
  const networkStateProvider = NetworkStateProvider((state) => ({
    network: state.network,
  }))

  const router = useRouter()
  const [category, setCategory] = useState<CategoryModel>()
  const [collections, setCollections] = useState<Collection[]>([])
  const [isFetchingCollections, setIsFetchingCollections] =
    useState<boolean>(true)
  const [isFetchingCategory, setIsFetchingCategory] = useState<boolean>(true)
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [total, setTotal] = useState<number>(0)
  const { filterParams, changeFilterParam } = useChangeFilterParams(initParams)
  const [filterChains, setFilterChains] = useState<Chain[]>([])
  const [isFilterChainsReady, setIsFilterChainsReady] =
    useState<boolean>(false)
  const [errorCode, setErrorCode] = useState<number | undefined>(undefined)
  const [categories, setCategories] = useState<CategoryModel[]>([])
  // Add state to track if network is ready
  const [isNetworkReady, setIsNetworkReady] = useState<boolean>(false)
  // Add a state to track previous chainId to detect changes
  const [prevChainId, setPrevChainId] = useState<string>('')

  // Add useEffect to monitor network chainId changes and reset page
  useEffect(() => {
    const currentChainId = networkStateProvider.network?.chainId || ''
    
    // Only reset if chainId actually changed and is not the initial load
    if (prevChainId !== currentChainId) {
      setCurrentPage(1)
      changeFilterParam('page', 1,false)
    }
    
    // Update previous chainId  
    setPrevChainId(currentChainId)
  }, [networkStateProvider.network?.chainId])

  const fetchCategory = async () => {
    // Only proceed if network is ready
    if (!isNetworkReady) return
    
    if (category?.slug !== categorySlug) {
      await categoryUseCase
        .findBySlug(categorySlug)
        .then((res) => {
          res.thumbnail = categoriesIcons[res.slug]
          setCategory(res)
          changeFilterParam('categoryIds', res.id, true)
        })
        .catch((_error) => {
          setErrorCode(404)
        })
    }
  }

  const fetchAllCetegoies = async () => {
    // Only proceed if network is ready
    if (!isNetworkReady) return
    setIsFetchingCategory(true)
    const query = new CategoryQuery()

    categoryUseCase
      .search(query)
      .then((res :any) => {
        // Ensure res is an array
        const categories = Array.isArray(res) ? [...res] : []
        
        // Apply thumbnails to categories
        categories.forEach((category :any) => {
          category.thumbnail = categoriesIcons[category.slug]
        })

        setCategories(categories)
      })
      .catch((_error) => {
        console.log('🚀 ~ fetchAllCetegoies ~ _error:', _error)
        toast.error('Error when fetching categories')
      })
    setIsFetchingCategory(false)
  }

  const fetchCollections = async () => {
    // Only proceed if network is ready
    if (!isNetworkReady) return
    if(categorySlug !== 'all' && !category) return

    setIsFetchingCollections(true)
    // Get chainId from network provider
    const chainId = networkStateProvider.network?.chainId || ''
    
    const query = new CollectionQuery({
      page: filterParams.page,
      limit: filterParams.limit,
      chains: filterParams?.chains?.map((chain) => chain.getName()) || [],
      categoryIds: [category?.id],
      chainId: chainId, // Add chainId to query
    })

    await collectionUseCase
      .totalCount(query)
      .then((res) => {
        setTotal(res)
      })
      .catch((_error) => {
        toast.error('Error when fetching collections')
      })
    await collectionUseCase
      .search(query)
      .then((res) => {
        setCollections(res)
      })
      .catch((_error) => {
        toast.error('Error when fetching collections')
      })
    setIsFetchingCollections(false)
  }

  const formatChains = () => {
    const chains = Chain.getResourceArray().map((chain: any) => {
      return Chain.fromName<Chain>(chain.name)
    })
    setFilterChains(chains)
    setIsFilterChainsReady(true)
  }
  const initData = async () => {
    if(categorySlug !== 'all') {
      await fetchCategory()
    }
    await fetchCollections()
  }
  useEffect(() => {
    if (networkStateProvider.network !== undefined) {
      setIsNetworkReady(true)
    }
  }, [networkStateProvider.network])
  // useEffect(() => {
  //   if (
  //     categorySlug === 'all' ||
  //     (filterParams.categoryIds && filterParams.categoryIds.length > 0)
  //   ) {
  //     fetchCollections()
  //   }
  // }, [filterParams, isNetworkReady])

  useEffect(() => {
    formatChains()
    fetchAllCetegoies()
  }, [isNetworkReady])

  useEffect(() => {
    if (categorySlug) {
      initData()
    } else {
      if (category?.name != null) {
        router.reload()
      }
    }
  }, [categorySlug,isNetworkReady, filterParams])

  if (errorCode === 404) {
    router.push('/404')
  }

  return (
    <>
      <Head>
        <title>Category - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>
      <main>
        <Container className="pt-[34px]">
          <div className="w-full md:w-1/2 mx-auto text-center">
            <Heading className="mb-4 text-dark-primary dark:text-white">
              Explore {category?.name ? category.name : 'All'}
            </Heading>
            {categorySlug != 'all' && category?.description && (
              <CollectionDescription description={category.description} />
              // <ShowMore
              //   height="22px"
              //   showMoreClass="flex items-center justify-center mt-2 mb-1"
              // >
              //   <p className="text-gray-1 dark:text-gray-3 text-stroke-thin">
              //     {category?.description}
              //   </p>
              // </ShowMore>
            )}
          </div>
          <div className="product-layout product-collection mt-6">
            <div className="product-layout-filter">
              {/* <ToggleWrap title="Filter" contentClass="border-t-0">
                {isFilterChainsReady && (
                  <ToggleVerticalFilterChains
                    defaultShow
                    chains={filterChains}
                    changeFilterParam={changeFilterParam}
                  />
                )}
              </ToggleWrap> */}
              <div className=" grid gap-5 grid-cols-2 p-4 pt-0">
                {isFetchingCategory ? (
                  <>
                    {[...Array(6)].map((_, i) => (
                      <div
                        key={i}
                        className="shrink-0 px-1 py-3 rounded-20 flex flex-col items-center border border-gray-3 animate-pulse"
                      >
                        <div className="w-12 h-12 rounded-full bg-gray-3"></div>
                        <div className="mt-2 h-4 w-3/4 mx-auto rounded-lg bg-gray-3"></div>
                      </div>
                    ))}
                  </>
                ) : (
                  <>
                    {categories.map((category: any, index: number) => (
                      <Link
                        href={`/categories/${category.slug}`}
                        key={index}
                        className="py-3 px-1 rounded-20 flex flex-col items-center border border-gray-3"
                      >
                        <Image
                          alt=""
                          src={category.thumbnail}
                          width={50}
                          height={50}
                          className="rounded-full"
                        />
                        <p className="font-medium mt-2 truncate">
                          {category.name}
                        </p>
                      </Link>
                    ))}
                  </>
                )}
              </div>
            </div>
            <div className="w-full md:w-9/12">
              <div
                className={`mt-0 product-wrap no-gap collection-main
                ${
    !isFetchingCollections && collections.length === 0
      ? 'no-data'
      : ''
    }`}
              >
                {isFetchingCollections ? (
                  [...Array(5)].map((_, i) => <SkeletonAsset key={i} />)
                ) : (
                  <>
                    {(categorySlug === 'all' || category?.id) &&
                    collections.length > 0 ? (
                        collections.map((collection: any, index) => (
                          <div key={index} className="collection">
                            <Link href={`/collections/${getChainName(collection.chainId)}/${collection.slug}`}>
                              <CollectionCommon
                                coinSymbolSize={14}
                                record={collection}
                              />
                            </Link>
                          </div>
                        ))
                      ) : (
                        <div className="w-full rounded-lg border flex justify-center items-center py-24">
                          <NoData />
                        </div>
                      )}
                  </>
                )}
              </div>
              <div className="flex justify-center mt-5">
                <PaginationDot
                  currentPage={currentPage}
                  pageSize={PageSize}
                  totalCount={total}
                  onPageChange={(page: number) => {
                    setCurrentPage(page), changeFilterParam('page', page)
                  }}
                />
              </div>
            </div>
          </div>
        </Container>
      </main>
    </>
  )
}

export async function getServerSideProps (context: any) {
  const { id } = context.params
  try {
    return {
      props: {
        categorySlug: id,
      },
    }
  } catch (error) {
    return {
      notFound: true,
    }
  }
}

export default Category
