import Collection from 'domain/models/collection'
import { Query as CollectionQuery } from 'domain/repositories/collection_repository/query'
import { pageSizes } from 'lib/hook/customHook'
import { NextPage } from 'next'
import Head from 'next/head'
import Link from 'next/link'
import But<PERSON> from 'presentation/components/button'
import ButtonIcon from 'presentation/components/button/button-icon'
import CollectionCommon from 'presentation/components/collection/common'
import Container from 'presentation/components/container'
import Heading from 'presentation/components/heading'
import NoData from 'presentation/components/no-data'
import PaginationDot from 'presentation/components/pagination/pagination-dot'
import RequireConnect from 'presentation/components/require-connect'
import SkeletonAsset from 'presentation/components/skeleton/assets'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'
import { NetworkStateProvider } from 'presentation/providers/network_state_provider'
import CollectionUseCase from 'presentation/use_cases/collection_use_case'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import { getChainName } from 'lib/utils'
const collectionUseCase = new CollectionUseCase()

let PageSize = pageSizes.pageSize30

const Collections: NextPage = () => {
  const [collections, setCollections] = useState<Collection[]>([])
  const [isFetchingCollections, setIsFetchingCollections] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [total, setTotal] = useState(0)
  const [isNetworkReady, setIsNetworkReady] = useState<boolean>(false)
  // Add state to track previous chainId to detect changes
  const [prevChainId, setPrevChainId] = useState<string>('')

  const authStateProvider = AuthStateProvider((state) => ({
    me: state.me,
  }))
  
  // Add NetworkStateProvider
  const networkStateProvider = NetworkStateProvider((state) => ({
    network: state.network,
  }))
  
  // Monitor network state changes and reset page when chainId changes
  useEffect(() => {
    if (networkStateProvider.network !== undefined) {
      setIsNetworkReady(true)
      
      const currentChainId = networkStateProvider.network?.chainId || ''
      
      // Only reset if chainId actually changed and is not the initial load
      if (prevChainId && prevChainId !== currentChainId) {
        // Reset page to 1 when chainId changes
        setTimeout(() => {
          setCurrentPage(1)
        }, 200)
      }
      
      // Update previous chainId
      setPrevChainId(currentChainId)
    }
  }, [networkStateProvider.network])
  
  const router = useRouter()
  const fetchCollections = async () => {
    // Only proceed if network is ready
    if (!isNetworkReady) return
    
    try {
      console.log('🚀 ~ fetchCollections ~ currentPage:', currentPage)
      setIsFetchingCollections(true)
      
      // Get chainId from network provider
      const chainId = networkStateProvider.network?.chainId || ''
      
      let query = new CollectionQuery({
        ownerId: authStateProvider.me?.id,
        chainId: chainId, // Add chainId to query
      })
      collectionUseCase
        .totalCount(query)
        .then((resTotal) => {
          setTotal(resTotal)
        })
        .catch((_error) => {
          console.log('🚀 ~ fetchCollections ~ _error:', _error)
        })
      query = new CollectionQuery({
        ownerId: authStateProvider.me?.id,
        limit: PageSize,
        page: currentPage,
        chainId: chainId, // Add chainId to query
      })

      collectionUseCase
        .search(query)
        .then((res) => {
          setCollections(res)
        })
        .catch((_error) => {
          console.log('🚀 ~ fetchCollections ~ _error:', _error)
        })
      // Note: We're not setting isFetchingCollections to false here anymore
      // It will be set to false after a delay to ensure skeleton is visible
    } catch (error) {
      console.log('🚀 ~ fetchCollections ~ error:', error)
      setIsFetchingCollections(false) // Still set to false on error
    }
  }

  useEffect(() => {
    console.log('🚀 ~ useEffect ~ authStateProvider.me?.id:', authStateProvider.me?.id)
    if (authStateProvider.me?.id && isNetworkReady) {
      fetchCollections()
    }
  }, [currentPage, authStateProvider.me?.id, networkStateProvider.network?.chainId, isNetworkReady])

  // Add a new useEffect to handle the loading state with a minimum duration
  useEffect(() => {
    let timer: any
    if (isFetchingCollections) {
      // Minimum loading time of 800ms to prevent flickering
      timer = setTimeout(() => {
        setIsFetchingCollections(false)
      }, 800)
    }
    return () => {
      if (timer) clearTimeout(timer)
    }
  }, [isFetchingCollections])

  return (
    <>
      <Head>
        <title>My Collections - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>
      <main>
        {authStateProvider.me?.id ? (
          <Container className="pt-10">
            <div className="w-full md:w-1/2 mx-auto text-center">
              <Heading>My Collections</Heading>
              <p className="text-gray-1 dark:text-gray-3 text-stroke-thin mt-3">
                Create, curate, and manage collections of unique NFTs to share
                and sell.
              </p>
              <div className="flex items-center mt-4 justify-center">
                <div className="flex flex-wrap gap-4 mr-5"> 
                  <Button
                    variant="dark"
                    className="min-w-[180px]"
                    onClick={() => router.push('/collections/create')}
                  >
                    Create a collection
                  </Button>
                  <Button
                    variant="dark"
                    className="min-w-[180px]"
                    onClick={() => router.push('/import-your-nft')}
                  >
                  Import your NFTs 
                  </Button>
                </div>
                <ButtonIcon
                  svgIcon="moreVertical"
                  iconClassName="w-5 h-5 stroke-none fill-primary-2"
                />
              </div>
            </div>
            <div className="mt-10 container-lg">
              {isFetchingCollections ? (
                // Enhanced skeleton loading state
                <div className="mt-0 product-wrap full-page no-gap collection-main justify-center">
                  {[...Array(6)].map((_, i) => (
                    <div key={i} className="collection-skeleton">
                      <div className="animate-pulse flex flex-col">
                        <div className="bg-gray-200 h-48 w-full rounded-t-lg"></div>
                        <div className="p-4 space-y-3">
                          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                          <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                          <div className="flex justify-between">
                            <div className="h-3 bg-gray-200 rounded w-1/4"></div>
                            <div className="h-3 bg-gray-200 rounded w-1/4"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                // Original content when not loading
                <div
                  className={`mt-0 product-wrap full-page no-gap
                    ${collections.length === 0 ? 'no-data' : ''}
                    collection-main justify-center`}
                >
                  {collections.length > 0 ? (
                    collections.map((collection: Collection, index) => (
                      <div key={index} className="collection">
                        <Link href={`/collections/${getChainName(collection.chainId)}/${collection.slug}`}>
                          <CollectionCommon
                            record={collection}
                            showMoreIcon
                            showItemCount={true}
                            showCoinSymbolCorner={true}
                          />
                        </Link>
                      </div>
                    ))
                  ) : (
                    <div className="w-full rounded-lg border flex justify-center items-center py-24">
                      <NoData />
                    </div>
                  )}
                </div>
              )}
              
              {/* Pagination - only show when not loading and has collections */}
              {!isFetchingCollections && collections.length > 0 && (
                <div className="flex justify-center mt-6">
                  <PaginationDot
                    currentPage={currentPage}
                    pageSize={PageSize}
                    totalCount={total}
                    onPageChange={(page: number) => setCurrentPage(page)}
                  />
                </div>
              )}
            </div>
          </Container>
        ) : (
          <RequireConnect />
        )}
      </main>
    </>
  )
}

export default Collections
