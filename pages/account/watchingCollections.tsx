import { Query as CollectionWatchQuery } from 'domain/repositories/collection_watch_repository/query'
import TargetEntityType from 'domain/value_objects/target_entity_type'
import { pageSizes } from 'lib/hook/customHook'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import { NextPage } from 'next'
import Head from 'next/head'
import Container from 'presentation/components/container'
import Heading from 'presentation/components/heading'
import Pagination from 'presentation/components/pagination'
import RequireConnect from 'presentation/components/require-connect'
import LoadingData from 'presentation/components/skeleton/loading'
import SvgIcon from 'presentation/components/svg-icon'
import Table from 'presentation/components/table'
import CollectionTableItem from 'presentation/components/table/collection-item'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'
import { NetworkStateProvider } from 'presentation/providers/network_state_provider'
import CollectionWatchUseCase from 'presentation/use_cases/collection_watch_use_case'
import MeUseCase from 'presentation/use_cases/me_use_case'
import WatchUseCase from 'presentation/use_cases/watch_use_case'
import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import { FilterParams } from 'types/type'

const watchUseCase = new WatchUseCase()
const meUseCase = new MeUseCase()
const collectionWatchUseCase = new CollectionWatchUseCase()

const watchlistHead = [
  {
    title: 'Collection',
    key: 'collection',
    render: (record: any) => <CollectionTableItem record={record} />,
  },
  // {
  //   title: 'Floor Price',
  //   key: 'floorPrice',
  //   sortable: true,
  // },
  {
    title: 'Total Volume',
    key: 'totalVolume',
    render: ({ data }: any) => <p className="text-red-1">{data}</p>,
  },
  {
    title: 'Owners',
    key: 'owners',
  },
  {
    title: 'Items',
    key: 'items',
  },
  {
    title: '',
    key: 'action',
    render: ({ id, onClick }: any) => (
      <button
        onClick={() => onClick(id)}
        className="cursor-pointer w-full text-center"
      >
        <SvgIcon
          name="xSquare"
          className="w-5 h-5 stroke-none fill-primary-2"
        />
      </button>
    ),
  },
]

const Watchlist: NextPage = () => {
  const PageSize = pageSizes.pageSize20

  const initParams: FilterParams = {
    userId: '',
    page: 1,
    limit: PageSize,
  }

  const [currentPage, setCurrentPage] = useState(1)
  const [currentLimit, setCurrentLimit] = useState(PageSize)
  const [watchList, setWatchList] = useState<any>([])
  const { filterParams, changeFilterParam, setFilterParams } =
    useChangeFilterParams(initParams)
  const [total, setTotal] = useState(0)
  const [isFetching, setIsFetching] = useState(true)
  const [isNetworkReady, setIsNetworkReady] = useState(false)
  const [prevChainId, setPrevChainId] = useState('')

  const authStateProvider = AuthStateProvider((state) => ({
    me: state.me,
  }))
  
  const networkStateProvider = NetworkStateProvider((state) => ({
    network: state.network,
    chainId: state.network?.chainId,
  }))

  const handleClick = async (id: any) => {
    watchUseCase
      .delete(TargetEntityType.collection(), id)
      .then(() => {
        toast.success('Removed the collection from watch list successfully.')
        setCurrentPage(1), changeFilterParam('page', 1)
        fetchCollectionWatch()
      })
      .catch(() => {
        toast.error('Had an error, couldn\'t unwatch the collection.')
      })
  }

  const fetchMe = async () => {
    await meUseCase
      .loginStatus()
      .then((res) => {
        if (res.isLoggedIn) {
          changeFilterParam('userId', res.user?.id)
        } else {
          changeFilterParam('userId', '')
        }
      })
      .catch()
  }

  const fetchCollectionWatch = async () => {
    try {
      setIsFetching(true)
      const query = new CollectionWatchQuery({
        userId: filterParams.userId,
        limit: filterParams.limit,
        page: filterParams.page,
        chainId: networkStateProvider.chainId,
      })
      const resTotal = await collectionWatchUseCase
        .totalCount(query)
      setTotal(resTotal)
    
      const res = await collectionWatchUseCase
        .search(query)
      const data = res.map((item) => {
        return {
          collection: item.collection,
          // floorPrice: item.collection?.floorPriceUsd
          //   ? item.collection?.floorPriceUsd
          //   : '--',
          totalVolume: item.collection?.totalVolumeUsd
            ? Number(item.collection?.totalVolumeUsd).toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 6 })
            : '--',
          owners: item.collection?.numOwners,
          items: item.collection?.numAssets,
          action: {
            id: item.collection?.id,
            onClick: handleClick,
          },
        }
      })
      setWatchList(data)
      setIsFetching(false)
    }catch(err) {
      toast.error('Failed to fetch data')
    }
   
  }

  useEffect(() => {
    fetchMe()
  }, [])

  useEffect(() => {
    if (networkStateProvider.network !== undefined) {
      setIsNetworkReady(true)
      
      const currentChainId = networkStateProvider.network?.chainId || ''
      
      if (prevChainId && prevChainId !== currentChainId) {
        setCurrentPage(1)
        changeFilterParam('page', 1, false)
      }
      
      setPrevChainId(currentChainId)
    }
  }, [networkStateProvider.network])

  useEffect(() => {
    console.log('🚀 ~ useEffect ~ filterParams:', filterParams)
    if (filterParams.userId !== '' && isNetworkReady) {
      fetchCollectionWatch()
    }
  }, [filterParams, isNetworkReady])

  return (
    <>
      <Head>
        <title>My Watchlist - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>
      <main>
        {authStateProvider.me?.id ? (
          <Container className="pt-10">
            <div className="text-center">
              <Heading>My Watchlist</Heading>
            </div>
            {isFetching ? (
              <LoadingData />
            ) : (
              <div className="container-lg">
                <div className="mt-5 table-wrap border border-gray-3 rounded-20">
                  <div className="w-full overflow-x-auto pb-3">
                    <Table
                      head={watchlistHead}
                      data={watchList}
                      className="watchlist-table"
                    />
                  </div>
                  {watchList.length > 0 && (
                    <div className="px-9 lg:mt-0.5 mt-5 pb-9">
                      <Pagination
                        currentPage={currentPage}
                        totalCount={total}
                        pageSize={currentLimit}
                        onPageChange={(page: number) => {
                          setCurrentPage(page)
                          setFilterParams({ ...filterParams, page })
                        }}
                        onChangePageSize={(limit: number) => {
                          console.log('🚀 ~ limit:', limit)
                          setCurrentPage(1)
                          setCurrentLimit(limit)
                          setFilterParams({ ...filterParams, limit, page: 1 })
                        }}
                      />
                    </div>
                  )}
                </div>
              </div>
            )}
          </Container>
        ) : (
          <RequireConnect />
        )}
      </main>
    </>
  )
}

export default Watchlist
