import { NextPage } from 'next'
import Head from 'next/head'
import Image from 'next/image'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import Container from 'presentation/components/container'
import Heading from 'presentation/components/heading'
import NoData from 'presentation/components/no-data'
import PaginationDot from 'presentation/components/pagination/pagination-dot'
import RequireConnect from 'presentation/components/require-connect'
import SkeletonAsset from 'presentation/components/skeleton/assets'
import Button from 'presentation/components/button'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'
import { NetworkStateProvider } from 'presentation/providers/network_state_provider'
import BuybackRemoteDatasource, { BuybackCard } from 'data/datasources/remote/buyback_remote_datasource'
import { pageSizes } from 'lib/hook/customHook'
import { DateTime } from 'luxon'
import { shortenAddress } from 'lib/utils'
import BuybackConfirmModal from 'presentation/components/modal/buyback-confirm'
import BigNumber from 'bignumber.js'
import SettingUseCase from 'presentation/use_cases/setting_use_case'
import VendingMachineContractService from 'domain/services/vending_machine_contract_service'
import { useAuthentication } from 'lib/hook/auth_hook'

const buybackDatasource = new BuybackRemoteDatasource()
const settingUseCase = new SettingUseCase()
let PageSize = pageSizes.pageSize30

const BuyBack: NextPage = () => {
  const [buybackCards, setBuybackCards] = useState<BuybackCard[]>([])
  const [isFetchingCards, setIsFetchingCards] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [total, setTotal] = useState(0)
  const [showBuybackModal, setShowBuybackModal] = useState(false)
  const [selectedCard, setSelectedCard] = useState<BuybackCard | null>(null)
  const [isBuybackLoading, setIsBuybackLoading] = useState(false)
  const [isLoadingAction, setIsLoadingAction] = useState<string | null>(null)
  const router = useRouter()

  const authStateProvider = AuthStateProvider((state) => ({
    me: state.me,
  }))
  
  const networkStateProvider = NetworkStateProvider((state) => ({
    network: state.network,
  }))

  const contractService = new VendingMachineContractService()
  const { getProviderEthers } = useAuthentication()

  const fetchBuybackCards = async () => {
    if (!authStateProvider.me?.id) return

    try {
      setIsFetchingCards(true)
      console.log('🚀 Fetching buyback cards for user:', authStateProvider.me.id)
      
      const response = await buybackDatasource.getBuybackCards(
        authStateProvider.me.id,
        currentPage,
        PageSize
      )

      const processedCards = response.buybackCards.map((card: BuybackCard) => {
        return {
          ...card,
        }
      })

      console.log('🚀 Buyback cards response:', response)
      setBuybackCards(processedCards)
      setTotal(response.pagination?.total || 0)
      
    } catch (error) {
      console.error('Error fetching buyback cards:', error)
    } finally {
      setIsFetchingCards(false)
    }
  }

  useEffect(() => {
    if (authStateProvider.me?.id) {
      fetchBuybackCards()
    }
  }, [currentPage, authStateProvider.me?.id])

  const formatDate = (dateString: string) => {
    try {
      return DateTime.fromISO(dateString).toFormat('MMM dd, yyyy')
    } catch {
      return 'Unknown date'
    }
  }

  const getChainName = (chainId: number) => {
    const chainNames: { [key: number]: string } = {
      1: 'Ethereum',
      137: 'Polygon',
      80002: 'Polygon Amoy',
      11155111: 'Sepolia Ethereum',
    }
    return chainNames[chainId] || `Chain ${chainId}`
  }

  const openTransactionInExplorer = (txHash: string, chainId: number) => {
    const explorers: { [key: number]: string } = {
      1: 'https://etherscan.io',
      137: 'https://polygonscan.com',
      80002: 'https://amoy.polygonscan.com',
      11155111: 'https://sepolia.etherscan.io',
    }
    
    const baseUrl = explorers[chainId] || 'https://etherscan.io'
    window.open(`${baseUrl}/tx/${txHash}`, '_blank')
  }

  const handleAcceptOffer = (card: BuybackCard) => {
    setSelectedCard(card)
    setShowBuybackModal(true)
  }

  const handleRejectOffer = async (card: BuybackCard) => {
    if (!card.buybackOfferId) return

    try {
      setIsLoadingAction(card.id)
      await buybackDatasource.rejectOffer({
        cardId: card.id,
        buybackOfferId: card.buybackOfferId,
      })
      
      // Remove card from list after rejection
      setBuybackCards((prevCards) => prevCards.filter((c) => c.id !== card.id))
      setTotal((prev) => prev - 1)
      
      console.log('Offer rejected successfully')
    } catch (error) {
      console.error('Error rejecting offer:', error)
    } finally {
      setIsLoadingAction(null)
    }
  }

  const handleBuybackConfirm = async (card: BuybackCard) => {
    try {
      setIsBuybackLoading(true)

      // Get ethers provider and signer
      const provider = await getProviderEthers()
      if (!provider) {
        throw new Error('Provider not available')
      }
      const signer = provider.getSigner()

      console.log('Calling smart contract for buyback:', {
        tokenId: card.tokenId,
        contractAddress: (card as any).contractAddress,
        chainId: card.chainId,
      })

      // Call smart contract buyback function
      const tx = await contractService.buybackNFT({
        tokenId: card.tokenId,
        contractAddress: (card as any).contractAddress || '',
        chainId: card.chainId,
        signer: signer,
      })

      console.log('Buyback transaction sent:', tx.hash)

      // Wait for transaction confirmation
      const receipt = await contractService.waitForTransaction(tx)
      console.log('Buyback transaction confirmed:', receipt.transactionHash)

      // Accept the offer via API
      if (card.buybackOfferId) {
        await buybackDatasource.acceptOffer({
          cardId: card.id,
          buybackOfferId: card.buybackOfferId,
        })
      }

      // Remove card from list after successful buyback
      setBuybackCards((prevCards) => prevCards.filter((c) => c.id !== card.id))
      setTotal((prev) => prev - 1)

      // Close modal on success
      setShowBuybackModal(false)
      setSelectedCard(null)

      console.log('Buyback successful!')
    } catch (error) {
      console.error('Error during buyback:', error)
    } finally {
      setIsBuybackLoading(false)
    }
  }

  // Check if card's buyback offer is still valid (not expired)
  const isOfferValid = (card: BuybackCard) => {
    if (!card.buybackExpirationTimestamp) return false
    const now = new Date()
    const expiryDate = new Date(card.buybackExpirationTimestamp)
    return now <= expiryDate
  }

  return (
    <>
      <Head>
        <title>Buy Back - Vending Machine - NFT Land</title>
        <meta name="description" content="Buy back request from Vending Machine" />
      </Head>
      <main>
        {authStateProvider.me?.id ? (
          <Container className="pt-10">
            <div className="w-full md:w-1/2 mx-auto text-center">
              <Heading>Buy Back</Heading>
              <p className="text-gray-1 dark:text-gray-3 text-stroke-thin mt-3">
                View and manage your buy back requests from vending machine purchases.
              </p>
            </div>
            <div className="mt-10 container-lg">
              {isFetchingCards ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                  {Array.from({ length: 10 }).map((_, index) => (
                    <SkeletonAsset key={index} />
                  ))}
                </div>
              ) : buybackCards.length > 0 ? (
                <>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                    {buybackCards.map((card) => (
                      <div
                        key={card.id}
                        className="bg-white dark:bg-blue-1 rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300"
                      >
                        {/* Card Image */}
                        <div className="relative w-full h-48 bg-gray-100 dark:bg-gray-700">
                          <Image
                            src={card.image}
                            alt={card.name}
                            fill
                            style={{ objectFit: 'cover' }}
                            className="rounded-t-2xl"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement
                              target.src = '/asset/images/vending-machine/card.jpg'
                            }}
                          />
                          
                          {/* Price Badge */}
                          <div className="absolute top-3 right-3">
                            <span className="px-2 py-1 rounded-full text-xs font-bold bg-gradient-to-r from-yellow-400 to-orange-500 text-white shadow-lg border border-yellow-300">
                              💰 {card.packPrice} $
                            </span>
                          </div>

                          {/* Validity Status */}
                          {!isOfferValid(card) && (
                            <div className="absolute top-3 left-3">
                              <span className="px-2 py-1 rounded-full text-xs font-bold bg-red-500 text-white">
                                Invalid DateTime
                              </span>
                            </div>
                          )}
                        </div>
                        
                        {/* Card Info */}
                        <div className="p-4">
                          <h3 className="font-semibold text-dark-primary dark:text-white text-sm mb-2 truncate">
                            {card.name}
                          </h3>
                          
                          {/* Card Details */}
                          <div className="space-y-1 text-xs text-gray-600 dark:text-gray-400">
                            <div className="flex justify-between">
                              <span>Series:</span>
                              <span className="font-medium">{card?.attributes?.series}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Card :</span>
                              <span className="font-medium">{card?.attributes?.cardNumber}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Shop:</span>
                              <span className="font-medium">{card?.attributes?.shop}</span>
                            </div>
                          </div>
                          
                          {/* Transaction Details */}
                          <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
                            {card.buybackExpirationTimestamp && (
                              <div className="flex items-center justify-between">
                                <span className="text-xs text-gray-500">
                                  {isOfferValid(card) ? 'Expires:' : 'Expired:'}
                                </span>
                                <span className={`text-xs ${isOfferValid(card) ? 'text-green-600' : 'text-red-500'}`}>
                                  {formatDate(card.buybackExpirationTimestamp)}
                                </span>
                              </div>
                            )}
                          </div>

                          {/* Action Buttons */}
                          <div className="mt-4 flex gap-2">
                            <Button
                              variant="dark"
                              size="xs"
                              onClick={() => handleAcceptOffer(card)}
                              className="flex-1 !py-2 text-xs"
                              disable={isLoadingAction === card.id || !isOfferValid(card)}
                            >
                              Accept offer for {card.buybackOfferPrice}$
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  {/* Pagination */}
                  {total > PageSize && (
                    <div className="flex justify-center mt-10">
                      <PaginationDot
                        currentPage={currentPage}
                        totalCount={total}
                        pageSize={PageSize}
                        onPageChange={(page: number) => setCurrentPage(page)}
                      />
                    </div>
                  )}
                </>
              ) : (
                <div className="flex flex-col items-center justify-center mt-20">
                  <NoData 
                    title="No Buyback Offers"
                    subtitle="You don't have any buyback offers available at the moment."
                  />
                  <button
                    onClick={() => router.push('/account/vending-machine-cards')}
                    className="mt-4 px-6 py-3 bg-primary-2 text-white rounded-full font-medium hover:bg-primary-3 transition-colors"
                  >
                    View My Cards
                  </button>
                </div>
              )}
            </div>
          </Container>
        ) : (
          <RequireConnect />
        )}

        {/* Buyback Confirm Modal */}
        {showBuybackModal && selectedCard && (
          <BuybackConfirmModal
            card={selectedCard}
            isOpen={showBuybackModal}
            isLoading={isBuybackLoading}
            onClose={() => {
              setShowBuybackModal(false)
              setSelectedCard(null)
            }}
            onConfirm={handleBuybackConfirm}
          />
        )}
      </main>
    </>
  )
}

export default BuyBack
