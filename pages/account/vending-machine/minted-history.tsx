import { NextPage } from 'next'
import Head from 'next/head'
import Image from 'next/image'
import Link from 'next/link'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import Container from 'presentation/components/container'
import Heading from 'presentation/components/heading'
import NoData from 'presentation/components/no-data'
import PaginationDot from 'presentation/components/pagination/pagination-dot'
import RequireConnect from 'presentation/components/require-connect'
import SkeletonAsset from 'presentation/components/skeleton/assets'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'
import { NetworkStateProvider } from 'presentation/providers/network_state_provider'
import CheckoutRemoteDatasource, { MintedCard } from 'data/datasources/remote/checkout_remote_datasource'
import { pageSizes } from 'lib/hook/customHook'
import { DateTime } from 'luxon'
import { shortenAddress } from 'lib/utils'
import CardViewerModal from 'presentation/components/modal/card-viewer-modal'
const checkoutDatasource = new CheckoutRemoteDatasource()
let PageSize = pageSizes.pageSize10

const MintedHistory: NextPage = () => {
  const [mintedCards, setMintedCards] = useState<MintedCard[]>([])
  const [isFetchingCards, setIsFetchingCards] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [total, setTotal] = useState(0)
  const [modalOpen, setModalOpen] = useState(false)
  const [selectedCardIndex, setSelectedCardIndex] = useState(0)
  const router = useRouter()

  const authStateProvider = AuthStateProvider((state) => ({
    me: state.me,
  }))
  
  const networkStateProvider = NetworkStateProvider((state) => ({
    network: state.network,
  }))

  const fetchMintedCards = async () => {
    if (!authStateProvider.me?.id) return

    try {
      setIsFetchingCards(true)
      console.log('🚀 Fetching minted cards for user:', authStateProvider.me.id)
      
      const response: any = await checkoutDatasource.getMintedCards(
        authStateProvider.me.id,
        currentPage,
        PageSize
      )
      const mintedCards = response.mintedCards.map((card: any) => {
        return {
          ...card,
        }
      })
      console.log('🚀 Minted cards response:', response)
      setMintedCards(mintedCards)
      setTotal(response.pagination?.total)
      
    } catch (error) {
      console.error('Error fetching minted cards:', error)
    } finally {
      setIsFetchingCards(false)
    }
  }

  useEffect(() => {
    if (authStateProvider.me?.id) {
      fetchMintedCards()
    }
  }, [currentPage, authStateProvider.me?.id])

  // Add a new useEffect to handle the loading state with a minimum duration
  useEffect(() => {
    let timer: any
    if (isFetchingCards) {
      // Minimum loading time of 800ms to prevent flickering
      timer = setTimeout(() => {
        setIsFetchingCards(false)
      }, 800)
    }
    return () => {
      if (timer) clearTimeout(timer)
    }
  }, [isFetchingCards])

  const formatDate = (dateString: string) => {
    try {
      return DateTime.fromISO(dateString).toFormat('MMM dd, yyyy')
    } catch {
      return 'Unknown date'
    }
  }

  const getChainName = (chainId: number) => {
    const chainNames: { [key: number]: string } = {
      1: 'Ethereum',
      137: 'Polygon',
      80002: 'Polygon Amoy',
      11155111: 'Sepolia Ethereum',
      // Add more chains as needed
    }
    return chainNames[chainId] || `Chain ${chainId}`
  }

  const openTransactionInExplorer = (txHash: string, chainId: number) => {
    const explorers: { [key: number]: string } = {
      1: 'https://etherscan.io',
      137: 'https://polygonscan.com',
      80002: 'https://amoy.polygonscan.com',
      11155111: 'https://sepolia.etherscan.io',
    }
    
    const baseUrl = explorers[chainId] || 'https://etherscan.io'
    window.open(`${baseUrl}/tx/${txHash}`, '_blank')
  }

  const handleCardClick = (index: number) => {
    setSelectedCardIndex(index)
    setModalOpen(true)
  }

  return (
    <>
      <Head>
        <title>Minted History - Vending Machine - NFT Land</title>
        <meta name="description" content="View your minted cards from vending machines" />
      </Head>
      <main>
        {authStateProvider.me?.id ? (
          <Container className="pt-10">
            <div className="w-full md:w-1/2 mx-auto text-center">
              <Heading>Minted History</Heading>
              <p className="text-gray-1 dark:text-gray-3 text-stroke-thin mt-3">
                View and manage your minted cards from vending machine purchases.
              </p>
            </div>
            
            <div className="mt-10 container-lg">
              {isFetchingCards ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                  {Array.from({ length: 10 }).map((_, index) => (
                    <SkeletonAsset key={index} />
                  ))}
                </div>
              ) : mintedCards.length > 0 ? (
                <>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                    {mintedCards.map((card, index) => (
                      <div
                        key={card.id}
                        className="bg-white dark:bg-blue-1 rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300 cursor-pointer"
                        onClick={() => handleCardClick(index)}
                      >
                        {/* Card Image */}
                        <div className="relative w-full h-48 bg-gray-100 dark:bg-gray-700">
                          <Image
                            src={card.image}
                            alt={card.name}
                            fill
                            style={{ objectFit: 'cover' }}
                            className="rounded-t-2xl"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement
                              target.src = '/asset/images/vending-machine/card.jpg' // Add a default image
                            }}
                          />
                          
                          {/* Price Badge */}
                          <div className="absolute top-3 right-3">
                            <span className="px-2 py-1 rounded-full text-xs font-bold bg-gradient-to-r from-yellow-400 to-orange-500 text-white shadow-lg border border-yellow-300">
                            💰 {card.packPrice} $
                            </span>
                          </div>
                        </div>
                        
                        {/* Card Info */}
                        <div className="p-4">
                          <h3 className="font-semibold text-dark-primary dark:text-white text-sm mb-2 truncate">
                            {card.name}
                          </h3>
                          
                          {/* Card Details */}
                          <div className="space-y-1 text-xs text-gray-600 dark:text-gray-400">
                            <div className="flex justify-between">
                              <span>Series:</span>
                              <span className="font-medium">{card?.attributes?.series}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Card:</span>
                              <span className="font-bold ml-1">{card?.attributes?.cardNumber}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Shop:</span>
                              <span className="font-medium capitalize">{card?.attributes?.shop}</span>
                            </div>
                          </div>
                          
                          {/* Transaction Hash */}
                          <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
                            <div className="flex items-center justify-between">
                              <span className="text-xs text-gray-500">TX Hash:</span>
                              <span className="text-xs text-primary-2 font-mono">
                                {shortenAddress(card.mintTxHash)}
                              </span>
                            </div>
                            <div className="flex items-center justify-between mt-1">
                              <span className="text-xs text-gray-500">Minted:</span>
                              <span className="text-xs text-gray-600 dark:text-gray-400">
                                {formatDate(card.mintBlockchainTimestamp)}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  {/* Pagination */}
                  {total > PageSize && (
                    <div className="flex justify-center mt-10">
                      <PaginationDot
                        currentPage={currentPage}
                        totalCount={total}
                        pageSize={PageSize}
                        onPageChange={(page: number) => setCurrentPage(page)}
                      />
                    </div>
                  )}
                </>
              ) : (
                <div className="flex flex-col items-center justify-center mt-20">
                  <NoData 
                    title="No Vending Machine Cards"
                    subtitle="You haven't minted any cards from vending machines yet. Visit the vending machine to start collecting!"
                  />
                  <button
                    onClick={() => router.push('/vending-machine')}
                    className="mt-4 px-6 py-3 bg-primary-2 text-white rounded-full font-medium hover:bg-primary-3 transition-colors"
                  >
                    Visit Vending Machine
                  </button>
                </div>
              )}
            </div>
          </Container>
        ) : (
          <RequireConnect />
        )}

        {/* Card Viewer Modal */}
        <CardViewerModal
          cards={mintedCards}
          initialIndex={selectedCardIndex}
          isOpen={modalOpen}
          onClose={() => setModalOpen(false)}
        />
      </main>
    </>
  )
}

export default MintedHistory
