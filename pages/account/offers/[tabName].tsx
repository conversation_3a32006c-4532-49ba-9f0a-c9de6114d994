import { NextPage } from 'next'
import { useEffect, useState } from 'react'
import Head from 'next/head'
import Container from 'presentation/components/container'

import RequireConnect from 'presentation/components/require-connect'
import MyProfileSection from 'presentation/page_components/account-details/userProfileSection'
import OfferReceivedTab from 'presentation/page_components/account-details/offerReceivedTab'
import OfferMadeTab from 'presentation/page_components/account-details/offerMadeTab'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'
import User from 'domain/models/user'

const MyAccount: NextPage = ({ tabName }: any) => {
  const [owner, setOwner] = useState<User>()
  const [isMe, setIsMe] = useState(false)
  const [ready, setReady] = useState(false)

  const authStateProvider = AuthStateProvider((state) => ({
    me: state.me,
  }))

  useEffect(() => {
    if (authStateProvider.me && authStateProvider.me.id) {
      const owner = authStateProvider.me
      setOwner(owner)
      setIsMe(true)
    }
  }, [authStateProvider.me])

  useEffect(() => {
    if (owner?.id) {
      setReady(true)
    } else {
      setReady(false)
    }
  }, [owner])

  return (
    <>
      <Head>
        <title>Account - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>

      <main>
        <Container className="pt-9">
          {ready ? (
            <>
              <MyProfileSection
                activeTab={tabName}
                owner={owner}
                address={authStateProvider.me?.publicAddresses[0]}
                isMe={isMe}
                path="/account"
              />
              {authStateProvider.me && (
                <>
                  {tabName === 'received' && (
                    <OfferReceivedTab userId={owner?.id!} isMe={isMe} />
                  )}
                  {tabName === 'made' && <OfferMadeTab userId={owner?.id!} isMe={isMe} />}
                </>
              )}
            </>
          ) : (
            <RequireConnect />
          )}
        </Container>
      </main>
    </>
  )
}

export async function getStaticProps (context: any) {
  const { tabName } = context.params
  try {
    return {
      props: {
        tabName: tabName,
      },
    }
  } catch (error) {
    return {
      notFound: true,
    }
  }
}

export async function getStaticPaths () {
  return {
    paths: [
      { params: { tabName: 'made' } },
      { params: { tabName: 'received' } },
    ],
    fallback: false,
  }
}

export default MyAccount
