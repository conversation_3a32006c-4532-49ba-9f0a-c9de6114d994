import { NextPage } from 'next'
import { useEffect, useState } from 'react'
import Head from 'next/head'
import Container from 'presentation/components/container'
import RequireConnect from 'presentation/components/require-connect'
import { toast } from 'react-toastify'

import MyProfileSection from 'presentation/page_components/account-details/userProfileSection'
import CreatedTab from 'presentation/page_components/account-details/createdTab'
import FavoritedTab from 'presentation/page_components/account-details/favoritedTab'
import ActivityTab from 'presentation/page_components/account-details/activityTab'

import MySettingMenu from 'presentation/page_components/account-settings/mySettingMenu'
import ProfileSettingsTab from 'presentation/page_components/user/profileSettingsTab'
import NotificationSettingsTab from 'presentation/page_components/user/notificationSettingsTab'
import OfferSettingsTab from 'presentation/page_components/user/offerSettingsTab'
import AccountSupportTab from 'presentation/page_components/user/accountSupportTab'
import { AuthStateProvider } from 'presentation/providers/auth_state_provider'
import MyNotificationSettingUseCase from 'presentation/use_cases/my_notification_setting_use_case'
import MeUseCase from 'presentation/use_cases/me_use_case'
import UserNotificationSetting from 'domain/models/user_notification_setting'
import User from 'domain/models/user'
import { DateTime } from 'luxon'

const myNotificationSettingUseCase = new MyNotificationSettingUseCase()
const meUseCase = new MeUseCase()

const emptyUserNotificationSetting = new UserNotificationSetting(
  undefined,
  '',
  true,
  true,
  true,
  true,
  true,
  true,
  true,
  true,
  true,
  0,
  0,
  DateTime.now(),
  DateTime.now()
)

const MyAccount: NextPage = ({ tabName }: any) => {
  const [owner, setOwner] = useState<User>()
  const [isMe, setIsMe] = useState(false)
  const [ready, setReady] = useState(false)

  const [notificationSettings, setNotificationSettings] = useState<UserNotificationSetting>(emptyUserNotificationSetting)
  const [isFetchingNotificationSettings, setIsFetchingNotificationSettings] =
    useState(true)

  const authStateProvider = AuthStateProvider((state) => ({
    me: state.me,
    reload: state.reload,
  }))

  const submitFormProfile = async (formData: any) => {
    if (formData.username ) {
      if (formData.email && formData.email !== authStateProvider.me?.email && !authStateProvider.me?.email) {
        // no email editing
        await meUseCase.updateEmail(formData.email).catch((_error) => {
          toast.error('Email already exists.')
        })
      }
      const user = new User(
        authStateProvider.me?.id,
        formData.username ? formData.username : authStateProvider.me?.username,
        formData.email ? formData.email : authStateProvider.me?.email,
        formData.thumbnail
          ? formData.thumbnail
          : authStateProvider.me?.thumbnail,
        formData.background
          ? formData.background
          : authStateProvider.me?.background,
        formData.description
          ? formData.description
          : authStateProvider.me?.description,
        formData.twitterUsername
          ? formData.twitterUsername
          : authStateProvider.me?.twitterUsername,
        formData.instagramUsername
          ? formData.instagramUsername
          : authStateProvider.me?.instagramUsername,
        formData.webUrl ? formData.webUrl : authStateProvider.me?.webUrl,
        authStateProvider.me?.language!,
        authStateProvider.me?.lastAccessedAt!,
        authStateProvider.me?.publicAddresses!,
        authStateProvider.me?.createdAt!,
        DateTime.now()
      )
      await meUseCase
        .update(user)
        .then(() => {
          authStateProvider.reload()
          toast.success('Update your profile succesfully.')
        })
        .catch((_error) => {
          toast.error('Update your profile failed.')
        })
    } else {
      toast.error('Please fill all requied fields.')
    }
  }

  const fetchNotificationSettings = async () => {
    setIsFetchingNotificationSettings(true)
    if (authStateProvider.me !== null) {
      await myNotificationSettingUseCase
        .mine()
        .then((res) => {
          setNotificationSettings(res)
        })
        .catch((error) => {
          if (error.message === 'Request failed with status code 400') {
            myNotificationSettingUseCase
              .update(emptyUserNotificationSetting)
              .then((res) => {
                setNotificationSettings(res)
              })
          }
        })
    }
    setIsFetchingNotificationSettings(false)
  }

  const submitFormNotificationSettings = async (
    formData: any,
    minimumBidThresholdJpy: number
  ) => {
    let newNotificationSettings = new UserNotificationSetting(
      authStateProvider.me?.id,
      authStateProvider.me?.id!,
      formData.onItemSold,
      formData.onBidActivity,
      formData.onPriceChange,
      formData.onAuctionExpiration,
      formData.onOutbid,
      formData.onReferralSuccess,
      formData.onOwnedAssetUpdates,
      formData.onSuccessfullyPurchase,
      formData.onNewsLetter,
      formData.minimumBidThresholdUsd,
      minimumBidThresholdJpy,
      DateTime.now(),
      DateTime.now()
    )
    await myNotificationSettingUseCase
      .update(newNotificationSettings)
      .then(() => {
        toast.success('Update your notification settings successfully.')
      })
      .catch((_error) => {
        toast.error('Update your notification settings failed.')
      })
  }

  useEffect(() => {
    fetchNotificationSettings()
  }, [authStateProvider.me?.id])

  useEffect(() => {
    if (authStateProvider.me !== null) {
      const owner = authStateProvider.me
      setOwner(owner)
      setIsMe(true)
    }
  }, [authStateProvider.me])

  useEffect(() => {
    if (owner?.id) {
      setReady(true)
    } else {
      setReady(false)
    }
  }, [owner])

  return (
    <>
      <Head>
        <title>Account - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>

      <main>
        {tabName === 'created' ||
        tabName === 'favorited' ||
        tabName === 'activities' ? (
            <Container className="pt-9">
              {ready ? (
                <>
                  <MyProfileSection
                    activeTab={tabName}
                    owner={owner}
                    isMe={isMe}
                    path="/account"
                  />
                  {tabName === 'created' && (
                    <CreatedTab userId={owner?.id!} isMe={isMe} />
                  )}
                  {tabName === 'favorited' && (
                    <FavoritedTab userId={owner?.id!} isMe={isMe} />
                  )}
                  {tabName === 'activities' && <ActivityTab userId={owner?.id!} />}
                </>
              ) : (
                <RequireConnect />
              )}
            </Container>
          ) : (
            <>
              {authStateProvider.me?.id ? (
                <Container className="md:pt-9 pt-12 md:pb-0 pb-9">
                  <div className="md2:pr-second">
                    <div
                      className={`user-setting flex flex-wrap
                ${
                tabName === 'offers'
                  ? 'offers lg:flex-nowrap lg:gap-12 gap-5'
                  : 'md2:flex-nowrap gap-12'
                }`}
                    >
                      <MySettingMenu activeTab={tabName} />

                      <div className="user-setting-content">
                        {tabName === 'profileSettings' && (
                          <ProfileSettingsTab
                            address={authStateProvider.me.publicAddresses[0]}
                            me={authStateProvider.me}
                            onSubmit={submitFormProfile}
                          />
                        )}
                        {tabName === 'notificationSettings' && (
                          <>
                            {!isFetchingNotificationSettings && (
                              <NotificationSettingsTab
                                address={authStateProvider.me.publicAddresses[0]}
                                settings={notificationSettings}
                                onSubmit={submitFormNotificationSettings}
                              />
                            )}
                          </>
                        )}
                        {tabName === 'offerSettings' && <OfferSettingsTab />}
                        {tabName === 'support' && <AccountSupportTab />}
                      </div>
                    </div>
                  </div>
                </Container>
              ) : (
                <RequireConnect />
              )}
            </>
          )}
      </main>
    </>
  )
}

export async function getStaticProps (context: any) {
  const { tabName } = context.params
  try {
    return {
      props: {
        tabName: tabName,
      },
    }
  } catch (error) {
    return {
      notFound: true,
    }
  }
}

export async function getStaticPaths () {
  return {
    paths: [
      { params: { tabName: 'created' } },
      { params: { tabName: 'favorited' } },
      { params: { tabName: 'activities' } },
      { params: { tabName: 'profileSettings' } },
      { params: { tabName: 'notificationSettings' } },
      { params: { tabName: 'offerSettings' } },
      { params: { tabName: 'support' } },
    ],
    fallback: false,
  }
}

export default MyAccount
