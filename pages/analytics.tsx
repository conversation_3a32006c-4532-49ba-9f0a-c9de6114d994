import Head from 'next/head'
import { NextPage } from 'next'
import { useRouter } from 'next/router'
import { useState, useEffect } from 'react'
import ButtonTabs from 'presentation/components/button-tabs'
import Welcome from 'presentation/page_components/analytics/welcome'
import CollectionBoardGUI from 'presentation/page_components/analytics/collectionBoard'
import { useChangeFilterParams } from 'lib/hook/useChangeFilterParams'
import { FilterParams } from 'types/type'
import { analyticsTabs } from 'presentation/page_components/analytics/_analyticsTabs'
import CategoryUseCase from 'presentation/use_cases/category_use_case'
import { pageSizes } from 'lib/hook/customHook'
import Category from 'domain/models/category'
import { Query } from 'domain/repositories/category_repository/query'
import { toast } from 'react-toastify'

const categoryUseCase = new CategoryUseCase()
const PageSize = pageSizes.pageSize20

//Todo remove mock data
import { analyticsCollectionData } from 'presentation/controllers/mockData/analytics_collection_mock_data'
//End todo remove mock data

const Analytics: NextPage = () => {
  const initParams: FilterParams = {
    categoryId: '',
    page: 1,
    limit: PageSize,
  }

  const router = useRouter()
  const [activeTab, setActiveTab] = useState<string>('')
  const [total, setTotal] = useState<number>(0)
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [currentLimit, setCurrentLimit] = useState<number>(PageSize)
  const [categories, setCategories] = useState<Category[]>([])
  const { filterParams, changeFilterParam } = useChangeFilterParams(initParams)
  const [isFetching, setIsFetching] = useState(true)
  //TODO remove any type
  const [filterCategories, setFilterCategories] = useState<any>([])
  const [tableData, setTableData] = useState<any>([])
  //End Todo

  const fetchCategories = async () => {
    const query = new Query()
    await categoryUseCase
      .search(query)
      .then((res) => {
        setCategories(res)
      })
      .catch((_error) => {
        toast.error('Failed to fetch categories')
      })
  }

  const changeRequestTab = (tab: string) => {
    router.push(`/analytics/${tab}`, undefined, { shallow: true })
  }

  const fetchTableData = () => {
    setIsFetching(true)
    //TODO
    setTotal(analyticsCollectionData.length)
    let data = analyticsCollectionData.slice(
      (currentPage - 1) * currentLimit,
      currentPage * currentLimit
    )
    setTableData(data)
    setIsFetching(false)
  }

  const formatCategories = () => {
    const formattedCategories = categories.map((category: any) => {
      return {
        id: category.id,
        name: category.name,
        value: category.slug,
      }
    })
    setFilterCategories(formattedCategories)
  }

  useEffect(() => {
    fetchTableData()
  }, [filterParams])

  useEffect(() => {
    if (categories.length > 0) {
      formatCategories()
    }
  }, [categories])

  useEffect(() => {
    fetchCategories()
  }, [])

  useEffect(() => {
    // Set init active tab by query params
    if (router.query && Object.keys(router.query).length) {
      if (Object.prototype.hasOwnProperty.call(router.query, 'tab')) {
        setActiveTab(router.query['tab']?.toString() || '')
      }
    }
  }, [router.query])

  return (
    <>
      <Head>
        <title>Analytics - Quill</title>
        <meta name="description" content="Enjoy Quill" />
      </Head>
      <main>
        <CollectionBoardGUI
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          currentLimit={currentLimit}
          setCurrentLimit={setCurrentLimit}
          tableData={tableData}
          total={total}
          onChangeFilterParam={changeFilterParam}
          categories={filterCategories}
          isFetching={isFetching}
          analyticTabs={
            <ButtonTabs
              tabs={analyticsTabs}
              onChangeTab={changeRequestTab}
              activeTab={activeTab}
            />
          }
        />
        <Welcome />
      </main>
    </>
  )
}

export default Analytics
