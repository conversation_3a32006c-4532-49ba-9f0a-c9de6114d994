import axios from 'axios'
import AxiosMockAdapter from 'axios-mock-adapter'
import dataConfig from './data/index'
const axiosInstance = axios.create()

export default axiosInstance

const axiosMockAdapterInstance = new AxiosMockAdapter(axiosInstance, {
  delayResponse: 100,
  onNoMatch: 'passthrough',
})

dataConfig.reduce((adapter: AxiosMockAdapter, { path, data }: { path: string; data: any }) => {
  return path ? adapter.onGet(path).reply(200, data) : adapter
}, axiosMockAdapterInstance)
