import { generateData } from '../../lib/utils'

const saleArray = generateData(30, {
  event: {
    icon: 'cart1',
    name: 'Sale',
    type: 'sales',
  },
  item: {
    imageSrc: '/asset/images/<EMAIL>',
    name: '141 LAND ABC',
    asset: '<PERSON><PERSON>',
    assetLink: 'alexa<PERSON>mith',
  },
  price: '2.456 ETH',
  from: {
    name: 'netocalifonia',
    link: 'netocalifonia',
  },
  to: {
    name: '<PERSON>_<PERSON>cNFT',
    link: '<PERSON>_McNFT',
  },
  time: '2021’01.01 15:00',
  chains: ['ethereum', 'polygon'],
})

const offerArray = generateData(20, {
  event: {
    icon: 'hand',
    name: 'Offer',
    type: 'offers',
  },
  item: {
    imageSrc: '/asset/images/<EMAIL>',
    name: '141 LAND ABC',
    asset: '<PERSON><PERSON>',
    assetLink: 'alexa<PERSON>mith',
  },
  price: '2.456 ETH',
  from: {
    name: 'netocalifonia',
    link: 'netocalifonia',
  },
  to: {
    name: '<PERSON>_<PERSON>cNF<PERSON>',
    link: '<PERSON>_<PERSON>cNFT',
  },
  time: '2021’01.01 15:00',
  chains: ['polygon'],
})

const transferArray = generateData(40, {
  event: {
    icon: 'reload',
    name: 'Transfer',
    type: 'transfers',
  },
  item: {
    imageSrc: '/asset/images/<EMAIL>',
    name: '141 LAND ABC',
    asset: 'Alexa Smith',
    assetLink: 'alexaSmith',
  },
  price: '2.456 ETH',
  from: {
    name: 'netocalifonia',
    link: 'netocalifonia',
  },
  to: {
    name: 'Mike_McNFT',
    link: 'Mike_McNFT',
  },
  time: '2021’01.01 15:00',
  chains: ['klaytn'],
})

export const activities = (saleArray.concat(offerArray, transferArray)).sort(() => Math.random() - 0.5)
