# Vending Machine Dashboard - Metrics Documentation

## 📊 Tổng quan Dashboard

Dashboard Vending Machine cung cấp 4 thông số quan trọng để theo dõi hiệu suất và trải nghiệm người dùng của hệ thống vending machine NFT.

---

## 🔢 Chi tiết 4 thông số chính

### 1. Sales Volume (Doanh thu bán hàng) 💰

**Định nghĩa**: Tổng số tiền USDC mà tất cả người dùng đã chi để mua pack/card từ vending machine trong khoảng thời gian được chọn.

**Cách tính**: 
```
Sales Volume = Σ (Giá pack × Số lượng pack đã bán thành công)
```

**Khi nào thay đổi**:
- ✅ **Tăng**: User hoàn thành thanh toán thành công
- ✅ **Tăng**: Nhiều user mua cùng lúc
- ❌ **Không đổi**: User chỉ approve mà không complete
- ❌ **Không đổi**: Transaction failed

**V<PERSON> dụ thực tế**:
```
- 10:00 AM: User A mua pack $30 → Sales Volume: $30
- 10:15 AM: User B mua pack $30 → Sales Volume: $60  
- 10:30 AM: User C approve nhưng cancel → Sales Volume: $60 (không đổi)
- 11:00 AM: User D mua pack $50 → Sales Volume: $110
```

**Trend tốt**: Màu xanh ↗️ (tăng so với kỳ trước)

---

### 2. Return Rate (Tỷ lệ hoàn trả) 🔄

**Định nghĩa**: Phần trăm giao dịch bị thất bại, hủy bỏ hoặc không hoàn thành so với tổng số giao dịch được khởi tạo.

**Cách tính**:
```
Return Rate = (Số giao dịch thất bại / Tổng số giao dịch) × 100%
```

**Các trường hợp được tính là "Return"**:
- User approve token nhưng không complete payment
- Transaction bị failed do insufficient gas
- Network timeout/congestion
- User manually cancel transaction
- Smart contract revert
- Insufficient token balance

**Khi nào thay đổi**:
- ⬆️ **Tăng**: Nhiều giao dịch thất bại
- ⬇️ **Giảm**: Tỷ lệ thành công cao hơn

**Ví dụ thực tế**:
```
Trong 1 ngày:
- 100 user khởi tạo giao dịch
- 85 user hoàn thành thành công  
- 15 user failed/cancel
→ Return Rate = 15/100 = 15%
```

**Trend tốt**: Màu đỏ ↘️ (giảm so với kỳ trước)

---

### 3. Average Retries per User (Số lần thử lại trung bình) 🔁

**Định nghĩa**: Số lần trung bình mỗi user phải thử lại (retry) để hoàn thành một giao dịch mua pack thành công.

**Cách tính**:
```
Average Retries = Tổng số attempts / Số user unique
```

**Các trường hợp được tính là "Retry"**:
- Click "Approve Token" nhiều lần do failed
- Click "Buy Now" nhiều lần do network issue
- Refresh page và thử lại
- Switch network và retry
- Increase gas price và retry

**Khi nào thay đổi**:
- ⬆️ **Tăng**: UX kém, network congestion, lỗi smart contract
- ⬇️ **Giảm**: UX tốt, network stable, process smooth

**Ví dụ thực tế**:
```
User A: Thành công ngay lần 1 → 1 attempt
User B: Failed 2 lần, thành công lần 3 → 3 attempts  
User C: Failed 1 lần, thành công lần 2 → 2 attempts
→ Average = (1 + 3 + 2) / 3 = 2.0 retries/user
```

**Trend tốt**: Màu đỏ ↘️ (giảm so với kỳ trước)

---

### 4. Circulation Speed (Tốc độ luân chuyển) ⚡

**Định nghĩa**: Thời gian trung bình (tính bằng ngày) từ khi một card được mint thành công đến khi nó được bán lại/trade trên secondary market.

**Cách tính**:
```
Circulation Speed = Σ(Trade Date - Mint Date) / Số card đã được trade
```

**Khi nào thay đổi**:
- ⬇️ **Giảm (tốt)**: 
  - Card có giá trị cao → user muốn trade nhanh
  - Market có liquidity tốt
  - Community active trading
  - Rare cards được discover
- ⬆️ **Tăng (không tốt)**:
  - Card ít giá trị → user hold lâu
  - Market không có buyer
  - Lack of trading platform integration

**Ví dụ thực tế**:
```
Card A: Mint 1/1, trade 5/1 → 4 ngày
Card B: Mint 2/1, trade 4/1 → 2 ngày  
Card C: Mint 3/1, trade 8/1 → 5 ngày
→ Average = (4 + 2 + 5) / 3 = 3.7 ngày
```

**Trend tốt**: Màu đỏ ↘️ (giảm = trade nhanh hơn)

---

## 🔄 Scenarios thực tế và tác động

### Scenario 1: User mua pack thành công (Happy Path)
```
📊 Tác động:
✅ Sales Volume: +$30 (tăng)
✅ Return Rate: Giảm (thêm 1 success case)  
✅ Average Retries: Giảm/giữ nguyên (nếu 1 attempt)
➖ Circulation Speed: Chưa thay đổi (card mới mint)
```

### Scenario 2: User approve nhưng cancel payment
```
📊 Tác động:
➖ Sales Volume: Không đổi
❌ Return Rate: Tăng (thêm 1 failed case)
❌ Average Retries: Tăng (wasted attempt)
➖ Circulation Speed: Không đổi
```

### Scenario 3: Network congestion, user retry nhiều lần
```
📊 Tác động:
✅ Sales Volume: Tăng chậm (ít transaction success)
❌ Return Rate: Tăng cao (nhiều failed attempts)
❌ Average Retries: Tăng đáng kể (3-5 retries/user)
➖ Circulation Speed: Có thể tăng (ít người trade)
```

### Scenario 4: Card rare được trade nhanh
```
📊 Tác động:
➖ Sales Volume: Không đổi (secondary market)
➖ Return Rate: Không đổi
➖ Average Retries: Không đổi  
✅ Circulation Speed: Giảm mạnh (trade trong 1-2 ngày)
```

### Scenario 5: Gas price cao, nhiều user failed
```
📊 Tác động:
❌ Sales Volume: Giảm (ít success)
❌ Return Rate: Tăng cao (nhiều "insufficient gas")
❌ Average Retries: Tăng (user tăng gas và retry)
➖ Circulation Speed: Có thể tăng (ít activity)
```

---

## 📈 Cách đọc Dashboard

### Color Coding
- 🟢 **Xanh lá + ↗️**: Chỉ số tích cực tăng (Sales Volume tăng)
- 🔴 **Đỏ + ↘️**: Chỉ số tiêu cực giảm (Return Rate giảm = tốt)
- 🔴 **Đỏ + ↗️**: Chỉ số tiêu cực tăng (cần attention)
- ⚪ **Xám + →**: Không thay đổi đáng kể

### Time Periods
- **24h**: Theo dõi real-time, phát hiện issues nhanh
- **7d**: Xu hướng tuần, planning và optimization  
- **30d**: Trend dài hạn, business performance
- **90d**: Strategic analysis, seasonal patterns

### Benchmark Values
```
Sales Volume: Không có limit (càng cao càng tốt)
Return Rate: < 10% = Excellent, < 20% = Good, > 30% = Need attention
Average Retries: < 2.0 = Excellent, < 3.0 = Good, > 4.0 = Poor UX
Circulation Speed: < 3 days = Very active, < 7 days = Good, > 14 days = Slow market
```

---

## 🚨 Alert Conditions

### Critical Issues (Cần xử lý ngay)
- Return Rate > 50% trong 24h
- Average Retries > 5.0 trong 24h  
- Sales Volume giảm > 70% so với baseline
- Circulation Speed > 30 ngày (market dead)

### Warning Conditions (Cần monitoring)
- Return Rate tăng > 20% so với tuần trước
- Average Retries tăng > 1.0 so với baseline
- Sales Volume giảm > 30% so với tuần trước
- Circulation Speed tăng > 100% so với tuần trước

---

## 🔧 Technical Implementation

### Data Sources
```typescript
// Primary sales tracking
Sales Volume: từ smart contract events + API database

// Transaction monitoring  
Return Rate: từ transaction logs + user behavior analytics

// User interaction tracking
Average Retries: từ frontend analytics + session tracking

// NFT marketplace integration
Circulation Speed: từ marketplace APIs + on-chain transfer events
```

### Update Frequency
- **Real-time**: Mỗi khi có transaction mới (WebSocket)
- **Batch**: Mỗi 5 phút (scheduled job)
- **Historical**: Daily aggregation cho long-term trends

### API Endpoint
```
GET /api/admin/vending-machine/dashboard?period={24h|7d|30d|90d}
```

---

## 📝 Notes cho Development

1. **Mock Data**: Hiện tại sử dụng mock data trong `VendingMachineDashboardRemoteDatasource`
2. **Real API**: Uncomment phần real API call khi backend ready
3. **WebSocket**: Có thể thêm real-time updates qua WebSocket
4. **Caching**: Consider Redis cache cho performance
5. **Analytics**: Integrate với Google Analytics hoặc Mixpanel để track user behavior

---

*Tài liệu này được tạo để hỗ trợ team hiểu rõ ý nghĩa và cách sử dụng Dashboard metrics. Cập nhật: 2024-01-15*
