# Overview

* NFT LAND Client Web Application follows `Repository Pattern`.
* `Repository Pattern` is famous in context of DDD (Domain Driven Development) and a one of design patterns to hide persisting data from application logic, which makes clean application logic and its testability well.

# Repository Pattern

* Whole typescript source code is organized with following Repository Pattern chart.

![Repository Pattern](/documents/images/repository_pattern_chart.png)

## Data Layer

* `Data Layer` includes `data`, `datasources`, `repository implementation classes`, and `data factories`.
* `Data` represents a unit of data which expressed with JSON. 
* `Datasource` is categorised into four groups, abstract, local, remote, and blockchain. `Datasource` has a responsibility of handling `data` correctly. `Abstract` provides just common interfaces, `Local` implementations follow the interfaces and handle local storages. `Remote` implementations follow the interfaces and handle remote storages by mainly communicating with remote API endpoints. `Blockchain` implementations follow the interfaces and handle communicating with smart contracts on blockchain.
* `Repository(Implementation)` classes have a responsibility of not only simple operations like datasource but also aggregating related objects.
* `Data Factories` have short cut functions to create `Data` instance. This does NOT persist the data, just create the instance.

## Domain Layer

* `Domain Layer` includes `Models`, `UseCases`, `Repositories(Interfaces)` and `Services`.
* Interfaces of `Repositories` which are located in lower layer are defined in this layer. Then classes in Domain Layer doesn't know about repositories implementation but they communicate with them by using interface.
* `Models` are common objects in domain layer. `Model` is similar to `Data` but `Model` can have convenient value objects(human friendly) and also can have aggregated info as a instance member. `Model` can be used in domain layer and presentation layer, but `Data` should not be used in presentation layer.
* `Usecases` are  reusable common business logics which communicate mainly with repositories. `Usecases` knows about only repository interfaces, should not know about concrete implementations.
* `Services` are basically static library, which does not has the status. Services can talks with entities in domain layer or presentation layer.

## Presentation Layer

* `Presentation Layer` includes each frontend logics and UIs (In server side web applications, it means controllers and views).
* `Presentation Layer` should focus on how to make the UI and should not care about internal technical issues. Each Presentation Layer components are supposed to talk to `Repositories`, `Usecases` and `Services` located in lower layer.

# Rules

## Presentation Layer

* Basically should not import any classes in data layer. 
    * Importing Repository Implementation Classes are exception.
    * Now We are planing to introduce dependency injection not to need to import.
* Should know only following classes.
    * `Models`
    * `Repositories` (when defining type, use abstract class)
    * `UseCases`
    * `Services`

## Domain Layer

* Only `Models` know about `Data` in data layer. `Models` can convert to `Data` and can be created from `Data`
    * Model.fromData()
    * model.toData()
* Except `Models`, other objects are allowed to talk to only objects in the same layer.

## Data Layer

* `Repositories(Imprementation)` knows `Models`, `Repositories(Interface)`, `Datas`, and `Datasources`. It also has a responsibility to convert repository queries to datasource queries.
* Except `Repositories`, other objects are allowed to talk to only objects in the same layer.