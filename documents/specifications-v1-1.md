# Specificaitons of Nft Land Client v1.1

* The base specifications are written in `/documents/files/specifications-v3.xlsx` by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.
* This excel file includes across some versions.
	* v1.1: prototype of marketplace (working with blockchain)
	* v2.0: release of marketplace
	* v3.0: relaase of analytics
	* v4.0: release of NFT generator
* Please see this specification documents with the complementary excel file.

## Overview of v1.1

* The scope of v1.1 is implementing basic NFT marketplace functions as follow.
    * A user can create an account and login in with WalletConnect or Metamask.
    * NFT LAND imports NFTs which users own (also updating the ownerships periodically).
    * A user can create an NFT with NftLand Contract (registering the NFT to both internal database and Smart Contract).
    * A user can create an NFT `listing` (registering the listing info to both internal database and Smart Contract, probably the latter can be ommited with signature).
    * A user can create collections.
    * A user can buy any NFTs being listed (changing the database and making a transaction with Smart Contract).

## List of pages to implement

| URL | Page Name | Authorization |
| --- | --- | --- |
| / | Top Page |  |
| /announcements | List of announcements page |  |
| /announcements/:id | Announcement detail page |  |
| /assets | List of assets page |  |
| /assets/:id | Asset detail page |  |
| /assets/create | Asset creation page | required |
| /listings/create | Listing creation page | required and owner |
| /collections/:id | Collection detail page (Searching assets) |  |
| /collections/:id/activities | List of collection's activities page |  |
| /collections/create | Collection creation page | required |
| /collections/rankings | Collection ranking page |  |
| /users/:id | User profile page |  |
| /users/:id/created | List of user's created assets page |  |
| /users/:id/favorited | List of user's favorited assets page |  |
| /users/:id/activities | List of user's activities page |  |
| /users/:id/offers/received | List of user's received offers page |  |
| /users/:id/offers/made | List of user's offers page |  |
| /activities | List of activities page |  |
| /categories/:id | Category detail page (Searching assets) |  |
| /account | My profile page | required |
| /account/created | List of my created assets page | required |
| /account/favorited | List of my favorited assets page | required |
| /account/activities | List of my activities page | required |
| /account/offers/received | List of my received offers page | required |
| /account/offers/made | List of my offers page | required |
| /account/collections | List of my collections page | required |
| /account/watchingCollections | List of my watching collections page | required |
| /account/profileSettings | My profile setting page | required |
| /account/notificationSettings | My notification setting page | required |
| /account/offerSettings | My offer setting page | required |
| /account/support | Account support page | required |

## Requirements of pages

### Common Parts: Header

* If a user put keywords in search console and enter, the user will be redirected to `List of assets page (/assets?q={urlencode(keyword)})` with the keyword.

* If a user clicks menu icon, list of links shows up.

```
Category
- Digital Art
- Gaming
- Trading Cards
- Utility
- Domain Names
- Limit
- Music
- Virtual Worlds
- Photo
- Sports
- Collect

Collections
- Ranking

Activities
```

* If a user's wallet is connected to blockchain, the blockchain network's icon and name shows (Ethereum, Matic, BSC, Aster, etc.).

* If a user is logged in, the user's thumbnail shows. If the user is not logged in, default user thumbnail shows.

* The last published announcement's title (shows within one line with postfix '...') shows and it's a link to the announcement detail page (`/announcements/:id`). `News >` is a text link to list of announcements page (`/announcements`).

### Top Page (/): Excel No.3

#### Main banner section

* Main banners show as a slide. 
* The order is sorted by `priority desc`.
* Each main banner is link to its own `url`.

#### Recommendation section

* Boosted collections show as a slide. 
* The order is sorted by `priority desc`.
* Each boosted collection is link to the colleciton's detail page (`/collections/:id`)

#### Now Sale section

* This section is removed with v1.1

#### Top Sale section

* A user can search assets(NFTs) with filter of time range. 
* Assets are sorted by `Recently Listed`. 
* Searching collections function is removed with v1.1. 
* The time range filter values are as follows.
    * `last 24 hours`
    * `last 7 days`
    * `last 30 days`
    * `all time (default)`
* In each asset box, following info shows.
    * Collection thumbnail and name (within one line). They are link to Collection datail page (`/collections/:id`)
    * Number of favorites
        * If the user is logged in and already liked it, the heart icon's color is red. If the user clicks the icon, user can unlike it and the number will be incremented.
        * If the user is logged in and not liked it yet, the heart icon's color is transparent. If the user clicks the icon, user can like it and the number will be decremented.
        * If the use is not logged in and click the heart icon, a popup shows up and ask the user to login in)
    * The asset's thumbnail image. If there is no image, `No Image` image shows. This is also link to asset detail page (`/assets/:id`).
    * The asset's name within one line appending '...'. This is also link to asset detail page (`/assets/:id`).
    * If there is listings of the asset, the minimum price and the token shows. If there are some listings with different tokens (like, Eth and Matic), these prices need to be calculated to equivalent `USD` and to be compared.
    * If there is a last sale price, `Last sale: {price} {token}` shows.
    * If there is at least one listing, the rest of time to be expired shows. 
        * More than 1day: `xx days left`
        * Within 1day: `xx hours left`
        * Within 1hour: `xx mins left`
* In left side, there are links to category detail page (`/category/:id`).
* The order is sorted by `order asc`.
* The mapping of slug and category name is as follows. The category's `id` is not pre-determined, but the `slug` is pre-determined. Category's icon filename can assume the slug.

|slug|category name|
|---|---|
|digitalArt|Digital Art|
|gaming|Gaming|
|tradingCards|Trading Cards|
|utility|Utility|
|domainNames|Domain Names|
|limit|Limit|
|music|Music|
|virtualWorlds|Virtual Worlds|
|photo|Photo|
|sports|Sports|
|collect|Collect|

* `See More` button is link to list of assets page (`/assets`)

### List of announcements page (/announcements): No Excel Sheet

* Shows a breadcrumb `Top page > announcements` in top of page.
* Shows number of hits of announcements.
* Shows list of announcements sorted by `published_at desc`. The maximum announcements per a page is `30`.
* Each row of announcement includes following elements. Each element is link to announcement detail page (`/announcements/:id`)
    * Published Date
    * Announcement thumbnail
    * Announcement title: showing within one line.
* If there is a next page, pager shows.

### Announcement detail page (/announcements/:id): No Excel Sheet

* Shows a breadcrumb `Top page > announcements > ${title}` in top of page.
* Shows published date.
* Shows the thumbnail.
* Shows the title.
* Shows the body.

### List of assets page (/assets): Excel No.4

* This page shows assets with conditions a user specifies.
* If a user specifies keywords (with query parameter, `q={urlencoded(keywords)}`) , only assets related to the keywords shows.
* A user can set conditions with filter sections at side bar. If the user changes the filter, the results will be changed dynamically, showing assets with the current filter on 1st page.
* Asset box info to show is the same as described in Top page.
* The number of assets per a page is `30`. 
* If there is next page, pager shows.
* Filtering assumes that users are most interested in the minimum price listing.

#### Filter: Status

* A user can specify the status of listings. The default is `on` to all status.
    * `Buy Now`: there is active mininum price listing as `buy now`
	* `On Auction`: there is active mininum price listing as `auction`
	* `New`: there is active minimum price listing created within 24 hours
	* `Has Offers`: there is at least one active offer

#### Filter: Price

* A user can specify the token (ETH or USD) and price. 
* If the minimum price listing is within the price range, the asset will hit.

#### Filter: Collections

* A user can specify a collection.
	* If the user inputs a string in a search console, max five candidates list will appear. If the user click one of collection, only the collection's assets will show.
	* Collections should be sorted by how popular it is (`total_volume desc`).

#### Filter: Categories

* A user can specify categories. The default is `on` to all categories.

#### Filter: Gacha

* Filter of Gacha is removed with v1.1.

#### Filter: Marketplace

* A user can specify the marketplace where the asset is sold. The default is only `NFT LAND`.

#### SortBy

* A user can select sort indicator. 
	* `Recently Listed` (default): The last date the asset is listed (`last_listed_at`) with declining
	* `Recently Created`: The date the asset was minted (`minted_at`) with declining
	* `Recently Sold`: The last date the asset was sold (`last_sold_at`) with declining
	* `Ending Soon`: The expiration of listing (`min_price_listing_expired_at`) with ascending
	* `Price: Low to High`: The minimum price of listings (`min_listing_price`) with ascending
	* `Price: High to Low`: The minimum price of listings (`min_listing_price`) with declining
	* `Highest Last Sale`: The last sale price (`last_sold_price`) with declining
	* `Oldest`: The date the asset was minted (`minted_at`) with declining
* Accurately, the price need to be calculated to ETH and USD to be compared.

### Asset detail page (/assets/:id): Excel No.5, No.26

#### Overview

* If a user is the owner of the asset, a following button shows at the right top corner.
	* `Sell`: If the asset is `ERC721` or the asset is `ERC1155` and the supply is only one, the asset could be listed as fixed price or timed auction.
	* `List item`: If the asset is `ERC1155` and the total supply is more than one, the asset could be listed as only fixed price.
* Shows following infos of the asset.
	* Thumbnail: if a user click the thumbnail, modal shows up to show the image in large scale
	* The asset name
	* Collection thumbnail and name, link to the collection detail page
	* Owner
		* If there is a user in NFT LAND, the username shows
		* If there is no user in NFT LAND, the address shows
		* In case of ERC721, only one user shows
		* In case of ERC1155, multiple users shows (within one line)
	* Right top area, there are some icon buttons
		* Share: Copy Link, Share on Facebook, Share on Twitter
		* More: Refresh page, View website, Report
	* Shows statistic numbers as follows.
		* Number of Views: 
		* Number of Favorites:
		* In the excel file "Number of owners" and "Total" is specified, but they are removed
	* If there is active min price listing, the expiration date and the current price shows (with equivalent USD).
	* If there is active min price listing and the listing is `buy now`, shows `Buy Now` button. If a user click `Buy Now` button, modal appears to purchase the asset.
	* If there is active min price listing and the listing is `auction`, shows `Place Bit` button. If a user click `Place Bit` button, modal appears to bid the auction.
	* If there is no listing, shows `Make Offer` button. If a user click `Make Offer` button, modal shows to make an offer.
	* In en excel file, there is `Same NFT` button. But it's removed with v1.1.

#### Left: Asset Info

* Shows creator
	* If the user is registered in NFT LAND, the username shows
	* If the user is not registered in NFT LAND, the address shows
* Shows Description
* Shows Details
	* Address
	* Token ID
	* Token Standard: Schema(ERC721 or ERC1155)
	* Blockchain
* Shows Properties
* Shows Collection Info
	* Thumbnail
	* Collection name
	* Description
	* Links: web site, Discord, Twitter, Telegram, Opensea
* Shows Stats

#### Right: Price History & Offers

* Shows chart of sales events (the sold price) with ETH, average price and volume.
* A user can select time range.
	* `last 7 days`
	* `last 14 days`
	* `last 30 days`
	* `last 60 days`
	* `last 90 days` (default)
	* `last year`
	* `All time`
* Shows list of active offers. Each raw shows following infos.
	* Unit Price: Token and price
	* USD Price: The equivalent value in USD in realtime.
	* Floor Difference
	* Expiration date
	* From: username of the user who made the offer

#### Activities

* Shows list of events of the asset sorted by `created_at` declining.
* Each raw shows following infos.
	* Event type
	* Unit Price: token and price
	* Quantity
	* From: who made the event
	* To: who received the event
	* Time: the date the event happened

#### More From This collection

* Shows max 5 assets of the same collection
* Asset box info is the same as list of assets page.
* Assets should be sorted by `last_sold_price` declining.

### Asset creation page (/assets/create): Excel No.20

* Only logged in user can access this page.
* Image, Video, Audio, or 3D Model: upload the local file
* Item Name: alphabet and number, max 25 characters
* External Link: max length 1000
* Description: max length 500 characters
* Collection
	* If a user click the select, already created collections shows as candidates
	* If a user click info icon, popup shows "You can manage your collections here" and it's link to List of my collections page (`/account/collections`)
* Properties: same as opensea (modal shows)
* Levels: same as opensea (modal shows)
* Stats: same as opensea (modal shows)
* Unlockable Content: If a user set this on, textarea shows
* Explicit & Sensitive Content
* Supply: The NFT Contract is ERC1155, any number is acceptable. Default value is 1.
* Blockchain: only Ethereum and Matic is selectable with v1.1
* Freeze metadata: same as opensea.
* After submitting "Create" button, following procedures will be done.
	* User's wallet connect to NftLand NFT Contract to mint the NFT.
	* If the NFT is minted successfully, connect to API server to register the NFT in the internal database.
* Preview function is removed with v1.1.

### Listing creation page (/listings/create)

* Only the user which logged in and the owner of the asset can access this page.
* Listing types are `Fixed Price` or `Timed Auction`. The default type is `Fixed Price`.

#### Fixed Price (default): Excel No.27

* Type is selected `Fixed Price`.
* The owner can select the token to sell, but the token is limited to the tokens the collection specifies. 
* The owner can specify the duration of listing.
* If the owner turns "Sell as a bundle" on, the following fields appear.
	* Bundle name
	* Bundle description
	* Add item
		* The asset is limited to ones in the same collection.
		* If the owner types a string, candidate items appear.
		* If the owner select the item, the item is selected and shows. There is `trash icon` to remove the item.
* If the owner turns "Reserve for specific buyer" on, the address input field appears.

#### Timed Auction: higest bidder: Excel No.28

* If the asset is `ERC721` or `ERC1155` and the total supply is one, the asset can be listed as timed auction.
* Type is selected `Timed Auction`.
* The owner can select the following auction method.
	* `Sell to highest bidder`
	* `Sell with declining price`
* The owner decides the starting price.
	* The token is limited to only `ERC20` token. Native currency like ETH is not allowed to use.
* The owner can decide the duration. 
* If the owner turns "Include reserve price" on, the input field appears.
	* The token is limited to `ERC20` token.

#### Timed Auction: declining price: Excel No.29

* If the asset is `ERC721` or `ERC1155` and the total supply is one, the asset can be listed as timed auction.
* Type is selected `Timed Auction`.
* The owner can select the following auction method.
	* `Sell to highest bidder`
	* `Sell with declining price`
* The owner decides the starting price.
	* The token is limited to only `ERC20` token. Native currency like ETH is not allowed to use.
	* Although `ETH` is set for the token in the excel file, this is mistake.
* The owner also can decide the ending price.
	* The token must be the same as starting price.
	* It's necessary to guarantee that starting price token and ending price token are the same.
	* It's also okay that making the ending price token display only.

### Collection detail page (Searching assets) (/collections/:id): Excel No.8

#### Overview

* There are some buttons at right top corner.
	* `+ Watchlist`: 
		* If the user is not logged in, this button is inactive.
		* If the user is logged in and haven't watch it, this button is not colored. If the user click it, the collection will be registered as wathing collection.
		* If the user is logged in and have watched it, this button is colored. If the user click it, the colleciton will be unregistered as watching collection.
	* `Your Site`: 
	* `Discord`: 
	* `Instagram`: 
	* `Medium`
	* `Twitter`: 
	* `Telegram`: 
* Shows following Infos.
	* Collection's backgound image
	* Colleciton's thumbnail image
	* Colleciton's name
	* Colleciton's creator name
	* Collection's description
	* Colleciton's statistics 
		* Number of assets
		* Number of owners
		* Floor price
		* Volume traded
	* Recommendation button is removed with v1.1
* Tabs under collection's overview
	* Items: default
	* Bundles: removed with v1.1
	* Activity: list of activities of this collection

#### Filter: Status

* A user can specify the status of listings. The default is `on` to all status.
    * `Buy Now`: there is active mininum price listing as `buy now`
	* `On Auction`: there is active mininum price listing as `auction`
	* `New`: there is active minimum price listing created within 24 hours
	* `Has Offers`: there is at least one active offer

#### Filter: Price

* A user can specify the token (ETH or USD) and price. 
* If the minimum price listing is within the price range, the asset will hit.

#### Filter: Chains

* A user can specify the chain on which the asset exists.

#### Filter: On Sale In

* A user can specify the token with which the assets are traded.
	* A NFT can be traded with different tokens.
	* For example, Asset A on Ethereum can be listed with ETH or WETH.

#### Result

* Shows following infos.
	* Results: the number of hits
	* Property filer: removed with v1.1
* Each Asset box is the same as `List of assets page (/assets)`
* A user can select sort indicator. 
	* `Recently Listed` (default): The last date the asset is listed (`last_listed_at`) with declining
	* `Recently Created`: The date the asset was minted (`minted_at`) with declining
	* `Recently Sold`: The last date the asset was sold (`last_sold_at`) with declining
	* `Ending Soon`: The expiration of listing (`min_price_listing_expired_at`) with ascending
	* `Price: Low to High`: The minimum price of listings (`min_listing_price`) with ascending
	* `Price: High to Low`: The minimum price of listings (`min_listing_price`) with declining
	* `Highest Last Sale`: The last sale price (`last_sold_price`) with declining
	* `Oldest`: The date the asset was minted (`minted_at`) with declining

### List of collection's activities page (/collections/:id/activities): Excel No.10

* The overview is the same as collection detail page.

#### Filter: Event

* Event type is as follows.
	* `Listed`
	* `Sale`
	* `Transfer`
	* `Offer`
	* `Minted`

#### Filter: Chains

* A user can specify the chains on which the events occurred.

#### Result

* If the event type is selected `Sale`, the price transition chart will appeara at the top.
* Shows following infos.
	* Results: Number of hits
	* Floor Price: remove with v1.1
	* Property Filter: remove with v1.1
* Althogh the excel file specifies the sort function, this is removed. The sort logic is `created_at desc` only.

### Collection creation page (/collections/create): Excel No.22

* Only logged in user can access this page.
* Logo Image: recommended 350*350 px image.
* Featured Image: recommended 600*400 px image.
* Banner Image: recommended 1400*350 px image.
* Name: only alphabet and number, max 25 characters
* URL: a user can decide the slug of the collection. If the user input a string, the slug is checked if the slug is already used or not.
	* max length is 100.
	* If it's used already, `The URL is already used` error message shows.
	* If it's not used, `The URL is valid.` success message shows.
* Description: max length is 1000.
* Category
	* Only one category can be selected
	* If one category is selected, the left plus icon's color turns to gley and inactive. 
* A user can set links
	* `Your Site`: URL
	* `Discord`: username
	* `Instagram`: username
	* `Medium`: username
	* `Twitter`: username
	* `Telegram`: username
* Creator Earnings
	* Only specify the address.
	* In the excel file, it seems that user can select the token of reward. But the reward token depends on the token of listing.
* Payment Token
	* Selectable tokens depends on which blockchain.
	* With v1.1, selectable tokens are limited.
	* Ethereum => ETH, WETH
	* Matic => ETH, MATIC
	* ERC20 tokens will be introduced as payment token in the future (out of scope of v1.1).
* Explicit & sensitive content
* Collaborators: who can edit this collection info
	* A user can add collaborators by specifying the address.
	* If there is no user with the address, `Not found the user` error message shows in a modal.
	* If there is a user with the address, the user's thumbnail and username appears (with remove icon at the right top corner).

### Collection ranking page (/collections/ranking): Excel No.15

* A user can see the collection rankings with filter.
* A user can specify the time range.
	* `last 7 days`
	* `last 14 days`
	* `last 30 days`
	* `last 60 days`
	* `last 90 days` (default)
	* `last year`
	* `All time`
* A user can specify the category. Showing all categories to select.
* A user can specify the chain. Showing all chains to select.

#### Result

* The row includes following infos.
	* Rank
	* Collection(thumbnail, name)
	* Volume(during the period)
	* 24H: remove with v1.1
	* 7D: remove with v1.1
	* Floor Price(during the period)
	* Owners (number of owners)
	* Items (number of assets)

* Showing pager
	* hits
	* pages
	* number of rows per a page

### User profile page (/users/:id): Excel No.12

#### Overview

* Background Image
* Thumbnail Image
* Public Address: truncated with "..." in the middle
* User's rank (Power Collector) is removed with v1.1
* Joined date (`created_at`)
* There are some buttons at the right top corner.
	* `Your Site`
	* `Twitter`
	* `Instagram`
	* `Share`: Copy Link, Share on Facebook, Share on Twitter
	* `Setting`: If the viewer is the user, setting button shows
* There are following tabs.
	* `Collected`: The assets the user is owning
	* `Created`: The assets the user has created
	* `Favorited`: The assets the user has liked 
	* `Activity`: The user's activities
	* `Offers`: The user's made offers or received offers
		* When you click the tab, a popup shows up having two links (`offers received` and `offers made`)

#### List of Assets

* The overview is the same as `User profile page (/users/:id)`.
* The function is the same as `List of assets page (/assets)`.
* Only assets being owned by the user are shown.

### List of user's created assets page (/users/:id/created)

* The overview is the same as `User profile page (/users/:id)`.
* The function is the same as `List of assets page (/assets)`.
* Only assets created by the user are shown.

### List of user's favorited assets page (/users/:id/favorited)

* The overview is the same as `User profile page (/users/:id)`.
* The function is the same as `List of assets page (/assets)`.
* Only assets favorited by the user are shown.

### List of user's activities page (/users/:id/activities)

* The overview is the same as `User profile page (/users/:id)`.
* The function is the same as `List of activities page (/activities)`.
* Only activities made by the user are shows.

### List of user's received offers page (/users/:id/offers/received): Excel No.13

* The overview is the same as `User profile page (/users/:id)`.
* A user can specify collection in the side filer.
* "Show all offers" toggle and setting icon is removed with v1.1
* The result shows as a list. Each row includes following infos.
	* Item: thumbnail, NFT name, collection name
	* Unit Price: 
	* USD Price: 
	* Floor Difference:
	* From: the offer's username
	* Expiration: 
	* Received(Date): 
	* Accept Button: If the viewer is the user, this button appears.

### List of user's offers page (/users/:id/offers/made): Excel No.14

* The overview is the same as `User profile page (/users/:id)`.
* A user can specify collection in the side filer.
* "Show all offers" toggle and setting icon is removed with v1.1
* The result shows as a list. Each row includes following infos.
	* Item: thumbnail, NFT name, collection name
	* Unit Price: 
	* USD Price: 
	* Floor Difference:
	* From: the offer's username
	* Expiration: 
	* Made(Date): 
	* Cancel Button: If the viewer is the user, this button appears.

### List of activities page (/activities): Excel No.16

#### Filter: Event

* Event type is as follows.
	* `Listed`
	* `Sale`
	* `Transfer`
	* `Offer`
	* `Minted`

#### Filter: Collection

* If you type a string, search console suggests max 5 candidate collections.
* If you input collection string or select the suggestion, the result will be reflected.

#### Filter: Chains

* A user can specify the chains on which the events occurred.

#### Result

* The result will shows at right side as a list. Each row includes following infos.
	* Event Type: Icon with name
	* Item: Thumbnail, Asset name, collection name
	* Price: 
	* From: the person who made the event
	* To: the person who received the event
	* Time: The time the event occurred
* The sort is `created_at desc` only.

### Category detail page (Searching assets) (/categories/:id): Excel No.17

#### Overview

* Category Name
* Category Description

#### Filter: Chains

* A user can specify the chain on which the assets exists. If select multiple chains, it's OR conditions.

#### Links: All Categories

* There are all category links with Icon and name.
* If a user click one of links, the user will be redirected to `Category detail page (/categories/:id)`.

#### Result

* Only collections belonging to the category are shown.
* The collecitons are sorted by `total_volume desc`.
* Each colleciton box includes following infos.
	* Background Image
	* Thumbnail Image
	* Collection Name
	* Total Volume (ETH)
* Shows pager at the bottom.

### My profile page (/account): Excel No.12

* This URL is accessed by only logged in user.
* The function is the same as `User profile page (/users/:id)`
* The `/account` shows the logged in user's info.

### List of my created assets page (/account/created)

* This URL is accessed by only logged in user.
* The function is the same as `List of user's created assets page (/users/:id/created)`
* The `/account/created` shows the logged in user's info.

### List of my favorited assets page (/account/favorited)

* This URL is accessed by only logged in user.
* The function is the same as `List of user's favorited assets page (/users/:id/favorited)`
* The `/account/favorited` shows the logged in user's info.

### List of my activities page (/account/activities)

* This URL is accessed by only logged in user.
* The function is the same as `List of user's activities page (/users/:id/activities)`
* The `/account/activities` shows the logged in user's info.

### List of my received offers page (/account/offers/received): Excel No.13

* This URL is accessed by only logged in user.
* The function is the same as `List of user's received offers page (/users/:id/offers/received)`
* The `/account/offers/received` shows the logged in user's info.

### List of my offers page (/account/offers/made): Excel No.14

* This URL is accessed by only logged in user.
* The function is the same as `List of user's made offers page (/users/:id/offers/made)`
* The `/account/offers/made` shows the logged in user's info.

### List of my collections page (/account/collections): Excel No.19

* This URL is accessed by only logged in user.
* This page is for managing the user's collections.

#### Overview

* `Create a collection` Button
	* If the user click it, the user will be redirected to `Collection creation page (/collections/create)`.

#### Result

* The collections of which the user is creator or collaborator are shown.
* Each collection box includes following infos.
	* Background Image
	* Thumbnail Image
	* Collection Name
	* Number of Items

### List of my watching collections page (/account/watchingCollections): Excel No.18

* This URL is accessed by only logged in user.
* This page is for managing the user's watching collections.
* The collections of which the user has liked before are shown.
* Each row shows following infos.
	* Collection: thumbnail, collection name
	* Floor Price: Floor price in whole period
	* 7d Volume: Volume in past 7 days
	* 24H: Difference of floor price compared to 24 hours before
	* 7D: Difference of floor price compared to 7 days before
	* Owners: Number of owners
	* Items: Number of assets
* If the user click "x" icon button at the rightmost in a row, the collection will be removed from watching collection and the row will disappear.
* At the bottom, there is a pager.
	* Shows number of hits.
	* There are page links.
	* A user can select number of rows per a page.
		* `Rows 20` (default)
		* `Rows 50`
		* `Rows 100`

### My profile setting page (/account/profileSettings): Excel No.32

#### Before Submitting

* Only logged in user can access this page.
* The user can specify the username.
	* The max length of username is 30.
	* The username must be unique.
	* If the user enter a username, client app should check if the username is valid (not used by anyone). If the username is the user's username, it's valid.
	* If the username is valid, success message `this username is valid` shows.
	* If the username is invalid, error message `this username is used already` shows.
* The user can specify the email address.
	* The max length of email is 250.
	* The email must be unique.
	* If the user enter an email, client app should check if the email is valid (not registered by anyone). If the email is the user's email, it's valid.
	* If the email is valid, success message `this email is valid` shows.
	* If the email is invalid, error message `this email is registered already` shows.
* The user can specify the description.
* The user can upload profile image and profile banner.
* The user can set social account infos.
	* `Your Site`: 
	* `Discord`: 
	* `Instagram`: 
	* `Medium`
	* `Twitter`: 
	* `Telegram`: 
* `Wallet Address` shows the user's wallet address. The user can not change it.

#### After Submitting

* If the user has changed the email, email verification mail will be sent to the user.
* The verification mail writes the secret URL including `token` to the registerd email address.
* If the user clicks the URL, the `token` and `email` will be resolved and the email address will be assigned to the user.

### My notification setting page (/account/notificationSettings): Excel No.33

* Only logged in user can access this page.
* The user can configure the notification settings. The default is all on.
	* `Item Sold`
	* `Bid Activity`
	* `Price Change`
	* `Auction Expiration`
	* `Outbid`
	* `Referral Successful`
	* `Owned Asset Updates`
	* `Successful Purchase`
	* `Newsletter`
	* `Now Sold`
	* `Minimum Bid Threshold`
* The token of Minimum Bid Threshold is fixed for `ETH`.

### My offer setting page (/account/offerSettings): Excel No.34

* Only logged in user can access this page.
* The user can set minimum offer price for collections.
* Showing collections of assets the user is owning.
* The default price is ETH 0.

### Account support page (/account/support): Excel No.36

* Only logged in user can access this page.
* Wrote some sentences and links with v1.1
* With v2.0 (when we relase first marketplace version), we will organize these infos.
