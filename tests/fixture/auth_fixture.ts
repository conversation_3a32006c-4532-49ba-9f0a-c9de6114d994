import User from 'domain/models/user'
import { ethers} from 'ethers'

import AuthUseCase from '../../presentation/use_cases/auth_use_case'
import { MessageToSignService } from 'domain/services/utils/message_to_sign_service'
import UserRepository from 'domain/repositories/user_repository'

type AuthFixtureObjects = {
    user: User
}

export class AuthFixture {

  private userRepository:UserRepository
  private authUseCase:AuthUseCase

  constructor () {
    this.userRepository = new UserRepository()
    this.authUseCase = new AuthUseCase()
  }

  async create (signer:ethers.Signer): Promise<AuthFixtureObjects> {

    const nonce:number = await this.userRepository.nonce(await signer.getAddress())
    const messageToSign = MessageToSignService.agreePrivacyPolicyAndTermsOfService(`${nonce}`)
    const signature = await signer.signMessage(messageToSign)

    await this.authUseCase.login(
      await signer.getAddress(),
      signature
    )

    const user = await this.userRepository.me()

    return {
      user: user,
    }
  }

  async finalize (): Promise<void> {
    await this.authUseCase.logout()
  }

}
