import { ethers } from 'ethers'

type Web3FixtureObjects = {
    provider: ethers.providers.InfuraProvider,
    signer: ethers.Signer
}

export class Web3Fixture {

  private network:string
  private index:number

  constructor (network:string = 'homestead', index:number = 0) {
    this.network = network
    this.index = index
  }

  create (): Web3FixtureObjects {
    return {
      provider: this._createProvider(),
      signer: this._createSigner(),
    }
  }

  private _createProvider () {
    return new ethers.providers.InfuraProvider(
      this.network,
      process.env.INFURA_API_KEY
    )
  }

  private _createSigner () {
    const path = `m/44'/60'/0'/0/${this.index}`
    return ethers.Wallet.fromMnemonic(
            process.env.OPERATION_MNEMONIC!,
            path
    )
  }

}