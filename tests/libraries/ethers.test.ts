/* eslint-disable no-undef */
import { ethers } from 'ethers'
import { generateTestingUtils } from 'eth-testing'

// Provider is connected to blockchain.
// The role is just relaying data between signers and chains.
// Provider itself does NOT have credential to make transactions.
describe('provider with node service', () => {

  test('basic usages', async () => {

    const provider = new ethers.providers.InfuraProvider(
      'homestead',
      process.env.INFURA_API_KEY
    )
            
    expect(await provider.lookupAddress('******************************************')).toBe('syamasaki.eth')

    // provider doesn't manage accounts because it doesn't know credential at all
    const accounts = await provider.listAccounts()
    expect(accounts.length).toBe(0)
  })
    
})

// Signer knows privateKey or mnemonic
// But signer itself is not connected to blockchain.
describe('signers from secretKey or mnemonic', () => {

  test('basic usages', async () => {

    // from secret key
    let signerWithKey = new ethers.Wallet(
            process.env.WALLET_SECRET_KEY!
    )
    expect(await signerWithKey.getAddress()).toBe('******************************************')
    expect(signerWithKey.provider).toBe(null) // the signer is not connected to provider
    expect(signerWithKey.mnemonic).toBe(null) // the signer from secret key does not have a mnemonic
        
    // from mnemonic (default path: "m/44'/60'/0'/0/0")
    let signerWithMnemonic = ethers.Wallet.fromMnemonic(
            process.env.OPERATION_MNEMONIC!
    )
    expect(await signerWithMnemonic.getAddress()).toBe('******************************************')
    expect(signerWithMnemonic.provider).toBe(null) // the signer is not connected to provider
    expect(signerWithMnemonic.mnemonic).not.toBe(null) // the signer from mnemonic has a mnemonic

    // private key is the same
    expect(signerWithKey.privateKey).toBe(signerWithMnemonic.privateKey)

    // failed with no provider
    try {
      await signerWithKey.getBalance()
    } catch(e:any) {
      expect(e.message).toBe('missing provider (operation=\\"getBalance\\", code=UNSUPPORTED_OPERATION, version=abstract-signer/5.7.0)')
    }
    try {
      await signerWithMnemonic.getBalance()
    } catch(e:any) {
      expect(e.message).toBe('missing provider (operation=\\"getBalance\\", code=UNSUPPORTED_OPERATION, version=abstract-signer/5.7.0)')
    }

    // To fetch data from onchain, signers need to connect to provider
    const provider = new ethers.providers.InfuraProvider(
      'homestead',
      process.env.INFURA_API_KEY
    )
    signerWithKey = signerWithKey.connect(provider)
    signerWithMnemonic = signerWithMnemonic.connect(provider)

    expect((await signerWithKey.getBalance()).toNumber()).toBe(0)
    expect((await signerWithMnemonic.getBalance()).toNumber()).toBe(0)
  })
})


// multiple accounts from the same mnemonic
describe('signer from mnemonic', () => {

  // If the signer is created from mnemonic phrase.
  // We can create multiple accounts by specifying different pathes
  test('multiple signers from the same mnemonic', async () => {

    const firstWallet = ethers.Wallet.fromMnemonic(
            process.env.OPERATION_MNEMONIC!,
            'm/44\'/60\'/0\'/0/0'
    )
    expect(await firstWallet.getAddress()).toBe('******************************************')

    const secondWallet = ethers.Wallet.fromMnemonic(
            process.env.OPERATION_MNEMONIC!,
            'm/44\'/60\'/0\'/0/1'
    )
    expect(await secondWallet.getAddress()).toBe('******************************************')

    const thirdWallet = ethers.Wallet.fromMnemonic(
            process.env.OPERATION_MNEMONIC!,
            'm/44\'/60\'/0\'/0/2'
    )
    expect(await thirdWallet.getAddress()).toBe('******************************************')

    const forthWallet = ethers.Wallet.fromMnemonic(
            process.env.OPERATION_MNEMONIC!,
            'm/44\'/60\'/0\'/0/3'
    )
    expect(await forthWallet.getAddress()).toBe('******************************************')

  })
})

// Web3Provider is connected to blockchain and have the credential too.
// This is why provider can fetch accounts.
describe('provider from browser(metamask)', () => {

  let testingUtils = generateTestingUtils({ providerType: 'MetaMask' })

  beforeAll(() => {
    global.window.ethereum = testingUtils.getProvider()
    testingUtils.mockNotConnectedWallet()
    testingUtils.mockConnectedWallet([
      '******************************************',
      '******************************************',
      '******************************************',
      '******************************************',
    ])
  })

  afterAll(() => {
    testingUtils.clearAllMocks()
  })

  test('ethers tests', async () => {

    const provider = new ethers.providers.Web3Provider(global.window.ethereum)
    const accounts = await provider.listAccounts()

    expect(accounts.length).toBe(4)
    expect(accounts[0]).toBe('******************************************')
    expect(accounts[1]).toBe('******************************************')
    expect(accounts[2]).toBe('******************************************')
    expect(accounts[3]).toBe('******************************************')
  })
    
})
