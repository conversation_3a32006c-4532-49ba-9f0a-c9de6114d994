/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'

import { Query } from 'domain/repositories/contract_repository/query'
import Contract from 'domain/models/contract'
import Chain from 'domain/value_objects/chain'
import BaseError from 'errors/base_error'
import { NotFoundError } from 'errors/custom_datasource_errors'
import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
import TokenManager from 'data/datasources/remote/token_manager'
import ContractRepository from 'domain/repositories/contract_repository'

const storageDatasource = container.get<StorageDatasource>(TYPES.StorageDatasource)

const tokenManager = new TokenManager()
const repository = new ContractRepository()

describe('Normal Cases', () => {

  beforeAll( async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('findById()', async () => {
    try {
      await repository.findById('Whippersnapper')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the contract, id:Whippersnapper')
      }

    }
  })

  test('findByChainAndAddress()', async () => {
    try {
      await repository.findByChainAndAddress(Chain.ethereum(), 'Whippersnapper')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the contract, chain:ethereum, address:Whippersnapper')
      }

    }
  })

  test('search()', async () => {
    const query = new Query()
    const Contract:Contract[] = await repository.search(query)
    expect(Contract.length).toBeGreaterThanOrEqual(0)
  })

  test('totalCount()', async () => {
    const query = new Query()
    const totalCount = await repository.totalCount(query)
    expect(totalCount).toBeGreaterThanOrEqual(0)
  })

})
