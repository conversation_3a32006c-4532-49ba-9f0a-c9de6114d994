/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'

import { Query } from 'domain/repositories/collection_watch_repository/query'
import CollectionWatch from 'domain/models/collection_watch'
import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
import TokenManager from 'data/datasources/remote/token_manager'
import CollectionWatchRepository from 'domain/repositories/collection_watch_repository'

const storageDatasource = container.get<StorageDatasource>(TYPES.StorageDatasource)

const tokenManager = new TokenManager()
const repository = new CollectionWatchRepository()

describe('Normal Cases', () => {

  beforeAll( async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('collectionIds()', async () => {
    const query = new Query()
    const collectionIds:string[] = await repository.collectionIds(query)
    expect(collectionIds.length).toBeGreaterThanOrEqual(0)
  })

  test('search()', async () => {
    const query = new Query()
    const CollectionWatch:CollectionWatch[] = await repository.search(query)
    expect(CollectionWatch.length).toBeGreaterThanOrEqual(0)
  })

  test('totalCount()', async () => {
    const query = new Query()
    const totalCount = await repository.totalCount(query)
    expect(totalCount).toBeGreaterThanOrEqual(0)
  })

})
