/* eslint-disable no-undef */
import { Query } from 'domain/repositories/collection_like_repository/query'
import CollectionLike from 'domain/models/collection_like'
import CollectionLikeRepository from 'domain/repositories/collection_like_repository'

const repository = new CollectionLikeRepository()

describe('Normal Cases', () => {

  test('collectionIds()', async () => {
    const query = new Query()
    const collectionIds:string[] = await repository.collectionIds(query)
    expect(collectionIds.length).toBeGreaterThanOrEqual(0)
  })

  test('search()', async () => {
    const query = new Query()
    const CollectionLike:CollectionLike[] = await repository.search(query)
    expect(CollectionLike.length).toBeGreaterThanOrEqual(0)
  })

  test('totalCount()', async () => {
    const query = new Query()
    const totalCount = await repository.totalCount(query)
    expect(totalCount).toBeGreaterThanOrEqual(0)
  })

})
