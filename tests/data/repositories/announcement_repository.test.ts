/* eslint-disable no-undef */
import { Query } from 'domain/repositories/announcement_repository/query'

import Announcement from 'domain/models/announcement'
import BaseError from 'errors/base_error'
import { NotFoundError } from 'errors/custom_datasource_errors'
import AnnouncementRepository from 'domain/repositories/announcement_repository'

const repository = new AnnouncementRepository()

describe('Normal Cases', () => {

  test('findById()', async () => {
    try {
      await repository.findById('hogehoge')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the announcement, id:hogehoge')
      }
    }
  })

  test('search()', async () => {
    const query = new Query()
    const announcements:Announcement[] = await repository.search(query)
    expect(announcements.length).toBeGreaterThanOrEqual(0)
  })

  test('totalCount()', async () => {
    const query = new Query()
    const totalCount = await repository.totalCount(query)
    expect(totalCount).toBeGreaterThanOrEqual(0)
  })

})

