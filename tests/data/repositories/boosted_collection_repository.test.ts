/* eslint-disable no-undef */
import { Query } from 'domain/repositories/boosted_collection_repository/query'
import BoostedCollection from 'domain/models/boosted_collection'
import BaseError from 'errors/base_error'
import { NotFoundError } from 'errors/custom_datasource_errors'
import BoostedCollectionRepository from 'domain/repositories/boosted_collection_repository'

const repository = new BoostedCollectionRepository()

describe('Normal Cases', () => {

  test('findById()', async () => {
    try {
      await repository.findById('Whippersnapper')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the boosted collection, id:Whippersnapper')
      }

    }
  })

  test('search()', async () => {
    const query = new Query()
    const boostedCollection:BoostedCollection[] = await repository.search(query)
    expect(boostedCollection.length).toBeGreaterThanOrEqual(0)
  })

  test('totalCount()', async () => {
    const query = new Query()
    const totalCount = await repository.totalCount(query)
    expect(totalCount).toBeGreaterThanOrEqual(0)
  })

})

