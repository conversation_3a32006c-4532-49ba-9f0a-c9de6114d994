/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'

import BaseError from 'errors/base_error'
import { BadRequestError, NotFoundError } from 'errors/custom_datasource_errors'
import CollectionReason from 'domain/models/report/collection_reason'
import Chain from 'domain/value_objects/chain'
import AssetReason from 'domain/models/report/asset_reason'
import AuthorizationInitializer from '../utils/authorization_initializer'
import Collection from 'domain/models/collection'
import User from 'domain/models/user'
import PaymentToken from 'domain/value_objects/payment_token'
import { DateTime } from 'luxon'
import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
import TokenManager from 'data/datasources/remote/token_manager'
import AssetRepository from 'domain/repositories/asset_repository'
import ReportRepository from 'domain/repositories/report_repository'
import CollectionRepository from 'domain/repositories/collection_repository'

const storageDatasource = container.get<StorageDatasource>(TYPES.StorageDatasource)

const tokenManager = new TokenManager()
const repository = new ReportRepository()
const collectionRepository = new CollectionRepository()
const assetRepository = new AssetRepository()

let authorizationInitializer:AuthorizationInitializer,
  account :string,
  collection : Collection,
  user :User
describe('Normal Cases', () => {
  authorizationInitializer = new AuthorizationInitializer()

  beforeAll(async ()=>{
    tokenManager.clearToken()
    storageDatasource.deleteAll()

    try{
      user = await authorizationInitializer.findUser(9)
    }catch(e){
      if(e instanceof NotFoundError){
        user = await authorizationInitializer.createUser(9)
      }
    }

    try{
      account = await authorizationInitializer.getAccount(9)
      let token = await authorizationInitializer.getApiToken(9)
      tokenManager.setToken(token)
    }catch(e){
      console.log(e)
    }
  })


  test('Create():Collection', async () => {
    try{
      collection = await collectionRepository.findBySlug('slug9')
    }catch(e){
      if(e instanceof BaseError){
        collection = new Collection(
          undefined,
          'slug9' ,
          'slug9' ,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          false,
          user,
          1,
          2,
          null,
          null,
          null,
          null,
          [PaymentToken.eth()] ,
          ['0xWhippersnappersaddress'],
          [user],
          DateTime.fromISO('12'),
          DateTime.fromISO('34')
        )
        collection = await collectionRepository.save(collection)
      }
    }

    try {

      const report = await repository.createCollectionReport(collection, CollectionReason.explicit())
      expect(report.targetEntityId).toBe(collection.id)
    } catch(e) {
      expect(e ).toBe(null)
    }
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test.skip('Create():For not existing Collection', async () => {

    try {
      let collectionD = new Collection(
        'dhvudsvgufwufg',
        'slug9' ,
        'slug9' ,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        false,
        user,
        1,
        2,
        null,
        null,
        null,
        null,
        [PaymentToken.eth()] ,
        ['0xWhippersnappersaddress'],
        [user],
        DateTime.fromISO('12'),
        DateTime.fromISO('34')
      )
      await repository.createCollectionReport(collectionD, CollectionReason.explicit())
      throw 'err'
    } catch(e) {
      expect(e instanceof BaseError ).toBe(true)
    }

    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })
  tokenManager.clearToken()
  storageDatasource.deleteAll()

  //Remove skip and modify accordingly when asset is created on the remote.
  test.skip('Create():Asset', async () => {
    try {
      const asset = await assetRepository.findByChainAndAddressAndTokenId(Chain.ethereum(), 'Address', 'TokenID')
      await repository.createAssetReport(asset, AssetReason.explicit())
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof BadRequestError).toBe(true)
    }
  })
})

describe('Error Cases', () => {

  test.skip('Create():Collection', async () => {
    try {
      let collectionD = new Collection(
        undefined,
        'slug9' ,
        'slug9' ,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        false,
        user,
        1,
        2,
        null,
        null,
        null,
        null,
        [PaymentToken.eth()],
        ['0xWhippersnappersaddress'],
        [user],
        DateTime.fromISO('12'),
        DateTime.fromISO('34'))

      await repository.createCollectionReport(collectionD, CollectionReason.explicit())
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      // expect(e instanceof NotFoundError).toBe(true)
    }
  })

  // test('Create():Asset', async () => {
  //   try {
  //     const asset = await assetRepository.findByChainAndAddressAndTokenId(Chain.ethereum(), 'Address doesn\'t exist', 'TokenID')
  //     await repository.createAssetReport(asset, AssetReason.explicit())
  //   } catch(e) {
  //     expect(e instanceof BaseError).toBe(true)
  //     expect(e instanceof NotFoundError).toBe(true)
  //   }
  // })
})

