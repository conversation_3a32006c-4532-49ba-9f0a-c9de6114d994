/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'
import { DateTime } from 'luxon'

import { Query } from 'domain/repositories/listing_repository/query'
import Listing from 'domain/models/listing'

import BaseError from 'errors/base_error'
import Asset from 'domain/models/asset'
import ListingType from 'domain/models/listing/listing_type'
import Marketplace from 'domain/value_objects/marketplace'
import PaymentToken from 'domain/value_objects/payment_token'
import { NotFoundError, UnauthorizedError } from 'errors/custom_datasource_errors'
import { MetadataStatus } from 'domain/models/asset/metadata_status'
import { AssetAttributeLevelJson } from 'data/jsons/asset_attribute_level_json'
import { AssetAttributePropertyJson } from 'data/jsons/asset_attribute_property_json'
import { AssetAttributeStatJson } from 'data/jsons/asset_attribute_stat_json'

import AuthorizationInitializer from '../utils/authorization_initializer'
import AuctionMethod from 'domain/models/listing/auction_method'
import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
import TokenManager from 'data/datasources/remote/token_manager'
import ListingRepository from 'domain/repositories/listing_repository'

const storageDatasource = container.get<StorageDatasource>(TYPES.StorageDatasource)

const tokenManager = new TokenManager()
const repository = new ListingRepository()

const authInitializer = new AuthorizationInitializer()

const metastatus = new MetadataStatus({['Whippersnapper']:'Whippersnapper'})

const assetAttributeLevelJson:AssetAttributeLevelJson = {
  name: 'Whippersnapper',
  value: 12,
  max: 34,
}
const assetAttributePropertyJson:AssetAttributePropertyJson = {
  type: 'Whippersnapper',
  name: 'Whippersnapper',
}
const assetAttributeStatJson:AssetAttributeStatJson = {
  name: 'Whippersnapper',
  value: 12,
  max: 34,
}

const asset = new Asset(
  'Whippersnapper',
  null,
  null,
  'ethereum',
  '0xWhippersnappersaddress',
  '123456',
  null,
  null,
  null,
  null,
  null,
  null,
  false,
  null,
  false,
  metastatus,
  null,
  null,
  null,
  null,
  null,
  'eth',
  null,
  null,
  null,
  null,
  null,
  null,
  null,
  null,
  null,
  null,
  null,
  0,
  [assetAttributeLevelJson],
  [assetAttributePropertyJson],
  [assetAttributeStatJson],
  [Marketplace.nftLand()],
  null,
  [],
  DateTime.fromISO('12'),
  DateTime.fromISO('12')
)

let listingType = ListingType.timedAuction()

describe('Normal Cases', () => {
  beforeAll( async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })
  //Todo: Have tests for created listings
  test('findById()', async () => {
    try {
      await repository.findById('Whippersnapper')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the listing, id:Whippersnapper')
      }

    }
  })

  test('search()', async () => {
    const query = new Query()
    const Listing:Listing[] = await repository.search(query)
    expect(Listing.length).toBeGreaterThanOrEqual(0)
  })

  test('totalCount()', async () => {
    const query = new Query()
    const totalCount = await repository.totalCount(query)
    expect(totalCount).toBeGreaterThanOrEqual(0)
  })
})

describe('Normal Cases - Authorized', () => {
  let newListing:Listing

  // Todo: Create and verify object from database
  //listing assets are not being created
  beforeAll(async ()=>{
    try{
      await authInitializer.findUser(2)
    }catch(e){
      if(e instanceof BaseError || e instanceof NotFoundError){
        await authInitializer.createUser(2)
      }
    }
  })

  afterAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test.skip('Save():create', async () => {
    const apiToken = await authInitializer.getApiToken(2)
    tokenManager.setToken(apiToken)

    let tempListing = new Listing(
      undefined,
      null,
      [ asset ],
      [ 1 ],
      listingType,
      AuctionMethod.sellToHighestBidder(),
      PaymentToken.eth(),
      0.7,
      0.7,
      null,
      DateTime.fromISO('12'),
      DateTime.fromISO('12'),
      false,
      null,
      null,
      false,
      null,
      false,
      null,
      DateTime.fromISO('12'),
      DateTime.fromISO('12'),
      '',
    )
    try {
      newListing = await repository.save(tempListing)
    } catch(e) {
      expect(e instanceof BaseError).toBe(false)
    }
  })

  // Todo: Update already created object
  test.skip('Save():update', async () => {
    const apiToken = await authInitializer.getApiToken(2)
    let newAsset = new Asset(
      'Whippersnapperasdasdsads',
      null,
      null,
      'ethereum',
      '0xWhippersnappersaddress',
      'Whippersnappertoken',
      null,
      null,
      null,
      null,
      null,
      null,
      false,
      null,
      false,
      metastatus,
      null,
      null,
      null,
      null,
      null,
      'eth',
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      [assetAttributeLevelJson],
      [assetAttributePropertyJson],
      [assetAttributeStatJson],
      [Marketplace.nftLand()],
      null,
      [],
      DateTime.fromISO('12'),
      DateTime.fromISO('12')
    )
    tokenManager.setToken(apiToken)
    newListing.assets = [newAsset]
    try {
      await repository.save(newListing)
    } catch(e) {

      expect(e instanceof BaseError).toBe(false)
    }

  })

})

describe('Error cases - Unauthorized', () => {
  beforeAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })
  test('Save():create', async () => {
    let tempListing = new Listing(
      undefined,
      null,
      [asset],
      [1],
      listingType,
      null,
      PaymentToken.eth(),
      0.7,
      null,
      null,
      DateTime.fromISO('12'),
      DateTime.fromISO('12'),
      false,
      null,
      null,
      false,
      null,
      false,
      null,
      DateTime.fromISO('12'),
      DateTime.fromISO('12'),
      '',
    )
    try {
      await repository.save(tempListing)
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }

    }
  })

  test('Save():create', async () => {
    let tempListing = new Listing(
      'Whippersnapper',
      null,
      [asset],
      [1],
      listingType,
      null,
      PaymentToken.eth(),
      0.9,
      null,
      null,
      DateTime.fromISO('12'),
      DateTime.fromISO('12'),
      false,
      null,
      null,
      false,
      null,
      false,
      null,
      DateTime.fromISO('12'),
      DateTime.fromISO('12'),
      ''
    )
    try {
      await repository.save(tempListing)
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })
})
