/* eslint-disable no-undef */
import { Query } from 'domain/repositories/main_banner_repository/query'
import MainBanner from 'domain/models/main_banner'
import BaseError from 'errors/base_error'
import { NotFoundError } from 'errors/custom_datasource_errors'
import MainBannerRepository from 'domain/repositories/main_banner_repository'

const repository = new MainBannerRepository()

describe('Normal Cases', () => {

  test('findById()', async () => {
    try {
      await repository.findById('Whippersnapper')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the main banner, id:Whippersnapper')
      }
    }
  })

  test('search()', async () => {
    const query = new Query()
    const MainBanner:MainBanner[] = await repository.search(query)
    expect(MainBanner.length).toBeGreaterThanOrEqual(0)
  })

  test('totalCount()', async () => {
    const query = new Query()
    const totalCount = await repository.totalCount(query)
    expect(totalCount).toBeGreaterThanOrEqual(0)
  })
})
