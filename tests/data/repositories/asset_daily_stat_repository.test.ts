/* eslint-disable no-undef */
import { Query } from 'domain/repositories/asset_daily_stat_repository/query'
import AssetDailyStat from 'domain/models/asset_daily_stat'
import BaseError from 'errors/base_error'
import { NotFoundError } from 'errors/custom_datasource_errors'
import AssetDailyStatRepository from 'domain/repositories/asset_daily_stat_repository'

const repository = new AssetDailyStatRepository()

describe('Normal Cases', () => {

  test('findById()', async () => {
    try {
      await repository.findById('Whippersnapper')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the asset daily stat, id:Whippersnapper')
      }
    }
  })

  test('search()', async () => {
    const query = new Query()
    const assetDailyStat:AssetDailyStat[] = await repository.search(query)
    expect(assetDailyStat.length).toBeGreaterThanOrEqual(0)
  })

  test('totalCount()', async () => {
    const query = new Query()
    const totalCount = await repository.totalCount(query)
    expect(totalCount).toBeGreaterThanOrEqual(0)
  })
})
