/* eslint-disable no-undef */
import { Query } from 'domain/repositories/category_repository/query'
import Category from 'domain/models/category'
import BaseError from 'errors/base_error'
import { NotFoundError } from 'errors/custom_datasource_errors'
import CategoryRepository from 'domain/repositories/category_repository'

const repository = new CategoryRepository()

describe('Normal Cases', () => {

  test('findById()', async () => {
    try {
      await repository.findById('Whippersnapper')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the category, id:Whippersnapper')
      }

    }
  })

  test('findBySlug()', async () => {
    try {
      await repository.findBySlug('Whippersnapper')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the category, slug:Whippersnapper')
      }

    }
  })

  test('search()', async () => {
    const query = new Query()
    const categories:Category[] = await repository.search(query)
    expect(categories.length).toBeGreaterThanOrEqual(0)
  })

  test('totalCount()', async () => {
    const query = new Query()
    const totalCount = await repository.totalCount(query)
    expect(totalCount).toBeGreaterThanOrEqual(0)
  })

})

