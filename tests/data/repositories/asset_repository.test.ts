/* eslint-disable no-undef */
import { Query } from 'domain/repositories/asset_repository/query'
import Asset from 'domain/models/asset'
import Chain from 'domain/value_objects/chain'
import BaseError from 'errors/base_error'
import { NotFoundError } from 'errors/custom_datasource_errors'
import AssetRepository from 'domain/repositories/asset_repository'

const repository = new AssetRepository()

describe('Normal Cases', () => {

  test('findById()', async () => {
    try {
      await repository.findById('Whippersnapper')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the asset, id:Whippersnapper')
      }

    }
  })

  test('findByChainAndAddressAndTokenId()', async () => {
    try {
      await repository.findByChainAndAddressAndTokenId(Chain.ethereum(), 'WhippersnapperAddress', 'Whippersnapper')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the asset, chain:ethereum, address:WhippersnapperAddress, tokenId:Whippersnapper')
      }

    }
  })

  test('search()', async () => {
    const query = new Query()
    const asset:Asset[] = await repository.search(query)
    expect(asset.length).toBeGreaterThanOrEqual(0)
  })

  test('totalCount()', async () => {
    const query = new Query()
    const totalCount = await repository.totalCount(query)
    expect(totalCount).toBeGreaterThanOrEqual(0)
  })

})
