/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'
import BaseError from 'errors/base_error'
import { NotFoundError,
  UnauthorizedError,
  BadRequestError} from 'errors/custom_datasource_errors'
import AuthorizationInitializer from '../utils/authorization_initializer'
import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
import TokenManager from 'data/datasources/remote/token_manager'
import UserPublicAddressRepository from 'domain/repositories/user_public_address_repository'

const storageDatasource = container.get<StorageDatasource>(TYPES.StorageDatasource)

const tokenManager = new TokenManager()
const repository = new UserPublicAddressRepository()

describe('Normal Cases', () => {

  beforeAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  },20000)

  test('search(): unAuth', async () => {
    try {
      await repository.search()
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test('create(): unAuth', async () => {
    try {
      await repository.create('123','12')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test('delete(): unAuth', async () => {
    try {
      await repository.delete('123')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })
})

describe('Authorized', () => {
  let account :string,
    authorizationInitializer: AuthorizationInitializer
  authorizationInitializer = new AuthorizationInitializer()

  beforeAll(async () => {
    try{
      await authorizationInitializer.findUser(9)
    }catch(e){
      if(e instanceof NotFoundError){
        await authorizationInitializer.createUser(9)
      }
    }

    try{
      account = await authorizationInitializer.getAccount(9)
      let token = await authorizationInitializer.getApiToken(9)
      tokenManager.setToken(token)
    }catch(e){
      console.log(e)
    }

  },20000)

  afterAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('create() : by existing address', async () => {
    try {
      let signature = await authorizationInitializer.getSignature(9)
      const result = await repository.create(account,signature)

    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof BadRequestError).toBe(true)
      // if (e instanceof BadRequestError) {
      //     const errors = e.getErrors()
      //     expect(errors[0]).toBe(`The address(${account}) has been registered already`)
      // }
    }
  })

  test('create(): by new address ', async () => {

    try {
      let  account7 = await authorizationInitializer.getAccount(7),
        nonce = await authorizationInitializer.getNonce(9),
        signature = await authorizationInitializer.getSignature(7,nonce )
      await repository.create(account7,signature)

    } catch(e) {
      expect(e ).toBe(null)

    }
  })

  test('search()', async () => {
    try {
      let addresses = await repository.search()

      expect(addresses.length).toBeGreaterThanOrEqual(1)

    } catch(e) {
      expect(e ).toBe(null)
    }
  })

  test('delete()', async () => {
    try{
      let  account7 = await authorizationInitializer.getAccount(7),
        result = await repository.delete(account7)

      expect(result).toBe(true)

    }catch(e){
      expect(e ).toBe(null)
    }

    //case

    try{
      let  account8 = await authorizationInitializer.getAccount(8),

        result = await repository.delete(account8)

    }catch(e){

      expect(e instanceof BaseError).toBe(true)

    }

  })

})
