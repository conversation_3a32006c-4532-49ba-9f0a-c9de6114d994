/* eslint-disable no-undef */
import { Query } from 'domain/repositories/asset_like_repository/query'
import AssetLike from 'domain/models/asset_like'
import AssetLikeRepository from 'domain/repositories/asset_like_repository'

const repository = new AssetLikeRepository()

describe('Normal Cases', () => {

  test('search()', async () => {
    const query = new Query()
    const assetLike:AssetLike[] = await repository.search(query)
    expect(assetLike.length).toBeGreaterThanOrEqual(0)
  })

  test('totalCount()', async () => {
    const query = new Query()
    const totalCount = await repository.totalCount(query)
    expect(totalCount).toBeGreaterThanOrEqual(0)
  })

  test('assetIds()', async () => {
    const query = new Query()
    const assetIds = await repository.assetIds(query)
    expect(assetIds.length).toBeGreaterThanOrEqual(0)
  })

})
