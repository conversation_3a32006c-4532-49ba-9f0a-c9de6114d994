/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'
import BaseError from 'errors/base_error'
import { NotFoundError,
  UnauthorizedError,
  BadRequestError} from 'errors/custom_datasource_errors'
import TargetEntityType from 'domain/value_objects/target_entity_type'
import AuthorizationInitializer from '../utils/authorization_initializer'
import Collection from 'domain/models/collection'
import { DateTime } from 'luxon'
import PaymentToken from 'domain/value_objects/payment_token'
import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
import TokenManager from 'data/datasources/remote/token_manager'
import CollectionRepository from 'domain/repositories/collection_repository'
import WatchRepository from 'domain/repositories/watch_repository'
import UserRepository from 'domain/repositories/user_repository'

const storageDatasource = container.get<StorageDatasource>(TYPES.StorageDatasource)

const tokenManager = new TokenManager()
const repository = new WatchRepository()
const userRepository = new UserRepository()
const collectionRepository = new CollectionRepository()

describe('unAuth', () => {

  beforeAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  },20000)


  test('create()', async () => {
    let targetEntityType = new TargetEntityType({id:3, name: 'hoge', label:'Hoge'})
    try {

      await repository.create(targetEntityType,'3')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test('delete()', async () => {
    let targetEntityType = new TargetEntityType({id:3, name: 'hoge', label:'Hoge'})

    try {
      await repository.delete(targetEntityType, '3')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })


})


describe('Authorized', () => {
  let account :string,
    collectionData : Collection,
    authorizationInitializer:AuthorizationInitializer,
    userId:string
  //let testSlug:string

  authorizationInitializer = new AuthorizationInitializer()

  beforeAll(async () => {
    try{
      await authorizationInitializer.findUser(9)
    }catch(e){
      if(e instanceof NotFoundError){
        await authorizationInitializer.createUser(9)
      }
    }

    try{
      // const query = new Query()
      // count = await collectionRepository.totalCount(query)
      // testCollectionName = `collection:${count}`
      // testSlug = `collection:${count-1}`
      account = await authorizationInitializer.getAccount(9)
      let token = await authorizationInitializer.getApiToken(9)
      tokenManager.setToken(token)
    }catch(e){
      console.log(e)
    }


  },20000)

  afterAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('create()', async () => {


    let targetEntityType = new TargetEntityType({id: 1 , name:'collection', label: 'Collection'})
    try{
      let user = await userRepository.me()
      userId = user.id!
      collectionData = await collectionRepository.findBySlug('slug9')
    }catch(e){
      if(e instanceof BaseError){
        let user = await userRepository.me()
        userId = user.id!
        const collection = new Collection(
          undefined,
          'slug9' ,
          'slug9' ,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          false,
          user,
          1,
          2,
          null,
          null,
          null,
          null,
          [PaymentToken.eth()] ,
          ['0xWhippersnappersaddress'],
          [user],
          DateTime.fromISO('12'),
          DateTime.fromISO('34')
        )
        collectionData = await collectionRepository.save(collection)
      }
    }

    try {

      // if(count && count ===0){
      //     const collection = new Collection(undefined,testCollectionName ,testCollectionName ,null, null, null, null, null, null, null, null, null,null, null, false, user, 1, 2, null, null, [user], [PaymentToken.eth()] , ['0xWhippersnappersaddress'],  DateTime.fromISO('12'), DateTime.fromISO('34'));
      //     collectionData = await collectionRepository.save(collection)
      // }else{
      //     collectionData = await collectionRepository.findBySlug(testSlug)
      // }

      let watch = await repository.create(targetEntityType, String(collectionData.id))
      expect(watch.id).not.toBe(null)
      expect(watch.userId).toBe(userId)
      expect(watch.uniqueKey).toBe(`collection:${collectionData.id}`)

    } catch(e) {

      expect(e ).toBe(null)
    }
  })

  test('create():For not existing collection', async () => {


    let targetEntityType = new TargetEntityType({id: 1 , name:'collection', label: 'Collection'})

    try {
      await repository.create(targetEntityType, 'sbfjsbvsjbvjehfv')
      throw 'err'
    } catch(e) {

      expect(e instanceof BaseError).toBe(true)
    }

  })

  test('delete() : by wrong id', async () => {
    let targetEntityType = new TargetEntityType({id: 5 , name:'collection', label: '5'})
    try {
      await repository.delete(targetEntityType, '68679896' )
    }
    catch(e) {

      expect(e instanceof BaseError).toBe(true)
      if (e instanceof BadRequestError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe(`Not found the watch, targetEntityType:collection, targetEntityTypeId:68679896, userId:${userId}`)
      }
    }
  })

  test('delete()', async () => {

    let targetEntityType = new TargetEntityType({id: 1 , name:'collection', label: '5'})
    try {
      const bool = await repository.delete(targetEntityType, String(collectionData.id))

      expect(bool).toBe(true)

    } catch(e) {
      expect(e ).toBe(null)
    }

  })

})

