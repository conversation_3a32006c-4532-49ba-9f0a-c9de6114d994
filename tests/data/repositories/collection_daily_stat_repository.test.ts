/* eslint-disable no-undef */
import { Query } from 'domain/repositories/collection_daily_stat_repository/query'
import CollectionDailyStat from 'domain/models/collection_daily_stat'
import BaseError from 'errors/base_error'
import { NotFoundError } from 'errors/custom_datasource_errors'
import CollectionDailyStatRepository from 'domain/repositories/collection_daily_stat_repository'

const repository = new CollectionDailyStatRepository()

describe('Normal Cases', () => {

  test('findById()', async () => {
    try {
      await repository.findById('Whippersnapper')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the collection daily stat, id:Whippersnapper')
      }

    }
  })

  test('search()', async () => {
    const query = new Query()
    const CollectionDailyStat:CollectionDailyStat[] = await repository.search(query)
    expect(CollectionDailyStat.length).toBeGreaterThanOrEqual(0)
  })

  test('totalCount()', async () => {
    const query = new Query()
    const totalCount = await repository.totalCount(query)
    expect(totalCount).toBeGreaterThanOrEqual(0)
  })

})
