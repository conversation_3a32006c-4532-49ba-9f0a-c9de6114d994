/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'
import { DateTime } from 'luxon'

import { Query } from 'domain/repositories/offer_repository/query'
import Offer from 'domain/models/offer'

import BaseError from 'errors/base_error'
import { NotFoundError, UnauthorizedError } from 'errors/custom_datasource_errors'
import PaymentToken from 'domain/value_objects/payment_token'
import Marketplace from 'domain/value_objects/marketplace'
import Asset from 'domain/models/asset'
import { MetadataStatus } from 'domain/models/asset/metadata_status'
import { AssetAttributeLevelJson } from 'data/jsons/asset_attribute_level_json'
import { AssetAttributePropertyJson } from 'data/jsons/asset_attribute_property_json'
import { AssetAttributeStatJson } from 'data/jsons/asset_attribute_stat_json'

import AuthorizationInitializer from '../utils/authorization_initializer'
import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
import TokenManager from 'data/datasources/remote/token_manager'
import OfferRepository from 'domain/repositories/offer_repository'

const storageDatasource = container.get<StorageDatasource>(TYPES.StorageDatasource)

const tokenManager = new TokenManager()
const repository = new OfferRepository()
const authInitializer = new AuthorizationInitializer()

describe('Normal Cases', () => {

  let count:number,
    testOfferName:string,
    newOffer:Offer

  beforeAll( async () => {
    const query = new Query()
    count = await repository.totalCount(query)
    testOfferName = `offer:${count}`

    tokenManager.clearToken()
    storageDatasource.deleteAll()
    try{
      await authInitializer.findUser(2)
    }catch(e){
      if(e instanceof BaseError && e instanceof NotFoundError){
        await authInitializer.createUser(2)
      }
    }
  })

  afterAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('findById()', async () => {
    try {
      await repository.findById('Whippersnapper')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the offer, id:Whippersnapper')
      }

    }
  })

  test('search()', async () => {
    const query = new Query()
    const Offer:Offer[] = await repository.search(query)
    expect(Offer.length).toBeGreaterThanOrEqual(0)
  })

  test('totalCount()', async () => {
    const query = new Query()
    const totalCount = await repository.totalCount(query)
    expect(totalCount).toBeGreaterThanOrEqual(0)
  })

  test.skip('save():For not existing asset', async () => {
    const apiToken = await authInitializer.getApiToken(2)
    tokenManager.setToken(apiToken)

    const metastatus = new MetadataStatus({[testOfferName]:testOfferName})
    const assetAttributeLevelJson:AssetAttributeLevelJson = {
      name: testOfferName,
      value: 12,
      max: 34,
    }
    const assetAttributePropertyJson:AssetAttributePropertyJson = {
      type: testOfferName,
      name: testOfferName,
    }
    const assetAttributeStatJson:AssetAttributeStatJson = {
      name: testOfferName,
      value: 12,
      max: 34,
    }

    const asset = new Asset(
      testOfferName,
      null,
      null,
      'ethereum',
      'hogehoge',
      '123456',
      null,
      testOfferName,
      null,
      testOfferName,
      null,
      null,
      false,
      null,
      false,
      metastatus,
      null,
      null,
      null,
      null,
      null,
      'eth',
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      [assetAttributeLevelJson],
      [assetAttributePropertyJson],
      [assetAttributeStatJson],
      [Marketplace.nftLand()],
      null,
      [],
      DateTime.fromISO('12'),
      DateTime.fromISO('12')
    )
    const offer = new Offer(undefined, asset, null, PaymentToken.eth(), 1, DateTime.fromISO('12'), DateTime.fromISO('34'), DateTime.fromISO('56'))

    try {
      await repository.save(offer)
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
    }
  })

  test.skip('save():Create', async () => {
    const apiToken = await authInitializer.getApiToken(2)
    tokenManager.setToken(apiToken)

    const metastatus = new MetadataStatus({[testOfferName]:testOfferName})
    const assetAttributeLevelJson:AssetAttributeLevelJson = {
      name: testOfferName,
      value: 12,
      max: 34,
    }
    const assetAttributePropertyJson:AssetAttributePropertyJson = {
      type: testOfferName,
      name: testOfferName,
    }
    const assetAttributeStatJson:AssetAttributeStatJson = {
      name: testOfferName,
      value: 12,
      max: 34,
    }

    const asset = new Asset(
      testOfferName,
      null,
      null,
      'ethereum',
      'hogehoge',
      '234567',
      null,
      testOfferName,
      null,
      testOfferName,
      null,
      null,
      false,
      null,
      false,
      metastatus,
      null,
      null,
      null,
      null,
      null,
      'eth',
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      [assetAttributeLevelJson],
      [assetAttributePropertyJson],
      [assetAttributeStatJson],
      [Marketplace.nftLand()],
      null,
      [],
      DateTime.fromISO('12'),
      DateTime.fromISO('12')
    )
    const offer = new Offer(undefined, asset, null, PaymentToken.eth(), 1, DateTime.fromISO('12'), DateTime.fromISO('34'), DateTime.fromISO('56'))

    try {
      newOffer = await repository.save(offer)

      expect(newOffer.id).toBeDefined()
    } catch(e:any) {
      expect(e instanceof BaseError).toBe(false)
    }
  })


  test.skip('save():Update', async () => {
    newOffer.price = 2
    try {
      await repository.save(newOffer)
    } catch(e:any) {
      expect(e instanceof BaseError).toBe(false)
      expect(e instanceof UnauthorizedError).toBe(false)
      let updatedOffer = await repository.findById(newOffer.id as string)
      expect(updatedOffer.price).toBe(2)
    }

  })

})

describe('Error Cases - Unauthorized', () => {
  beforeAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })
  test('save():Create', async () => {
    const metastatus = new MetadataStatus({['Whippersnapper']:'Whippersnapper'})
    const assetAttributeLevelJson:AssetAttributeLevelJson = {
      name: 'Whippersnapper',
      value: 12,
      max: 34,
    }
    const assetAttributePropertyJson:AssetAttributePropertyJson = {
      type: 'Whippersnapper',
      name: 'Whippersnapper',
    }
    const assetAttributeStatJson:AssetAttributeStatJson = {
      name: 'Whippersnapper',
      value: 12,
      max: 34,
    }

    const asset = new Asset(
      'Whippersnapper',
      null,
      null,
      'ethereum',
      '0xWhippersnappersaddress',
      'Whippersnappertoken',
      null,
      null,
      null,
      null,
      null,
      null,
      false,
      null,
      false,
      metastatus,
      null,
      null,
      null,
      null,
      null,
      'eth',
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      [assetAttributeLevelJson],
      [assetAttributePropertyJson],
      [assetAttributeStatJson],
      [Marketplace.nftLand()],
      null,
      [],
      DateTime.fromISO('12'),
      DateTime.fromISO('12'),
    )

    const offer = new Offer(undefined, asset, null, PaymentToken.eth(), 1, DateTime.fromISO('12'), DateTime.fromISO('34'), DateTime.fromISO('56'))

    try {
      await repository.save(offer)
    } catch(e:any) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test('save():Update', async () => {
    const offer = new Offer('Whippersnapper', null, null, PaymentToken.eth(), 1, DateTime.fromISO('12'), DateTime.fromISO('34'), DateTime.fromISO('56'))
    try {
      await repository.save(offer)
    } catch(e:any) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

})
