/* eslint-disable no-undef */
import * as fs from 'fs'
import BaseError from 'errors/base_error'
import { UnauthorizedError } from 'errors/custom_datasource_errors'
import { InfuraProvider } from '@ethersproject/providers'
import { Signer } from 'ethers'
import { Web3Fixture } from 'tests/fixture/web3_fixture'
import User from 'domain/models/user'
import { AuthFixture } from 'tests/fixture/auth_fixture'
import MultimediaRepository from 'domain/repositories/multimedia_repository'

const repository = new MultimediaRepository()

describe('Unauthorized', () => {

  test('upload() failed', async () => {
    try {
      const buffer = fs.readFileSync('tests/images/sample_image.png')
      const file:File = new File([buffer], 'sample_image.png', { type: 'image/png' })
      await repository.upload(file)
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

})

describe('Authorized', () => {

  let provider:InfuraProvider,
    signer:Signer,
    authUser:User

  beforeAll(async () => {

    const web3Fixture = new Web3Fixture().create()
    provider = web3Fixture.provider
    signer = web3Fixture.signer.connect(provider)

    const authFixture = await (new AuthFixture()).create(signer)
    authUser = authFixture.user
  })

  afterAll(async () => {
    await (new AuthFixture()).finalize()
  })

  test('upload() successfully', async () => {
    try {
      const buffer = fs.readFileSync('tests/images/sample_image.png')
      const file:File = new File([buffer], 'sample_image.png', { type: 'image/png' })
      const response = await repository.upload(file)

      expect(response.id).not.toBe(undefined)
      expect(response.url).not.toBe('')
      expect(response.storageName).toBe('s3')
      expect(response.mimeType).toBe('image/png')
      expect(response.dataSize).not.toBe(undefined)
      expect(response.height).not.toBe(undefined)
      expect(response.width).not.toBe(undefined)
      expect(response.hash).not.toBe(undefined)
    } catch(e) {
      expect(1).toBe(0) // should not be here
    }
  })

})

