/* eslint-disable no-undef */
import { Query } from 'domain/repositories/activity_repository/query'
import Activity from 'domain/models/activity'
import BaseError from 'errors/base_error'
import { NotFoundError } from 'errors/custom_datasource_errors'
import ActivityRepository from 'domain/repositories/activity_repository'

const repository = new ActivityRepository()

describe('Normal Cases', () => {

  test('findById()', async () => {
    try {
      await repository.findById('hogehoge')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the activity, id:hogehoge')
      }
    }
  })

  test('search()', async () => {
    const query = new Query()
    const activities:Activity[] = await repository.search(query)
    expect(activities.length).toBeGreaterThanOrEqual(0)
  })

  test('totalCount()', async () => {
    const query = new Query()
    const totalCount = await repository.totalCount(query)
    expect(totalCount).toBeGreaterThanOrEqual(0)
  })

})

