/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'
import BaseError from 'errors/base_error'
import { NotFoundError,
  UnauthorizedError} from 'errors/custom_datasource_errors'
import AuthorizationInitializer from '../utils/authorization_initializer'
import Collection from 'domain/models/collection'
import PaymentToken from 'domain/value_objects/payment_token'

import { DateTime } from 'luxon'
import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
import TokenManager from 'data/datasources/remote/token_manager'
import UserOfferSettingRepository from 'domain/repositories/user_offer_setting_repository'
import UserRepository from 'domain/repositories/user_repository'
import CollectionRepository from 'domain/repositories/collection_repository'

const storageDatasource = container.get<StorageDatasource>(TYPES.StorageDatasource)

const tokenManager = new TokenManager()
const repository = new UserOfferSettingRepository()
const userRepository = new UserRepository()
const collectionRepository = new CollectionRepository()

describe('unAuth', () => {

  beforeAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  },20000)

  test('search()', async () => {
    try {
      await repository.search()
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test('update()', async () => {
    try {
      await repository.update('1',12)
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })


})


describe('Authorized', () => {
  let account :string,
    collectionData : Collection,
    authorizationInitializer:  AuthorizationInitializer

  authorizationInitializer = new AuthorizationInitializer()
  beforeAll(async () => {
    try{
      await authorizationInitializer.findUser(9)
    }catch(e){
      if(e instanceof NotFoundError){
        await authorizationInitializer.createUser(9)
      }
    }

    try{
      account = await authorizationInitializer.getAccount(9)
      let token = await authorizationInitializer.getApiToken(9)
      tokenManager.setToken(token)
    }catch(e){
      console.log(e)
    }


  },20000)

  afterAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  // let data = {
  //     slug: 'slug1',
  //     name: 'new collection name',
  //     description: '',
  //     logoImageId: null,
  //     featuredImageId: null,
  //     bannerImageId: null,
  //     categoryId: null,
  //     webUrl: null,
  //     discordUrl: null,
  //     telegramUrl: null,
  //     mediumUsername: null,
  //     twitterUsername: null,
  //     instagramUsername: null,
  //     isExplicit: false
  // }

  test('update()', async () => {
    try{
      collectionData = await collectionRepository.findBySlug('slug9')
    }catch(e){
      if(e instanceof BaseError){
        let user = await userRepository.me()
        const collection = new Collection(
          undefined,
          'slug9' ,
          'slug9' ,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          false, user,
          1,
          2,
          null,
          null,
          null,
          null,
          [PaymentToken.eth()] ,
          ['0xWhippersnappersaddress'],
          [user],
          DateTime.fromISO('12'),
          DateTime.fromISO('34')
        )
        collectionData = await collectionRepository.save(collection)
      }

    }

    try {
      const result = await repository.update(collectionData.id as string, 5)

      expect(result.collectionId).toBe(collectionData.id)
      expect(result.minimumPrice).toBe(5)


    } catch(e) {
      expect(e ).toBe(null)
    }

  })

  test.skip('update():For not existing collection', async () => {


    try {
      await repository.update('collectionData.idsdvbjhsdvasstring', 5)
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
    }

  })

  test('search()', async () => {
    try {
      let userOffers = await repository.search()
      expect(userOffers.length).toBeGreaterThanOrEqual(1)
    } catch(e) {
      expect(e ).toBe(null)
    }

  })



})
