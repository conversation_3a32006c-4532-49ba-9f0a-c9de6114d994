/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'
import BaseError from 'errors/base_error'
import { NotFoundError,
  UnauthorizedError,
  BadRequestError,
  GeneralError} from 'errors/custom_datasource_errors'

import AuthorizationInitializer from '../utils/authorization_initializer'

import { Language } from 'domain/value_objects/language'
import User from 'domain/models/user'
import  {DateTime}  from 'luxon'
import LoginStatus from 'domain/models/login_status'
import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
import TokenManager from 'data/datasources/remote/token_manager'
import UserRepository from 'domain/repositories/user_repository'

const storageDatasource = container.get<StorageDatasource>(TYPES.StorageDatasource)

const tokenManager = new TokenManager()
const repository = new UserRepository()

describe('unAuth', () => {

  beforeAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
    let authorizationInitializer = new AuthorizationInitializer()
    try{
      await authorizationInitializer.findUser(9)
    }catch(e){
      if(e instanceof NotFoundError){
        await authorizationInitializer.createUser(9)
      }
    }
  },20000)

  test('me()', async () => {
    try {
      await repository.me()
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test('findByPublicAddress()', async () => {
    try {
      await repository.findByPublicAddress('123')

    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the user, publicAddress:123')
      }
    }
  })

  test('findById()', async () => {
    try {
      await repository.findById('123')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the user, id:123')
      }
    }
  })

  test('loginStatus()', async () => {
    try {
      let result = await repository.loginStatus()
      expect(result.isLoggedIn).toBe(false)
    } catch(e) {
      expect(e).toBe(null)
    }
  })

  test('nonce()', async () => {
    try {
      await repository.nonce('123')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the user, publicAddress:123')
      }
    }
  })

  //already exists
  test('create(): already exists', async () => {
    try {
      let t = await repository.create('0xe9bA259458c229E805f9908D51B54F9e72ee02e4')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof BadRequestError).toBe(true)
      if (e instanceof BadRequestError) {
        const errors = e.getErrors()

        expect(errors[0]).toBe('The public address has registered already, publicAddress:0xe9bA259458c229E805f9908D51B54F9e72ee02e4')
      }
    }
  })



  test('login(): ', async () => {
    try {
      await repository.login('account','signature')

    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the user, publicAddress:account')
      }
    }
  })

  test('logout(): ', async () => {
    try {
      await repository.logout()
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
    }
  })


  test('isUsernameAvailable(): ', async () => {
    try {
      let result = await repository.isUsernameAvailable('0000000')

      expect(result).toBe(true)
    } catch(e) {
      expect(e).toBe(null)
    }
  })

  test('isEmailAvailable(): ', async () => {
    try {
      let result =  await repository.isEmailAvailable('0000000')

      expect(result).toBe(true)
    } catch(e) {
      expect(e ).toBe(null)
    }
  })

  test('update() ', async () => {
    let  l =  new Language({id : 1, name:'en', label:'English'}),

      user = new User (
        '123',
        '123',
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        Language.japanese(),
        DateTime.now(),
        [],
        DateTime.now(),
        DateTime.now()
      )

    try {
      await repository.update(user)
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test('updateEmail() ', async () => {
    try {
      await repository.updateEmail('123')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test('updateLanguage() ', async () => {
    try {

      await repository.updateLanguage('123')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })
})


describe('Authorized', () => {
  let wrongAccount : string,
    authorizationInitializer:AuthorizationInitializer,
    account:string,
    signature:string,
    token:string,
    userId:string

  authorizationInitializer = new AuthorizationInitializer()

  beforeAll(async () => {
    try{
      await authorizationInitializer.findUser(9)
    }catch(e){
      if(e instanceof NotFoundError){
        await authorizationInitializer.createUser(9)
      }
    }

    try{
      account = await authorizationInitializer.getAccount(9)
      wrongAccount = await authorizationInitializer.getAccount(0)
      token = await authorizationInitializer.getApiToken(9)
      tokenManager.setToken(token)
      signature = await authorizationInitializer.getSignature(9)
    }catch(e){
      console.log(e)
    }


  },20000)

  afterAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('update()', async () => {
    try {
      let user = new User(
        '1234-1234-1234-1234',
        'hogehoge',
        null,
        null,
        null,
        null,
        '@hogehoge',
        'hogehoge',
        null,
        Language.japanese(),
        DateTime.now(),
        [],
        DateTime.now(),
        DateTime.now()
      )

      await repository.update(user)

    } catch(e) {

      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }

    try {
      let user = await repository.me()
      userId = user.id!

      let language = Language.english(),
        userT = new User(
          userId,
          'hogehoge',
          null,
          null,
          null,
          null,
          '@hogehoge',
          'hogehoge',
          null,
          language,
          null,
          [],
          DateTime.now(),
          DateTime.now()
        )

      user = await repository.update(userT)

      expect(user.username).toBe('hogehoge')
      expect(user.email).toBe('check')
      expect(user.thumbnail).toBe(null)
      expect(user.background).toBe(null)
      expect(user.description).toBe(null)
      expect(user.twitterUsername).toBe('@hogehoge')
      expect(user.instagramUsername).toBe('hogehoge')
      expect(user.webUrl).toBe(null)
      expect(user.lastAccessedAt).toBe(null)
      expect(user.createdAt).not.toBe(null)
      expect(user.updatedAt).not.toBe(null)

    } catch(e) {
      expect(e ).toBe(null)

    }

    try{
      let userT = new User(
        userId,
        account,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        Language.english(),
        null,
        [],
        DateTime.now(),
        DateTime.now()
      )

      await repository.update(userT)
    } catch(e) {
      expect(e ).toBe(null)

    }
  })

  test('updateEmail()', async () => {
    try {
      let user = await repository.updateEmail('check')

      expect(user.username).toBe(account)
      expect(user.email).toBe('check')
      expect(user.thumbnail).toBe(null)
      expect(user.background).toBe(null)
      expect(user.description).toBe(null)
      expect(user.twitterUsername).toBe(null)
      expect(user.instagramUsername).toBe(null)
      expect(user.webUrl).toBe(null)
      expect(user.lastAccessedAt).toBe(null)
      expect(user.createdAt).not.toBe(null)
      expect(user.updatedAt).not.toBe(null)

    } catch(e) {
      expect(e ).toBe(null)

    }
  })


  test('me()', async () => {
    try {
      let user = await repository.me()


      expect(user.username).toBe(account)
      expect(user.email).toBe('check')
      expect(user.thumbnail).toBe(null)
      expect(user.background).toBe(null)
      expect(user.description).toBe(null)
      expect(user.twitterUsername).toBe(null)
      expect(user.instagramUsername).toBe(null)
      expect(user.webUrl).toBe(null)
      expect(user.lastAccessedAt).toBe(null)
      expect(user.createdAt).not.toBe(null)
      expect(user.updatedAt).not.toBe(null)



    } catch(e) {
      expect(e ).toBe(null)
    }
  })

  test('findByPublicAddress()', async () => {
    // must work
    try {

      let user = await repository.findByPublicAddress(account)

      expect(user.username).toBe(account)
      expect(user.email).toBe('check')
      expect(user.thumbnail).toBe(null)
      expect(user.background).toBe(null)
      expect(user.description).toBe(null)
      expect(user.twitterUsername).toBe(null)
      expect(user.instagramUsername).toBe(null)
      expect(user.webUrl).toBe(null)
      expect(user.lastAccessedAt).toBe(null)
      expect(user.createdAt).not.toBe(null)
      expect(user.updatedAt).not.toBe(null)


    } catch(e) {

      expect(e ).toBe(null)
    }


  })

  test('findById()', async () => {

    try {
      let user = await repository.findById(userId)

      expect(user.username).toBe(account)
      expect(user.email).toBe('check')
      expect(user.thumbnail).toBe(null)
      expect(user.background).toBe(null)
      expect(user.description).toBe(null)
      expect(user.twitterUsername).toBe(null)
      expect(user.instagramUsername).toBe(null)
      expect(user.webUrl).toBe(null)
      expect(user.lastAccessedAt).toBe(null)
      expect(user.createdAt).not.toBe(null)
      expect(user.updatedAt).not.toBe(null)

    } catch(e) {

      expect(e ).toBe(null)
    }


  })



  test('nonce()', async () => {
    try {

      let nonce = await repository.nonce(account)

      expect(nonce).not.toBe(null)

    } catch(e) {
      expect(e ).toBe(null)

    }
  })



  test('isUsernameAvailable() ', async () => {
    try {
      const result = await repository.isUsernameAvailable(account)
      expect(result).toBe(true)

    } catch(e) {
      expect(e ).toBe(null)

    }

    // case

    try {
      const result = await repository.isUsernameAvailable('wrongAccount')
      expect(result).toBe(true)

    } catch(e) {
      expect(e ).toBe(null)

    }

  })


  test('isEmailAvailable(): ', async () => {
    try {
      const result = await repository.isEmailAvailable('check')
      expect(result).toBe(true)
    } catch(e) {
      expect(e ).toBe(null)

    }

    try {
      const result = await repository.isEmailAvailable('0000000')
      expect(result).toBe(true)
    } catch(e) {
      expect(e ).toBe(null)

    }

  })

  test('updateLanguage() ', async () => {
    try {
      let user = await repository.updateLanguage('ja')
      expect(user.language.getName()).toBe('ja')
      user = await repository.updateLanguage('en')
      expect(user.language.getName()).toBe('en')
    } catch(e) {
      expect(e ).toBe(null)
    }
  })

  test('login(): with already login ', async () => {
    try {

      // account = await authorizationInitializer.getAccount(9)

      //let signature = await authorizationInitializer.getSignature(9)

      let data = await repository.login(account,signature )

      //    tokenData.setToken(data.token);

      //     expect(data.type).toBe("bearer")
      //     expect(data.token).not.toBe(null)
      //     expect(data.tokenHash).not.toBe(null)
      //     expect(data.expiresAt).not.toBe(null)

    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof GeneralError).toBe(true)
      if (e instanceof GeneralError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Authorized user is not supposed to access')
      }
    }
  })

  test('login()', async () => {
    try {

      tokenManager.clearToken()
      storageDatasource.deleteAll()

      signature = await authorizationInitializer.getSignature(9)

      let data = await repository.login(account,signature )
      tokenManager.setToken(data.token)

      expect(data.type).toBe('bearer')
      expect(data.token).not.toBe(null)
      expect(data.tokenHash).not.toBe(null)
      expect(data.expiresAt).not.toBe(null)

    } catch(e) {
      expect(e ).toBe(null)
      // expect(e instanceof NotFoundError).toBe(true)
      // if (e instanceof NotFoundError) {
      //     const errors = e.getErrors()
      //     expect(errors[0]).toBe(`Not found the user, publicAddress:${account}`)
      // }
    }
  })

  test('loginStatus()', async () => {
    try {

      const status:LoginStatus = await repository.loginStatus()
      expect(status.isLoggedIn).toBe(true)
      expect(status.user).not.toBe(null)
      expect(status.user!.username).toBe(account)

    } catch(e) {
      expect(e ).toBe(null)

    }

  })

  test('logout()', async () => {
    try {

      const result = await repository.logout()
      expect(result).toBe(true)
    } catch(e) {
      expect(e ).toBe(null)
    }
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('create()', async () => {
    try {
      let user = await repository.create(account)
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof BadRequestError).toBe(true)
      if (e instanceof BadRequestError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe(`The public address has registered already, publicAddress:${account}`)
      }
    }


  })

})

