/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'
import TargetEntityType from 'domain/value_objects/target_entity_type'

import BaseError from 'errors/base_error'
import { NotFoundError, UnauthorizedError } from 'errors/custom_datasource_errors'
import AuthorizationInitializer from '../utils/authorization_initializer'
import Collection from 'domain/models/collection'
import PaymentToken from 'domain/value_objects/payment_token'
import { DateTime } from 'luxon'
import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
import TokenManager from 'data/datasources/remote/token_manager'
import LikeRepository from 'domain/repositories/like_repository'
import CollectionRepository from 'domain/repositories/collection_repository'
import UserRepository from 'domain/repositories/user_repository'

const storageDatasource = container.get<StorageDatasource>(TYPES.StorageDatasource)

const tokenManager = new TokenManager()
const repository = new LikeRepository()
const collectionRepository = new CollectionRepository()
const userRepository = new UserRepository()

let authorizationInitializer:AuthorizationInitializer

authorizationInitializer = new AuthorizationInitializer()

describe('Normal Cases', () => {

  beforeAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('create like asset when unauthorized', async () => {
    let likeAsset = TargetEntityType.asset()
    try {
      await repository.create(likeAsset, 'Whippersnapper')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test('create like asset when unauthorized', async () => {
    let likeCollection = TargetEntityType.collection()
    try {
      await repository.create(likeCollection, 'Whippersnapper')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test('delete like asset when unauthorized', async () => {
    let likeAsset = TargetEntityType.asset()
    try {
      await repository.delete(likeAsset, 'Whippersnapper')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test('delete like collection when unauthorized', async () => {
    let likeCollection = TargetEntityType.collection()
    try {
      await repository.delete(likeCollection, 'Whippersnapper')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })
})

describe('Auth Cases', () => {
  // let testCollectionName:string;
  // let count: number;
  let collectionData : Collection
  //let testSlug:string

  beforeAll(async () => {
    try{
      await authorizationInitializer.findUser(2)
    }catch(e){
      if(e instanceof NotFoundError){
        await authorizationInitializer.createUser(2)
      }
    }

    try{
      // const query = new Query()
      // count = await collectionRepository.totalCount(query)
      // testCollectionName = `collection:${count}`
      // testSlug = `collection:${count-1}`
      let token = await authorizationInitializer.getApiToken(2)
      tokenManager.setToken(token)

    }catch(e){
      console.log(e)
    }

  })

  afterAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  //test('create like asset when authorized', async () => {
  // let likeAsset = TargetEntityType.asset();
  // try {
  //     let Like = await repository.create(likeAsset, 'Whippersnapper')
  //     expect(Like.targetEntityId).toBe('Whippersnapper')
  //     expect(Like.uniqueKey).toBe('asset:Whippersnapper')
  // } catch(e) {
  //     expect(e).toBeNull()
  // }
  //})

  test('create like collection when authorized', async () => {
    let targetEntityType = new TargetEntityType({id: 1 , name:'collection', label: '5'})


    try{
      collectionData = await collectionRepository.findBySlug('slug9')
    }catch(e){
      if(e instanceof BaseError){
        let user = await userRepository.me()
        const collection = new Collection(
          undefined,
          'slug9' ,
          'slug9' ,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          false,
          user,
          1,
          2,
          null,
          null,
          null,
          null,
          [PaymentToken.eth()] ,
          ['0xWhippersnappersaddress'],
          [user],
          DateTime.fromISO('12'),
          DateTime.fromISO('34')
        )
        collectionData = await collectionRepository.save(collection)
      }

    }

    try {

      // if(count && count>0){
      //     collectionData = await collectionRepository.findBySlug(testCollectionName)

      // }else{
      //     let user = await userRepository.me()
      //     const collection = new Collection(undefined,testCollectionName ,testCollectionName ,null, null, null, null, null, null, null, null, null,null, null, false, user, 1, 2, null, null, [user], [PaymentToken.eth()] , ['0xWhippersnappersaddress'],  DateTime.fromISO('12'), DateTime.fromISO('34'));
      //     collectionData = await collectionRepository.save(collection)
      // }
      await repository.create(targetEntityType, String(collectionData.id))
      // let Like = await repository.create(likeCollection, 'Whippersnapper')
      // expect(Like.targetEntityId).toBe('Whippersnapper')
      // expect(Like.uniqueKey).toBe('collection:Whippersnapper')
    } catch(e) {
      expect(e).toBeNull()
    }
  })


  test('create(): for not existing', async () => {
    let targetEntityType = new TargetEntityType({id: 1 , name:'collection', label: '5'})

    try {

      await repository.create(targetEntityType, 'dvbjksdvgbskjngiwehfuiw')
      throw 'err'
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
    }
  })

  // test('delete like asset when authorized', async () => {
  //     let likeAsset = TargetEntityType.asset();
  //     try {
  //         await repository.delete(likeAsset, 'Whippersnapper')
  //     } catch(e) {
  //         expect(e instanceof BaseError).toBe(true)

  //     }
  // })

  test('delete like collection when authorized', async () => {
    try{
      collectionData = await collectionRepository.findBySlug('slug9')
    }catch(e){
      if(e instanceof BaseError){
        let user = await userRepository.me()
        const collection = new Collection(
          undefined,
          'slug9' ,
          'slug9' ,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          false,
          user,
          1,
          2,
          null,
          null,
          null,
          null,
          [PaymentToken.eth()] ,
          ['0xWhippersnappersaddress'],
          [user],
          DateTime.fromISO('12'),
          DateTime.fromISO('34')
        )
        collectionData = await collectionRepository.save(collection)
      }

    }

    try {
      let targetEntityType = new TargetEntityType({id: 1 , name:'collection', label: '5'})

      await repository.delete(targetEntityType, String(collectionData.id))

    } catch(e) {
      expect(e).toBeNull()
    }

  })

})
