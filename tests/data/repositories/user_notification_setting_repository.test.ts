/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'
import BaseError from 'errors/base_error'
import { NotFoundError,
  UnauthorizedError} from 'errors/custom_datasource_errors'
import UserNotificationSetting from 'domain/models/user_notification_setting'
import  {DateTime}  from 'luxon'
import AuthorizationInitializer from '../utils/authorization_initializer'
import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
import TokenManager from 'data/datasources/remote/token_manager'
import UserNotificationSettingRepository from 'domain/repositories/user_notification_setting_repository'
import UserRepository from 'domain/repositories/user_repository'

const storageDatasource = container.get<StorageDatasource>(TYPES.StorageDatasource)

const tokenManager = new TokenManager()
const repository = new UserNotificationSettingRepository()
const userRepository = new UserRepository()

describe('unAuth', () => {

  beforeAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  },20000)

  test('mine()', async () => {
    try {
      await repository.mine()
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }

    }
  })

  test('update()', async () => {
    let userNotificationSetting = new UserNotificationSetting(undefined, '1', true,true,true, true,true,true, true,true,true,1 ,DateTime.now(),DateTime.now())

    try {
      await repository.update(userNotificationSetting)
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }

    }
  })
})


describe('Authorized', () => {
  let account :string,
    userId:string,
    authorizationInitializer:AuthorizationInitializer
  authorizationInitializer = new AuthorizationInitializer()
  beforeAll(async () => {
    try{
      await authorizationInitializer.findUser(9)
    }catch(e){
      if(e instanceof NotFoundError){
        await authorizationInitializer.createUser(9)
      }
    }
    try{
      account = await authorizationInitializer.getAccount(9)
      let token = await authorizationInitializer.getApiToken(9)
      tokenManager.setToken(token)
    }catch(e){
      console.log(e)
    }
  },20000)

  afterAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('update() ', async () => {
    try {
      let user = await userRepository.me()
      userId = user.id!
      let userNotificationSetting = new UserNotificationSetting(undefined, user.id!, false, true, true, true, true, true, true, true, true,1 , DateTime.now(), DateTime.now())
      const userNotification = await repository.update(userNotificationSetting)

      expect(userNotification.id).not.toBe(null)
      expect(userNotification.userId).toBe(user.id)
      expect(userNotification.onItemSold).toBe(false)

    } catch(e) {
      expect(e ).toBe(null)
    }
  })

  test('mine()', async () => {

    try {
      let userNotification = await repository.mine()
      expect(userNotification.id).not.toBe(null)
      expect(userNotification.userId).toBe(userId)
    } catch(e) {
      expect(e).toBe(null)
    }

  })

})


