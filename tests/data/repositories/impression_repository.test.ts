/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'

import BaseError from 'errors/base_error'
import { BadRequestError, NotFoundError } from 'errors/custom_datasource_errors'
import TargetEntityType from 'domain/value_objects/target_entity_type'
import Collection from 'domain/models/collection'
import PaymentToken from 'domain/value_objects/payment_token'
import { DateTime } from 'luxon'
import AuthorizationInitializer from '../utils/authorization_initializer'
import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
import TokenManager from 'data/datasources/remote/token_manager'
import CollectionRepository from 'domain/repositories/collection_repository'
import UserRepository from 'domain/repositories/user_repository'
import ImpressionRepository from 'domain/repositories/impression_repository'

const storageDatasource = container.get<StorageDatasource>(TYPES.StorageDatasource)

const tokenManager = new TokenManager()
const repository = new ImpressionRepository()
const collectionRepository = new CollectionRepository()
const userRepository = new UserRepository()

const authorizationInitializer = new AuthorizationInitializer()

describe('Normal Cases', () => {
  let collectionData : Collection

  beforeAll(async () => {
    // try{
    //     const query = new Query()
    //     count = await collectionRepository.totalCount(query)
    //     testCollectionName = `collection:${count-1}`

    // }catch(e){
    //     console.log(e)
    // }
    tokenManager.clearToken()
    storageDatasource.deleteAll()

    try{
      await authorizationInitializer.findUser(2)
    }catch(e){
      if(e instanceof NotFoundError){
        await authorizationInitializer.createUser(2)
      }
    }

    try{
      let token = await authorizationInitializer.getApiToken(2)
      tokenManager.setToken(token)

    }catch(e){
      console.log(e)
    }

  },20000)

  afterAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('Create():Collection', async () => {

    try{
      collectionData = await collectionRepository.findBySlug('slug9')
    }catch(e){
      let user = await userRepository.me()
      const collection = new Collection(
        undefined,
        'slug9' ,
        'slug9' ,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        false,
        user,
        1,
        2,
        null,
        null,
        null,
        null,
        [PaymentToken.eth()] ,
        ['0xWhippersnappersaddress'],
        [user],
        DateTime.now().minus({ week: 1 }),
        DateTime.now().minus({ week: 1 })
      )
      collectionData = await collectionRepository.save(collection)
    }

    try {
      await repository.create(TargetEntityType.collection(), String(collectionData.id))

    } catch(e) {
      expect(e instanceof BaseError).toBe(false)

    }
  })


  test('Create():For not existing Collection', async () => {

    try {
      await repository.create(TargetEntityType.collection(), 'cvhjsvcduewvjdfhbwugf')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)

    }
  })
})


describe('Error Cases', () => {
  let targetEntityType = new  TargetEntityType(
    {
      id: 1234,
      name: 'wrongName',
      label: 'wrongLabel',
    }
  )

  test('Create():Wrong target entity type', async () => {
    try {
      await repository.create(targetEntityType,'123456789')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof BadRequestError).toBe(true)
      if (e instanceof BadRequestError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('targetEntityType is invalid, targetEntityType:wrongName')
      }

    }
  })
})

