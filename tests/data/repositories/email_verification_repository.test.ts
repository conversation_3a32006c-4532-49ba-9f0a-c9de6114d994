/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'

import BaseError from 'errors/base_error'
import { BadRequestError, NotFoundError, UnauthorizedError } from 'errors/custom_datasource_errors'
import AuthorizationInitializer from '../utils/authorization_initializer'
import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
import TokenManager from 'data/datasources/remote/token_manager'
import EmailVerificationRepository from 'domain/repositories/email_verification_repository'

const storageDatasource = container.get<StorageDatasource>(TYPES.StorageDatasource)

const tokenManager = new TokenManager()
const repository = new EmailVerificationRepository()

describe('UnAuthorized Cases', () => {

  beforeAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('issue()', async () => {
    try {
      await repository.issue('<EMAIL>')
    } catch (e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }

    }
  })

  test('verify()', async () => {
    try {
      await repository.verify('Whippersnapper')
    } catch (e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }

    }
  })

  test('hasActiveRequest()', async () => {
    try {
      await repository.hasActiveRequest()
    } catch (e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test('resendEmail()', async () => {
    try {
      await repository.resendEmail()
    } catch (e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }

    }
  })
})

describe('Authorized Cases', () => {
  const authInitializer = new AuthorizationInitializer()
  let token: string

  beforeAll(async () => {
    try {
      await authInitializer.findUser(0)
    } catch (e) {
      if (e instanceof BaseError && e instanceof NotFoundError) {
        await authInitializer.createUser(0)
      }
    }
    try {
      const apiToken = await authInitializer.getApiToken(0)
      tokenManager.setToken(apiToken)
    } catch (e) {
      console.log(e)
    }

  })

  afterAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('issue()', async () => {
    try {
      const emailVerification = await repository.issue('<EMAIL>')
      token = emailVerification.token
      expect(emailVerification.email).toBe('<EMAIL>')
    } catch (e) {
      expect(e instanceof BaseError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      } else {
        const emailVerificationNew = await repository.issue('<EMAIL>')
        expect(emailVerificationNew.email).toBe('<EMAIL>')
        token = emailVerificationNew.token
      }
    }
  })

  test('verify()', async () => {
    try {
      const emailVerification = await repository.verify(token)
      expect(emailVerification).toBe(true)
    } catch (e) {
      expect(e instanceof BaseError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      } else {
        const errors = (e as BadRequestError).getErrors()
        expect(errors[0]).toBe('token must be specified')
      }
    }
  })

  test('hasActiveRequest()', async () => {
    try {
      const emailVerification = await repository.hasActiveRequest()
      expect(emailVerification).toBe(false)
    } catch (e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test('resendEmail()', async () => {
    try {
      const emailVerification = await repository.resendEmail()
      expect(emailVerification).toBe(false)
    } catch (e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })
})
