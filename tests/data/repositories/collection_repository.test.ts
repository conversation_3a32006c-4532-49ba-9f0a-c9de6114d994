/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'
import { DateTime } from 'luxon'

import { Query } from 'domain/repositories/collection_repository/query'
import Collection from 'domain/models/collection'
import { Language } from 'domain/value_objects/language'
import User from 'domain/models/user'
import PaymentToken from 'domain/value_objects/payment_token'
import BaseError from 'errors/base_error'
import { NotFoundError, UnauthorizedError } from 'errors/custom_datasource_errors'

import AuthorizationInitializer from '../utils/authorization_initializer'
import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
import TokenManager from 'data/datasources/remote/token_manager'
import CollectionRepository from 'domain/repositories/collection_repository'
import UserRepository from 'domain/repositories/user_repository'

const storageDatasource = container.get<StorageDatasource>(TYPES.StorageDatasource)

const tokenManager = new TokenManager()
const repository = new CollectionRepository()
const userRepository = new UserRepository()

const testUser = new User(
  undefined,
  'Whippersnapper',
  null,
  null,
  null,
  null,
  null,
  null,
  null,
  Language.english(),
  null,
  [],
  DateTime.now().minus({ week: 1 }),
  DateTime.now().minus({ week: 1 }),
)

const authInitializer = new AuthorizationInitializer()

describe('Normal Cases', () => {

  let testCollectionName:string,
    count: number


  beforeAll( async () => {

    tokenManager.clearToken()
    storageDatasource.deleteAll()
    try{
      const query = new Query()
      count = await repository.totalCount(query)
      testCollectionName = `collection:${count}`

    }catch(e){
      console.log(e)
    }

    try{
      await authInitializer.findUser(2)
    }catch(e){
      if(e instanceof NotFoundError){
        await authInitializer.createUser(2)
      }
    }


  })

  afterAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('findById()', async () => {
    try {
      await repository.findById('Whippersnapper')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the collection, id:Whippersnapper')
      }

    }
  })

  test('findBySlug()', async () => {
    try {
      await repository.findBySlug('Whippersnapper')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the collection, slug:Whippersnapper')
      }

    }
  })

  test('search()', async () => {
    const query = new Query()
    const Collections:Collection[] = await repository.search(query)
    expect(Collections.length).toBeGreaterThanOrEqual(0)
  })

  test('totalCount()', async () => {
    const query = new Query()
    const totalCount = await repository.totalCount(query)
    expect(totalCount).toBeGreaterThanOrEqual(0)
  })

  test('save():create', async () => {

    try {
      const apiToken = await authInitializer.getApiToken(2)
      tokenManager.setToken(apiToken)
      let user = await userRepository.me()

      const collection = new Collection(
        undefined,
        testCollectionName ,
        testCollectionName ,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        false,
        user,
        1,
        2,
        null,
        null,
        null,
        null,
        [ PaymentToken.eth() ],
        [ '0xWhippersnappersaddress' ],
        [user],
        DateTime.now().minus({ week: 1 }),
        DateTime.now().minus({ week: 1 })
      )

      let collectionData  = await repository.save(collection)

      expect(collectionData.slug).toBe(collection.slug)
      expect(collectionData.name).toBe(collection.name)

    } catch(e) {
      expect(e).toBe(null)

    }

  })

  test('save():update', async () => {
    const collection = await repository.findBySlug(testCollectionName)
    collection.name = 'Whippersnapper?'
    try {
      let collectionData  = await repository.save(collection)

      expect(collectionData.slug).toBe(collection.slug)
      expect(collectionData.name).toBe('Whippersnapper?')
    } catch(e) {
      expect(e ).toBe(null)

    }


  })

})

describe('Error Cases - Unauthorized', () => {
  beforeAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('save():Create', async () => {
    const collection = new Collection(
      undefined,
      'Whippersnapper',
      'Whippersnapper',
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      false,
      testUser,
      1,
      2,
      null,
      null,
      null,
      null,
      [PaymentToken.eth()] ,
      ['0xWhippersnappersaddress'],
      [testUser],
      DateTime.now().minus({ week: 1 }),
      DateTime.now().minus({ week: 1 }))

    try {
      await repository.save(collection)
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }

    }
  })

  test('save():Update', async () => {
    const collection = new Collection(
      'Whippersnapper',
      'Whippersnapper',
      'Whippersnapper',
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      false,
      testUser,
      1,
      2,
      null,
      null,
      null,
      null,
      [PaymentToken.eth()] ,
      ['0xWhippersnappersaddress'],
      [testUser],
      DateTime.now().minus({ week: 1 }),
      DateTime.now().minus({ week: 1 }),
    )
    try {
      await repository.save(collection)
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }

    }
  })
})
