/* eslint-disable no-undef */
import { Query } from 'data/datasources/interfaces/collection_like_datasource/query'
import { CollectionLikeJson } from 'data/jsons/collection_like_json'
import CollectionLikeRemoteDatasource from 'data/datasources/remote/collection_like_remote_datasource'

const datasource = new CollectionLikeRemoteDatasource()

describe('Normal Cases', () => {

  test('search()', async () => {
    const query = new Query()
    const data:CollectionLikeJson[] = await datasource.search(query)
    expect(data.length).toBeGreaterThanOrEqual(0)
  })

  test('totalCount()', async () => {
    const query = new Query()
    const totalCount = await datasource.totalCount(query)
    expect(totalCount).toBeGreaterThanOrEqual(0)
  })

  test('collectionIds()', async () => {
    const query = new Query()
    const collectionIds = await datasource.collectionIds(query)
    expect(collectionIds.length).toBeGreaterThanOrEqual(0)
  })

})

