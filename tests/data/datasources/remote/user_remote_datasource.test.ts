/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'

import AuthorizationInitializer from '../../utils/authorization_initializer'
import { UserJson } from 'data/jsons/user_json'
import { LoginStatusJson } from 'data/jsons/login_status_json'
import BaseError from 'errors/base_error'
import { GeneralError, NotFoundError, UnauthorizedError } from 'errors/custom_datasource_errors'
import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
import TokenManager from 'data/datasources/remote/token_manager'
import UserRemoteDatasource from 'data/datasources/remote/user_remote_datasource'

const storageDatasource = container.get<StorageDatasource>(TYPES.StorageDatasource)

const tokenManager = new TokenManager()
const datasource = new UserRemoteDatasource()

describe('Unauthorized', () => {

  beforeAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()

  },20000)

  test('me()', async () => {
    try {
      await datasource.me()
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test('findByPublicAddress()', async () => {
    try {
      await datasource.findByPublicAddress('123456789')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the user, publicAddress:123456789')
      }
    }
  })

  test('findById()', async () => {
    try {
      await datasource.findById('hogehoge')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the user, id:hogehoge')
      }
    }
  })

  test('loginStatus()', async () => {
    const status:LoginStatusJson = await datasource.loginStatus()
    expect(status.isLoggedIn).toBe(false)
    expect(status.user).toBe(null)
  })

  test('nonce()', async () => {
    try {
      await datasource.nonce('123456789')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the user, publicAddress:123456789')
      }
    }
  })

  test('login() with wrong publicAddress', async () => {
    try {
      await datasource.login('123456789','signature')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the user, publicAddress:123456789')
      }
    }
  })

  test('logout()', async () => {
    try{
      await datasource.logout()
    } catch(e){
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }

  })
  test('logout()', async () => {
    try {
      const result = await datasource.logout()
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
    }
  })

  test('isUsernameAvailable()', async () => {
    const result = await datasource.isUsernameAvailable('hogehogeufgweiu')
    expect(result).toBe(true)
  })

  test('isEmailAvailable()', async () => {
    const result = await datasource.isEmailAvailable('<EMAIL>')
    expect(result).toBe(true)
  })
  test('isEmailAvailable()', async () => {
    const result = await datasource.isEmailAvailable('<EMAIL>')
    expect(result).toBe(true)
  })

  test('update()', async () => {
    try {
      await datasource.update({
        id: '1234-1234-1234-1234',
        username: 'hogehoge',
        description: null,
        thumbnailId: null,
        backgroundId: null,
        twitterUsername: '@hogehoge',
        instagramUsername: 'hogehoge',
        webUrl: null,
      })
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test('updateEmail()', async () => {
    try {
      await datasource.updateEmail('<EMAIL>')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test('updateLanguage()', async () => {
    try {
      await datasource.updateLanguage('english')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })



})


describe('Normal Cases - Authorized', () => {
  let authorizationInitializer:AuthorizationInitializer,
    account:string,
    signature:string,
    token:string,
    userID:string

  authorizationInitializer = new AuthorizationInitializer()

  beforeAll(async () => {
    try{
      await authorizationInitializer.findUser(2)
    }catch(e){
      if(e instanceof NotFoundError){
        await authorizationInitializer.createUser(2)
      }
    }

    try{
      account = await authorizationInitializer.getAccount(2)
      token = await authorizationInitializer.getApiToken(2)
      signature = await authorizationInitializer.getSignature(2)
      tokenManager.setToken(token)
    }catch(e){
      console.log(e)
    }

  },20000)

  afterAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('update()', async () => {
    try {
      let user = await datasource.me()
      userID = user.id!
      user = await datasource.update({
        id: userID,
        username: account,
        description: 'User description',
        thumbnailId: null,
        backgroundId: null,
        twitterUsername: '@User1',
        instagramUsername: 'User1',
        webUrl: 'www.google.com',
      })
      user = await datasource.updateLanguage('en')
      expect(user.username).toBe(account)
      expect(user.email).toBe('AvailableEmail1')
      expect(user.thumbnail).toBe(null)
      expect(user.background).toBe(null)
      expect(user.description).toBe('User description')
      expect(user.twitterUsername).toBe('@User1')
      expect(user.instagramUsername).toBe('User1')
      expect(user.webUrl).toBe('www.google.com')
      expect(user.language.name).toBe('en')
      expect(user.lastAccessedAt).toBe(null)
      expect(user.createdAt).not.toBe(null)
      expect(user.updatedAt).not.toBe(null)
    } catch(e) {
      expect(e).toBeNull()
    }
  })

  test('updateEmail()', async () => {
    let user:UserJson
    try {
      user = await datasource.updateEmail('AvailableEmail1')
      expect(user.email).not.toBe('AnyThingElse')
      expect(user.email).toBe('AvailableEmail1')
    } catch(e) {
      expect(e).toBe(null)
    }
  })

  test('me()', async () => {
    let user:UserJson
    try {
      user = await datasource.me()
      userID = user.id!
      expect(user.username).toBe(account)
      expect(user.email).toBe('AvailableEmail1')

    } catch(e) {
      expect(e).toBe(null)
    }

  })

  test('findByPublicAddress()', async () => {
    try {
      let user = await datasource.findByPublicAddress(account)
      expect(user.username).toBe(account)
      expect(user.email).toBe('AvailableEmail1')
    } catch(e) {
      expect(e).toBe(null)
    }
  })

  test('findById()', async () => {
    try {
      let user = await datasource.findById(userID)
      expect(user.username).toBe(account)
      expect(user.email).toBe('AvailableEmail1')
    } catch(e) {
      expect(e).toBe(null)
    }
  })

  test('nonce()', async () => {
    try {
      let nonce = await datasource.nonce(account)
      expect(nonce).not.toBeNull()
    } catch(e) {
      expect(e).toBe(null)
    }
  })

  test('create()', async () => {
    try {
      await datasource.create(account)
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof GeneralError).toBe(true)
      if (e instanceof GeneralError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Authorized user can not access')
      }
    }
  })

  test('isEmailAvailable() ', async () => {
    let result1 = await datasource.isEmailAvailable('AvailableEmail1')
    expect(result1).toBe(true)

    let result2 = await datasource.isEmailAvailable('AvailableEmail')
    expect(result2).toBe(true)

  })

  test('updateLanguage()', async () => {
    try {
      let user = await datasource.updateLanguage('ja')
      expect(user.language).toBe('ja')
      user = await datasource.updateLanguage('en')
      expect(user.language).toBe('en')
    } catch(e) {
      expect(e instanceof BaseError).toBe(false)
    }
  })

  test('isUsernameAvailable() ', async () => {
    const result1 = await datasource.isUsernameAvailable(account)
    expect(result1).toBe(true)

    const result2 = await datasource.isUsernameAvailable('AvailableUsername')
    expect(result2).toBe(true)

  })


  test('login()', async () => {

    try {
      account = await authorizationInitializer.getAccount(2)
      token = await authorizationInitializer.getApiToken(2)
      signature = await authorizationInitializer.getSignature(2)
      let data = await datasource.login(account,signature)
      tokenManager.setToken(data.token)

      expect(data.type).toBe('bearer')
      expect(data.token).not.toBe(null)
      expect(data.tokenHash).not.toBe(null)
      expect(data.expiresAt).not.toBe(null)
    } catch (e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof GeneralError).toBe(true)
      if (e instanceof GeneralError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Authorized user is not supposed to access')
      }
    }
  })

  test('loginStatus()', async () => {
    try {
      let status = await datasource.loginStatus()
      expect(status.isLoggedIn).toBe(true)
      expect(status.user).not.toBe(null)
      expect(status.user?.email).toBe('AvailableEmail1')
    } catch (e) {
      expect(e instanceof BaseError).toBe(false)
    }
  })


  test('logout()', async () => {
    try {
      let result = await datasource.logout()

      expect(result).toBe(true)
    } catch(e) {
      expect(e instanceof BaseError).toBe(false)
    }

  })

})
