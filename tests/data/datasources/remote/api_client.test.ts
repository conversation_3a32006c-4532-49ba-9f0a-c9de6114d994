/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'

import type ApiClient from 'data/datasources/remote/api_client'
import BaseError from 'errors/base_error'
import { BadRequestError, NotFoundError } from 'errors/custom_datasource_errors'

const apiClient = container.get<ApiClient>(TYPES.ApiClient)

describe('Normal Cases', () => {
  test('access to active url', async () => {
    const data = await apiClient.get('/')
    expect(data.message).toBe('Welcome to NFT LAND')
  })
})

describe('Error Cases', () => {
  test('access to not registered url', async () => {
    try {
      await apiClient.get('/not-exist-endpoint')
    } catch (e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe(
          'E_ROUTE_NOT_FOUND: Cannot GET:/not-exist-endpoint'
        )
      }
    }
  })

  test('post to not registered url', async () => {
    try {
      await apiClient.post('/')
    } catch (e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('E_ROUTE_NOT_FOUND: Cannot POST:/')
      }
    }
  })

  test('post wrong parameters', async () => {
    try {
      await apiClient.post('/users/create')
    } catch (e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof BadRequestError).toBe(true)
      if (e instanceof BadRequestError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('publicAddress must be specified')
      }
    }
  })
})
