/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'

import BaseError from 'errors/base_error'
import { UnauthorizedError,NotFoundError,BadRequestError } from 'errors/custom_datasource_errors'
import AuthorizationInitializer from '../../utils/authorization_initializer'
import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
import TokenManager from 'data/datasources/remote/token_manager'
import UserPublicAddressRemoteDatasource from 'data/datasources/remote/user_public_address_remote_datasource'

const storageDatasource = container.get<StorageDatasource>(TYPES.StorageDatasource)

const tokenManager = new TokenManager()
const datasource = new UserPublicAddressRemoteDatasource()

describe('Unauthorized', () => {

  beforeAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()

  },20000)

  test('search()', async () => {
    try {
      await datasource.search()
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test('create()', async () => {
    try {
      await datasource.create(
        'someAddress',
        'someSignature'
      )
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test('delete()', async () => {
    try {
      await datasource.delete(
        'somneAddress'
      )
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

})


describe('Authorized', () => {
  let authorizationInitializer:AuthorizationInitializer,
    account:string

  authorizationInitializer = new AuthorizationInitializer()
  beforeAll(async () => {
    try{
      await authorizationInitializer.findUser(2)
    }catch(e){
      if(e instanceof NotFoundError){
        await authorizationInitializer.createUser(2)
      }
    }
    try{
      account = await authorizationInitializer.getAccount(2)
      let token = await authorizationInitializer.getApiToken(2)
      tokenManager.setToken(token)
    }catch(e){
      console.log(e)
    }

  },20000)

  afterAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  //Todo: verify the signature then create
  test('create():by existing address', async () => {
    try {
      let signature = await authorizationInitializer.getSignature(2)
      await datasource.create(account,signature)
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof BadRequestError).toBe(true)
      if (e instanceof BadRequestError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe(`The address(${account}) has been registered already`)
      }
    }
  })

  //after creating the public address delete it
  test('create():by new address ', async () => {
    try{
      let account1 = await authorizationInitializer.getAccount(5),
        nonce = await authorizationInitializer.getNonce(2),
        signature1 = await authorizationInitializer.getSignature(5,nonce)

      await datasource.create(account1,signature1)
    }catch(e) {
      expect(e).toBe(null)
    }
  })

  test('search()', async () => {
    try {
      let addresses = await datasource.search()
      expect(addresses.length).toBeGreaterThanOrEqual(1)
    } catch(e) {
      expect(e).toBe(null)
    }
  })

  test('delete()', async () => {

    try {
      let account1 = await authorizationInitializer.getAccount(5),
        result = await datasource.delete(account1)

      expect(result).toBe(true)
    } catch(e) {
      expect(e).toBe(null)
    }
  })
})

