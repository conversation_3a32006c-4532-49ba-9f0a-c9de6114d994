/* eslint-disable no-undef */
import { Query } from 'data/datasources/interfaces/boosted_collection_datasource/query'
import { BoostedCollectionJson } from 'data/jsons/boosted_collection_json'
import BaseError from 'errors/base_error'
import { NotFoundError } from 'errors/custom_datasource_errors'
import BoostedCollectionRemoteDatasource from 'data/datasources/remote/boosted_collection_remote_datasource'

const datasource = new BoostedCollectionRemoteDatasource()

describe('Normal Cases', () => {

  test('findById()', async () => {
    try {
      await datasource.findById('hogehoge')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the boosted collection, id:hogehoge')
      }
    }
  })

  test('search()', async () => {
    const query = new Query()
    const data:BoostedCollectionJson[] = await datasource.search(query)
    expect(data.length).toBeGreaterThanOrEqual(0)
  })

  test('totalCount()', async () => {
    const query = new Query()
    const totalCount = await datasource.totalCount(query)
    expect(totalCount).toBeGreaterThanOrEqual(0)
  })

})

