/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'

import BaseError from 'errors/base_error'
import { UnauthorizedError ,NotFoundError } from 'errors/custom_datasource_errors'
import AuthorizationInitializer from '../../utils/authorization_initializer'
import { CollectionJson } from 'data/jsons/collection_json'
import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
import TokenManager from 'data/datasources/remote/token_manager'
import UserOfferSettingRemoteDatasource from 'data/datasources/remote/user_offer_setting_remote_datasource'
import CollectionRemoteDatasource from 'data/datasources/remote/collection_remote_datasource'

const storageDatasource = container.get<StorageDatasource>(TYPES.StorageDatasource)

const tokenManager = new TokenManager()
const datasource = new UserOfferSettingRemoteDatasource()
const collectionDatasource = new CollectionRemoteDatasource()

describe('Unauthorized', () => {

  beforeAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()

  },20000)

  test('search()', async () => {
    try {
      await datasource.search()
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test('update()', async () => {
    try {
      await datasource.update('collectionId', 0.03)
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

})


describe('Authorized', () => {
  let authorizationInitializer :AuthorizationInitializer,
    account :string,
    token : string,
    collectionData : CollectionJson

  authorizationInitializer = new AuthorizationInitializer()

  beforeAll(async () => {
    try{
      await authorizationInitializer.findUser(2)
    }catch(e){
      if(e instanceof NotFoundError){
        await authorizationInitializer.createUser(2)
      }
    }
    try{
      account = await authorizationInitializer.getAccount(2)
      let token = await authorizationInitializer.getApiToken(2)
      tokenManager.setToken(token)
    }catch(e){
      console.log(e)
    }
  },20000)

  afterAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('update()', async () => {
    try{
      collectionData =  await collectionDatasource.findBySlug('slug9')

    }catch(e){
      if(e instanceof BaseError){
        collectionData = await collectionDatasource.create({
          slug: 'slug9',
          name: 'slug9',
          description: '',
          logoImageId: null,
          featuredImageId: null,
          bannerImageId: null,
          categoryId: null,
          webUrl: null,
          discordUrl: null,
          telegramUrl: null,
          mediumUsername: null,
          twitterUsername: null,
          instagramUsername: null,
          isExplicit: false,
          collaboratorIds: [],
        })
      }
    }

    try {

      const result1 = await datasource.update(collectionData.id as string, 5)

      expect(result1.collectionId).toBe(collectionData.id)
      expect(result1.minimumPrice).toBe(5)
      const result2 = await datasource.update(collectionData.id as string, 0.55)
      expect(result2.minimumPrice).toBe(0.55)
    } catch(e) {
      expect(e ).toBe(null)
    }
  })

  test.skip('update():For not existing collection', async () => {
    try {
      await datasource.update('there-is-no-record-with-this-id', 5)
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
    }

  })

  test('search()', async () => {
    try{
      let userOffers = await datasource.search()
      expect(userOffers.length).toBeGreaterThanOrEqual(1)
    }catch(e){
      expect(e ).toBe(null)
    }
  })
})

