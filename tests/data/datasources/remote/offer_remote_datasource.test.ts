/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'

import { Query } from 'data/datasources/interfaces/offer_datasource/query'
import { OfferJson } from 'data/jsons/offer_json'
import BaseError from 'errors/base_error'
import { NotFoundError, UnauthorizedError } from 'errors/custom_datasource_errors'
import AuthorizationInitializer from '../../utils/authorization_initializer'
import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
import TokenManager from 'data/datasources/remote/token_manager'
import OfferRemoteDatasource from 'data/datasources/remote/offer_remote_datasource'
import AssetRemoteDatasource from 'data/datasources/remote/asset_remote_datasource'

const storageDatasource = container.get<StorageDatasource>(TYPES.StorageDatasource)

const tokenManager = new TokenManager()
const datasource = new OfferRemoteDatasource()
const assetDatasource = new AssetRemoteDatasource()

describe('Normal Cases', () => {

  beforeAll( async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('findById()', async () => {
    try {
      await datasource.findById('hogehoge')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the offer, id:hogehoge')
      }
    }
  })

  test('search()', async () => {
    const query = new Query()
    const data:OfferJson[] = await datasource.search(query)
    expect(data.length).toBeGreaterThanOrEqual(0)
  })

  test('totalCount()', async () => {
    const query = new Query()
    const totalCount = await datasource.totalCount(query)
    expect(totalCount).toBeGreaterThanOrEqual(0)
  })

})

describe('Unauthorized', () => {

  beforeAll( async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('create()', async () => {
    try {
      await datasource.create({
        assetId: 'hogehoge',
        paymentToken: 'ethereum',
        price: 0.003,
        expiresAt: null,
      })
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test('update()', async () => {
    try {
      await datasource.update({
        id: 'hogehoge',
        paymentToken: 'ethereum',
        price: 0.004,
        expiresAt: null,
      })
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

})
describe('Authorized', () => {

  let apiToken : string
  const authInitializer = new AuthorizationInitializer()

  beforeAll(async () => {
    tokenManager.clearToken()
    // apiToken = await authInitializer.getApiToken(0)
    // tokenData.setToken(apiToken)
  })

  afterAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test.skip('save():For not existing asset', async () => {
    try {
      await datasource.create({
        assetId: 'there-is-no-asset-with-this-id',
        paymentToken: 'eth',
        price: 0.00663,
        expiresAt: null,
      })
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
    }
  })
  //TODO : Offer is not beign created in the datasource
  test.skip('create()', async () => {
    let asset
    try{
      await assetDatasource.findById('hogehoge')
    }catch(e){
      console.log(e)
    }

    try {
      const offer = await datasource.create({
        assetId: 'hogehoge',
        paymentToken: 'eth',
        price: 0.003,
        expiresAt: null,
      })
      expect(offer.id).toBeDefined()
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }

  })
  //TODO
  test.skip('update()', async () => {
    const query = new Query({paytemToken: 'eth'})
    const offer = await datasource.search(query)
    try {
      const updatedOffer = await datasource.update({
        id: offer[0].id as string,
        paymentToken: 'weth',
        price: 0.004,
        expiresAt: null,
      })
      expect(updatedOffer.id).toBe(offer[0].id)
      expect(updatedOffer.paymentToken.name).toBe('weth')
      expect(updatedOffer.price).toBe(0.004)
    } catch(e) {

      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }

  })

})

