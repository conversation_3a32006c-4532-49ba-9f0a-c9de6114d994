/* eslint-disable no-undef */
import { Query } from 'data/datasources/interfaces/asset_like_datasource/query'
import { AssetLikeJson } from 'data/jsons/asset_like_json'
import AssetLikeRemoteDatasource from 'data/datasources/remote/asset_like_remote_datasource'

const datasource = new AssetLikeRemoteDatasource()

describe('Normal Cases', () => {

  test('search()', async () => {
    const query = new Query()
    const data:AssetLikeJson[] = await datasource.search(query)
    expect(data.length).toBeGreaterThanOrEqual(0)
  })

  test('totalCount()', async () => {
    const query = new Query()
    const totalCount = await datasource.totalCount(query)
    expect(totalCount).toBeGreaterThanOrEqual(0)
  })

  test('assetIds()', async () => {
    const query = new Query()
    const assetIds = await datasource.assetIds(query)
    expect(assetIds.length).toBeGreaterThanOrEqual(0)
  })

})

