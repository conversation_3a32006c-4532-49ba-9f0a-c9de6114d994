/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'

import BaseError from 'errors/base_error'
import { BadRequestError, NotFoundError, UnauthorizedError } from 'errors/custom_datasource_errors'
import AuthorizationInitializer from '../../utils/authorization_initializer'
import { CollectionJson } from 'data/jsons/collection_json'
import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
import TokenManager from 'data/datasources/remote/token_manager'
import WatchRemoteDatasource from 'data/datasources/remote/watch_remote_datasource'
import CollectionRemoteDatasource from 'data/datasources/remote/collection_remote_datasource'

const storageDatasource = container.get<StorageDatasource>(TYPES.StorageDatasource)

const tokenManager = new TokenManager()
const datasource = new WatchRemoteDatasource()
const collectionDatasource = new CollectionRemoteDatasource()

describe('Unauthorized', () => {

  beforeAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  },20000)

  test('create()', async () => {
    try {
      await datasource.create('something', '*********')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test('delete()', async () => {
    try {
      await datasource.delete('something', '*********')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

})


describe('Authorized', () => {
  let authorizationInitializer :AuthorizationInitializer,
    account :string,
    collectionData : CollectionJson

  authorizationInitializer = new AuthorizationInitializer()

  beforeAll(async () => {
    try{
      await authorizationInitializer.findUser(9)
    }catch(e){
      if(e instanceof NotFoundError){
        await authorizationInitializer.createUser(9)
      }
    }
    try{
      account = await authorizationInitializer.getAccount(9)
      let token = await authorizationInitializer.getApiToken(9)
      tokenManager.setToken(token)
    }catch(e){
      console.log(e)
    }
  },20000)

  afterAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('create()', async () => {

    try{
      collectionData =  await collectionDatasource.findBySlug('slug9')

    }catch(e){
      if(e instanceof BaseError){
        collectionData = await collectionDatasource.create({
          slug: 'slug9',
          name: 'slug9',
          description: '',
          logoImageId: null,
          featuredImageId: null,
          bannerImageId: null,
          categoryId: null,
          webUrl: null,
          discordUrl: null,
          telegramUrl: null,
          mediumUsername: null,
          twitterUsername: null,
          instagramUsername: null,
          isExplicit: false,
          collaboratorIds: [],
        })
      }
    }

    try {
      let watch = await datasource.create('collection', String(collectionData.id))
      expect(watch.id).not.toBe(null)
      expect(watch.uniqueKey).toBe(`collection:${collectionData.id}`)
    } catch(e) {
      expect(e ).toBe(null)
    }
  })

  test('create():For not existing collection', async () => {

    try {
      await datasource.create('collection', 'dbjfberubgjerngmrbv')
      throw 'err'
    } catch(e) {

      expect(e instanceof BaseError).toBe(true)
    }

  })

  test('delete() : by wrong id', async () => {
    try {
      await datasource.delete('collection', '43897538627' )

    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof BadRequestError).toBe(true)
    }
  })

  test('delete()', async () => {

    try {
      const bool = await datasource.delete('collection', String(collectionData.id) )
      expect(bool).toBe(true)

    } catch(e) {
      expect(e ).toBe(null)
    }
  })

})

