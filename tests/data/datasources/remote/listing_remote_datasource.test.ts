/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'

import { Query } from 'data/datasources/interfaces/listing_datasource/query'
import { ListingJson } from 'data/jsons/listing_json'
import BaseError from 'errors/base_error'
import { NotFoundError, UnauthorizedError } from 'errors/custom_datasource_errors'
import AuthorizationInitializer from '../../utils/authorization_initializer'
import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
import TokenManager from 'data/datasources/remote/token_manager'
import ListingRemoteDatasource from 'data/datasources/remote/listing_remote_datasource'

const storageDatasource = container.get<StorageDatasource>(TYPES.StorageDatasource)

const tokenManager = new TokenManager()
const datasource = new ListingRemoteDatasource()

describe('Normal Cases', () => {

  beforeAll( async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('findById()', async () => {
    try {
      await datasource.findById('hogehoge')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the listing, id:hogehoge')
      }
    }
  })

  test('search()', async () => {
    const query = new Query()
    const data:ListingJson[] = await datasource.search(query)
    expect(data.length).toBeGreaterThanOrEqual(0)
  })

  test('totalCount()', async () => {
    const query = new Query()
    const totalCount = await datasource.totalCount(query)
    expect(totalCount).toBeGreaterThanOrEqual(0)
  })

})

describe('Unauthorized', () => {

  beforeAll( async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('create()', async () => {
    try {
      await datasource.create({
        assetIds: ['hogehoge', 'fugafuga'],
        quantities: [1, 1],
        listingType: 'fixedPrice',
        auctionMethod: null,
        paymentToken: 'ethereum',
        price: 0.003,
        endingPrice: null,
        startAt: '',
        endAt: '',
        isBundle: false,
        bundleName: null,
        bundleDescription: null,
        isReservedForBuyer: false,
        reservedBuyerAddress: null,
        hasReservePrice: false,
        reservePrice: null,
        signature: '',
      })
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test('update()', async () => {
    try {
      await datasource.update({
        id: 'hogehoge',
        endAt: '',
        bundleName: null,
        bundleDescription: null,
        isReservedForBuyer: false,
        reservedBuyerAddress: null,
        hasReservePrice: false,
        reservePrice: null,
        signature: '',
      })
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

})

describe('Authorized', () => {

  let apiToken : string

  beforeAll(async () => {
    const authInitializer = new AuthorizationInitializer()
    //apiToken = await authInitializer.getApiToken(0)
    //tokenData.setToken(apiToken);
  })

  afterAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })


  test.skip('create()', async () => {
    try {
      await datasource.create({
        assetIds: ['hogehoge'],
        quantities: [ 1 ],
        listingType: 'fixedPrice',
        auctionMethod: null,
        paymentToken: 'eth',
        price: 0.003,
        endingPrice: null,
        startAt: '',
        endAt: '',
        isBundle: false,
        bundleName: null,
        bundleDescription: null,
        isReservedForBuyer: false,
        reservedBuyerAddress: null,
        hasReservePrice: false,
        reservePrice: null,
        signature: '',
      })
    } catch(e) {

      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test.skip('update()', async () => {
    try {
      await datasource.update({
        id: 'hogehoge',
        endAt: '',
        bundleName: null,
        bundleDescription: null,
        isReservedForBuyer: false,
        reservedBuyerAddress: null,
        hasReservePrice: false,
        reservePrice: null,
        signature: '',
      })
    } catch(e) {

      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }

  })

})

