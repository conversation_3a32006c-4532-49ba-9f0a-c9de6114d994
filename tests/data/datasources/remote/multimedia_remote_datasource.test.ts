/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'

import * as fs from 'fs'
import BaseError from 'errors/base_error'
import { UnauthorizedError } from 'errors/custom_datasource_errors'
import { InfuraProvider } from '@ethersproject/providers'
import { Signer } from 'ethers'
import { Web3Fixture } from 'tests/fixture/web3_fixture'
import { MessageToSignService } from 'domain/services/utils/message_to_sign_service'
import { LoginSuccessJson } from 'data/jsons/login_success_json'
import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
import TokenManager from 'data/datasources/remote/token_manager'
import MultimediaRemoteDatasource from 'data/datasources/remote/multimedia_datasource'
import UserRemoteDatasource from 'data/datasources/remote/user_remote_datasource'

const storageDatasource = container.get<StorageDatasource>(TYPES.StorageDatasource)

const tokenManager = new TokenManager()
const datasource = new MultimediaRemoteDatasource()
const userDatasource = new UserRemoteDatasource()

describe('Unauthorized', () => {

  beforeAll( async () => {
    tokenManager.clearToken()
  })

  test('upload()', async () => {
    try {
      const buffer = fs.readFileSync('tests/images/sample_image.png')
      const file:File = new File([buffer], 'sample_image.png', { type: 'image/png' })
      await datasource.upload(file)
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test('upload() failed', async () => {
    try {
      const buffer = fs.readFileSync('tests/images/sample_image.png')
      const file:File = new File([buffer], 'sample_image.png', { type: 'image/png' })
      await datasource.upload(file)
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

})

describe('Authorized', () => {

  let provider:InfuraProvider,
    signer:Signer

  beforeAll(async () => {

    // setup web3 instances
    const web3Fixture = new Web3Fixture().create()
    provider = web3Fixture.provider
    signer = web3Fixture.signer.connect(provider)

    // login process
    // Because of datasource test, not using AuthFixture returns model
    const nonce = await userDatasource.nonce(await signer.getAddress())
    const messageToSign = MessageToSignService.agreePrivacyPolicyAndTermsOfService(`${nonce}`)
    const signature = await signer.signMessage(messageToSign)
    const response:LoginSuccessJson = await userDatasource.login(
      await signer.getAddress(),
      signature
    )
    tokenManager.setToken(response.token)
  })

  afterAll(() => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('upload() successfully', async () => {
    try {
      const buffer = fs.readFileSync('tests/images/sample_image.png')
      const file:File = new File([buffer], 'sample_image.png', { type: 'image/png' })
      const response = await datasource.upload(file)

      expect(response.id).not.toBe(undefined)
      expect(response.url).not.toBe('')
      expect(response.storageName).toBe('s3')
      expect(response.mimeType).toBe('image/png')
      expect(response.dataSize).not.toBe(undefined)
      expect(response.height).not.toBe(undefined)
      expect(response.width).not.toBe(undefined)
      expect(response.hash).not.toBe(undefined)
    } catch(e) {
      expect(1).toBe(0) // should not be here
    }

  })

})

