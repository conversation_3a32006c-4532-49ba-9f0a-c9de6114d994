/* eslint-disable no-undef */
import { ActivityJson } from 'data/jsons/activity_json'
import { Query } from 'data/datasources/interfaces/activity_datasource/query'
import BaseError from 'errors/base_error'
import { NotFoundError } from 'errors/custom_datasource_errors'
import ActivityRemoteDatasource from 'data/datasources/remote/activity_remote_datasource'

const datasource = new ActivityRemoteDatasource()

describe('Normal Cases', () => {
  test('findById()', async () => {
    try {
      await datasource.findById('hogehoge')
    } catch (e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the activity, id:hogehoge')
      }
    }
  })

  test('search()', async () => {
    const query = new Query()
    const data: ActivityJson[] = await datasource.search(query)
    expect(data.length).toBeGreaterThanOrEqual(0)
  })

  test('totalCount()', async () => {
    const query = new Query()
    const totalCount = await datasource.totalCount(query)
    expect(totalCount).toBeGreaterThanOrEqual(0)
  })
})
