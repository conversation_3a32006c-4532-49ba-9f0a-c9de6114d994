/* eslint-disable no-undef */
import { Query } from 'data/datasources/interfaces/main_banner_datasource/query'
import { MainBannerJson } from 'data/jsons/main_banner_json'
import BaseError from 'errors/base_error'
import { NotFoundError } from 'errors/custom_datasource_errors'
import MainBannerRemoteDatasource from 'data/datasources/remote/main_banner_remote_datasource'

const datasource = new MainBannerRemoteDatasource()

describe('Normal Cases', () => {

  test('findById()', async () => {
    try {
      await datasource.findById('hogehoge')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the main banner, id:hogehoge')
      }
    }
  })

  test('search()', async () => {
    const query = new Query()
    const data:MainBannerJson[] = await datasource.search(query)
    expect(data.length).toBeGreaterThanOrEqual(0)
  })

  test('totalCount()', async () => {
    const query = new Query()
    const totalCount = await datasource.totalCount(query)
    expect(totalCount).toBeGreaterThanOrEqual(0)
  })

})

