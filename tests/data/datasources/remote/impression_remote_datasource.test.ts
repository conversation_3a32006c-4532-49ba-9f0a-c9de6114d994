/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'

import BaseError from 'errors/base_error'
import { BadRequestError, NotFoundError } from 'errors/custom_datasource_errors'
import { CollectionJson } from 'data/jsons/collection_json'
import AuthorizationInitializer from '../../utils/authorization_initializer'
import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
import TokenManager from 'data/datasources/remote/token_manager'
import ImpressionRemoteDatasource from 'data/datasources/remote/impression_remote_datasource'
import CollectionRemoteDatasource from 'data/datasources/remote/collection_remote_datasource'

const storageDatasource = container.get<StorageDatasource>(TYPES.StorageDatasource)

const tokenManager = new TokenManager()
const datasource = new ImpressionRemoteDatasource()
const collectionDatasource = new CollectionRemoteDatasource()

const authorizationInitializer = new AuthorizationInitializer()

describe('Error Cases', () => {

  test('create():Wrong target entity type', async () => {
    try {
      await datasource.create('something', '123456789')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof BadRequestError).toBe(true)
      if (e instanceof BadRequestError){
        const errors = e.getErrors()
        expect(errors[0]).toBe('targetEntityType is invalid, targetEntityType:something')
      }
    }
  })

})

describe('Normal case', () => {

  // let testCollectionName:string;
  // let count: number;
  let collectionJson : CollectionJson

  beforeAll(async () => {
    // try{
    //     const query = new Query()
    //     count = await CollectionDatasource.totalCount(query)
    //     testCollectionName = `collection:${count-1}`

    // }catch(e){
    //     console.log(e)
    // }

    tokenManager.clearToken()
    storageDatasource.deleteAll()

    try{
      await authorizationInitializer.findUser(2)
    }catch(e){
      if(e instanceof NotFoundError){
        await authorizationInitializer.createUser(2)
      }
    }

    try{
      let token = await authorizationInitializer.getApiToken(2)
      tokenManager.setToken(token)

    }catch(e){
      console.log(e)
    }


  },20000)

  afterAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('create(): by collection ', async () => {

    try{
      collectionJson =  await collectionDatasource.findBySlug('slug9')
    }catch(e){
      collectionJson = await collectionDatasource.create({
        slug: 'slug9',
        name: 'slug9',
        description: '',
        logoImageId: null,
        featuredImageId: null,
        bannerImageId: null,
        categoryId: null,
        webUrl: null,
        discordUrl: null,
        telegramUrl: null,
        mediumUsername: null,
        twitterUsername: null,
        instagramUsername: null,
        isExplicit: false,
        collaboratorIds: [],
      })
    }

    try {
      await datasource.create('collection', String(collectionJson.id))

    } catch(e) {

      expect(e instanceof BaseError).toBe(false)

    }
  })
})

