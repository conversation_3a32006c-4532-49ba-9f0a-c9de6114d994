/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'

import BaseError from 'errors/base_error'
import { NotFoundError, UnauthorizedError } from 'errors/custom_datasource_errors'
import AuthorizationInitializer from '../../utils/authorization_initializer'
import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
import TokenManager from 'data/datasources/remote/token_manager'
import UserNotificationSettingRemoteDatasource from 'data/datasources/remote/user_notification_setting_remote_datasource'
import UserRemoteDatasource from 'data/datasources/remote/user_remote_datasource'

const storageDatasource = container.get<StorageDatasource>(TYPES.StorageDatasource)

const tokenManager = new TokenManager()
const datasource = new UserNotificationSettingRemoteDatasource()
const userDatasource = new UserRemoteDatasource()

describe('Unauthorized', () => {
  beforeAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()

  },20000)
  test('mine()', async () => {
    try {
      await datasource.mine()
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test('update()', async () => {
    try {
      await datasource.update({
        onItemSold: true,
        onBidActivity: true,
        onPriceChange: true,
        onAuctionExpiration: true,
        onOutbid: true,
        onReferralSuccess: true,
        onOwnedAssetUpdates: true,
        onSuccessfullyPurchase: true,
        onNewsLetter: true,
        minimumBidThresholdUsd: 1,
        minimumBidThresholdJpy: 150,
      })
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

})


describe('Authorized', () => {
  let authorizationInitializer :AuthorizationInitializer,
    account :string,
    token : string,
    userId: string

  authorizationInitializer = new AuthorizationInitializer()

  beforeAll(async () => {
    try{
      await authorizationInitializer.findUser(9)
    }catch(e){
      if(e instanceof NotFoundError){
        await authorizationInitializer.createUser(9)
      }
    }

    try{
      account = await authorizationInitializer.getAccount(9)
      token = await authorizationInitializer.getApiToken(9)
      tokenManager.setToken(token)
    }catch(e){
      console.log(e)
    }
  },20000)

  afterAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('update() ', async () => {

    let data = {
      onItemSold: false,
      onBidActivity: true,
      onPriceChange: true,
      onAuctionExpiration: true,
      onOutbid: true,
      onReferralSuccess: true,
      onOwnedAssetUpdates: true,
      onSuccessfullyPurchase: true,
      onNewsLetter: true,
      minimumBidThresholdUsd: 1,
      minimumBidThresholdJpy: 150,
    }
    try {
      let user = await userDatasource.me()
      userId = user.id!
      const userNotification = await datasource.update(data)

      expect(userNotification.id).not.toBe(null)
      expect(userNotification.userId).toBe(user.id)
      expect(userNotification.onItemSold).toBe(false)

    } catch(e) {
      expect(e ).toBe(null)

    }

  })
  test('mine()', async () => {

    try {
      let userNotification = await datasource.mine(),
        user = await userDatasource.me()
      expect(userNotification.id).not.toBe(null)
      expect(userNotification.userId).toBe(userId)

    } catch(e) {
      expect(e ).toBe(null)
    }

  })

})

