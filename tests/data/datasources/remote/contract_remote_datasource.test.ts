/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'

import { Query } from 'data/datasources/interfaces/contract_datasource/query'
import { ContractJson } from 'data/jsons/contract_json'
import BaseError from 'errors/base_error'
import { NotFoundError } from 'errors/custom_datasource_errors'
import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
import TokenManager from 'data/datasources/remote/token_manager'
import ContractRemoteDatasource from 'data/datasources/remote/contract_remote_datasource'

const storageDatasource = container.get<StorageDatasource>(TYPES.StorageDatasource)

const tokenManager = new TokenManager()
const datasource = new ContractRemoteDatasource()

describe('Normal Cases', () => {

  beforeAll( async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('findById()', async () => {
    try {
      await datasource.findById('hogehoge')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the contract, id:hogehoge')
      }
    }
  })

  test('findByChainAndAddress()', async () => {
    try {
      await datasource.findByChainAndAddress(
        'ethereum',
        '******************************************'
      )
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the contract, chain:ethereum, address:******************************************')
      }
    }
  })

  test('search()', async () => {
    const query = new Query()
    const data:ContractJson[] = await datasource.search(query)
    expect(data.length).toBeGreaterThanOrEqual(0)
  })

  test('totalCount()', async () => {
    const query = new Query()
    const totalCount = await datasource.totalCount(query)
    expect(totalCount).toBeGreaterThanOrEqual(0)
  })
})

