/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'

import { Query } from 'data/datasources/interfaces/collection_datasource/query'
import { CollectionJson } from 'data/jsons/collection_json'
import BaseError from 'errors/base_error'
import { BadRequestError, NotFoundError, UnauthorizedError } from 'errors/custom_datasource_errors'
import AuthorizationInitializer from '../../utils/authorization_initializer'
import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
import TokenManager from 'data/datasources/remote/token_manager'
import CollectionRemoteDatasource from 'data/datasources/remote/collection_remote_datasource'

const tokenManager:TokenManager = new TokenManager()
const storageDatasource = container.get<StorageDatasource>(TYPES.StorageDatasource)

const datasource = new CollectionRemoteDatasource()
const authInitializer = new AuthorizationInitializer()

describe('Normal Cases', () => {

  beforeAll( async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('findById()', async () => {
    try {
      await datasource.findById('hogehoge')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the collection, id:hogehoge')
      }
    }
  })

  test('findBySlug()', async () => {
    try {
      await datasource.findBySlug('airbits')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the collection, slug:airbits')
      }
    }
  })

  test('search()', async () => {
    const query = new Query()
    const data:CollectionJson[] = await datasource.search(query)
    expect(data.length).toBeGreaterThanOrEqual(0)
  })

  test('totalCount()', async () => {
    const query = new Query()
    const totalCount = await datasource.totalCount(query)
    expect(totalCount).toBeGreaterThanOrEqual(0)
  })
})

describe('Unauthorized', () => {

  beforeAll( async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('create()', async () => {
    try {
      await datasource.create({
        slug: 'new collection slug',
        name: 'new collection name',
        description: '',
        logoImageId: null,
        featuredImageId: null,
        bannerImageId: null,
        categoryId: null,
        webUrl: null,
        discordUrl: null,
        telegramUrl: null,
        mediumUsername: null,
        twitterUsername: null,
        instagramUsername: null,
        isExplicit: false,
        collaboratorIds: [],
      })
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test('update()', async () => {
    try {
      await datasource.update({
        id: 'hogehoge',
        slug: 'new collection slug',
        name: 'new collection name',
        description: '',
        logoImageId: null,
        featuredImageId: null,
        bannerImageId: null,
        categoryId: null,
        webUrl: null,
        discordUrl: null,
        telegramUrl: null,
        mediumUsername: null,
        twitterUsername: null,
        instagramUsername: null,
        isExplicit: false,
        collaboratorIds: [],
      })
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

})

describe('Authorised', () => {
  beforeAll( async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  afterAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('create()', async () => {
    const apiToken = await authInitializer.getApiToken(0)
    tokenManager.setToken(apiToken)
    try {
      await datasource.create({
        slug: 'slug',
        name: 'new collection name',
        description: '',
        logoImageId: null,
        featuredImageId: null,
        bannerImageId: null,
        categoryId: null,
        webUrl: null,
        discordUrl: null,
        telegramUrl: null,
        mediumUsername: null,
        twitterUsername: null,
        instagramUsername: null,
        isExplicit: false,
        collaboratorIds: [],
      })
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      } else{
        const errors = (e as BadRequestError).getErrors()
        expect(errors[0]).toBe('The slug is used by another collection: slug')
      }
    }
  })

  test('update()', async () => {
    try {
      const collection = await datasource.findBySlug('slug')
      const updatedCollection = await datasource.update({
        id: collection.id as string,
        slug: 'slug',
        name: 'newName',
        description: 'newDesc',
        logoImageId: null,
        featuredImageId: null,
        bannerImageId: null,
        categoryId: null,
        webUrl: null,
        discordUrl: null,
        telegramUrl: null,
        mediumUsername: null,
        twitterUsername: null,
        instagramUsername: null,
        isExplicit: false,
        collaboratorIds: [],
      })
      expect(collection.id).toBe(updatedCollection.id)
      expect(updatedCollection.name).toBe('newName')
      expect(updatedCollection.description).toBe('newDesc')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })
})

