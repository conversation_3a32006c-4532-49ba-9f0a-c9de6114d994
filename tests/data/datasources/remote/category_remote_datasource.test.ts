/* eslint-disable no-undef */
import { Query } from 'data/datasources/interfaces/category_datasource/query'
import { CategoryJson } from 'data/jsons/category_json'
import BaseError from 'errors/base_error'
import { NotFoundError } from 'errors/custom_datasource_errors'
import CategoryRemoteDatasource from 'data/datasources/remote/category_remote_datasource'

const datasource = new CategoryRemoteDatasource()

describe('Normal Cases', () => {

  test('findById()', async () => {
    try {
      await datasource.findById('hogehoge')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the category, id:hogehoge')
      }
    }
  })

  test('findBySlug()', async () => {
    try {
      await datasource.findBySlug('digitalArt')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the category, slug:digitalArt')
      }
    }
  })

  test('search()', async () => {
    const query = new Query()
    const data:CategoryJson[] = await datasource.search(query)
    expect(data.length).toBeGreaterThanOrEqual(0)
  })

  test('totalCount()', async () => {
    const query = new Query()
    const totalCount = await datasource.totalCount(query)
    expect(totalCount).toBeGreaterThanOrEqual(0)
  })

})

