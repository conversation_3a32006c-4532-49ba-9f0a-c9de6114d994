/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'

import { Query } from 'data/datasources/interfaces/collection_watch_datasource/query'
import { CollectionWatchJson } from 'data/jsons/collection_watch_json'
import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
import TokenManager from 'data/datasources/remote/token_manager'
import CollectionWatchRemoteDatasource from 'data/datasources/remote/collection_watch_remote_datasource'

const storageDatasource = container.get<StorageDatasource>(TYPES.StorageDatasource)
const tokenManager:TokenManager = new TokenManager()

const datasource = new CollectionWatchRemoteDatasource()

describe('Normal Cases', () => {

  beforeAll( async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('search()', async () => {
    const query = new Query()
    const data:CollectionWatchJson[] = await datasource.search(query)
    expect(data.length).toBeGreaterThanOrEqual(0)
  })

  test('totalCount()', async () => {
    const query = new Query()
    const totalCount = await datasource.totalCount(query)
    expect(totalCount).toBeGreaterThanOrEqual(0)
  })

  test('collectionIds()', async () => {
    const query = new Query()
    const collectionIds = await datasource.collectionIds(query)
    expect(collectionIds.length).toBeGreaterThanOrEqual(0)
  })

})

