/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'

import BaseError from 'errors/base_error'
import { BadRequestError, NotFoundError } from 'errors/custom_datasource_errors'
import { CollectionJson } from 'data/jsons/collection_json'
import AuthorizationInitializer from '../../utils/authorization_initializer'
import User from 'domain/models/user'
import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
import TokenManager from 'data/datasources/remote/token_manager'
import ReportRemoteDatasource from 'data/datasources/remote/report_remote_datasource'
import CollectionRemoteDatasource from 'data/datasources/remote/collection_remote_datasource'

const storageDatasource = container.get<StorageDatasource>(TYPES.StorageDatasource)

const tokenManager = new TokenManager()
const datasource = new ReportRemoteDatasource()
const collectionDatasource = new CollectionRemoteDatasource()

let authorizationInitializer:AuthorizationInitializer,

  user :User

describe('Error Cases', () => {

  test('create() with wrong target entity type', async () => {
    try {
      await datasource.create('something', '123456789', 1)
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof BadRequestError).toBe(true)
      if (e instanceof BadRequestError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('targetEntityType is invalid, targetEntityType:something')
      }
    }
  })

})
describe('Correct TET passed', () => {

  authorizationInitializer = new AuthorizationInitializer()
  let collection: CollectionJson

  beforeAll(async () => {

    tokenManager.clearToken()
    storageDatasource.deleteAll()

    try{
      user = await authorizationInitializer.findUser(9)
    }catch(e){
      if(e instanceof NotFoundError){
        user = await authorizationInitializer.createUser(9)
      }
    }

    try{
      let token = await authorizationInitializer.getApiToken(9)
      tokenManager.setToken(token)
    }catch(e){
      console.log(e)
    }
  })
  afterAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('create()', async () => {

    try{
      collection = await collectionDatasource.findBySlug('slug9')
    }catch(e){
      if(e instanceof BaseError){
        collection = await collectionDatasource.create({
          slug: 'slug9',
          name: 'slug9',
          description: '',
          logoImageId: null,
          featuredImageId: null,
          bannerImageId: null,
          categoryId: null,
          webUrl: null,
          discordUrl: null,
          telegramUrl: null,
          mediumUsername: null,
          twitterUsername: null,
          instagramUsername: null,
          isExplicit: false,
          collaboratorIds: [],
        })
      }
    }

    try {
      const report = await datasource.create('collection', collection.id as string, 1)
      expect(report.reasonId).toBe(1)
      expect(report.targetEntityType.name).toBe('collection')
    } catch(e) {
      expect(e ).toBe(null)
    }

  })

  test.skip('Create():For not existing Collection', async () => {


    try {
      const report = await datasource.create('collection', 'fbvjbivbermbg', 1)
      throw 'err'
    } catch(e) {
      expect(e instanceof BaseError ).toBe(true)

    }


  })


})


