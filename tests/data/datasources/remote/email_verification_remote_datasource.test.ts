/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'

import BaseError from 'errors/base_error'
import { BadRequestError, NotFoundError, UnauthorizedError } from 'errors/custom_datasource_errors'
import AuthorizationInitializer from '../../utils/authorization_initializer'
import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
import TokenManager from 'data/datasources/remote/token_manager'
import EmailVerificationRemoteDatasource from 'data/datasources/remote/email_verification_remote_datasource'

const storageDatasource = container.get<StorageDatasource>(TYPES.StorageDatasource)
const tokenManager:TokenManager = new TokenManager()

const datasource = new EmailVerificationRemoteDatasource()

const authInitializer = new AuthorizationInitializer()

describe('Unauthorized', () => {

  beforeAll( async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('issue()', async () => {
    try {
      await datasource.issue('<EMAIL>')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test('verify()', async () => {
    try {
      await datasource.verify('123456789')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test('hasActiveRequest()', async () => {
    try {
      await datasource.hasActiveRequest()
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test('resendEmail()', async () => {
    try {
      await datasource.resendEmail()
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

})

describe('Authorized', () => {

  let apiToken: string,
    token: string

  beforeAll(async () => {
    try{
      await authInitializer.findUser(0)
    }catch(e){
      if(e instanceof NotFoundError){
        await authInitializer.createUser(0)
      }
    }

    try{
      apiToken = await authInitializer.getApiToken(0)
      tokenManager.setToken(apiToken)
    }
    catch(e){
      console.log(e)
    }
  })
  afterAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('issue()', async () => {
    try {
      const emailVerification = await datasource.issue('<EMAIL>')
      expect(emailVerification.email).toBe('<EMAIL>')
      token = emailVerification.token
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      } else {
        const errors = (e as BadRequestError).getErrors()
        expect(errors[0]).toBe('The email has been registered already: <EMAIL>')
      }
    }
  })

  test('verify()', async () => {
    try {
      const emailVerification = await datasource.verify(token)
      expect(emailVerification).toBe(true)
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      } else {
        const errors = (e as BadRequestError).getErrors()
        expect(errors[0]).toBe('token must be specified')
      }
    }
  })

  test('hasActiveRequest()', async () => {
    try {
      const response = await datasource.hasActiveRequest()
      expect(response).toBe(false)
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test('resendEmail()', async () => {
    try {
      const resendResponse = await datasource.resendEmail()
      expect(resendResponse).toBe(false)
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })
})

