/* eslint-disable no-undef */
import { Query } from 'data/datasources/interfaces/announcement_datasource/query'
import { AnnouncementJson } from 'data/jsons/announcement_json'
import BaseError from 'errors/base_error'
import { NotFoundError } from 'errors/custom_datasource_errors'
import AnnouncementRemoteDatasource from 'data/datasources/remote/announcement_remote_datasource'

const datasource = new AnnouncementRemoteDatasource()

describe('Normal Cases', () => {

  test('findById()', async () => {
    try {
      await datasource.findById('hogehoge')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the announcement, id:hogehoge')
      }
    }
  })

  test('search()', async () => {
    const query = new Query()
    const data:AnnouncementJson[] = await datasource.search(query)
    expect(data.length).toBeGreaterThanOrEqual(0)
  })

  test('totalCount()', async () => {
    const query = new Query()
    const totalCount = await datasource.totalCount(query)
    expect(totalCount).toBeGreaterThanOrEqual(0)
  })

})

