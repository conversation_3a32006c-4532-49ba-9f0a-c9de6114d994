/* eslint-disable no-undef */
import { Query } from 'data/datasources/interfaces/asset_datasource/query'
import { Asset<PERSON>son } from 'data/jsons/asset_json'
import BaseError from 'errors/base_error'
import { NotFoundError } from 'errors/custom_datasource_errors'
import AssetRemoteDatasource from 'data/datasources/remote/asset_remote_datasource'

const datasource = new AssetRemoteDatasource()

describe('Normal Cases', () => {

  test('findById()', async () => {
    try {
      await datasource.findById('hogehoge')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the asset, id:hogehoge')
      }
    }
  })

  test('findByChainAndAddressAndTokenId()', async () => {
    try {
      await datasource.findByChainAndAddressAndTokenId(
        'ethereum',
        '******************************************',
        '1'
      )
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof NotFoundError).toBe(true)
      if (e instanceof NotFoundError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Not found the asset, chain:ethereum, address:******************************************, tokenId:1')
      }
    }
  })

  test('search()', async () => {
    const query = new Query()
    const data:AssetJson[] = await datasource.search(query)
    expect(data.length).toBeGreaterThanOrEqual(0)
  })

  test('totalCount()', async () => {
    const query = new Query()
    const totalCount = await datasource.totalCount(query)
    expect(totalCount).toBeGreaterThanOrEqual(0)
  })

})

