/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'

import BaseError from 'errors/base_error'
import { UnauthorizedError } from 'errors/custom_datasource_errors'
import { CollectionJson } from 'data/jsons/collection_json'
import AuthorizationInitializer from '../../utils/authorization_initializer'
import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
import TokenManager from 'data/datasources/remote/token_manager'
import LikeRemoteDatasource from 'data/datasources/remote/like_remote_datasource'
import CollectionRemoteDatasource from 'data/datasources/remote/collection_remote_datasource'

const storageDatasource = container.get<StorageDatasource>(TYPES.StorageDatasource)

const tokenManager = new TokenManager()
const datasource = new LikeRemoteDatasource()
const collectionDatasource = new CollectionRemoteDatasource()

describe('Unauthorized', () => {

  beforeAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('create()', async () => {
    try {
      await datasource.create('something', '123456789')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

  test('delete()', async () => {
    try {
      await datasource.delete('something', '123456789')
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

})
describe('Authorized', () => {
  let authInitializer = new AuthorizationInitializer(),
    collectionJson: CollectionJson,
    testCollectionName:string,
    count: number

  beforeAll(async () => {
    const apiToken = await authInitializer.getApiToken(0)
    tokenManager.setToken(apiToken)
  })

  afterAll(async () => {
    tokenManager.clearToken()
    storageDatasource.deleteAll()
  })

  test('create()', async () => {

    try{
      collectionJson =  await collectionDatasource.findBySlug('slug9')
    }catch(e){
      collectionJson = await collectionDatasource.create({
        slug: 'slug9',
        name: 'slug9',
        description: '',
        logoImageId: null,
        featuredImageId: null,
        bannerImageId: null,
        categoryId: null,
        webUrl: null,
        discordUrl: null,
        telegramUrl: null,
        mediumUsername: null,
        twitterUsername: null,
        instagramUsername: null,
        isExplicit: false,
        collaboratorIds: [],
      })
    }
    try {
      await datasource.create('collection', String(collectionJson.id))
    } catch(e) {
      expect(e).toBe(null)
    }

  })

  test('create(): for not existing', async () => {

    try {
      await datasource.create(
        'collection',
        'there-is-no-collection-with-this-id'
      )
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
    }
  })

  test.skip('delete()', async () => {
    try {
      await datasource.delete(
        'collection',
        collectionJson.id as string
      )
    } catch(e) {
      expect(e instanceof BaseError).toBe(true)
      expect(e instanceof UnauthorizedError).toBe(true)
      if (e instanceof UnauthorizedError) {
        const errors = e.getErrors()
        expect(errors[0]).toBe('Only authorized user can access')
      }
    }
  })

})

