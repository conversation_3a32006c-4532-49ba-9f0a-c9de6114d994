/* eslint-disable no-undef */
import container from 'inversify.config.test'
import TYPES from 'types'

import StorageDatasource from 'data/datasources/interfaces/storage_datasource'
describe('Normal Operations', () => {

  const localStorage = container.get<StorageDatasource>(TYPES.StorageDatasource)

  beforeEach(() => {
    localStorage.deleteAll()
  })

  afterEach(() => {
    localStorage.deleteAll()
  })

  test('read empty', () => {
    const value = localStorage.read('key')
    expect(value).toBeNull()
  })

  test('write and read', () => {
    localStorage.write('key', 'hogehoge')
    const value = localStorage.read('key')
    expect(value).toBe('hogehoge')
  })

  test('delete and read', () => {
    localStorage.delete('key')
    const value = localStorage.read('key')
    expect(value).toBeNull()
  })

  test('delete all', () => {
    localStorage.write('key1', 'hogehoge')
    localStorage.write('key2', 'fugafuga')
    localStorage.write('key3', 'gorogoro')

    let value1 = localStorage.read('key1'),
      value2 = localStorage.read('key2'),
      value3 = localStorage.read('key3')

    expect(value1).toBe('hogehoge')
    expect(value2).toBe('fugafuga')
    expect(value3).toBe('gorogoro')

    localStorage.deleteAll()

    value1 = localStorage.read('key1')
    value2 = localStorage.read('key2')
    value3 = localStorage.read('key3')

    expect(value1).toBeNull()
    expect(value2).toBeNull()
    expect(value3).toBeNull()
  })
})
