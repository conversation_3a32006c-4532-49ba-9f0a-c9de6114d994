/* eslint-disable no-undef */
import TokenManager from 'data/datasources/remote/token_manager'

describe('Default Key Prefix', () => {

  const tokenManager:TokenManager = new TokenManager()

  beforeEach(() => {
    tokenManager.clearToken()
  })
    
  afterEach(() => {
    tokenManager.clearToken()
  })    

  test('get empty token', () => {
    const value = tokenManager.getToken()
    expect(value).toBeNull()
  })
    
  test('set and get', () => {
    tokenManager.setToken('123456789')
    const value = tokenManager.getToken()
    expect(value).toBe('123456789')
  })
    
  test('clear and read', () => {
    tokenManager.clearToken()
    const value = tokenManager.getToken()
    expect(value).toBeNull()
  })

  test('set and get realistic case', () => {
    const token = 'MtG4.ZW-wJr6BtDabAKeoXuJjyd72RydZo9BQmOII4f6BX_gXvoMotfuW8jr_MFLR'
    tokenManager.setToken(token)
    const fetched = tokenManager.getToken()
    expect(fetched).toBe(token)
  })
    
})
