import DataToSignGenerator from './data_to_sign_generator'
import { ethers } from 'ethers'

import TokenManager from '../../../data/datasources/remote/token_manager'
import UserRepository from '../../../domain/repositories/user_repository'

const tokenManager = new TokenManager()
const repository = new UserRepository()

export default class AuthorizationInitializer {
  public accounts: string[] = []
  public wallets: any[] = []

  constructor () {
    for (let i = 0; i < 10; i++) {
      const wallet = ethers.Wallet.fromMnemonic(
        process.env.DEMO_MNEMONIC!,
        `m/44'/60'/0'/0/${i}`
      )
      this.accounts.push(wallet.address)
      this.wallets.push(wallet)
    }
  }

  public async getAccount (
    accountIndex: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9
  ): Promise<string> {
    return this.accounts[accountIndex]
  }

  public async getNonce (
    accountIndex: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9
  ): Promise<number> {
    const nonce1 = await repository.nonce(this.accounts[accountIndex])
    return nonce1
  }

  public async getApiToken (
    accountIndex: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9
  ): Promise<string> {
    //await repository.logout();
    let status = await repository.loginStatus()
    if (status.isLoggedIn) {
      await repository.logout()
    }
    tokenManager.clearToken()
    try {
      await repository.findByPublicAddress(this.accounts[accountIndex])
    } catch (e) {
      await repository.create(this.accounts[accountIndex])
    }

    let nonce = await repository.nonce(this.accounts[accountIndex])
    const signature = await this.getSignature(accountIndex) //await this.wallets[accountIndex].signMessage(DataToSignGenerator.general(String(nonce)))
    try {
      const loginResponse = await repository.login(
        this.accounts[accountIndex],
        signature
      )
      return loginResponse.token
    } catch (e) {
      // console.log("account  " +  this.accounts[accountIndex])
      // console.log("msg  " +  pp)
      // console.log("signature  " + signature)
      // console.log("auth  " + e)
      return (
        ''
      )
    }
  }

  public async getSignature (
    accountIndex: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9,
    nonce?: number
  ): Promise<string> {
    if (nonce) {
      const signature = await this.wallets[accountIndex].signMessage(
        DataToSignGenerator.generate(String(nonce))
      )
      return signature
    }

    const nonce1 = await repository.nonce(this.accounts[accountIndex])
    const signature = await this.wallets[accountIndex].signMessage(
      DataToSignGenerator.generate(String(nonce1))
    )
    return signature
  }

  public async createUser (accountIndex: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9) {
    const user = await repository.create(this.accounts[accountIndex])
    return user
  }

  public async findUser (accountIndex: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9) {
    const user = await repository.findByPublicAddress(
      this.accounts[accountIndex]
    )
    return user
  }
}
