import HDWalletProvider from '@truffle/hdwallet-provider'
import Chain from 'domain/value_objects/chain'

export default class HDWalletProviderFactory {

  public static getProvider (chain:Chain): HDWalletProvider {

    if (chain.isPolygon()) {
      return new HDWalletProvider({
        mnemonic: {
          phrase: process.env.OPERATION_MNEMONIC!,
        },
        providerOrUrl: process.env.ETHEREUM_HDWALLET_PROVIDER_URL,
      })
    }

    const  prov = new HDWalletProvider({
      mnemonic: {
        phrase: process.env.OPERATION_MNEMONIC!,
      },
      providerOrUrl: process.env.ETHEREUM_HDWALLET_PROVIDER_URL,
    })

    return prov
  }
}