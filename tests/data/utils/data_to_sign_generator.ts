import {envConfig} from 'presentation/constant/env'
export default class DataToSignGenerator {
  public static generate (nonce: string): string {
    return `Welcome to Quill!

Click to sign in and accept Quill's privacy policy and terms of service.
${envConfig.privacyPolicyUrl}
${envConfig.termsAndConditionsUrl}

This request will not trigger a blockchain transaction or cost any gas fees.

Nonce:
${nonce}`
  }
}
